package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.google.common.hash.Hashing;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 16:04 2021/7/15
 * @Desc:
 */
@UtilityClass
public class ErpTempDataHelper {
    public String md5(Object... values) {
        return Hashing.md5().hashString(Stream.of(values).map(Objects::toString).collect(Collectors.joining("^")), StandardCharsets.UTF_8).toString();
    }

    public Bson filterByIdOrNum(boolean byNum,ErpTempData message){
        Document result = new Document();
        result.put("tenant_id", message.getTenantId());
        result.put("dc_id", message.getDcId());
        result.put("obj_api_name", message.getObjApiName());
        if (byNum){
            result.put("data_number", message.getDataNumber());
        }else {
            result.put("data_id", message.getDataId());
        }
        return result;
    }

    public Bson updateByEiObjDataId(ErpTempData message) {
        return filterByIdOrNum(false, message);
    }

    public Bson updateByEiObjDataNum(ErpTempData message) {
        return filterByIdOrNum(true, message);
    }

    @SuppressWarnings("unchecked")
    public Bson upsert(ErpTempData message) {
        Document setOnInsertDoc = new Document();
        Document updateDoc = new Document();
        Document addToSet = new Document();
        Document currentDateDoc = new Document();
        Document timestamp = new Document();
        timestamp.append("$type","date");

        //更新
        updateDoc.append("update_time", System.currentTimeMillis())
                .append("data_id", AssertUtil.notEmpty(message.getDataId(), I18NStringEnum.s230, message.getTenantId()))
                .append("data_number", AssertUtil.notNull(message.getDataNumber(), I18NStringEnum.s234, message.getTenantId()));
        //不为空,才更新
        if(message.getOperationType()!=null){
            if(EventTypeEnum.INVALID.getType()==message.getOperationType()||EventTypeEnum.DELETE_DIRECT.getType()==message.getOperationType()){
                updateDoc.append("operation_type", message.getOperationType());
            }else{//非作废更新为空
                updateDoc.append("operation_type", null);
            }
        }

        if (StringUtils.isNotBlank(message.getDataBody())) {
            updateDoc.append("data_body", message.getDataBody());
        }
        if (message.getStatus() != null) {
            updateDoc.append("status", message.getStatus());
        }
        if (message.getSyncStatusMap() != null) {
            updateDoc.append("sync_status", message.getSyncStatusMap());
        }
        if (StringUtils.isNotBlank(message.getRemark())) {
            updateDoc.append("remark", message.getRemark());
        }
        if (StringUtils.isNotBlank(message.getTraceId())) {
            updateDoc.append("trace_id", message.getTraceId());
        }
        if (message.getLastSyncTime() != null) {
            currentDateDoc.append("new_last_sync_time",timestamp);//轮询使用新字段
            updateDoc.append("last_sync_time", System.currentTimeMillis());//保留原来的字段
        }
        updateDoc.append("expire_time", new Date());//过期时间
        if (message.getDataMd5() != null) {
            updateDoc.append("data_md5", message.getDataMd5());
        }
        if (message.getSyncLogId() != null) {
            updateDoc.append("sync_log_id", message.getSyncLogId());
        }
        if (message.getDataReceiveType() != null) {
            updateDoc.append("data_receive_type", message.getDataReceiveType());
        }
        if (message.getLastPollingTime() != null) {
            updateDoc.append("last_polling_time", message.getLastPollingTime());
        }
        //插入
        setOnInsertDoc
                .append("tenant_id", Objects.requireNonNull(message.getTenantId(), "tenantId cannot be null"))
                .append("dc_id", Objects.requireNonNull(message.getDcId(), "dcId cannot be null"))
                .append("obj_api_name", Objects.requireNonNull(message.getObjApiName(), "objApiName cannot be null"))
                .append("create_time", Objects.requireNonNull(message.getCreateTime(), "createTime cannot be null"));

        //添加到集合
        if (CollectionUtils.isNotEmpty(message.getTaskNum())) {
            addToSet.put("task_num", new Document("$each", message.getTaskNum()));
        }
        if (StringUtils.isNotBlank(message.getLocale())) {
            updateDoc.append("locale", message.getLocale());
        }

        if (message.getAllObjCount() != null) {
            updateDoc.append("all_obj_count", message.getAllObjCount());
        }
        if (message.getPriority() != null) {
            updateDoc.append("priority", message.getPriority());
        }

        Document doc = new Document();
        doc.put("$set", updateDoc);
        doc.put("$setOnInsert", setOnInsertDoc);
        if (!addToSet.isEmpty()) {
            doc.put("$addToSet", addToSet);
        }
        if(!currentDateDoc.isEmpty()){
            doc.put("$currentDate", currentDateDoc);
        }

        return doc;
    }
}
