package com.fxiaoke.open.erpsyncdata.common.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2022/3/20 19:20
 * @Version 1.0
 */
public enum IntegrationStreamNodeEnum {
    SOURCE_SYSTEM_NODE("sourceSystemNode", 1, "源系统"),
    SYNC_CONDITIONS_NODE("syncConditionsNode", 2, "数据范围"),
    BEFORE_FUNCTION_NODE("beforeFunctionNode", 3, "同步前函数"),
    CHECK_SYNC_DATA_MAPPING_NODE("checkSyncDataMappingNode", 4, "ID映射"),
    QUERY_CRM_OBJECT2DEST_NODE_BY_SOURCE("queryCrmObject2DestNodeBySource", 5, "查询CRM"),
    FIELD_MAPPING_NODE("fieldMappingNode", 6, "字段映射"),
    QUERY_CRM_OBJECT2DEST_NODE_BY_DEST("queryCrmObject2DestNodeByDest", 7, "查询CRM"),
    DURATION_FUNCTION_API_NODE("durationFunctionApiNode", 8, "同步中函数"),
    DEST_SYSTEM_NODE("destSystemNode", 9, "目标系统"),
    REVERSE_WRITE_NODE("reverseWriteNode", 10, "回写CRM"),
    AFTER_FUNCTION_NODE("afterFunctionNode", 11, "同步后函数"),
    RESYNC_ERROR_DATA_NODE("reSyncErrorDataNode", 12, "条件重试"),
    NOTIFY_COMPLEMENT_NODE("notifyComplementNode", 13, "通知推送")
    ;

    /**
     * 前端节点名称
     */
    @Getter
    private String nodeName;
    /**
     * 前端no
     */
    private int no;
    private String name;

    IntegrationStreamNodeEnum(String nodeName, int no, String name) {
        this.nodeName = nodeName;
        this.no = no;
        this.name = name;
    }
}
