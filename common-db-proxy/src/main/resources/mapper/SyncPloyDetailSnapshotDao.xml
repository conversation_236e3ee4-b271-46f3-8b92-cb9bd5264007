<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao">

  <update id="updateStatusByPloyDetailId">
    update sync_ploy_detail_snapshot
    <set>
        status = #{status}
    </set>
    where sync_ploy_detail_id = #{syncPloyDetailId}
  </update>

  <update id="updateStatusByPloyDetailIds">
    update sync_ploy_detail_snapshot
    <set>
      status = #{status}
    </set>
    where sync_ploy_detail_id in
    <foreach collection="syncPloyDetailIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and status != #{status}
  </update>

  <delete id="deleteByPloyDetailIds">
    delete from sync_ploy_detail_snapshot
    where sync_ploy_detail_id in
    <foreach collection="syncPloyDetailIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    and source_tenant_id = #{sourceTenantId}
  </delete>

  <select id="countBySourceTenantId" resultType="java.lang.Integer">
    select count(*)
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and status = #{status}
  </select>

  <select id="listNewestBySourceTenantIdAndSrouceObjectApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and source_object_api_name = #{sourceObjectApiName} and status = #{status} order by update_time desc
  </select>

  <select id="get" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and id = #{id}
  </select>

    <select id="listBySourceTenantIdAndObjectApiNames" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId}
    and source_object_api_name  in
        <foreach collection="sourceObjectApiNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    and status = #{status}
  </select>

  <select id="listEnableSnapshotsBySyncPloyDetailsId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where  source_tenant_id = #{sourceTenantId}
      and  sync_ploy_detail_id = #{syncPloyDetailsId} and status = #{status} order by update_time desc
  </select>

  <update id="updateSyncPloyDataDetailById">
    update sync_ploy_detail_snapshot
    set
    "sync_ploy_detail_data" = #{syncPloyDetailData,javaType=com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData,typeHandler = com.fxiaoke.open.erpsyncdata.dbproxy.dao.typehandler.SyncPloyDetailDataTypeHandler}
    where id  =  #{id}
  </update>
  <select id="listBySourceTenantIdAndObjectApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity">
    select * from sync_ploy_detail_snapshot
    where source_tenant_id = #{sourceTenantId} and source_object_api_name=#{sourceObjectApiName} and status=1
  </select>

  <select id="listNewestByDestTenantIdAndDestObjectApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity">
    select *
    from sync_ploy_detail_snapshot
    where dest_tenant_id = #{destTenantId} and dest_object_api_name = #{destObjectApiName} and status = #{status} order by update_time desc
  </select>


  <update id="batchUpdateStatusBySourceTenantIdAndDestTenantIdReverse">
    update sync_ploy_detail_snapshot
    set
    "status" = #{status}
    WHERE (dest_tenant_id = #{destTenantId} AND source_tenant_id = #{sourceTenantId})
    OR
    (dest_tenant_id = #{sourceTenantId} AND source_tenant_id = #{destTenantId})
  </update>

  <select id="listEnableSnapshotTenantId" resultType="java.lang.String">
    select distinct (source_tenant_id)
    from sync_ploy_detail_snapshot
    where status = 1
  </select>

</mapper>