package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpServerStatusResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 17:39 2023/3/13
 * @Desc:
 */
@Ignore
public class ErpServerStatusCheckServiceImplTest extends BaseTest {
    @Autowired
    private ErpServerStatusCheckServiceImpl erpServerStatusCheckService;

    @Test
    public void executorCheckServerUrl() {
        Result<ErpServerStatusResult> erpServerStatusResultResult = erpServerStatusCheckService.executorCheckServerUrl(null,"http://33q28n1342.wicp.vip:32042/zhixin/dbadapt/proxy/api/test");
        System.out.println("");
    }
}