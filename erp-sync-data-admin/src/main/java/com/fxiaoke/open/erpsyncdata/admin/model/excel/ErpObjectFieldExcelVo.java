package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-03-10
 * ERP对象字段导入模板
 */
@Data
public class ErpObjectFieldExcelVo {
    /**
     * erp中间对象apiName
     */
    @ExcelProperty(value = "erpdss.global.global.s3624",index = 0)
    private String erpObjectApiName;
    /**
     * 字段名称
     */
    @ExcelProperty(value = "erpdss.global.global.s1056", index = 1)
    private String fieldLabel;

    /**
     * 字段编码
     */
    @ExcelProperty(value = "erpdss.global.global.s1057",index = 2)
    private String fieldApiName;

    /**
     * 字段类型
     */
    @ExcelProperty(value = "erpdss.global.global.s1058",index = 3)
    private String fieldDefineType;

    /**
     * 字段扩展值
     */
    @ExcelProperty(value = "erpdss.global.global.s1059",index = 4)
    private String fieldExtendValue;

    /**
     * 查看接口字段编码
     */
    @ExcelProperty(value = "erpdss.global.global.s1060",index = 5)
    private String viewCode;

    /**
     * 保存接口字段编码
     */
    @ExcelProperty(value = "erpdss.global.global.s1061",index = 6)
    private String saveCode;

    /**
     * 批量查询接口字段编码
     */
    @ExcelProperty(value = "erpdss.global.global.s1062",index = 7)
    private String queryCode;

    /**
     * 是否必填
     */
    @ExcelProperty(value = "erpdss.global.global.s1063",index = 8)
    private boolean required;


    public static ErpObjectFieldExcelVo getTempData(String erpSplitObjectApiname) {
        ErpObjectFieldExcelVo vo = new ErpObjectFieldExcelVo();
        vo.setErpObjectApiName(erpSplitObjectApiname);
        vo.setFieldLabel("");
        vo.setFieldApiName("");
        vo.setFieldDefineType("");
        vo.setFieldExtendValue("");
        vo.setSaveCode("");
        vo.setViewCode("");
        vo.setQueryCode("");
        vo.setRequired(false);
        return vo;
    }

}
