package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 17:29 2020/9/12
 * @Desc:
 */
@Data
@ApiModel("根据真实对象获取对象信息参数")
public class QueryErpObjectByActualObjArg implements Serializable {
    @ApiModelProperty("对象apiName")
    public String erpObjectApiName;
    @ApiModelProperty("拆分批次")
    public Integer splitSeq;
}
