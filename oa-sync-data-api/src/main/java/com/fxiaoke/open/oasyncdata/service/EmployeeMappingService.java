package com.fxiaoke.open.oasyncdata.service;


import com.facishare.uc.api.model.BaseArg;
import com.fxiaoke.open.oasyncdata.arg.QueryEmployeeMappingListArg;
import com.fxiaoke.open.oasyncdata.model.PageArg;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.QueryResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 13:54 2020/8/19
 * @Desc:
 */
public interface EmployeeMappingService {
    Result<String> updateOAEmployeeMappingByErpId(String tenantId, EmployeeMappingResult employeeMappingResult);
}
