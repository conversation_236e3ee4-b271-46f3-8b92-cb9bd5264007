package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpSyncTimeManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.RedisDataSourceManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PollingIntervalDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.PollingTempFailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LocalDispatcherUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncTimeUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.PollingIntervalUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.CronPattern;
import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExtraEventTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpSyncTimeVO;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.fxiaoke.open.erpsyncdata.probe.service.ProbeDataTaskService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.PROBE_DATA_SET_REDIS_KEY;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Slf4j
@Service("probeDataTaskService")
public class ProbeDataServiceImpl implements ProbeDataTaskService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ProbeErpDataService probeErpDataService;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ErpSyncTimeManager erpSyncTimeManager;
    @Autowired
    private RedisDataSourceManager redisDataSourceManager;
    @Autowired
    private PollingTempFailDao pollingTempFailDao;

    private LocalDispatcherUtil<TriggerPollingData> localDispatcherUtil = null;


    private final Map<String, CronPattern> cronPatternMap = new ConcurrentHashMap<>();
    private final static String NO_SEND_DETAIL_EVENT_CRM_OBJ_SET = "NO_SEND_DETAIL_EVENT_CRM_OBJ_SET";

    @PostConstruct
    public void postInit() {
        //配置先写死
        Integer threadSize = 20;
        Integer queueCapacity = 10;
        Integer batchProcessTimeLimitInSecond = 30;
        localDispatcherUtil = new LocalDispatcherUtil<>((key, dataList) -> {
            //批量处理的代码放这里
            doPollingTempData(dataList);
        }, threadSize);
        localDispatcherUtil.setQueueCapacity(queueCapacity);
        localDispatcherUtil.setBatchProcessTimeLimitInSecond(batchProcessTimeLimitInSecond);
    }

    public Set<String> getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET() {
        String value = redisDataSource.get(this.getClass().getSimpleName()).get(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET);
        Set<String> set = null;
        if (StringUtils.isNotEmpty(value)) {
            set = JSONObject.parseObject(value, Set.class);
        } else {
            set = configCenterConfig.getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET();
            redisDataSource.get(this.getClass().getSimpleName()).set(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET, JSONObject.toJSONString(set));
            redisDataSource.get(this.getClass().getSimpleName()).expire(NO_SEND_DETAIL_EVENT_CRM_OBJ_SET, 15 * 60L);//15分钟过期
        }
        return set;
    }

    @Override
    public void retryPollingTempFail(@ContextEi() String tenantId) {
        Integer limit = 100;
        String maxId = null;
        while (true) {
            final List<String> allFailEntity = pollingTempFailDao.getNextFailIds(tenantId, maxId, limit);
            if (CollectionUtils.isEmpty(allFailEntity)) {
                break;
            }
            maxId = allFailEntity.get(allFailEntity.size() - 1);
//            兼容环境还没有发布对应的代码时,调用失败的问题
//            比如在gray环境中的,挪到了normal,服务还没有部署到normal,导致报错
            for (String id : allFailEntity) {
                try {
                    TraceUtil.setEi(tenantId);
                    probeErpDataService.retryPollingTempFail(id);
                } catch (Exception e) {
                    log.error("retryPollingTempFail 调用重试报错 tenantId:{} id:{}", tenantId, id, e);
                    pollingTempFailDao.fail(id, i18NStringManager.getByEi(I18NStringEnum.s3721, tenantId) + e.getMessage());
                }finally {
                    TraceUtil.removeTrace();
                }
            }
        }
    }
    /**
     * 执行所有策略
     *
     * @param tenantId   企业ei
     * @param objApiName
     * @return
     */
    @Override
    public Result<Void> executeProbeErpDataJob(String tenantId, String objApiName) {
        TraceUtil.initTraceWithFormat(tenantId);
        log.info("execute probe erp begin,{},{}", tenantId, objApiName);
        List<ErpSyncTimeVO> syncTimeVos = getErpSyncTimeVoList(tenantId, objApiName, true);
        routeExecutePloys(tenantId, syncTimeVos);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> executeRollingErpDataFromMongoJob(String tenantId) {
        TraceUtil.initTraceWithFormat(tenantId);
        List<ErpSyncTimeVO> erpSyncTimeVoList = getErpSyncTimeVoList(tenantId, null, false);
        routeRollingErpDataFromMongoJob(tenantId, erpSyncTimeVoList);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> executeRollingErpHistoryDataJob(String tenantId) {
        TraceUtil.initTraceWithFormat(tenantId);
        probeErpDataService.executeRollingErpHistoryDataJob(tenantId);
        return Result.newSuccess();
    }

    /**
     * 轮询ERP相同对象多个策略合并为一个策略更新轮询（可能对面一些根据策略的操作有影响）
     * 轮询临时库相同策略合并为轮询更新数据，不同策略分开轮询。
     *
     * @param tenantId
     * @param objApiName
     * @param queryErp   轮询erp为true
     * @return
     */
    protected List<ErpSyncTimeVO> getErpSyncTimeVoList(String tenantId, String objApiName, boolean queryErp) {
        Long currentTime = System.currentTimeMillis();
        List<ErpSyncExtentDTO> erpSyncTimeExtentDTO = erpSyncTimeDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listSyncExtentByTenantId(tenantId);
        return transSyncTimeDto2Vo(tenantId, objApiName, queryErp, currentTime, erpSyncTimeExtentDTO);
    }

    private List<ErpSyncTimeVO> transSyncTimeDto2Vo(String tenantId, String objApiName, boolean queryErp, Long currentTime, List<ErpSyncExtentDTO> erpSyncTimeExtentDTO) {
    /*
    同时包含新增和更新，把新增去掉，只保留更新
    轮询ERP相同对象多个策略合并为一个策略更新轮询（可能对面一些根据策略的操作有影响）
    轮询临时库相同策略合并为轮询更新数据，不同策略分开轮询。
     */
        if (queryErp) {
            erpSyncTimeExtentDTO = SyncTimeUtil.mergeEventTypeByObj(erpSyncTimeExtentDTO);
        } else {
            SyncTimeUtil.mergeEventTypeByPloy(erpSyncTimeExtentDTO);
        }
        List<ErpSyncTimeVO> syncTimeVos = new ArrayList<>();
        for (ErpSyncExtentDTO dto : erpSyncTimeExtentDTO) {
            if (StringUtils.isNotBlank(objApiName) && !objApiName.equals(dto.getObjectApiName())) {
                //支持指定对象
                continue;
            }
            if(EventTypeEnum.DELETE_DIRECT.getType()==dto.getOperationType()){//过滤掉删除
                continue;
            }
            if (ExtraEventTypeEnum.TRANS_MAP.containsKey(dto.getOperationType())) {
                if (StringUtils.isBlank(objApiName) && queryErp) {
                    //扩展类型只能指定对象轮询，轮询mongo不跳过推送的数据
                    continue;
                }
                dto.setOperationType(ExtraEventTypeEnum.TRANS_MAP.get(dto.getOperationType()));
            }
            SyncPloyDetailData ployDetail = dto.getSyncPloyDetailData();
            //筛除未开启的事件
            if (!ployDetail.getSyncRules().getEvents().contains(dto.getOperationType())) {
                continue;
            }
            //目前所有主动轮询都是不传objApiName的，手动触发都是传objApiName的，只有主动轮询才校验时间间隔
            if (StringUtils.isBlank(objApiName) && queryErp) {
                boolean needSync = needSyncWithRule(tenantId, dto.getObjectApiName(), currentTime, dto.getUpdateTime(), dto.getSnapshotCreateTime(), dto.getPollingInterval(), dto.getId());
                if (!needSync) {
                    log.debug("notNeedSync currentTime={} ErpSyncExtentDTO={} objApiName={}", currentTime, dto, objApiName);
                    continue;
                }
            }
            ErpSyncTimeVO syncTime = new ErpSyncTimeVO();
            BeanUtils.copyProperties(dto, syncTime);
            if (getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET().contains(ployDetail.getDestObjectApiName())) {
                //不需要发送明细数据
                syncTime.setNeedSendDetailEvent(false);
            }
            syncTimeVos.add(syncTime);
        }
        return syncTimeVos;
    }


    /**
     * 路由执行任务
     *
     * @param tenantId
     * @param syncTimeVos
     */
    private void routeExecutePloys(String tenantId, List<ErpSyncTimeVO> syncTimeVos) {
        probeErpDataService.executePloys(tenantId, syncTimeVos);
    }

    /**
     * 路由执行从mongo获取erp数据的任务
     *
     * @param tenantId
     * @param syncTimeVos
     */
    private void routeRollingErpDataFromMongoJob(String tenantId, List<ErpSyncTimeVO> syncTimeVos) {
        probeErpDataService.executeRollingErpDataFromMongoJob(tenantId, syncTimeVos);
    }

    /**
     * 根据集成流配置判断这一分钟是否执行
     *
     * @param tenantId
     * @param objApiName
     * @param currentTime
     * @param pollingIntervalStr
     * @param syncTimeId
     * @return
     */
    public boolean needSyncWithRule(String tenantId, String objApiName, Long currentTime, Long updateTime, Long snapshotCreateTime, String pollingIntervalStr, String syncTimeId) {
        log.info("trace check ,{},{}", currentTime, pollingIntervalStr);
        CronPattern cronPattern = null;
        Boolean result = null;
        if (StrUtil.isBlank(pollingIntervalStr)) {
            //配置为空时默认6分钟轮询
            cronPattern = cronPatternMap.computeIfAbsent("0/6 * * * *", CronPattern::of);
            result = cronPattern.match(currentTime, false);
        } else {
            PollingIntervalDto pollingIntervalDto = JSON.parseObject(pollingIntervalStr, PollingIntervalDto.class);
            String cron = pollingIntervalDto.getCronExpression();
            if (StrUtil.isNotBlank(cron)) {
                //当cron表达式不为空时，直接使用
                cronPattern = cronPatternMap.computeIfAbsent(cron, CronPattern::of);
                result = cronPattern.match(currentTime, false);
            } else {
                //历史数据cron为空，转换
                PollingIntervalApiDto apiDto = PollingIntervalUtil.dto2ApiDto(pollingIntervalDto);
                try {
                    cron = PollingIntervalUtil.getCronFromApiDto(tenantId, apiDto, configCenterConfig.getCronBeginMinute(tenantId, objApiName));
                    cronPattern = cronPatternMap.computeIfAbsent(cron, CronPattern::of);
                } catch (ErpSyncDataException e) {
                    log.warn("set cron failed,try trans it begin");
                    //目前存在按天的时间间隔，需要修改一下数据
                    cron = erpSyncTimeManager.transAndUpdateDayCron(tenantId, objApiName, pollingIntervalDto, apiDto, syncTimeId);
                    cronPattern = cronPatternMap.computeIfAbsent(cron, CronPattern::of);
                } catch (Exception e) {
                    log.error("set cron exception", e);
                    //异常时使用默认cron。6分钟一次
                    cronPattern = cronPatternMap.computeIfAbsent("0/6 * * * *", CronPattern::of);
                }
                //统一使用cron表达式
                result = cronPattern.match(currentTime, false);
                log.info("trace check,cron:{},match:{}", cron, result);
            }
        }
        //不符合时，如果当前时间大于sync_time的更新时间的下一个时间也执行
        if (!result) {
            GregorianCalendar calendar = new GregorianCalendar(TimeZone.getDefault());
            if (updateTime < snapshotCreateTime) {
                calendar.setTimeInMillis(snapshotCreateTime);//如果启用策略，没有更新过updateTime，会以启用策略时间作为判断，目的是过滤停用时间太久导致符合条件的情况
            } else {
                calendar.setTimeInMillis(updateTime);
            }
            Long nextMatchTime = cronPattern.nextMatchAfter(calendar).getTimeInMillis();
            result = currentTime > nextMatchTime;
            log.info("needSyncWithRule check,currentTime:{},nextMatchTime:{},result:{}", currentTime, nextMatchTime, result);
        }
        return result;
    }



    /**
     * 执行策略，会进入队列，并尝试触发轮询。
     * 无论定时任务还是主动触发的轮询，轮询结束后会重新检查队列。
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    @Override
    public Result<Void> enqueueAndTryExecute(String tenantId, String objApiName) {
        TraceUtil.initTraceWithFormat(tenantId);
        String queueRedisKey = String.format(PROBE_DATA_SET_REDIS_KEY, tenantId);
        redisDataSource.get(this.getClass().getSimpleName()).sadd(queueRedisKey, objApiName);
        probeErpDataService.executePloys(tenantId, new ArrayList<>());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> enqueueAndTryExecutePollingTempData(TriggerPollingData triggerPollingData) {
        log.info("enqueueAndTryExecutePollingTempData triggerPollingData={}", triggerPollingData);
        String tenantId = triggerPollingData.getTenantId();
        TraceUtil.initTraceWithFormat(tenantId);
        if (CollectionUtils.isNotEmpty(triggerPollingData.getObjApiName())) {//抢锁失败，发送的触发，objApiName就是空的
            redisDataSourceManager.addTriggerPollingTempDataObj(tenantId, triggerPollingData.getObjApiName(), this.getClass().getSimpleName());
        }
        List<TriggerPollingData> dataList = Lists.newArrayList();
        dataList.add(triggerPollingData);
        //发送本地聚合
        localDispatcherUtil.produceDataBatch(tenantId, dataList);//如果数量达到了阈值，本线程会触发doPollingTempData
        return Result.newSuccess();
    }

    //批量处理触发
    private void doPollingTempData(List<TriggerPollingData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        String tenantId = dataList.get(0).getTenantId();
        //重新初始化traceId
        TraceUtil.initTraceWithFormat(tenantId);
        log.info("doPollingTempData task dataList={}", dataList);
        probeErpDataService.executePollingTempData(tenantId);
    }
}
