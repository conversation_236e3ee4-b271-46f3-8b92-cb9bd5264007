package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AlarmRuleMatchModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RoleModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.UserRoleUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * ErpAlarmRuleManager 单元测试
 * <AUTHOR>
 * @date 2023-11-23
 */
@Ignore
@Slf4j
public class ErpAlarmRuleManagerTest extends BaseTest {
    @Autowired
    private ErpAlarmRuleManager erpAlarmRuleManager;
    @Autowired
    private PaasAuthService paasAuthService;

    @Test
    public void insert() {
        ErpAlarmRuleEntity entity = new ErpAlarmRuleEntity();
        entity.setTenantId("81243");
        entity.setDataCenterId("64feb0e7a8fadb0001cbaf6f");
        entity.setPloyDetailIds("10e860b3be0e4261898c5b7ec8fd7237;536e2437eb81401e944ea6a7543690fb");
        entity.setAlarmRuleType(AlarmRuleType.GENERAL);
        entity.setAlarmRuleName("通用告警");
        entity.setAlarmType(AlarmType.POLLING_ERP_API_EXCEPTION);
        entity.setAlarmLevel(AlarmLevel.IMPORTANT);
        entity.setUserIds("1069");
        entity.setThreshold(100);
        int count = erpAlarmRuleManager.insert(entity);
        assert count == 1;

        findData();
        findDataByDcId();
    }

    @Test
    public void findData(){
        List<ErpAlarmRuleEntity> entityList = erpAlarmRuleManager.findData("81243");
        assert entityList.size() > 0;
        findDataByDcId();
        findDataByAlarmRuleType();
    }

    @Test
    public void count(){
        int size = erpAlarmRuleManager.count("88521","643f7322b54ea80001767d86",null,null);
        assert size > 0;
    }

    @Test
    public void findDataByDcId(){
        List<ErpAlarmRuleEntity> entityList = erpAlarmRuleManager.findData("81243",
                "64feb0e7a8fadb0001cbaf6f",
                2,
                2 * 2);
        assert entityList.size() > 0;
    }

    @Test
    public void findDataByAlarmRuleType(){
        List<ErpAlarmRuleEntity> entityList = erpAlarmRuleManager.findData("81243",
                "64feb0e7a8fadb0001cbaf6f",AlarmRuleType.GENERAL);
        assert entityList.size() > 0;
    }

    @Test
    public void getRoleModelList() {
        List<RoleModel> roleModels = UserRoleUtils.getRoleModelList("81243", Lists.newArrayList("00000000000000000000000000000006"), paasAuthService);
        assert roleModels.size() > 0;
        getRoleUserList();
    }

    @Test
    public void getRoleUserList() {
        List<String> userList = UserRoleUtils.getRoleUserList("81243", Lists.newArrayList("00000000000000000000000000000006"), paasAuthService);
        assert userList.size() > 0;
    }

    @Test
    public void checkAndInitAlarmRuleData() {
        erpAlarmRuleManager.checkAndInitAlarmRuleData("81243","64feb0e7a8fadb0001cbaf6f",null);
    }

    @Test
    public void getAlarmTypeConfig() {
        List<ErpAlarmRuleEntity> entity = erpAlarmRuleManager.getAlarmTypeConfig("88521","643f7322b54ea80001767d86",
                "61b4c18e0b25401bb55d94078fabf699",
                AlarmType.POLLING_ERP_API_EXCEPTION);
        assert CollectionUtils.isNotEmpty(entity);
        AlarmRuleMatchModel mathResult = erpAlarmRuleManager.getThresholdMathResult(entity, 301);
        assert mathResult != null;
    }
}