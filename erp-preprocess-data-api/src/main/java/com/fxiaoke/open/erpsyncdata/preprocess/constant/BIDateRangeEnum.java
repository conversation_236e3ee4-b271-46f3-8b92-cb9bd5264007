package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2024/3/29 15:52
 * 大屏看板的信息，线上线下统一
 * @see https://wiki.firstshare.cn/pages/viewpage.action?pageId=388236290
 * @desc
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BIDateRangeEnum {

    /**
     *  自定义筛选日期的
     */
    LAST_SEVEN_DAYS("16","最近7天",7,604800000L),

    LAST_FOURTEEN_DAYS("16","最近14天",14,1209600000L),

    LAST_THIRTY_DAYS("16","最近30天",30,2592000000L),

    INTERVAL_MIN_TEN("16","最近10分钟",10,600000L),

    YESTERDAY("1","昨天",0,0L),

    THIS_WEEK("2","本周",0,0L),

    LAST_WEEK("3","昨天",0,0L),

    THIS_MONTH("4","本月",0,0L),

    LAST_MONTH("5","上月",0,0L),

    THIS_YEAR("6","本年度",0,0L),

    TODAY("11","今天",0,0L),
    ;
    private String dataRangeId;
    private String rangeDesc;

    private Integer lastDays;
    //过去多少毫秒
    private Long calculateTimesMillseconds;

    public static boolean lastTimeEnum(BIDateRangeEnum biDateRangeEnum){
        if(YESTERDAY.equals(biDateRangeEnum)||LAST_MONTH.equals(biDateRangeEnum)){
            return true;
        }
        return false;
    }
}
