package com.fxiaoke.open.oasyncdata.constant;

/**
 * 尽量参考  com.fxiaoke.open.erpsyncdata.common.constant.FieldType
 * <AUTHOR>
 * @Date: 16:23 2020/8/22
 * @Desc:描述和code对应关系前端写死的，改动记得通知前端
 */
public enum ErpFieldTypeEnum {
    /**
     * 文本
     */
    text,
    /**
     * 长文本
     */
    long_text,
    /**
     * string  字符串
     */
    string,
    /**
     * object_reference  查找关联
     */
    object_reference,
    /**
     * object_reference  查找关联多选
     */
    object_reference_many,
    /**
     * select_one  单选
     */
    select_one,
    /**
     * country  国家
     */
    country,
    /**
     * province  省份
     */
    province,
    /**
     * city 城市
     */
    city,
    /**
     * district  区
     */
    district,
    /**
     * category  erp:物料分组  crm:产品分类
     */
    category,
    /**
     * employee 人员
     */
    employee,
    /**
     * department  部门
     */
    department,
    /**
     * 布尔值
     */
    true_or_false,
    /**
     * 日期
     */
    date,
    /**
     * 日期时间
     */
    date_time,
    /**
     * 多选
     */
    select_many,
    /**
     * 人员多选
     */
    employee_many,
    /**
     * 数值
     */
    number,
    /**
     * 金额
     */
    currency,
    /**
     * master_detail  关联主对象字段
     */
    master_detail,
    /**
     * 标识id字段
     */
    id,
    /**
     * 业务类型
     */
    record_type,

    /**
     * K3专用，明细字段
     */
    detail,
    /**
     * K3专用，用户字段
     */
    user,

    /**
     * 统计字段
     */
    count,
    /**
     * 邮箱
     */
    email,
    /**
     * 手机号
     */
    phone_number,
    /**
     * oa用户
     */
    employee_oa,

    /**
     * 乡镇
     */
    town,
    /**
     * 附件
     */
    file_attachment,
    ;

    public static ErpFieldTypeEnum getFieldType(String fieldType) {
        ErpFieldTypeEnum erpFieldTypeEnum = null;
        try {
            erpFieldTypeEnum=valueOf(fieldType);
        } catch (Exception e) {

        }
        return erpFieldTypeEnum;
    }

}
