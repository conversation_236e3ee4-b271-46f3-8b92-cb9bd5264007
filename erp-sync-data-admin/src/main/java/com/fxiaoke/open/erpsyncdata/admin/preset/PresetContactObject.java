package com.fxiaoke.open.erpsyncdata.admin.preset;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PresetContactObject extends AbstractPresetObject {
    @Override
    protected List<String> getFormId() {
        return Lists.newArrayList(K3CloudForm.BD_CommonContact);
    }

    @Override
    public List<String> getFieldMappingJson() {
        String json = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"ContactObj\",\"destObjectApiName\":\"BD_CommonContact.BillHead\",\"fieldMappings\":[{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"BD_Customer\",\"optionMappings\":[],\"destApiName\":\"FCompanyType\",\"destType\":\"select_one\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceType\":\"text\",\"destApiName\":\"FName\",\"destType\":\"long_text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"AccountObj\",\"destApiName\":\"FCompany.FNumber\",\"destType\":\"object_reference\",\"destTargetApiName\":\"BOS_ItemClass.BillHead\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"email\",\"sourceType\":\"email\",\"destApiName\":\"FEmail\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"job_title\",\"sourceType\":\"text\",\"destApiName\":\"FPost\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"mobile1\",\"sourceType\":\"phone_number\",\"destApiName\":\"FMobile\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"tel1\",\"sourceType\":\"phone_number\",\"destApiName\":\"FTel\",\"destType\":\"text\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"add\",\"sourceType\":\"text\",\"destApiName\":\"FBizAddress\",\"destType\":\"long_text\"}]},\"detailObjectMappings\":[]}";
        return Lists.newArrayList(json);
    }


    @Override
    public List<SyncPloyDetailCreateArg> getSyncPloyDetailCreateArg() {
        SyncPloyDetailCreateArg arg = new SyncPloyDetailCreateArg();
        arg.setPloyId(tenantId);
        arg.setSourceTenantIds(Lists.newArrayList(tenantId));
        arg.setSourceTenantType(TenantType.CRM);
        arg.setSourceObjectApiName("ContactObj");
        arg.setDestTenantIds(Lists.newArrayList(tenantId));
        arg.setDestTenantType(TenantType.ERP);
        arg.setDestObjectApiName("BD_CommonContact.BillHead");
        arg.setDcId(dataCenterId);
        return Lists.newArrayList(arg);
    }
}
