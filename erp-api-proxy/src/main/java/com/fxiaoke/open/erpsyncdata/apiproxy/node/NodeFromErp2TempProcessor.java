package com.fxiaoke.open.erpsyncdata.apiproxy.node;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ErpTempDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.AbsNodeProcessor;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.context.FromErp2TempCtx;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 获取ERP数据并进入临时库
 *
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class NodeFromErp2TempProcessor extends AbsNodeProcessor<FromErp2TempCtx> {
    @Autowired
    private ErpDataService erpDataService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;

    public NodeFromErp2TempProcessor() {
        super(DataNodeNameEnum.EnterTempData);
    }

    @Override
    protected void preReport(FromErp2TempCtx ctx) {
        //先补录一下streamIds
        List<String> streamIds = syncPloyDetailManager.listEnableStreamIdsBySrcObj(ctx.getTenantId(), ctx.getDcId(), ctx.getSplitObjApiName(), SyncPloyDetailStatusEnum.ENABLE.getStatus());
        ctx.setStreamIds(streamIds);
    }

    @Override
    protected void postReport(FromErp2TempCtx ctx) {
        LogIdUtil.clear();
    }

    @Override
    public FromErp2TempCtx processMessage(FromErp2TempCtx context) {
        String logId = syncLogManager.initLogId(context.getTenantId(), context.getTimeFilterArg().getObjAPIName());
        log.info("listErpObjDataByTime init log id,{}", logId);
        TimeFilterArg timeFilterArg = context.getTimeFilterArg();
        IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(context.getTenantId(), context.getDcId(), context.getSplitObjApiName(), timeFilterArg.getObjAPIName());
        String dcId = context.getDcId();
        Result<StandardListData> result = erpDataService.listStandardErpObjDataByTime(timeFilterArg, dcId);
        if (result.isSuccess()) {
            //入库，在里面会上报状态
            erpTempDataManager.batchUpsertErpTempData(timeFilterArg.getTenantId(), dcId, timeFilterArg.getObjAPIName(), timeFilterArg.getOperationType(), result.getData(), idFieldKey, true,null);
        }
        //转换格式
        Result<ListErpObjDataResult> targetResult = FormatConvertUtil.listResultStdErp2Crm(timeFilterArg.getTenantId(), timeFilterArg.getObjAPIName(), result);
        context.setResult(targetResult);
        return context;
    }
}
