package com.facishare.open.erp.connector.proxy.facebook.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erp.connector.proxy.facebook.service.FacebookApiClient;
import com.facishare.open.erp.connector.proxy.facebook.service.FacebookClientHelp;
import com.facishare.open.erp.connector.proxy.model.Oauth2ConnectParam;
import com.facishare.open.erp.connertor.sdk.controller.IOauthLoginController;
import com.facishare.open.erp.connertor.sdk.controller.facebook.IFacebookController;
import com.facishare.open.erp.connertor.sdk.model.GetUserToken;
import com.facishare.open.erp.connertor.sdk.model.RefreshToken;
import com.facishare.open.erp.connertor.sdk.model.dto.FormMetaData;
import com.facishare.open.erp.connertor.sdk.model.dto.IdAndName;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetAllForm;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetAllPage;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetFormMetaData;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.restfb.DefaultFacebookClient;
import com.restfb.FacebookClient;
import com.restfb.Version;
import com.restfb.types.Account;
import com.restfb.types.User;
import com.restfb.types.ads.LeadgenForm;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/22 17:10:13
 */
@RestController
@RequestMapping("erp/proxy/facebook")
public class FacebookController implements IFacebookController, IOauthLoginController {

    @RequestMapping("/getUserToken")
    public GetUserToken.Result getUserToken(@RequestBody GetUserToken.Arg arg) {
        // FacebookClient facebookClient = new DefaultFacebookClient(Version.VERSION_16_0);
        final FacebookClient facebookClient = FacebookClientHelp.getFacebookClient(null);
        final String appId = arg.getAppId();
        final String redirectUrl = arg.getRedirectUri();
        final String appSecret = arg.getAppSecret();

        FacebookClient.AccessToken accessToken = facebookClient.obtainUserAccessToken(appId, appSecret, redirectUrl, arg.getVerificationCode());

        final String token = accessToken.getAccessToken();
        // 封装下,方便以后扩展
        final Oauth2ConnectParam object = new Oauth2ConnectParam();
        object.setToken(token);
        final String jsonString = JSON.toJSONString(object);

        // FacebookClient client = new DefaultFacebookClient(token, Version.VERSION_16_0);
        final FacebookApiClient client = new FacebookApiClient(arg.getTenantId(), jsonString);
        User user = client.connectionObject("me", User.class, null);
        if(Strings.isNullOrEmpty(user.getName())){
            user.setName(" ");
        }
        return new GetUserToken.Result(user.getName(), jsonString, null);
    }

    @Override
    public RefreshToken.Result refreshToken(final RefreshToken.Arg arg) {
        return null;
    }

    @RequestMapping("/getAllPage")
    public GetAllPage.Result getAllPage(@RequestBody GetAllPage.Arg arg) {
        final FacebookApiClient client = new FacebookApiClient(arg.getTenantId(), arg.getConnectParam());

        final List<IdAndName> pages = client.connectionWithHandler("me/accounts", Account.class, ImmutableMap.of("fields", "id,name,access_token"), account -> new IdAndName(account.getId(), account.getName()));

//        List<IdAndName> result = new ArrayList<>();
//        boolean existAccountId = false;
//        if(!Objects.isNull(arg.getAccountIds()) && arg.getAccountIds().length()>0){
//            existAccountId = true;
//            List<String> pageIds = Arrays.stream(arg.getAccountIds().split(",")).collect(Collectors.toList());
//            result = pages.stream().filter(page->pageIds.contains(page.getId())).collect(Collectors.toList());
//        }
//        return existAccountId?new GetAllPage.Result(result):new GetAllPage.Result(pages);

        return new GetAllPage.Result(pages);
    }

    @RequestMapping("/getAllForm")
    public GetAllForm.Result getAllForm(@RequestBody GetAllForm.Arg arg) {
        final FacebookApiClient client = new FacebookApiClient(arg.getTenantId(), arg.getConnectParam());

        final List<IdAndName> forms = client.connectionWithHandler(arg.getPageId(), arg.getPageId() + "/leadgen_forms", LeadgenForm.class, ImmutableMap.of("fields", "id,name"), form -> new IdAndName(form.getId(), form.getName()));


        return new GetAllForm.Result(forms);
    }

    @RequestMapping("/getFormMetaData")
    public GetFormMetaData.Result getFormMetaData(@RequestBody GetFormMetaData.Arg arg) {
        final FacebookApiClient client = new FacebookApiClient(arg.getTenantId(), arg.getConnectParam());

        final String s = client.connectionObject(arg.getPageId(), arg.getFormId(), String.class, ImmutableMap.of("fields", "questions"));

        final JSONArray questions = JSON.parseObject(s).getJSONArray("questions");
        List<FormMetaData> metaDataList = new ArrayList<>();
        questions.stream()
                .map(o -> (JSONObject) o)
                .forEach(jsonObject -> {
                    final FormMetaData formMetaData = jsonObject.toJavaObject(FormMetaData.class);
                    if (!Objects.equals("CUSTOM", formMetaData.getType())) {
                        metaDataList.add(formMetaData);
                        return;
                    }

                    if (CollectionUtils.isNotEmpty(formMetaData.getOptions())) {
                        formMetaData.setType(FormMetaData.TYPE_SELECT_ONE);
                        metaDataList.add(formMetaData);
                        return;
                    }

                    if (!jsonObject.containsKey("conditional_questions_choices")) {
                        metaDataList.add(formMetaData);
                        return;
                    }
                    final JSONArray conditionalQuestionsChoices = jsonObject.getJSONArray("conditional_questions_choices");
                    formMetaData.setType(FormMetaData.TYPE_SELECT_ONE);
                    formMetaData.setOptions(getOptions(conditionalQuestionsChoices, 0));
                    metaDataList.add(formMetaData);

                    final JSONArray dependentConditionalQuestions = jsonObject.getJSONArray("dependent_conditional_questions");
                    AtomicInteger question = new AtomicInteger(0);
                    dependentConditionalQuestions.stream()
                            .map(o -> (JSONObject) o)
                            .forEach(json -> {
                                final FormMetaData formMetaData1 = new FormMetaData();
                                formMetaData1.setKey(json.getString("field_key"));
                                formMetaData1.setLabel(json.getString("name"));
                                formMetaData1.setType(FormMetaData.TYPE_SELECT_ONE);

                                final int incrementAndGet = question.incrementAndGet();
                                formMetaData1.setOptions(getOptions(conditionalQuestionsChoices, incrementAndGet));
                                metaDataList.add(formMetaData1);
                            });
                });
        return new GetFormMetaData.Result(metaDataList);
    }

    private List<FormMetaData.Options> getOptions(final JSONArray list, final int incrementAndGet) {
        return getOptionsStream(list.stream().map(o -> (JSONObject) o), incrementAndGet)
                .distinct()
                .map(json -> new FormMetaData.Options(json.getString("customized_token"), json.getString("value")))
                .collect(Collectors.toList());
    }

    private Stream<JSONObject> getOptionsStream(final Stream<JSONObject> stream, int incrementAndGet) {
        if (incrementAndGet == 0) {
            return stream;
        }

        final Stream<JSONObject> nextQuestionChoices = stream
                .flatMap(jsonObject -> jsonObject.getJSONArray("next_question_choices").stream().map(o -> (JSONObject) o));
        return getOptionsStream(
                nextQuestionChoices
                , --incrementAndGet
        );
    }
}
