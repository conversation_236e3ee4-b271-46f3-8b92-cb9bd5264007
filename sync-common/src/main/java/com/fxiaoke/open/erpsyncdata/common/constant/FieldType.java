package com.fxiaoke.open.erpsyncdata.common.constant;

import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Set;

public interface FieldType {
    String ID = "id";
    String NUMBER = "number";
    String PERCENTILE = "percentile";
    String CURRENCY = "currency";
    String DATE = "date";
    String TIME = "time";
    String DATE_TIME = "date_time";
    //布尔类型
    String BOOL = "true_or_false";
    String STRING = "string";
    String TEXT = "text";
    String LONG_TEXT = "long_text";
    String PHONE_NUMBER = "phone_number";
    String EMAIL = "email";
    String SELECT_ONE = "select_one";
    String RECORD_TYPE = "record_type";
    String SELECT_MANY = "select_many";
    String EMPLOYEE_MANY = "employee_many";
    String DEPARTMENT_MANY = "department_many";
    String COUNTRY = "country";
    String PROVINCE = "province";
    String DISTRICT = "district";
    String CITY = "city";
    /**
     * 产品分类
     */
    String CATEGORY = "category";
    String USE_RANGE = "use_range";
    /**
     * quote field support
     */
    String QUOTE = "quote";
    String EMPLOYEE = "employee";
    String DEPARTMENT = "department";
    /***
     * 计算字段
     */
    String FORMULA = "formula";
    String COUNT = "count";
    /***
     * 引用字段类型(查找关联)
     */
    String OBJECT_REFERENCE = "object_reference";
    String OBJECT_REFERENCE_MANY = "object_reference_many";
    String MASTER_DETAIL = "master_detail";
    //文件类型
    String IMAGE = "image";
    String FILE_ATTACHMENT = "file_attachment";

    static boolean isFieldTypeExist(String fieldType) {
        if(StringUtils.isEmpty(fieldType)) return false;
        fieldType = fieldType.trim();

        if(StringUtils.equalsIgnoreCase(fieldType,"id")) return true;
        Field[] fields = FieldType.class.getDeclaredFields();
        for(Field field : fields) {
            if(StringUtils.equalsIgnoreCase(field.getName(),fieldType))
                return true;
        }
        return false;
    }

    static Set<String> getStringTypeList() {
        return Sets.newHashSet(
            FieldType.STRING,
            FieldType.TEXT,
            FieldType.LONG_TEXT,
            FieldType.PHONE_NUMBER,
            FieldType.EMAIL
        );
    }

    static Set<String> getListStringTypeList() {
        return Sets.newHashSet(
            FieldType.EMPLOYEE,
            FieldType.DEPARTMENT
        );
    }

    static Set<String> getNumberTypeList() {
        return Sets.newHashSet(
            FieldType.NUMBER,
            FieldType.DATE,
            FieldType.DATE_TIME
        );
    }

    static Set<String> getObjectReferenceFieldTypeList() {
        return Sets.newHashSet(
            FieldType.OBJECT_REFERENCE
        );
    }
    static Set<String> getObjectsReferenceFieldTypeList() {
        return Sets.newHashSet(
                FieldType.OBJECT_REFERENCE,
                FieldType.OBJECT_REFERENCE_MANY
        );
    }
    static Set<String> getMasterDetailTypeList() {
        return Sets.newHashSet(
            FieldType.MASTER_DETAIL
        );
    }

    static Set<String> getFileFieldTypeList() {
        return Sets.newHashSet(
            FieldType.IMAGE,
            FieldType.FILE_ATTACHMENT
        );
    }

    static Set<String> getBooleanFieldTypeList() {
        return Sets.newHashSet(
            FieldType.BOOL
        );
    }

    static void main(String[] args) {
        boolean exist = isFieldTypeExist("text");
        System.out.println(exist);
    }
}
