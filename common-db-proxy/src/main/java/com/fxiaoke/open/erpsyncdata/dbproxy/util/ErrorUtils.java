package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2024/6/6 15:22
 * @desc
 */
@Slf4j
public class ErrorUtils {

    /**
     * 判断是不是指定的报错
     *
     * @param exception
     * @return
     */
    public static boolean judgeException(Throwable exception) {
        String className = exception.getClass().getName();
        if (ConfigCenter.needRetryException.contains(className)) {
            log.info("Retry needed for exception: " + className);
            return true;
        }
        Throwable cause = exception.getCause();
        if (cause != null) {
            return judgeException( cause);
        }
        return false;
    }
}
