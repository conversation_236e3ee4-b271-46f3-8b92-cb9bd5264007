package com.fxiaoke.open.erpsyncdata.common.monitor;

import lombok.Setter;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/27
 */
public class TimePointRecorderStatic {

    @Setter
    private static TimePointRecorder timePointRecorder;

    public interface TimePointRecorder {
        void begin(String tenantId, String objApiName, String dataId, Long firstParseTime);

        void record(String pointName);

        void recordSync(String pointName, String syncDataId);

        void asyncRecord(String tenantId, String objApiName, String dataId, String pointName);
        void setOtherMsg(String syncDataId, Integer count, Boolean reWriteFailed,String reWriteFailedMsg, Boolean afterSyncFailed, String afterSyncFailedMsg,
                         Boolean syncStepException, String syncStepExceptionMsg, Boolean throwable, String throwableMsg);
        void changeTimePointRecorder2OtherThread(String syncDataId);
    }
    public static void setCountMsg(String syncDataId, Integer count){
        if (timePointRecorder != null) {
            timePointRecorder.setOtherMsg(syncDataId,count, null,null,null, null, null, null, null, null);
        }
    }
    public static void setReWriteFailedMsg(String syncDataId, Boolean reWriteFailed,String reWriteFailedMsg){
        if (timePointRecorder != null) {
            timePointRecorder.setOtherMsg(syncDataId,null, reWriteFailed,reWriteFailedMsg,null, null, null, null, null, null);
        }
    }
    public static void setAfterSyncFailedMsg(String syncDataId, Boolean afterSyncFailed,String afterSyncFailedMsg){
        if (timePointRecorder != null) {
            timePointRecorder.setOtherMsg(syncDataId,null, null,null, afterSyncFailed,afterSyncFailedMsg, null, null, null, null);
        }
    }
    public static void setSyncStepExceptionMsg(String syncDataId, Boolean syncStepException, String syncStepExceptionMsg){
        if (timePointRecorder != null) {
            timePointRecorder.setOtherMsg(syncDataId,null, null,null,null, null, syncStepException, syncStepExceptionMsg, null, null);
        }
    }
    public static void changeTimePointRecorder2OtherThread(String syncDataId){
        if (timePointRecorder != null) {
            timePointRecorder.changeTimePointRecorder2OtherThread(syncDataId);
        }
    }

    public static void setThrowMsg(String syncDataId, Boolean throwable, String throwableMsg){
        if (timePointRecorder != null) {
            timePointRecorder.setOtherMsg(syncDataId,null, null,null,null, null, null, null, throwable, throwableMsg);
        }
    }
    public static void begin(String tenantId, String objApiName, String dataId, Long firstParseTime) {
        if (timePointRecorder != null) {
            timePointRecorder.begin(tenantId, objApiName, dataId, firstParseTime);
        }
    }

    public static void record(String pointName) {
        if (timePointRecorder != null) {
            timePointRecorder.record(pointName);
        }
    }

    public static void recordSync(String pointName, String syncDataId) {
        if (timePointRecorder != null) {
            timePointRecorder.recordSync(pointName, syncDataId);
        }
    }

    public static void asyncRecord(String tenantId, String objApiName, String dataId, String pointName) {
        if (timePointRecorder != null) {
            timePointRecorder.asyncRecord(tenantId, objApiName, dataId, pointName);
        }
    }

}
