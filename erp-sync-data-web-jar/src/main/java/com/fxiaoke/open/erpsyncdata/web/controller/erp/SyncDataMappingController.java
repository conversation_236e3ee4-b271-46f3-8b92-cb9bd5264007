package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.arg.*;
import com.fxiaoke.open.erpsyncdata.admin.model.CreateSyncDataMapping;
import com.fxiaoke.open.erpsyncdata.admin.model.RedoSyncRecord;
import com.fxiaoke.open.erpsyncdata.admin.model.UpdateSyncDataMapping;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.ErRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingGetDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingListByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingNumByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.CheckCodeService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.GetByIdBreakManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpObjectApiNameArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.*;

@Slf4j
@Api(tags = "数据映射相关接口")
@RestController("erp/SyncDataMappingController")
@RequestMapping("cep/syncDataMapping")
public class SyncDataMappingController extends AsyncSupportController {
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErRemoteManager erRemoteManager;
    @Autowired
    private GetByIdBreakManager getByIdBreakManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private ErpObjectService erpObjectService;
    @Autowired
    private CheckCodeService checkCodeService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "获取单条数据的最新一次同步详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    public Result<SyncDataMappingGetDetailResult> getDetail(@RequestBody SyncDataMappingGetDetailArg arg) {
        String tenantId = getLoginUserTenantId();
        String dataCenterId = getDcId();
        return adminSyncDataMappingService.getSyncDataDetail(tenantId,dataCenterId, arg.getId(), arg.getSyncDataId());
    }


    public Result<Void> deleteBySyncDataMappingIdsSync(@RequestBody IdListArg arg, String lang) {
        Integer tenantId = getIntLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        return adminSyncDataMappingService.deleteBySyncDataMappingIds(tenantId, dcId, userId, arg.getIds(),arg.getPloyDetailId(), lang);
    }

    @ApiOperation(value = "通过映射id删除对应的映射关系,异步")
    @RequestMapping(value = "/deleteBySyncDataMappingIds", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> asyncDeleteBySyncDataMappingIds(@RequestBody IdListArg arg,
                                                                        @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String actionName = i18NStringManager.get(I18NStringEnum.s86,lang,getLoginUserTenantId());
        return asyncExecute(() -> deleteBySyncDataMappingIdsSync(arg, lang), 3, false, actionName,lang);
    }

    @ApiOperation(value = "通过策略详情id，删除数据映射列表")
    @RequestMapping(value = "/deleteMappingByPloyDetailId", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> deleteMappingByPloyDetailId(@RequestBody DeleteMappingByPloyDetailIdArg arg,
                                                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String actionName = i18NStringManager.get(I18NStringEnum.s87,lang,getLoginUserTenantId());
        return asyncExecute(() -> deleteSyncDataMappingByPloyDetailId(arg,lang), 5, false, actionName,lang);
    }

    @ApiOperation(value = "添加中间表映射")
    @PostMapping(value = "/createSyncDataMapping")
    public Result<Void> createSyncDataMapping(@RequestBody CreateSyncDataMapping.Arg arg,
                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        final String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();

        return adminSyncDataMappingService.adminCreateSyncDataMapping(tenantId, userId, dcId, arg.getPloyDetailId(), arg.getSourceObjectApiName(), arg.getSourceDataId(), arg.getSourceDataName(), arg.getDestObjectApiName(), arg.getDestDataId(), arg.getDestDataName(), arg.getMasterDataId(), lang);
    }

    @ApiOperation(value = "修改中间表映射")
    @PostMapping(value = "/updateSyncDataMapping")
    public Result<Void> updateSyncDataMapping(@RequestBody UpdateSyncDataMapping.Arg arg,
                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) {
        final String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();

        return adminSyncDataMappingService.adminUpdateSyncDataMapping(tenantId, userId, dcId, arg.getPloyDetailId(), arg.getDataId(), arg.getSourceDataId(), arg.getSourceDataName(), arg.getDestDataId(), arg.getDestDataName(), arg.getMasterDataId(), lang);
    }

    private Result<Void> deleteSyncDataMappingByPloyDetailId(DeleteMappingByPloyDetailIdArg arg,String lang) {
        Integer tenantId = getIntLoginUserTenantId();
        Integer userId = getLoginUserId();
        String dcId = getDcId();
        String phone=getPhone();
        return adminSyncDataMappingService.deleteSyncDataMappingByPloyDetailId(tenantId, dcId, userId, phone, arg,lang);
    }

    @ApiOperation(value = "通过策略详情id，获取数据映射列表")
    @RequestMapping(value = "/listByPloyDetailId", method = RequestMethod.POST)
    public DeferredResult<Result<SyncDataMappingListByPloyDetailIdResult>> listByPloyDetailId(@RequestBody SyncDataMappingListByPloyDetailIdArg arg,
                                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return asyncExecute(() -> listByPloyDetailId(arg, lang, tenantId, dataCenterId, userId), 12, false, I18NStringEnum.s4502, lang);
    }

    private Result<SyncDataMappingListByPloyDetailIdResult> listByPloyDetailId(SyncDataMappingListByPloyDetailIdArg arg, String lang, String tenantId, String dataCenterId, Integer userId) {
        if(arg.getPageSize()==1){//默认pageSize=1，只需要全部的totalCount
            arg.setOnlyTotalCount(true);
        }
        Result<SyncDataMappingListByPloyDetailIdResult> result = adminSyncDataMappingService
                .listByPloyDetailId(tenantId, arg, lang);
        if (!result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(result.getData().getSyncDataMappingList())) {
            return result;
        }
        List<SyncDataMappingResult> syncDataMappings = result.getData().getSyncDataMappingList();
        //获取企业名称
        Set<String> tenentIds = new HashSet<>();
        for (SyncDataMappingResult syncDataMapping : syncDataMappings) {
            tenentIds.add(syncDataMapping.getSourceTenantId());
            tenentIds.add(syncDataMapping.getDestTenantId());
        }
        Map<String, String> tenantIdToNameMap = erRemoteManager.listTenantNamesByIds(tenantId, Lists.newArrayList(tenentIds));

        //获取对象名称
        Map<String, Map<String, String>> tenantToObjectApiNameToNameMap = new HashMap<>();
        Map<String, Set<String>> tenantToObjectApiNamesMap = new HashMap<>();
        for (SyncDataMappingResult syncDataMapping : syncDataMappings) {
            if (!tenantToObjectApiNamesMap.containsKey(syncDataMapping.getSourceTenantId())) {
                tenantToObjectApiNamesMap.put(syncDataMapping.getSourceTenantId(), new HashSet<>());
            }
            tenantToObjectApiNamesMap.get(syncDataMapping.getSourceTenantId()).add(syncDataMapping.getSourceObjectApiName());
            if (!tenantToObjectApiNamesMap.containsKey(syncDataMapping.getDestTenantId())) {
                tenantToObjectApiNamesMap.put(syncDataMapping.getDestTenantId(), new HashSet<>());
            }
            tenantToObjectApiNamesMap.get(syncDataMapping.getDestTenantId()).add(syncDataMapping.getDestObjectApiName());
        }

        for (String oneTenantId : tenantToObjectApiNamesMap.keySet()) {
            List<String> apiNames = Lists.newArrayList(tenantToObjectApiNamesMap.get(oneTenantId));
            Map<String, String> objectApiNameToNameMap = erpObjectService.queryErpObjectNameByApiName(oneTenantId, dataCenterId, userId, apiNames);
            Map<String, String> crmObjectApiNameToNameMap = crmRemoteManager.listObjectNamesByApiNames(oneTenantId, apiNames);
            objectApiNameToNameMap.putAll(crmObjectApiNameToNameMap);
            tenantToObjectApiNameToNameMap.put(oneTenantId, objectApiNameToNameMap);
        }

        for (SyncDataMappingResult syncDataMapping : syncDataMappings) {
            syncDataMapping.setSourceTenantName(tenantIdToNameMap.get(syncDataMapping.getSourceTenantId()));
            syncDataMapping.setDestTenantName(tenantIdToNameMap.get(syncDataMapping.getDestTenantId()));
            Map<String, String> sourceApiNameToNameMap = tenantToObjectApiNameToNameMap.get(syncDataMapping.getSourceTenantId());
            syncDataMapping.setSourceObjectName(sourceApiNameToNameMap.get(syncDataMapping.getSourceObjectApiName()));
            Map<String, String> destApiNameToNameMap = tenantToObjectApiNameToNameMap.get(syncDataMapping.getDestTenantId());
            syncDataMapping.setDestObjectName(destApiNameToNameMap.get(syncDataMapping.getDestObjectApiName()));
        }
        return result;
    }

    @ApiOperation(value = "通过集成流id，获取数据映射总数，异步")
    @RequestMapping(value = "/getSyncDataMappingTotalByPloyDetailId", method = RequestMethod.POST)
    public DeferredResult<Result<SyncDataMappingNumByPloyDetailIdResult>> asyncGetSyncDataMappingTotalByPloyDetailId(@RequestBody SyncDataMappingListByPloyDetailIdArg arg,
                                                                                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        Result<SyncDataMappingNumByPloyDetailIdResult> timeoutResult = Result.newError(ResultCodeEnum.ACTION_EXECUTE_TIME_OUT);
        String taskId="mapping_total_"+idGenerator.get();
        SyncDataMappingNumByPloyDetailIdResult result=new SyncDataMappingNumByPloyDetailIdResult();
        result.setIsSuccess(false);
        result.setTaskId(taskId);
        timeoutResult.setData(result);
        String actionName = i18NStringManager.get(I18NStringEnum.s88,lang,getLoginUserTenantId());
        return asyncExecute(() -> getSyncDataMappingTotalByPloyDetailId(taskId,arg,lang), 10, false, actionName,timeoutResult,lang);
    }

    public Result<SyncDataMappingNumByPloyDetailIdResult> getSyncDataMappingTotalByPloyDetailId(String taskId,
                                                                                                SyncDataMappingListByPloyDetailIdArg arg,
                                                                                                String lang) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return adminSyncDataMappingService.getSyncDataMappingNumByPloyDetailId(tenantId,dataCenterId,userId,taskId,arg,lang);
    }

    @ApiOperation(value = "通过taskId，获取数据映射列表总数")
    @RequestMapping(value = "/getTotalByTaskId")
    public Result<SyncDataMappingNumByPloyDetailIdResult> getTotalByTaskId(@RequestBody TaskIdArg taskIdArg) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        return adminSyncDataMappingService.getTotalByTaskId(tenantId, dataCenterId, userId, taskIdArg.getTaskId());
    }

    @ApiOperation(value = "重新同步，异步")
    @RequestMapping(value = "/resync", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> asyncResync(@RequestBody SyncDataMappingResyncArg arg,
                                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId=getLoginUserTenantId();
        String dataCenterId=getDcId();
        SyncPloyDetailEntity entryById = syncPloyDetailManager.getEntryById(tenantId, arg.getPloyDetailId());

        if(!ObjectUtils.isEmpty(entryById)&&entryById.getSourceTenantType().equals(TenantTypeEnum.ERP.getType())){
            ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(getLoginUserTenantId(),dataCenterId);
            ErpChannelEnum channel = connectInfo.getChannel();
            String realApiName=idFieldConvertManager.getRealObjApiName(getLoginUserTenantId(),entryById.getSourceObjectApiName());
            boolean getByIdBreak = getByIdBreakManager.isBreak(getLoginUserTenantId(), getDcId(), channel, realApiName);
            if(getByIdBreak){
                DeferredResult<Result<Void>> deferredResult = new DeferredResult<>();
                deferredResult.setResult(Result.clearI18NKey(Result.newError(ResultCodeEnum.GET_BY_ID_BREAK)));
                return deferredResult;
            }
        }
        return asyncExecute(() -> adminSyncDataMappingService.redoSyncData(getLoginUserTenantId(),getLoginUserId(), arg.getIds(),arg.getSourceObjectApiName(),arg.getSourceDataId(),arg.getPloyDetailId()), 3, false, i18NStringManager.get(I18NStringEnum.s3770, lang, tenantId),lang);
    }
    @ApiOperation(value = "解除getByid熔断")
    @RequestMapping(value = "/releaseBreak", method = RequestMethod.POST)
    public Result<Void> releaseBreak(@RequestBody SyncDataMappingResyncArg arg) {
        SyncPloyDetailEntity entryById = syncPloyDetailManager.getEntryById(getLoginUserTenantId(), arg.getPloyDetailId());
        String realApiName=idFieldConvertManager.getRealObjApiName(getLoginUserTenantId(),entryById.getSourceObjectApiName());
        getByIdBreakManager.removeBreak(getLoginUserTenantId(),getDcId(),entryById.getId(),realApiName);
       return Result.newSuccess();
    }

    @ApiOperation(value = "重新同步满足筛选条件数据，异步")
    @RequestMapping(value = "/resyncByPloyDetailId", method = RequestMethod.POST)
    public DeferredResult<Result<Void>> asyncResyncByPloyDetailId(@RequestBody SyncDataMappingListByPloyDetailIdArg arg,
                                                                  @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        SyncPloyDetailEntity entryById = syncPloyDetailManager.getEntryById(getLoginUserTenantId(), arg.getPloyDetailId());
        String tenantId=getLoginUserTenantId();
        String dataCenterId=getDcId();
       if(!ObjectUtils.isEmpty(entryById)&&entryById.getSourceTenantType().equals(TenantTypeEnum.ERP.getType())){
           String realApiName=idFieldConvertManager.getRealObjApiName(getLoginUserTenantId(),entryById.getSourceObjectApiName());
           ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(getLoginUserTenantId(),dataCenterId);
           ErpChannelEnum channel = connectInfo.getChannel();
           boolean getByIdBreak = getByIdBreakManager.isBreak(getLoginUserTenantId(), getDcId(), channel, realApiName);
           if(getByIdBreak){
               DeferredResult<Result<Void>> deferredResult = new DeferredResult<>();
               deferredResult.setResult(Result.clearI18NKey(Result.newError(ResultCodeEnum.GET_BY_ID_BREAK)));
               return deferredResult;
           }
       }
        String actionName = i18NStringManager.get(I18NStringEnum.s89,tenantId,lang);
        return asyncExecute(() -> redoSyncDataByPloyDetailId(arg,lang), 3, false, actionName,lang);
    }

    Result<Void> redoSyncDataByPloyDetailId(SyncDataMappingListByPloyDetailIdArg arg, String lang) {
        String tenantId = getLoginUserTenantId();
        String dcId = getDcId();
        arg.setStatus(2);//只重试失败的
        Result<RedoSyncRecord> result = adminSyncDataMappingService.redoSyncDataByPloyDetailId(tenantId,getLoginUserId(), DataReceiveTypeEnum.MANUAL_SYNC_DATA, dcId, arg, lang);
        if (result.isSuccess()) {
            RedoSyncRecord record = result.getData();
            if (record.getFailedCount() > 0) {
                //构建错误信息
                return Result.newErrorExtra(ResultCodeEnum.REDO_SYNC_ERROR_MSG, record.getAllCount() + "", record.getSuccessCount() + "", record.getFailedCount() + "", record.getLastError());
            }
        }
        return Result.copy(result);
    }

    @ApiOperation(value = "发送验证码")
    @RequestMapping(value = "/sendCode")
    public Result<String> sendCode(@RequestBody CepArg cepArg) {
        String tenantId = getLoginUserTenantId();
        String phone=getPhone();
        Result<String> result = checkCodeService.sendCode(tenantId,phone,null);
        result.setData("");
        return result;
    }

    @ApiOperation(value = "初始化数据映射")
    @RequestMapping(value = "/initMapping", method = RequestMethod.POST)
    public DeferredResult<Result<List<Result>>> asyncInitMapping(@RequestBody InitDataMappingArg arg,
                                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String actionName = i18NStringManager.get(I18NStringEnum.s90,lang,getLoginUserTenantId());
        return asyncExecute(() -> initDataMapping(arg,lang), 10, false, actionName,lang);
    }

    private Result<List<Result>> initDataMapping(InitDataMappingArg arg,
                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return adminSyncDataMappingService.initDataMapping(tenantId,arg,lang);
    }
    @ApiOperation(value = "主动熔断")
    @RequestMapping(value = "/breakData", method = RequestMethod.POST)
    public Result<Void> breakData(@RequestBody ErpObjectApiNameArg objectApiName) {
        String dataCenterId = getDcId();
        String tenantId = getLoginUserTenantId();
        String key = "key_get_by_id_break" + "_" + tenantId + "_" + dataCenterId + "_" + objectApiName.getErpObjectApiName();
        GetByIdBreakManager.FailedData failedData=new GetByIdBreakManager.FailedData();
        failedData.setTimestamp(System.currentTimeMillis());
        failedData.setFailedCount(1000);
        String set = redisDataSource.get(this.getClass().getSimpleName()).setex(key, 100,JSONObject.toJSONString(failedData));
        return Result.newSuccess();
    }
}
