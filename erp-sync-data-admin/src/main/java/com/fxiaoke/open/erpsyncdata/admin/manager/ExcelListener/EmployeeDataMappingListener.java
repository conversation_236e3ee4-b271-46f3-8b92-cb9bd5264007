//package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;
//
//import com.alibaba.excel.context.AnalysisContext;
//import com.facishare.organization.api.model.employee.EmployeeDto;
//import com.fxiaoke.open.erpsyncdata.admin.model.excel.EmployeeDataMappingExcelVo;
//import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
//import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
//import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
//import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
//import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
//import com.google.common.base.Joiner;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 以erp侧数据Id作为唯一id。导入数据按顺序执行。
// *
// * <AUTHOR>
// * @Date: 15:20 2020/9/14
// * @Desc:
// */
//@Slf4j
//@Getter
//public class EmployeeDataMappingListener extends BaseListener<EmployeeDataMappingExcelVo> {
//
//    private final EmployeeMappingService employeeMappingService;
//
//    private final IdGenerator idGenerator;
//
//    private final ErpFieldTypeEnum datatype;
//
//    private final String tenantId;
//
//    private final String dataCenterId;
//
//    private final ErpChannelEnum channel;
//
//    private final ImportExcelFile.Result importResult;
//
//    private final Map<String,EmployeeDto> tel2FsEmployeeMap;
//
//    public EmployeeDataMappingListener(String tenantId, ErpChannelEnum channel, ErpFieldTypeEnum datatype,Map<String,EmployeeDto> tel2FsEmployeeMap,
//                                       EmployeeMappingService employeeMappingService, IdGenerator idGenerator,String dataCenterId) {
//        this.tel2FsEmployeeMap=tel2FsEmployeeMap;
//        this.employeeMappingService=employeeMappingService;
//        this.datatype = datatype;
//        this.tenantId = tenantId;
//        this.dataCenterId=dataCenterId;
//        this.channel = channel;
//        this.idGenerator = idGenerator;
//        this.importResult = new ImportExcelFile.Result();
//    }
//
//    @Override
//    public void invoke(EmployeeDataMappingExcelVo data, AnalysisContext context) {
//        super.invoke(data, context);
//        importResult.incrInvoke(1);
//        Integer rowNo = context.readRowHolder().getRowIndex();
//        dealWithErpEmployee(data, rowNo);
//    }
//
//    private void dealWithErpEmployee(EmployeeDataMappingExcelVo data, Integer rowNo) {
//        if (StringUtils.isEmpty(data.getErpEmployeeId()) || StringUtils.isEmpty(data.getErpEmployeeName()) || StringUtils.isEmpty(data.getErpEmployeeTel())) {
//            importResult.addImportError(rowNo, "erp员工id、erp员工姓名、erp员工电话不能为空");
//        } else {
//            EmployeeMappingResult employeeMappingResult=new EmployeeMappingResult();
//            employeeMappingResult.setChannel(channel);
//            employeeMappingResult.setDataCenterId(dataCenterId);
//            employeeMappingResult.setErpEmployeeId(data.getErpEmployeeId());
//            employeeMappingResult.setErpEmployeeName(data.getErpEmployeeName());
//            employeeMappingResult.setErpEmployeePhone(data.getErpEmployeeTel());
//            EmployeeDto fsEmployee=tel2FsEmployeeMap.get(data.getErpEmployeeTel());
//            if(fsEmployee!=null){//根据电话号码自动绑定
//                employeeMappingResult.setFsEmployeeStatus(fsEmployee.getStatus().getValue());
//                employeeMappingResult.setFsEmployeePhone(fsEmployee.getMobile());
//                employeeMappingResult.setFsEmployeeName(fsEmployee.getName());
//                employeeMappingResult.setFsEmployeeId(fsEmployee.getEmployeeId());
//            }
//            Result<String> updateResult = employeeMappingService.updateEmployeeMappingByErpId(tenantId, -10000,dataCenterId, employeeMappingResult);
//            if(updateResult.isSuccess()){
//                if("add".equals(updateResult.getData())){
//                    importResult.incrInsert(1);
//                }else if("update".equals(updateResult.getData())){
//                    importResult.incrUpdate(1);
//                }
//            }else{
//                importResult.addImportError(rowNo, "插入数据失败:"+updateResult.getErrMsg());
//            }
//        }
//    }
//
//
//    @Override
//    public void doAfterAllAnalysed(AnalysisContext context) {
//        super.doAfterAllAnalysed(context);
//        //组装打印信息
//        StringBuffer printMsg = new StringBuffer();
//        printMsg.append(String.format("解析成功【%s】条，新增数据【%s】条，删除数据【%s】条，更新数据【%s】条\n",
//                importResult.getInvokedNum(), importResult.getInsertNum(), importResult.getDeleteNum(), importResult.getUpdateNum()));
//        printMsg.append(String.format("处理失败【%s】条：\n", importResult.getImportErrorRows().size()));
//        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
//                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
//                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
//        importErrorMap.forEach((k, v) -> {
//            printMsg.append(String.format("  行号【%s】：%s\n", Joiner.on(",").join(v), k));
//        });
//        printMsg.append(String.format("解析异常【%s】条：\n", importResult.getInvokeExceptionRows().size()));
//        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
//                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
//                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
//        invokeExceptionMap.forEach((k, v) -> {
//            printMsg.append(String.format("  行号【%s】：%s\n", Joiner.on(",").join(v), k));
//        });
//        importResult.setPrintMsg(printMsg.toString());
//    }
//
//    /**
//     * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
//     * entire read will terminate.
//     *
//     * @param exception
//     * @param context
//     */
//    @Override
//    public void onException(Exception exception, AnalysisContext context) throws Exception {
//        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
//    }
//}
