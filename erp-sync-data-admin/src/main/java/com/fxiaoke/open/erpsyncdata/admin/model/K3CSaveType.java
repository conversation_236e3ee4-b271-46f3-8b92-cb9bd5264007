package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CSaveAction;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/3 15:02:14
 */
@Getter
public enum K3CSaveType {
    DRAFT("Z", "暂存", false, false, true, Sets.newHashSet(K3CSaveAction.DRAFT)),

    SAVE("A", "创建", false, false, false, Sets.newHashSet(K3CSaveAction.SAVE)),

    SUBMIT("B", "审核中", false, true, false, Sets.newHashSet(K3CSaveAction.SAVE, K3CSaveAction.SUBMIT), Sets.newHashSet(K3CSaveAction.SAVE, K3CSaveAction.UN_AUDIT, K3CSaveAction.SUBMIT)),

    AUDIT("C", "已审核", true, false, false, Sets.newHashSet(K3CSaveAction.SAVE, K3CSaveAction.SUBMIT, K3CSaveAction.AUDIT), Sets.newHashSet(K3CSaveAction.SAVE, K3CSaveAction.SUBMIT, K3CSaveAction.AUDIT)),

    RE_AUDIT("D", "重新审核", Sets.newHashSet(K3CSaveAction.SAVE, K3CSaveAction.UN_AUDIT, K3CSaveAction.CANCEL_ASSIGN)),

    MODIFY("MODIFY", "仅调用保存接口", Sets.newHashSet(K3CSaveAction.SAVE)),
    ;

    private final String status;
    private String detail;

    /**
     * 是否需要自动提交并审核，默认为true
     */
    private Boolean isAutoSubmitAndAudit;

    /**
     * 只提交不审核，默认为false
     */
    private Boolean isAutoSubmitWithoutAudit;

    /**
     * 使用暂存接口不直接保存，默认为false
     */
    private Boolean useDraft;

    private Set<String> addActions;
    private Set<String> modifyActions;

    K3CSaveType(final String status, final String detail, final Set<K3CSaveAction> modifyActions) {
        this.status = status;
        this.detail = detail;
        this.modifyActions = modifyActions.stream().map(K3CSaveAction::getType).collect(Collectors.toSet());
    }

    K3CSaveType(final String status, final String detail, final Boolean isAutoSubmitAndAudit, final Boolean isAutoSubmitWithoutAudit, final Boolean useDraft, final Set<K3CSaveAction> addActions) {
        this.status = status;
        this.detail = detail;
        this.isAutoSubmitAndAudit = isAutoSubmitAndAudit;
        this.isAutoSubmitWithoutAudit = isAutoSubmitWithoutAudit;
        this.useDraft = useDraft;
        this.addActions = addActions.stream().map(K3CSaveAction::getType).collect(Collectors.toSet());
    }

    K3CSaveType(final String status, final String detail, final Boolean isAutoSubmitAndAudit, final Boolean isAutoSubmitWithoutAudit, final Boolean useDraft, final Set<K3CSaveAction> addActions, final Set<K3CSaveAction> modifyActions) {
        this(status, detail, isAutoSubmitAndAudit, isAutoSubmitWithoutAudit, useDraft, addActions);
        this.modifyActions = modifyActions.stream().map(K3CSaveAction::getType).collect(Collectors.toSet());
    }

    public static K3CSaveType getSaveType(IdSaveExtend saveExtend) {
        if (saveExtend.getIsAutoSubmitAndAudit()) {
            return AUDIT;
        }
        if (saveExtend.getIsAutoSubmitWithoutAudit()) {
            return SUBMIT;
        }
        if (saveExtend.getUseDraft()) {
            return DRAFT;
        }
        return SAVE;
    }

    private static Map<String, K3CSaveType> statusMap = Arrays.stream(K3CSaveType.values()).collect(Collectors.toMap(K3CSaveType::getStatus, Function.identity()));

    public static K3CSaveType getByStatus(String status) {
        return statusMap.get(status.toUpperCase());
    }

    @Override
    public String toString() {
        return "K3CSaveType{" +
                "type=" + status +
                ", detail='" + detail + '\'' +
                "} " + super.toString();
    }

    public static void main(String[] args) {
        System.out.println("K3c状态:");
        for (K3CSaveType value : K3CSaveType.values()) {
            System.out.println(value.getStatus() + ": " + value.getDetail());
        }
        System.out.println("\nK3c事件:");
        for (K3CSaveAction value : K3CSaveAction.values()) {
            System.out.println(value.getType() + ": " + value.getDetail());
        }
    }
}
