package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.Wensli.WensliUtil;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.opple.OppleUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 * @createTime 2021/9/26 11:16
 * @description 用于【欧普】自定义函数调用，实现项目授权相关接口调用
 */
@Slf4j
@Api(tags = "欧普相关接口")
@RestController("OppleController")
// 80771：112纷享专用 zsl测试企业020       727543：欧普沙盒   726941：欧普正式
@RequestMapping(path = {"inner/erp/syncdata/customfunction/80771", "inner/erp/syncdata/customfunction/727543", "inner/erp/syncdata/customfunction/726941"})
public class OppleController {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private OppleUtil oppleUtil;

    @ApiOperation(value = "通用方法")
    @RequestMapping(value = "/dowork",method = RequestMethod.POST)
    public Result<String> dowork(@RequestHeader(value="x-fs-ei") String tenantId,
                                 @RequestHeader Map<String, String> headerMap,
                                 @RequestBody Map<String, String> bodyMap) {

        log.info("trace OppleController dowork tenantId:{}, headerMap:{}, bodymap: {} ", tenantId, headerMap, bodyMap);

        String param = bodyMap.get("param");

        if(StringUtil.isEmpty(param)){
            return Result.newError("fail", I18NStringEnum.s3659 + param);
        }

        try{
            Map<String,String> params = JacksonUtil.fromJson(param,Map.class);

            return oppleUtil.createByFile(params,tenantId);

        }catch (Exception e){
            log.info("WensliController exception: ", e);

            return Result.newError("fail", e.getMessage());
        }

    }

}
