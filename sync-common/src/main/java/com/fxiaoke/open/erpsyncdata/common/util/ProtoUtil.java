package com.fxiaoke.open.erpsyncdata.common.util;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;

@SuppressWarnings("ALL")
public class ProtoUtil {
    /**
     * 将pb字节数组反序列化为目标类型的对象
     */
    public static <T> T fromProto(byte[] data, Class<T> tClass) throws Exception {
        Schema schema = RuntimeSchema.getSchema(tClass);
        T object = tClass.newInstance();
        ProtobufIOUtil.mergeFrom(data, object, schema);
        return object;
    }

    /**
     * 将对象序列化为pb字节数组
     */
    public static byte[] toProto(Object data) {
        Schema schema = RuntimeSchema.getSchema(data.getClass());
        return ProtobufIOUtil.toByteArray(data, schema, LinkedBuffer.allocate(512));
    }
}
