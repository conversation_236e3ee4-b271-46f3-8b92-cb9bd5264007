package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.DispatcherMongoStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 10:58 2023/3/1
 * @Desc:
 */
@Slf4j
@Component
public class Send2DispatcherSpeedLimitManager {
    @Autowired
    private DispatcherMongoStore dispatcherMongoStore;
    @Autowired
    private TenantConfigurationManager configurationManager;
    @Autowired
    private SpeedLimitManager speedLimitManager;

    private static TimedCache<String, Long> tenantTopicRemaining = CacheUtil.newTimedCache(10 * 1000L);


    @LogLevel(LogLevelEnum.TRACE)
    public Long needSendDispatcherNum(String tenantId) {
        try {
            Long num = getTenantDisPatcherRemainNum(tenantId);
            Long maximum = getNumPerInterval(tenantId);
            Long result = maximum - num;
            if (result < 0) {
                log.info("needSendDispatcherNum is {},tenantId={} num={},maximum={}", result, tenantId, num, maximum);
            }
            return result;
        } catch (Exception e) {
            log.error("needSendDispatcherNum error", e);
        }
        return 100L;
    }


    @LogLevel(LogLevelEnum.TRACE)
    public Long getNumPerInterval(String tenantId) {
        long write2CrmLimit = speedLimitManager.getLimitEachInterval(tenantId, SpeedLimitTypeEnum.TO_CRM);
        Long maximum = write2CrmLimit * configurationManager.getTenantDispatcherRemainMaximum(tenantId);
        return maximum;
    }

    private long getTenantDisPatcherRemainNum(String tenantId) {
        Long num = tenantTopicRemaining.get(tenantId, false);
        if (num != null) {
            return num;
        }

        num = dispatcherMongoStore.getStoreCollectionCount(tenantId);
        tenantTopicRemaining.put(tenantId, num);
        return num;
    }
}
