{"api_name": "ErpOrganizationObj", "display_name": "ERP组织机构", "is_udef": true, "store_table_name": "erp_organization", "description": "ERP数据同步平台中使用，用于记录ERP的组织信息。", "define_type": "package", "icon_path": "A_201705_11_a0131af8e55549c68233e12bc53f5a60.png", "icon_index": 5, "config": {"edit": 1, "fields": {"add": 1}, "layout": {"add": 1, "assign": 1}, "record_type": {"add": 1, "assign": 1}, "cascade": {"add": 1}, "layout_rule": {"add": 1}, "rule": {"add": 1}, "button": {"add": 1}}, "is_active": true, "version": 1, "index_version": 1, "package": "CRM", "fields": {"extend_obj_data_id": {"is_required": false, "api_name": "extend_obj_data_id", "is_index": true, "status": "released", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "max_length": 64, "is_extend": false}, "name": {"description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "is_single": false, "max_length": 64, "is_index": true, "is_active": true, "label": "组织名称", "api_name": "name", "help_text": "ERP组织名称", "status": "new"}, "org_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "组织ID", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "org_id", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "ERP组织ID", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "org_number": {"is_index": true, "is_active": true, "is_unique": false, "label": "组织编码", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "org_number", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "ERP组织编码", "max_length": 64, "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "need_sync_product": {"is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "是否同步物料", "type": "true_or_false", "is_required": false, "api_name": "need_sync_product", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "若为是，且存在ERP到CRM的物料同步策略，会将使用组织为该组织的物料从ERP同步至CRM。", "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "need_sync_warehouse": {"describe_api_name": "ErpOrganizationObj", "is_index": true, "is_active": true, "create_time": 1605520739093, "is_unique": false, "default_value": false, "label": "是否同步仓库", "type": "true_or_false", "is_required": false, "api_name": "need_sync_warehouse", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "若为是，且存在ERP到CRM的仓库同步策略，会将使用组织为该组织的物料从ERP同步至CRM。", "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "need_sync_customer": {"is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "是否同步客户", "type": "true_or_false", "is_required": false, "api_name": "need_sync_customer", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "若为是，且存在ERP到CRM的客户同步策略，会将创建组织为该组织的客户从ERP同步至CRM。", "status": "new", "config": {"add": 0, "display": 1, "edit": 1, "enable": 0, "remove": 0, "attrs": {"label": 0}}}, "owner": {"label": "负责人", "is_single": true, "department_list": [], "api_name": "owner", "define_type": "package", "employee_list": [], "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": true, "is_unique": false, "resource_bundle_key": null, "type": "employee", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"label": 0}}, "status": "new"}, "owner_department": {"label": "负责人主属部门", "default_value": "", "default_to_zero": false, "max_length": 100, "api_name": "owner_department", "define_type": "package", "department_list": [], "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": true, "is_unique": false, "page_acitve": true, "resource_bundle_key": null, "type": "text", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"label": 0}}, "status": "new"}, "record_type": {"label": "业务类型", "description": "", "api_name": "record_type", "define_type": "package", "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": false, "is_unique": false, "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "status": "released", "type": "record_type", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"label": 0}}, "resource_bundle_key": null}, "life_status": {"label": "生命状态", "default_value": "normal", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "maxItems": -1, "api_name": "life_status", "define_type": "package", "description": "", "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_single": false, "is_unique": false, "status": "new", "type": "select_one", "config": {"add": 0, "display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"default_value": 0, "options": 0, "label": 0, "help_text": 0, "is_required": 0}}, "resource_bundle_key": null}, "data_own_department": {"label": "归属部门", "is_single": true, "department_list": [], "api_name": "data_own_department", "define_type": "package", "help_text": "", "is_active": true, "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "page_acitve": true, "resource_bundle_key": null, "type": "department", "config": {"display": 1, "attrs": {"label": 0}}, "status": "new"}}}