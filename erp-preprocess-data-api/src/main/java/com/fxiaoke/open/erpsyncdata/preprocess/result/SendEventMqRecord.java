package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Synchronized;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/9
 */
@Data
@NoArgsConstructor()
public class SendEventMqRecord implements Serializable {
    private static final long serialVersionUID = 5003083749214682065L;

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failedNum;

    /**
     *
     */
    private List<IdResult> idResults;

    public static SendEventMqRecord init(){
        SendEventMqRecord sendEventMqRecord = new SendEventMqRecord();
        sendEventMqRecord.setSuccessNum(0);
        sendEventMqRecord.setFailedNum(0);
        sendEventMqRecord.setIdResults(new ArrayList<>());
        return sendEventMqRecord;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    @NoArgsConstructor()
    public static class IdResult implements Serializable{
        private static final long serialVersionUID = -6805266678441862180L;
        private String dataId;
        private String traceId;
        private Boolean success;
        private String errMsg;
    }

    @Synchronized
    public void addResult(Result<Void> result,String dataId,String traceId){
        if (result.isSuccess()){
            successNum++;
        }else {
            failedNum++;
        }
        IdResult idResult = IdResult.of(dataId,traceId,result.isSuccess(),result.getErrMsg());
        idResults.add(idResult);
    }

}
