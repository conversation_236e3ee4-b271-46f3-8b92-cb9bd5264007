package com.fxiaoke.open.oasyncdata.result;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 16:35 2021/8/11
 * @Desc:
 */
@Data
@ApiModel
public class ErpTempDataMonitorResult implements Serializable {
    @ApiModelProperty("数据id")
    public String id;

    @ApiModelProperty("对象apiName")
    private String objApiName;

    @ApiModelProperty("操作类型")
    private Integer operationType;

    @ApiModelProperty("数据id")
    private String dataId;

    @ApiModelProperty("数据编码")
    private String dataNumber;

    @ApiModelProperty("数据体")
    private String dataBody;

    @ApiModelProperty("数据体是否大于2M")
    private Boolean dataBodyLarge2M=false;

    @ApiModelProperty("数据源状态")
    private Integer status;

    @ApiModelProperty("数据源状态描述")
    private String statusDesc;

    @ApiModelProperty("所有数据同步状态")
    private Map<String,Integer> syncStatusMap;

    @ApiModelProperty("数据同步状态")
    private Integer syncStatus;

    @ApiModelProperty("数据同步状态描述")
    private String syncStatusDesc;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("数据体最后一次traceId")
    private String traceId;

    @ApiModelProperty("所属任务id（历史数据同步任务）")
    private Set<String> taskNum;

    @ApiModelProperty("数据体最后更新时间")
    private Long lastSyncTime;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("更新时间")
    private Long updateTime;

    @ApiModelProperty("过期时间")
    private Date expireTime;

    @ApiModelProperty("是否需要轮询")
    private Boolean needSync;

}
