package com.fxiaoke.open.oasyncdata.mongo;


import com.fxiaoke.open.oasyncdata.db.entity.mongo.OAResyncLogDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASettingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogSnapshotDoc;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Repository
@Slf4j
public class OASyncLogMongoStore {

    private  DatastoreExt store;

    private final static String SYNC_LOG_SNAPSHOT = "oa_sync_log_snapshot";
    private final static String SYNC_LOG_MAPPING = "oa_sync_log_mappings";
    private final static String OA_RESYNC_LOGS = "oa_resync_logs";
    private final static String OA_SETTINGS = "oa_settings";
    private final String dbName;
    private final Set<String> syncLogSnapshotCollectionCache = Sets.newConcurrentHashSet();
    private final Set<String> syncLogMappingsCollectionCache = Sets.newConcurrentHashSet();
    private final Set<String> oaResyncLogsCache = Sets.newConcurrentHashSet();
    private final Set<String> oaSettingCollectionCache = Sets.newConcurrentHashSet();


    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(OASyncLogSnapshotDoc.class,OASyncLogMappingDoc.class, OASettingDoc.class)
                        .automatic(true).build()));
    }
    @Autowired
    public OASyncLogMongoStore( DatastoreExt store) {
        this.store = store;
        this.dbName =  ConfigFactory.getInstance().getConfig("fs-erp-oa-all")
                .get("syncLogDbName","fs-erp-sync-data");
        //已有的集合，储存下来。不再在代码动态创建索引。代码仅用于创建新集合时创建索引，旧集合使用统一刷索引的接口
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v->{
                            if (v.startsWith(SYNC_LOG_SNAPSHOT)){
                                syncLogSnapshotCollectionCache.add(v);
                            }});
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v->{
            if (v.startsWith(SYNC_LOG_MAPPING)){
                syncLogMappingsCollectionCache.add(v);
            }});
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v->{
            if (v.startsWith(OA_SETTINGS)){
                oaSettingCollectionCache.add(v);
            }});
    }

    private String getColName(String tenantId) {

        return SYNC_LOG_SNAPSHOT;
    }
    private String getColNameMapping(String tenantId) {
        return SYNC_LOG_MAPPING;
    }
    private String getOaResyncLogs(String tenantId) {
        return OA_RESYNC_LOGS;
    }
    private String getOaSettings(String tenantId) {
        return OA_SETTINGS;
    }

    /**
     * 创建集合，检查索引
     * 这里不移除索引，另外使用批量接口移除
     * 也不根据名称更新索引，同样，更新索引需要走批量接口更新
     */
    public synchronized MongoCollection<OASyncLogSnapshotDoc> getOASyncLogSnapshotCollection(String tenantId) {
        //dbName会从配置文件的mongo.servers解析
        String colName = getColName(tenantId);
        MongoCollection<OASyncLogSnapshotDoc> collection = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(colName, OASyncLogSnapshotDoc.class);
        if (!syncLogSnapshotCollectionCache.add(colName)) {
            return collection;
        }

        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
        //过期自动清理时间,30天
        Bson idxExpire = Indexes.descending("createTime");
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(30L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("dataId")
                , Indexes.ascending("receiverId")
                , Indexes.ascending("eventType")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("data_filters").background(true)));

        Bson idxTypeByDataName = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("dataName")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxTypeByDataName, new IndexOptions().name("data_name_filters").background(true)));

        Bson idxType2 = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("status")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxType2, new IndexOptions().name("status_filters2").background(true)));

        List<String> created = collection.createIndexes(toBeCreate);
        log.info("created indexes: {}, wanted: {}, created: {}", created, toBeCreate, created);
        return collection;
    }

    public synchronized MongoCollection<OASyncLogMappingDoc> getSyncLogMappingCollection(String tenantId) {
        //dbName会从配置文件的mongo.servers解析
        String colName = getColNameMapping(tenantId);
        MongoCollection<OASyncLogMappingDoc> collection = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(colName, OASyncLogMappingDoc.class);
        if (!syncLogMappingsCollectionCache.add(colName)) {
            return collection;
        }

        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
        //过期自动清理时间,30天
        Bson idxExpire = Indexes.descending("createTime");
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(30L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("dataId")
                , Indexes.ascending("receiverId")
                , Indexes.ascending("eventType")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("data_filters").background(true)));

        Bson idxTypeByDataName = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("dataName")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxTypeByDataName, new IndexOptions().name("data_name_filters").background(true)));


        List<String> created = collection.createIndexes(toBeCreate);
        log.info("created indexes: {}, wanted: {}, created: {}", created, toBeCreate, created);
        return collection;
    }


    public synchronized MongoCollection<OAResyncLogDoc> getResyncCollection(String tenantId) {
        //dbName会从配置文件的mongo.servers解析
        String colName = getOaResyncLogs(tenantId);
        MongoCollection<OAResyncLogDoc> collection = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(colName, OAResyncLogDoc.class);
        if (!oaResyncLogsCache.add(colName)) {
            return collection;
        }

        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
        //过期自动清理时间,30天
        Bson idxExpire = Indexes.descending("createTime");
        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
                .expireAfter(30L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("dataId")
                , Indexes.ascending("receiverId")
                , Indexes.ascending("eventType")
                , Indexes.descending("nextRetryTime"));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("data_filters").background(true)));

        Bson idxTypeByDataName = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("dataName")
                , Indexes.descending("nextRetryTime"));
        toBeCreate.add(new IndexModel(idxTypeByDataName, new IndexOptions().name("data_name_filters").background(true)));

        Bson idxType2 = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("status")
                , Indexes.descending("nextRetryTime"));
        toBeCreate.add(new IndexModel(idxType2, new IndexOptions().name("status_filters2").background(true)));

        List<String> created = collection.createIndexes(toBeCreate);
        log.info("created indexes: {}, wanted: {}, created: {}", created, toBeCreate, created);
        return collection;
    }

    public synchronized MongoCollection<OASettingDoc> getOaSettingDocs(String tenantId) {
        //dbName会从配置文件的mongo.servers解析
        String colName = getOaSettings(tenantId);
        MongoCollection<OASettingDoc> collection = store.getMongo().getDatabase(dbName)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(colName, OASettingDoc.class);
        if (!oaSettingCollectionCache.add(colName)) {
            return collection;
        }

        List<IndexModel> toBeCreate = Lists.newArrayList();
        String key;
//        //过期自动清理时间,30天
//        Bson idxExpire = Indexes.descending("createTime");
//        toBeCreate.add(new IndexModel(idxExpire, new IndexOptions().name("index_expire_time")
//                .expireAfter(30L, TimeUnit.DAYS).background(true)));

        Bson idxType = Indexes.compoundIndex(Indexes.ascending("tenantId")
                , Indexes.ascending("dataCenterId")
                , Indexes.ascending("type")
                , Indexes.descending("updateTime"));
        toBeCreate.add(new IndexModel(idxType, new IndexOptions().name("type_filters").background(true)));


        List<String> created = collection.createIndexes(toBeCreate);
        log.info("created indexes: {}, wanted: {}, created: {}", created, toBeCreate, created);
        return collection;
    }

}
