package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjExtendDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpObjGroupDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/22
 */
@RestController
@RequestMapping("erp/syncdata/superadmin")
public class SuperAdminObjDescribeController {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;

    /**
     * 直接删除空的帐套配置，已经正常配置的账套，不能用这个删除
     *
     * @param tenantId
     * @param dcId
     * @return
     */
    @GetMapping("/removeDc/{tenantId}/{dcId}")
    public Result<Integer> removeDc(@PathVariable String tenantId, @PathVariable String dcId) {
        ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByIdAndTenantId(tenantId, dcId);
        if (connectInfoEntity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (StringUtils.isNotEmpty(connectInfoEntity.getConnectParams())) {
            return Result.newError(ResultCodeEnum.DELETE_CONNECTED_DC_NOT_ALLOWED);
        }
        int count = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .deleteByEiAndId(tenantId, dcId);
        return Result.newSuccess(count);
    }

    @RequestMapping(value = "/listDcInfos", method = RequestMethod.GET)
    public Result<List<ErpConnectInfoEntity>> listDcInfos(@RequestParam String tenantId) {
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.listErpDcByTenantId(tenantId);
        return Result.newSuccess(erpConnectInfoEntities);
    }

    @RequestMapping(value = "/listObjInfos", method = RequestMethod.GET)
    public Result<List<ErpObjExtendDto>> listObjInfos(@RequestParam String tenantId, @RequestParam(required = false) String dcId) {
        //这里将对象完全显示，方便检测脏数据
        List<ErpObjectEntity> objs = erpObjectDao.listByDcId(tenantId, dcId);
        List<ErpObjectRelationshipEntity> relations = erpObjectRelationshipDao.listByTenantIdAndDataCenterId(tenantId, dcId);
        Map<String, ErpObjectEntity> realObjMap = objs.stream().filter(v -> v.getErpObjectType().equals(ErpObjectTypeEnum.REAL_OBJECT)).collect(Collectors.toMap(v -> v.getErpObjectApiName(), v -> v));
        Map<String, ErpObjectEntity> splitObjMap = objs.stream().filter(v -> v.getErpObjectType().equals(ErpObjectTypeEnum.SPLIT_OBJECT)).collect(Collectors.toMap(v -> v.getErpObjectApiName(), v -> v));
        Map<String, List<ErpObjectRelationshipEntity>> relationGroupMap = relations.stream().collect(Collectors.groupingBy(v -> v.getErpRealObjectApiname()));
        //不放过任何一个realObj
        Set<String> realObjApiNames = CollUtil.newHashSet(realObjMap.keySet());
        relations.forEach(v -> realObjApiNames.add(v.getErpRealObjectApiname()));
        //针对每个realObj，构建数据
        List<ErpObjExtendDto> result = new ArrayList<>();
        for (String realObjApiName : realObjApiNames) {
            ErpObjGroupDto realObjGroup = new ErpObjGroupDto();
            result.add(realObjGroup);
            realObjGroup.setRealObjApiName(realObjApiName);
            realObjGroup.setSplitObjApiName(realObjApiName);
            realObjGroup.setChildren(new ArrayList<>());
            //获取对象
            ErpObjectEntity realObj = realObjMap.get(realObjApiName);
            if (realObj == null) {
                realObjGroup.setMissingObject(true);
            } else {
                realObjGroup.setObjName(realObj.getErpObjectName());
            }
            //获取relation
            List<ErpObjectRelationshipEntity> groupRelations = relationGroupMap.get(realObjApiName);
            if (groupRelations == null) {
                realObjGroup.setMissingRelation(true);
            } else {
                for (ErpObjectRelationshipEntity groupRelation : groupRelations) {
                    ErpObjGroupDto splitObjGroup = new ErpObjGroupDto();
                    realObjGroup.getChildren().add(splitObjGroup);
                    splitObjGroup.setRealObjApiName(groupRelation.getErpRealObjectApiname());
                    splitObjGroup.setSplitObjApiName(groupRelation.getErpSplitObjectApiname());
                    splitObjGroup.setSplitType(groupRelation.getSplitType());
                    splitObjGroup.setSplitSeq(groupRelation.getSplitSeq());
                    //获取对象
                    String splitObjApiName = groupRelation.getErpSplitObjectApiname();
                    ErpObjectEntity splitObj = splitObjMap.get(splitObjApiName);
                    if (splitObj == null) {
                        splitObjGroup.setMissingObject(true);
                    } else {
                        splitObjGroup.setObjName(splitObj.getErpObjectName());
                        splitObjGroup.setExtentValue(splitObj.getErpObjectExtendValue());
                        //将真实code解析到realObjApiName上
                        splitObjGroup.setRealObjApiName(splitObjGroup.parseRealApiCode());
                    }
                }
            }

        }
        //另外再用一个组，放只有obj没有relation的数据
        Set<String> splitObjApiNames = relations.stream().map(v -> v.getErpSplitObjectApiname()).collect(Collectors.toSet());
        Collection<String> onlySplitObjApiNames = CollUtil.subtract(splitObjMap.keySet(), splitObjApiNames);
        if (!onlySplitObjApiNames.isEmpty()) {
            ErpObjGroupDto onlyObjGroup = new ErpObjGroupDto();
            result.add(onlyObjGroup);
            onlyObjGroup.setRealObjApiName("onlyObj");
            onlyObjGroup.setSplitObjApiName("onlyObj");
            onlyObjGroup.setObjName("只有object没有relation");   // ignoreI18n   实施和开发自用
            onlyObjGroup.setChildren(new ArrayList<>());
            for (String onlySplitObjApiName : onlySplitObjApiNames) {
                ErpObjectEntity splitObj = splitObjMap.get(onlySplitObjApiName);
                ErpObjGroupDto splitObjGroup = new ErpObjGroupDto();
                splitObjGroup.setSplitObjApiName(splitObj.getErpObjectApiName());
                onlyObjGroup.getChildren().add(splitObjGroup);
            }
        }
        return Result.newSuccess(result);
    }


    @RequestMapping(value = "/listFields", method = RequestMethod.GET)
    public Result<List<ErpObjectFieldEntity>> listFields(@RequestParam String tenantId,
                                                         @RequestParam(required = false) String dcId,
                                                         @RequestParam String objApiName,
                                                         @RequestParam(required = false) ErpFieldTypeEnum fieldType,
                                                         @RequestParam(required = false) String searchStr) {
        List<ErpObjectFieldEntity> data = erpObjectFieldDao.findData(tenantId, dcId, objApiName);
        if (fieldType != null) {
            data.removeIf(v -> !fieldType.equals(v.getFieldDefineType()));
        }

        if (StrUtil.isNotEmpty(searchStr)) {
            data = data.stream().filter(v -> StrUtil.containsIgnoreCase(v.getFieldApiName(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getFieldLabel(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getFieldExtendValue(), searchStr)).collect(Collectors.toList());
        }
        data.sort(Comparator.comparingLong(v-> v.getUpdateTime()*-1));
        return Result.newSuccess(data);
    }

    @PostMapping(value = "/upsertField")
    public Result<Void> upsertField(@RequestBody ErpObjectFieldEntity erpObjectFieldEntity) {

        if (StrUtil.isBlank(erpObjectFieldEntity.getId())) {
            erpObjectFieldEntity.setId(IdGenerator.get());
            erpObjectFieldEntity.setCreateTime(System.currentTimeMillis());
            erpObjectFieldEntity.setUpdateTime(System.currentTimeMillis());
            erpObjectFieldDao.insert(erpObjectFieldEntity);
        } else {
            erpObjectFieldEntity.setUpdateTime(System.currentTimeMillis());
            erpObjectFieldDao.updateById(erpObjectFieldEntity);
        }
        return Result.newSuccess();
    }


    @GetMapping("listFieldExtends")
    public Result<List<ErpFieldExtendEntity>> listFieldExtends(@RequestParam String tenantId,
                                                               @RequestParam(required = false) String dcId,
                                                               @RequestParam String objApiName,
                                                               @RequestParam(required = false) ErpFieldTypeEnum erpFieldTypeEnum,
                                                               @RequestParam(required = false) String searchStr) {
        dcId = StrUtil.emptyToNull(dcId);
        List<ErpFieldExtendEntity> data = erpFieldExtendDao.queryByObjApiName(tenantId, dcId, objApiName);
        if (erpFieldTypeEnum != null) {
            data.removeIf(v -> !erpFieldTypeEnum.equals(v.getFieldDefineType()));
        }

        if (StrUtil.isNotEmpty(searchStr)) {
            data = data.stream().filter(v -> StrUtil.containsIgnoreCase(v.getFieldApiName(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getSaveCode(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getQueryCode(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getViewCode(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getSaveExtend(), searchStr)
                    || StrUtil.containsIgnoreCase(v.getViewExtend(), searchStr)
            ).collect(Collectors.toList());
        }
        data.sort(Comparator.comparingLong(v-> v.getUpdateTime()*-1));
        return Result.newSuccess(data);
    }

    @PostMapping(value = "/upsertFieldExtend")
    public Result<Void> upsertFieldExtend(@RequestBody ErpFieldExtendEntity extend) {
        if (StrUtil.isBlank(extend.getId())) {
            extend.setId(IdGenerator.get());
            extend.setCreateTime(System.currentTimeMillis());
            extend.setUpdateTime(System.currentTimeMillis());
            erpFieldExtendDao.insert(extend);
        } else {
            extend.setUpdateTime(System.currentTimeMillis());
            erpFieldExtendDao.updateById(extend);
        }
        return Result.newSuccess();
    }


}
