package com.fxiaoke.open.erpsyncdata.admin.manager.support;

import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.BulkDeleteArg;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.GetRelatedDataListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.BulkDeleteResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/14
 */
@Ignore
@Slf4j
public class CrmObjectSupportManagerJavaTest extends BaseTest {
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataActionService metadataActionService;

    @Test
    public void name() {
        HeaderObj headerObj = HeaderObj.newInstance(81138, 1001);
        ControllerListArg listArg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(200);
        listArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, "WarehouseObj", listArg);
        List<ObjectData> objectDatas = list.getData().getDataList();
        Map<String, String> collect = objectDatas.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName));
        log.info(JacksonUtil.toJson(collect));
        List<String> needDeleteList = new ArrayList<>();
        ObjectData objectData = new ObjectData();
        objectData.put("is_enable","2");
        collect.forEach((k,v)->{
            if (v.contains("||")){
                System.out.println(v);
                needDeleteList.add(k);
                Result<ObjectData> warehouseObj = objectDataService.updateObjectData(headerObj, "WarehouseObj", k, null, objectData);
            }
        });
        System.out.println(needDeleteList);
    }

    @Test
    public void test() {
        HeaderObj headerObj = HeaderObj.newInstance(81138, 1001);
        ObjectData objectData = new ObjectData();
        objectData.put("name","test5");
        objectData.put("category_code","test5.code");
        objectData.put("code",13);
        objectData.put("pid","5f6847d83e9c440001ff10f6");
        objectData.put("order_field",0);
        Result<ObjectDataCreateResult> result = objectDataService.create(headerObj,"ProductCategoryObj",
                false,false,false,objectData);
        System.out.println(result);
    }

    @Test
    public void testDelete() {
        HeaderObj headerObj = HeaderObj.newInstance(81138, 1001);
        ObjectData objectData = new ObjectData();
        objectData.put("name","test5");
        objectData.put("category_code","test5.code");
        objectData.put("code",13);
        objectData.put("pid","5f6847d83e9c440001ff10f6");
        objectData.put("order_field",0);

        BulkDeleteArg arg = new BulkDeleteArg();
        arg.setDataIds(Lists.newArrayList("5ffee898013ca70001dbe2ce"));

        Result<BulkDeleteResult> result = objectDataService.bulkDelete(headerObj,"ProductCategoryObj",
                arg,true);
        System.out.println(result);
    }

    @Test
    public void testCreateBatchStock(){
        extracted();
    }

    private void extracted() {
        HeaderObj headerObj = HeaderObj.newInstance(81772, 1000);
        ObjectData objectData = new ObjectData();
        objectData.put("product_id", "60b8aa3f34567c0001db507d");
        objectData.put("batch_id", "60cb100b449de1000115057f");
        //"仓位仓库1||存货区.货架C"：6082802cb304d6000108a518 仓位仓库1：6082802ab304d6000108a503
        objectData.put("warehouse_id", "6082802cb304d6000108a518");
        objectData.put("owner", Lists.newArrayList("1001"));
        objectData.put("batch_real_stock", 12);
//        objectData.put("stock_id","60431b26d215ff00016268d9");
        ActionAddArg addArg = new ActionAddArg();
        addArg.setObjectData(objectData);
        Result<ActionAddResult> result = metadataActionService.add(headerObj, "BatchStockObj",false,false, null, null, addArg);
        log.info("batchObj:{}", result);
    }

    @Test
    public void getCategoryTest() {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf("81772"), CrmConstants.SYSTEM_USER);
        ControllerListArg listArg = new ControllerListArg();
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> product_category =
                metadataControllerService.list(headerObj, "product_category", listArg);
        System.out.println(JacksonUtil.toJson(product_category));
    }

    @Test
    public void testRelatedList() {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf("81772"), CrmConstants.SYSTEM_USER);
        ControllerListArg listArg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("order_id", Collections.singletonList("609cdfab38ec88000143ea78"), FilterOperatorEnum.EQ);
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> details =
                metadataControllerService.list(headerObj, "SalesOrderProductObj", listArg);
        System.out.println("list result:"+JacksonUtil.toJson(details));
        GetRelatedDataListArg arg = new GetRelatedDataListArg();
        arg.setAssociateObjectDataId("609cdfab38ec88000143ea78");
        arg.setAssociateObjectDescribeApiName("SalesOrderObj");
        arg.setAssociatedObjectDescribeApiName("SalesOrderProductObj");
        arg.setAssociatedObjectFieldRelatedListName("order_id_list");
        arg.setIncludeAssociated(true);
        arg.setSearchQueryInfo("{\"limit\":2000,\"offset\":0,\"filters\":[]}");
        arg.setOrdered(true);

        Result<ObjectDataQueryListByIdsResult> salesOrderProductObj = metadataControllerService.getRelatedDataList(headerObj, "SalesOrderProductObj", arg);
        System.out.println("new res:"+JacksonUtil.toJson(salesOrderProductObj));
    }

    @Test
    public void testBatchObjDetail() {
        HeaderObj headerObj = HeaderObj.newInstance(81772, -10000);
        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setObjectDataId("604b1afc1c08f0000175800b");
        String stockObjApiName = ObjectApiNameEnum.FS_BATCHSTOCK.getObjApiName();
        controllerDetailArg.setObjectDescribeApiName(stockObjApiName);
        try {
            Result<ControllerGetDescribeResult> batchStockRes = metadataControllerService.detail(headerObj, stockObjApiName, controllerDetailArg);
            if (!batchStockRes.isSuccess()) {
                log.error("get batchStock error,result:{}", batchStockRes);
            }
            log.info("arg:{},result:{}",controllerDetailArg,batchStockRes);
        }catch (Exception e){
            log.info("error:",e);
            return;
        }

    }
}
