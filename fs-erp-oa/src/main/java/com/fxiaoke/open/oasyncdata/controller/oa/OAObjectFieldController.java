package com.fxiaoke.open.oasyncdata.controller.oa;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.OAObjectFieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA对象字段接口")
@RestController("oaObjectFieldController")
@RequestMapping("cep/oa/field")
@Slf4j
public class OAObjectFieldController extends BaseController {
    @Autowired
    private OAObjectFieldService oaObjectFieldService;

    @ApiOperation(value = "获取字段")
    @RequestMapping(value = "/getObjectField", method = RequestMethod.POST)
    public Result<Map<String, List<OAObjectFieldVO>>> getObjectField(@RequestBody CepArg arg, @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        String tenantId = getLoginUserTenantId();
        return oaObjectFieldService.getObjectField(tenantId, lang);
    }
}
