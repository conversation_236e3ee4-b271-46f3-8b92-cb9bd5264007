package com.fxiaoke.open.erpsyncdata.apiproxy.node.context;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class FromErp2TempCtx extends NodeContext<FromErp2TempCtx> {
    //里面存储的是真实objApiName
    private TimeFilterArg timeFilterArg;
    private String splitObjApiName;
    private Result<ListErpObjDataResult> result;

    @Override
    public Integer getSourceTenantType() {
        return TenantType.ERP;
    }
}
