package com.fxiaoke.open.erpsyncdata.monitor.service;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByIdArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByIdResult;
import com.fxiaoke.open.erpsyncdata.monitor.model.arg.ListDataByTimeArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.ListDataByTimeResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 数据接口
 * 暂时只有EPR的会获取
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
public interface DataSupportService {
    default Result<ListDataByTimeResult> listDataByTime(ListDataByTimeArg arg) {
        throw new ErpSyncDataException(I18NStringEnum.s240,arg.getTenantId());
    }

    default Result<ListDataByIdResult> listDataById(ListDataByIdArg listDataByIdArg) {
        throw new ErpSyncDataException(I18NStringEnum.s240,listDataByIdArg.getTenantId());
    }
}
