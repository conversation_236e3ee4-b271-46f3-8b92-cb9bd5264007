package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 12:01 2021/6/16
 * @Desc:
 */
@Data
@ApiModel
public class QueryFieldDataMappingArg implements Serializable {
    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("基础数据类型")
    private ErpFieldTypeEnum dataType;
    @ApiModelProperty("fs数据ids")
    private List<String> fsDataId;
    @ApiModelProperty("erp数据ids")
    private List<String> erpDataId;
}
