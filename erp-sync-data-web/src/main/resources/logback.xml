<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--    turboFilter会按照配置的顺序执行。作用于全局-->
    <turboFilter class="com.fxiaoke.open.erpsyncdata.dbproxy.manager.LogConsoleStrategyFilter" ></turboFilter>
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/fs-app.log</File>
        <!-- 可让每天产生一个日志文件，最多 15 个，最大500M，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/fs-app-%d{yyyyMMdd}.%i.log.zip</fileNamePattern>
            <maxHistory>15</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>500MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
            <pattern>%d{HH:mm:ss.SSS} w [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class{12}:%line] %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                com.alicp.jetcache,
                com.fxiaoke.open.erpsyncdata.dbproxy.aop,
                at com.fxiaoke.open.erpsyncdata.dbproxy.interceptor,
                com.fxiaoke.dispatcher.processor,
                com.fxiaoke.open.erpsyncdata.apiproxy.aop,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC_ROLLING" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="RollingFile"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!--记录warn级别以上日志-->
    <appender name="ErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File> ${catalina.home}/logs/error.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} w [%thread] %-5level %logger{12} %X{traceId} %X{userId} [%class{12}:%line] %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                com.alicp.jetcache,
                com.fxiaoke.open.erpsyncdata.dbproxy.aop,
                at com.fxiaoke.open.erpsyncdata.dbproxy.interceptor,
                com.fxiaoke.dispatcher.processor,
                com.fxiaoke.open.erpsyncdata.apiproxy.aop,
                }%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/error.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>15</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!-- 配置基础组件为ERROR级别，避免打印过多影响服务自己日志 -->
    <logger name="org.apache" level="ERROR"/>
    <logger name="com.github" level="WARN"/>
    <logger name="Access" level="INFO"/>
    <logger name="com.facishare.uc.api.cache" level="ERROR"/>
    <logger name="com.facishare.paas.license.factory.LicenseFactoryBean" level="ERROR"/>

    <logger level="INFO" name="com.fxiaoke.open.erpsyncdata" additivity="true"/>

    <!--    <logger name="com.fxiaoke.dispatcher.mq" level="INFO"/>-->
    <!--    <logger name="com.fxiaoke.dispatcher.processor" level="INFO"/>-->
    <!--    <logger name="com.fxiaoke.dispatcher.store" level="INFO"/>-->
    <!--    <logger name="com.fxiaoke.dispatcher.processor.LockService" level="INFO"/>-->
    <logger name="com.facishare.function" level="ERROR"/>
    <logger name="org.mongodb.driver" level="ERROR"/>
    <logger name="com.fxiaoke.i18n" level="ERROR"/>
    <logger name="com.xxl.job" level="ERROR"/>
    <logger name="com.alibaba" level="ERROR"/>
    <logger name="com.facishare.paas" level="ERROR"/>
    <logger name="com.fxiaoke.common.StopWatch" level="ERROR"/>
    <logger name="com.facishare.uc" level="ERROR"/>
    <logger name="org.apache.kafka" level="ERROR"/>
    <logger name="xxl-job logger" level="ERROR"/>
    <logger name="com.fxiaoke.common.http.handler" level="WARN"/>
    <logger name="com.alicp.jetcache.support.BroadcastManager" level="ERROR" />

    <root level="INFO">
        <appender-ref ref="ASYNC_ROLLING"/>
        <appender-ref ref="ErrorLog"/>
    </root>

</configuration>