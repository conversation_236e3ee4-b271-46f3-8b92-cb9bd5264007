package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/4 16:20:19
 */
public interface GetAddDownstreamTask {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Arg implements Serializable {
        @ApiModelProperty("分组id")
        private String id;
    }

    @Data
    @ApiModel
    class Result implements Serializable {
        @ApiModelProperty("是否校验完成 1-完成 2-未完成")
        private Integer status;
        @ApiModelProperty("任务添加下游企业数量")
        private Integer total;
        @ApiModelProperty("成功数量")
        private Integer success;
        @ApiModelProperty("失败数量")
        private Integer fail;
    }
}
