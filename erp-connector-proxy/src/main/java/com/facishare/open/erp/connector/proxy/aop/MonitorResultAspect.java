package com.facishare.open.erp.connector.proxy.aop;

import com.facishare.open.erp.connector.proxy.linkedin.model.SimpleResponse;
import com.facishare.open.erp.connertor.sdk.model.Base;
import com.restfb.WebRequestor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/29 14:43:49
 */
@Aspect
@Component
@Slf4j
public class MonitorResultAspect {

    private ThreadLocal<Base.MonitorResult> monitorResultThreadLocal = new ThreadLocal<>();

    @Around("execution(* com.facishare.open.erp.connertor.sdk.controller.IConnectorController.*(..))")
    public Object invokeMonitor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        final Object proceed;
        try {
            monitorResultThreadLocal.set(new Base.MonitorResult());
            proceed = proceedingJoinPoint.proceed();
            if (proceed instanceof Base.MonitorResult && ((Base.MonitorResult) proceed).getErrCode() == 0) {
                final Base.MonitorResult monitorResult = monitorResultThreadLocal.get();
                for (Field field : Base.MonitorResult.class.getDeclaredFields()) {
                    field.setAccessible(true);
                    final Object o = field.get(monitorResult);
                    if (Objects.nonNull(o)) {
                        field.set(proceed, o);
                    }
                }
            }
        } finally {
            monitorResultThreadLocal.remove();
        }
        return proceed;
    }

    /**
     * facebook日志
     */
    @Around("execution(* com.restfb.WebRequestor.*(..)) && args(request)")
    public Object setFacebookMonitor(ProceedingJoinPoint joinPoint, WebRequestor.Request request) throws Throwable {
        Long callTime = System.currentTimeMillis();

        WebRequestor.Response response = null;
        try {
            response = (WebRequestor.Response) joinPoint.proceed();
            return response;
        } finally {
            setFacebookMonitorResult(request, callTime, response);
        }
    }

    private void setFacebookMonitorResult(final WebRequestor.Request request, final Long callTime, final WebRequestor.Response response) {
        final Base.MonitorResult monitorResult = monitorResultThreadLocal.get();
        //FacebookController接口不需要monitor
        if (Objects.isNull(monitorResult)) {
            return;
        }
        monitorResult.setUrl(request.getUrl());
        monitorResult.setHeaderMap(null);
        if (request.hasBody()) {
            monitorResult.setParams(request.getBody().getData());
        } else {
            String parameters = request.getParameters();
            // 删除敏感信息
            parameters = parameters.replaceAll("&access_token=\\w*", "");
            monitorResult.setParams(parameters);
        }
        if (Objects.nonNull(response)) {
            monitorResult.setResponseBody(response.getBody());
            monitorResult.setResponseStatus(response.getStatusCode());
        }
        monitorResult.setCost(System.currentTimeMillis() - callTime);
    }

    /**
     * linkedin日志
     */
    @Around("execution(* com.facishare.open.erp.connector.proxy.linkedin.service.OkHttpSupportHelp.getResponse(..)) && args(request)")
    public Object setLinkedinMonitor(ProceedingJoinPoint joinPoint, Request request) throws Throwable {
        Long callTime = System.currentTimeMillis();

        SimpleResponse response = null;
        try {
            response = (SimpleResponse) joinPoint.proceed();
            return response;
        } finally {
            setlinkedinMonitorResult(request, callTime, response);
        }
    }

    private void setlinkedinMonitorResult(final Request request, final Long callTime, final SimpleResponse response) {
        final Base.MonitorResult monitorResult = monitorResultThreadLocal.get();
        //FacebookController接口不需要monitor
        if (Objects.isNull(monitorResult)) {
            return;
        }

        final Map<String, String> header = request.headers().toMultimap().entrySet().stream()
                .filter(entry -> Objects.equals(entry.getKey(), "Authorization"))
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.join(",", entry.getValue())));

        final HttpUrl url = request.url();
        monitorResult.setUrl(url.scheme() + "//" + url.host() + url.encodedPath());
        monitorResult.setHeaderMap(header);
        // 暂时只有get需要记录日志
        monitorResult.setParams(url.query());
        monitorResult.setResponseStatus(response.getCode());
        monitorResult.setResponseBody(response.getBody());
        monitorResult.setCost(System.currentTimeMillis() - callTime);
    }
}
