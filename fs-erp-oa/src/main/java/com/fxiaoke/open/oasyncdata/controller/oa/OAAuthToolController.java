package com.fxiaoke.open.oasyncdata.controller.oa;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.SendTextNoticeArg;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import com.fxiaoke.open.oasyncdata.db.dao.OAConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.OAConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.ParallelUtils;
import com.fxiaoke.open.oasyncdata.model.ImportExcelFile;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.model.OASettingVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA工具接口，有权限")
@RestController()
@RequestMapping("erp/syncdata/oa/authTools")
@Slf4j
public class OAAuthToolController extends BaseController {


    @Autowired
    private OASettingService oaSettingService;
    @Autowired
    private OAConnectInfoDao oaConnectInfoDao;


    @ApiOperation(value = "返回相关的配置项")
    @RequestMapping(value = "/getConfigurationList", method = RequestMethod.POST)
    public Result<List<OASettingVO>> getConfigurationList(@RequestBody OASettingVO oaSettingVO) {
        OATenantEnum oaTenantEnum=null;
        if(StringUtils.isNotBlank(oaSettingVO.getType())){
            oaTenantEnum=OATenantEnum.valueOf(oaSettingVO.getType());
        }
        Result<List<OASettingVO>> listResult = oaSettingService.listSettingInfo(oaSettingVO.getTenantId(), oaTenantEnum, oaSettingVO.getDataCenterId());
        return listResult;
    }

    @ApiOperation(value = "新增相关的配置项")
    @RequestMapping(value = "/addConfiguration", method = RequestMethod.POST)
    public Result<String> addConfiguration(@RequestBody OASettingVO oaSettingVO) {
        OATenantEnum oaTenantEnum=null;
        if(StringUtils.isNotBlank(oaSettingVO.getType())){
            oaTenantEnum=OATenantEnum.valueOf(oaSettingVO.getType());
        }
        Result<String> upsertResult = oaSettingService.upsertSettingInfo(oaSettingVO.getTenantId(), oaSettingVO, oaSettingVO.getDataCenterId());
        return upsertResult;
    }

    @ApiOperation(value = "获取数据中心ids")
    @RequestMapping(value = "/getDataCenterIds", method = RequestMethod.GET)
    public Result<List<OAConnectParam.OADataCenterInfo>> getDataCenterIds(@RequestParam("tenantId") String tenantId) {
        if(tenantId.isEmpty()){
            return Result.newSuccess();
        }
        List<OAConnectInfoEntity> oaConnectInfoEntities = oaConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listInfo(tenantId);
        if(CollectionUtils.isEmpty(oaConnectInfoEntities)){
            return Result.newSuccess();
        }
        List<OAConnectParam.OADataCenterInfo> oaConnectParams=Lists.newArrayList();
        for (OAConnectInfoEntity oaConnectInfoEntity : oaConnectInfoEntities) {
            OAConnectParam.OADataCenterInfo oaDataCenterInfo=new OAConnectParam.OADataCenterInfo();
            oaDataCenterInfo.setId(oaConnectInfoEntity.getId());
            OAConnectParam oaConnectParam = JSONObject.parseObject(oaConnectInfoEntity.getConnectParams(), OAConnectParam.class);
            oaDataCenterInfo.setConnectOaName(oaConnectParam.getConnectOaName());
            oaConnectParams.add(oaDataCenterInfo);
        }
        return Result.newSuccess(oaConnectParams);
    }

    @ApiOperation(value = "编辑相关的配置项")
    @RequestMapping(value = "/editConfiguration", method = RequestMethod.POST)
    public Result<String> editConfiguration(@RequestBody OASettingVO oaSettingVO) {

        Result<String> stringResult = oaSettingService.upsertSettingInfo(oaSettingVO.getTenantId(), oaSettingVO, oaSettingVO.getDataCenterId());
        return stringResult;
    }

    @ApiOperation(value = "删除相关的配置项")
    @RequestMapping(value = "/deleteConfiguration", method = RequestMethod.POST)
    public Result<String> deleteConfiguration(@RequestBody OASettingVO oaSettingVO) {
        Result<String> stringResult = oaSettingService.deleteSettingInfoById(oaSettingVO.getTenantId(), oaSettingVO.getId());
        return Result.newSuccess(stringResult.getData());
    }

    @ApiOperation(value = "返回配置类型列表")
    @RequestMapping(value = "/getTypeList", method = RequestMethod.GET)
    public Result<List<Map<String,String>>> getTypeList() {
        List<Map<String,String>> apiNameList= Lists.newArrayList();
        for (OATenantEnum value : OATenantEnum.values()) {
            Map<String,String> selectValues=Maps.newHashMap();
            selectValues.put("value",value.name());
            selectValues.put("label",value.getDesc());
            apiNameList.add(selectValues);
        }
        return Result.newSuccess(apiNameList);
    }




}
