package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class GetObjectTemplateStatusArg implements Serializable {
    @ApiModelProperty("源对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("目标对象apiName")
    private String destObjectApiName;
}
