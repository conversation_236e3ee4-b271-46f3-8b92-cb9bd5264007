package com.fxiaoke.open.erpsyncdata.preprocess.auditlog

import com.fxiaoke.open.erpsyncdata.SpringBaseTest
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service.ModificationRecordService
import com.fxiaoke.open.erpsyncdata.preprocess.service.remote.AuditLogger
import org.assertj.core.util.Lists
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/10/12 15:53:54
 */
class AuditLoggerTest extends Specification {

    AuditLogger auditLogger

    def setup() {
        auditLogger = new AuditLogger(modificationRecordService: Mock(ModificationRecordService))
    }

    def "修改"() {
//        客户地址000
        String objectId = "64bfab5e4b5d8000013f6e40"

        def oldData = new ObjectData()
        oldData.putId(objectId)
        oldData.put("name", "test_kehu123")
        oldData.put("tel", "*********")

        def newData = new ObjectData()
        newData.putId(objectId)
        newData.put("name", "test_kehu456")
        newData.put("tel", "*********")

        expect:
        auditLogger.recordModifyLog("88521", "-10000", "AccountObj", Lists.newArrayList(oldData), Lists.newArrayList(newData))
    }

    def "新增"() {
//        客户地址000
        String objectId = "64bfab5e4b5d8000013f6e40"

        def newData = new ObjectData()
        newData.putId(objectId)
        newData.put("name", "test_kehu456")
        newData.put("tel", "*********")

        expect:
        auditLogger.recordAddLog("88521", "-10000", "AccountObj", Lists.newArrayList(newData))
    }
}
