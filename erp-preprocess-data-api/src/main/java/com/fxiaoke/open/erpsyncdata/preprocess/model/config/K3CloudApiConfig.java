package com.fxiaoke.open.erpsyncdata.preprocess.model.config;

import lombok.Data;

/**
 * k3c api接口配置
 * <AUTHOR> (^_−)☆
 * @date 2023/2/16
 */
@Data
public class K3CloudApiConfig {
    /**
     * 是否使用app 鉴权方式。
     * 适用于公有云token访问方式
     */
    private boolean useAppToken = false;

    /**
     * 打开调试模式
     */
    private boolean enableDebug = false;

    /**
     * 对金蝶调用参数做去除零宽字符处理。
     * 解决调用参数的单据编码不能带有零宽字符的问题。
     */
    private boolean removeZeroWidthChar = false;

    /**
     * 新的读取response方式
     */
    private boolean useNewResponseReader;
}
