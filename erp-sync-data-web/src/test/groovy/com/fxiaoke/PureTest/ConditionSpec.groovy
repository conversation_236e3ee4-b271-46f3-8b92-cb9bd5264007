package com.fxiaoke.PureTest

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.open.erpsyncdata.common.rule.ConditionUtil
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.googlecode.aviator.AviatorEvaluator
import com.googlecode.aviator.Expression
import org.apache.commons.collections4.CollectionUtils
import org.bson.types.ObjectId
import org.junit.Ignore
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @Date 2022/11/14 17:37
 * @Version 1.0
 */
@Ignore
class ConditionSpec extends Specification {


    def "测试生成condition表达式"(){
        given:
        FilterData filterData1= JSONObject.parseObject("{\"label\":\"金额\",\"operate\":\"GT\",\"fieldType\":\"currency\",\"fieldValue\":[\"10\"],\"fieldApiName\":\"field_FY6bw__c\",\"isVariableBetween\":false}");
        FilterData filterData2= JSONObject.parseObject("{\"label\":\"金额\",\"operate\":\"GT\",\"fieldType\":\"currency\",\"fieldValue\":[\"10\"],\"fieldApiName\":\"field_FY6bw__c\",\"isVariableBetween\":false}");
        List<List<FilterData>> filters= Lists.newArrayList();
        filters.add(Lists.newArrayList(filterData1,filterData2))
        when:
        def message=ConditionUtil.parseToOrExpression(filters)
        println message
        then:
        1==1;

    }

    def "测试aviator表达式"(){

        given:
        org.bson.types.ObjectId objectId1 = new ObjectId(new Date(1668009600000l));
        def expression = "a-(b-c) > 100";
        def envMap= Maps.newHashMap();
        envMap.put("a",100.3);
        envMap.put("b",45);
        envMap.put("c",-199.100);
        when:
        boolean compiledExp=  AviatorEvaluator.execute(expression, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
       then:
       with(compiledExp){
            compiledExp==true;
       }

    }

    @Unroll
    def "校验avaiator表达式 #uid"(){
        given:
        def conditionExpress="FDescription != nil  && FDescription != ''";
        def envMap= valueMap
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==type;
        }
        where: "表格方式验证订单信息的分支场景"
        uid | valueMap                 || type
        1   | ["FDescription": ''] || true

    }

    def "校验avaiator表达式2"(){
        given:
        def conditionExpress="(field_9m25X__c != nil  && field_9m25X__c != '')";
        def envMap= Maps.newHashMap();
        envMap.put("field_9m25X__c","3")
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==true;
        }

    }

    def "校验avaiator表达式数字类型"(){
        given:
//        BigDecimal a1 = new BigDecimal(100);
//        BigDecimal a2  = new BigDecimal("60.00");
//        BigDecimal a3  = new BigDecimal("-60.000001");
//        def com1=a1>a2;
//        def com2=a1>=100;
//        def com3=a1>a3;

//        BigDecimal bd = new BigDecimal("3.40256010353E14");
//        println bd.toPlainString();
//        BigDecimal bd2 = new BigDecimal("1e-2");
//        println bd2.toPlainString();
//        def decimal = new BigDecimal("1.03E+08")
//        def decimal2= BigDecimal.valueOf(1000L)
        def conditionExpress="(field_FY6bw__c > 11110.0M)";
        def envMap= Maps.newHashMap();
        envMap.put("field_FY6bw__c","-11.00")
        when:
        boolean compiledExp=  AviatorEvaluator.execute(conditionExpress, envMap, false);
        System.out.println(compiledExp);
        // Execute with injected variables.
        then:
        with(compiledExp){
            compiledExp==true;
        }

    }

    def "测试length"(){
        given:
        String text="调用CRM接口错误:数据[CH1912#20121901-edit2]必填字段产品分类未填写不可进行当前操作";
      long length=  text.length()
        expect:
        println length
    }

    def "测试lengthsize"(){
        given:
        // 创建一个数组
        ArrayList<Integer> ages = new ArrayList<>();

        //往数组中添加一些元素
        ages.add(10);
        ages.add(12);
        ages.add(15);
        ages.add(19);
        ages.add(23);
        ages.add(34);
        expect:
        List<Integer> data1= ages.subList(0,1);
        List<Integer> data2= ages.subList(0,2);
        println length
    }





}
