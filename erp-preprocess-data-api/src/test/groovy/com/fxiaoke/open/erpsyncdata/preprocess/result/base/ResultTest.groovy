package com.fxiaoke.open.erpsyncdata.preprocess.result.base


import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum
import spock.lang.Specification
import spock.lang.Unroll

import java.util.function.Function
import java.util.function.Supplier

/**
 * <AUTHOR> 
 * @date 2024/7/24 16:37:39
 */
class ResultTest extends Specification {

    void setup() {
        //mock i18n
        I18nClient i18nClient = Mock(I18nClient)
        I18nUtil._setI18ClientOnlySpring(i18nClient)
        i18nClient.get(_ as String, 0, _ as String, _ as String) >> { i18nKey, _, locale, defaultValue ->
            return "i18n"
        }
    }

    @Unroll
    def "测试ResultCode"() {
        when:
        def data = Result.newError(i18nEnum)

        then:
        data.getErrCode() == errorCode
        data.getErrMsg() == i18nEnum.getI18nValue()

        where:
        i18nEnum             || errorCode
        I18NStringEnum.s1    || "s306240001"
        I18NStringEnum.s12   || "s306240012"
        I18NStringEnum.s112  || "s306240112"
        I18NStringEnum.s1111 || "s306241111"

        I18NStringEnum.md1   || "s306240000"
    }
}
