package com.facishare.open.erp.connertor.service;

import com.facishare.open.erp.connertor.codec.AesCodec;
import com.facishare.open.erp.connertor.rest.annotation.RestResource;
import com.facishare.open.erp.connertor.sdk.model.GetUserToken;
import com.facishare.open.erp.connertor.sdk.model.RefreshToken;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/5/10 17:45:50
 */
@RestResource(value = "ErpConnectorProxy", desc = "调用国外的erp", codec = AesCodec.class)
public interface OauthLoginService {
    @RequestMapping("/{channel}/getUserToken")
    GetUserToken.Result getUserToken(@PathVariable("channel") String channel, @RequestBody GetUserToken.Arg arg);

    @RequestMapping("/{channel}/refreshToken")
    RefreshToken.Result refreshToken(@PathVariable("channel") String channel, @RequestBody RefreshToken.Arg arg);

}
