package com.facishare.open.erp.connertor.rest.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erp.connertor.rest.grayrule.RouteGrayRule;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:20:51
 */
public class GrayServiceConfig {
    private RouteGrayRule grayRule;
    @Getter
    private ServiceConfig serviceConfig;

    public GrayServiceConfig(JSONObject o) {
        final ServiceConfigGray serviceConfigGray = o.toJavaObject(ServiceConfigGray.class);
        serviceConfig = serviceConfigGray;

        grayRule = new RouteGrayRule(serviceConfigGray.getRule());
    }

    public boolean isAllow(String... keys) {
        return grayRule.isAllow(keys);
    }

    @Data
    public static class ServiceConfigGray extends ServiceConfig {
        private String rule;
    }
}
