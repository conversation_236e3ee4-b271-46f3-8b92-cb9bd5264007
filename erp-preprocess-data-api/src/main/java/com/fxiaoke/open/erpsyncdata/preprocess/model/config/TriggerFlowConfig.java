package com.fxiaoke.open.erpsyncdata.preprocess.model.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/14 14:30:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TriggerFlowConfig {
    public static final TriggerFlowConfig defaultConfig = new TriggerFlowConfig(true, true, true);

    /**
     * 总开关
     */
    private boolean enable;
    /**
     * 审批流
     */
    private boolean triggerFlow;
    /**
     * 工作流
     */
    private boolean triggerWorkFlow;

    public boolean checkTriggerFlow() {
        return enable && triggerFlow;
    }

    public boolean checkTriggerWorkFlow() {
        return enable && triggerWorkFlow;
    }
}
