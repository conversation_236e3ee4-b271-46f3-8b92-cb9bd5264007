package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/4 10:35:54
 */
public interface SaveEmployeeAutoBindRule {
    public String integrationType = "1";//按集成流
    public String autoBindRuleType = "2";//按绑定规则

    @Data
    class Arg {
        @ApiModelProperty("是否开启")
        private Boolean isOpen;
        @ApiModelProperty("开启类型,1:按集成流，2：按绑定规则")
        private String type;
        @ApiModelProperty("crm人员字段中的erp主属性字段")
        private String erpUserNameFiled;
        @ApiModelProperty("crm人员字段中的erpId字段")
        private String erpUserIdField;
    }

    @Data
    class Result {

    }

}
