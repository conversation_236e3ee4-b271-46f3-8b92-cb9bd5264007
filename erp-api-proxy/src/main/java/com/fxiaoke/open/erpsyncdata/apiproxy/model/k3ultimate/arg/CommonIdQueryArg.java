package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class CommonIdQueryArg extends K3UltimateQueryBaseArg {
    @Getter
    private List<String> id; //id=ids，旗舰版的接口比较混乱，
    public void setId(List<String> value) {
        this.id = value;
        put("id",id);
    }

    @Getter
    private List<String> ids;//id=ids，旗舰版的接口比较混乱，
    public void setIds(List<String> value) {
        this.ids = value;
        put("ids",ids);
    }

    public void rewriteId() {
        if(CollectionUtils.isNotEmpty(id)) {
            ids = id;
            put("id",id);
            put("ids",id);
        }
        if(CollectionUtils.isNotEmpty(ids)) {
            id = ids;
            put("id",ids);
            put("ids",ids);
        }
    }
}
