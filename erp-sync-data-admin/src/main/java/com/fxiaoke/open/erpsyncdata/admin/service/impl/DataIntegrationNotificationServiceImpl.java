package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.arg.GetDataIntegrationNotificationArg;
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager;
import com.fxiaoke.open.erpsyncdata.admin.service.DataIntegrationNotificationService;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataIntegrationNotificationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("dataIntegrationNotificationService")
@Slf4j
public class DataIntegrationNotificationServiceImpl implements DataIntegrationNotificationService {
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;
    @Autowired
    private UserRoleManager userRoleManager;

    @Override
    public Result<DataIntegrationNotificationModel> getDataList(GetDataIntegrationNotificationArg arg) {
        Date startTime = null;
        Date endTime = null;
        if (arg.getStartTime() != null) {
            startTime = new Date(arg.getStartTime());
        }
        if (arg.getEndTime() != null) {
            endTime = new Date(arg.getEndTime());
        }

        DataIntegrationNotificationModel model = dataIntegrationNotificationManager.getDataListByPage(arg.getTenantId(),
                arg.getDcId(),
                StringUtils.isEmpty(arg.getPloyDetailId()) ? null : Lists.newArrayList(arg.getPloyDetailId()),
                arg.getUserId(),
                arg.getNotificationType(),
                null,
                null,
                startTime,
                endTime,
                arg.getPageSize(),
                arg.getPage());

        return Result.newSuccess(model);
    }

    @Override
    public Result<List<DataCenterModel>> getDcList(String tenantId) {
        return Result.newSuccess(dataIntegrationNotificationManager.getDcList(tenantId));
    }

    @Override
    public Result<Boolean> checkAdmin(String tenantId, String userId) {
        return Result.newSuccess(userRoleManager.checkAdminAuth(tenantId,userId));
    }

    @Override
    public Result<Long> clearData() {
        return Result.newSuccess(dataIntegrationNotificationManager.clearData());
    }

    @Override
    public Result<Boolean> hasData(String tenantId, Integer userId) {
        return Result.newSuccess(dataIntegrationNotificationManager.hasData(tenantId, userId));
    }
}
