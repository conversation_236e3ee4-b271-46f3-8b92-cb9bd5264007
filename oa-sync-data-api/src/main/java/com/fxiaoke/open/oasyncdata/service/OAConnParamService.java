package com.fxiaoke.open.oasyncdata.service;


import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/3/18
 */
public interface OAConnParamService {
    Result<OAConnectInfoVO> getOAConnectInfo(String tenantId,String dataCenterId);

    Result<List<OAConnectInfoVO>> listInfoByTenantId(String tenantId);

    Result<String> initOAConnectInfo(String tenantId, String enterpriseName);

    Result<String> createOAConnectInfo(String tenantId, String enterpriseName,String connectName,String sourceDataCenterId,boolean copySettings);

    Result<String> updateOAConnectInfo(String tenantId, OAConnectInfoVO oaConnectInfoVO);

    Result<String> coverUpdateOAConnectInfo(String tenantId, OAConnectInfoVO oaConnectInfoVO);
}
