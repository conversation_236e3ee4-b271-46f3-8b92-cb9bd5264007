package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16 15:17:42
 */
public interface BatchExportSystemFieldMapping {
    @Data
    class Arg {
        @ApiModelProperty("需要导出的类型")
        private List<ErpFieldTypeEnum> fieldType;
        @ApiModelProperty("选择的id集合")
        private List<String> idList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @ApiModelProperty("npath")
        private String path;
        @ApiModelProperty("下载地址")
        private String downloadUrl;
    }
}
