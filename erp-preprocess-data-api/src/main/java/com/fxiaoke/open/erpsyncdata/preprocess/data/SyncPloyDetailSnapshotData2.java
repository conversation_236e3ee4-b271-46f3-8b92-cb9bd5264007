package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import lombok.Data;

import java.util.Map;

/**
 * 改个名字区分下
 *
 * 原来的同步策略明细，改名为集成流快照
 * 集成流快照
 */
@Data
public class SyncPloyDetailSnapshotData2 extends BaseData  {
    private String id;
    private String sourceTenantId;
    private String sourceObjectApiName;
    private String destTenantId;
    private String destObjectApiName;
    /**
     * {@link  SyncPloyDetailStatusEnum}
     */
    private Integer status;
    /**
     * 策略对象主键id
     * 已作废，仅为刷库使用
     */
    @Deprecated
    private String syncPloyId;
    private String syncPloyDetailId;
    private SyncPloyDetailData2 syncPloyDetailData;
    /**加点注释数据范围的过滤条件在 集成流和集成流快照中存储的格式不一样。
     * 集成流中存储在SyncConditionsData的是原始条件： filters *
     *
     *   "filters": [
     *     [
     *       {
     *         "operate": "EQ",
     *         "fieldType": "long_text",
     *         "fieldValue": [
     *           "哈哈"
     *         ],
     *         "fieldApiName": "remark"
     *       }
     *     ]
     *   ]
     * 集成流快照中中存储的表达式syncConditionsExpression： ((remark == "哈哈"))
     * */
    private String syncConditionsExpression;
    private Map<String, String> detailObjectSyncConditionsExpressions;
    private Integer newest;

    @Override
    public String toString() {
        return "SyncPloyDetailSnapshotData2";
    }
}
