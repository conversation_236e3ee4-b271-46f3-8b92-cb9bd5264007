package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.common.data.BaseResult;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SimpleSyncData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class CompleteDataWriteArg {
    public static final Integer SUCCESS_CODE = 0;
    public static final Integer OUT_ERROR_CODE = 5001;
    public static final Integer UNSUPPORT_EVENT_TYPE = 5002;
    public static final Integer SOCKET_TIME_OUT_CODE = 5003;
    /**
     * 调用成功但是结果有误
     */
    public static final Integer SUCCESS_BUT_RESULT_ERROR = 5004;
    /**
     * 同步异常，同步过程中出现代码异常或数据异常
     */
    public static final Integer SYNC_EXCEPTION = 5005;
    public static final Integer INTERCEPT_CODE = -1;
    private String tenantId;
    private Integer destEventType;
    private WriteResult writeResult;
    private List<WriteResult> detailWriteResults = new ArrayList<>();
    private Integer destTenantType;
    private String syncPloyDetailSnapshotId;

    @Data
    public static class WriteResult extends BaseResult {
        @Deprecated
        private String syncDataId;
        private SimpleSyncData simpleSyncData;
        private String destDataId;
        private Map<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
    }

    public static CompleteDataWriteArg newSuccess(Integer destEventType, String syncDataId, String destDataId) {
        CompleteDataWriteArg arg = new CompleteDataWriteArg();
        arg.setDestEventType(destEventType);
        WriteResult writeResult = new WriteResult();
        writeResult.setErrCode(SUCCESS_CODE);
        writeResult.setErrMsg(I18NStringManager.getByTraceLang(I18NStringEnum.s6));
        writeResult.setDestDataId(destDataId);
        writeResult.setSyncDataId(syncDataId);
        arg.setWriteResult(writeResult);
        return arg;
    }

    public static CompleteDataWriteArg newError(Integer destEventType, String syncDataId, String errMsg) {
        CompleteDataWriteArg arg = new CompleteDataWriteArg();
        arg.setDestEventType(destEventType);
        WriteResult writeResult = new WriteResult();
        writeResult.setSyncDataId(syncDataId);
        writeResult.setErrCode(OUT_ERROR_CODE);
        writeResult.setErrMsg(errMsg);
        arg.setWriteResult(writeResult);
        return arg;
    }

    public static CompleteDataWriteArg newError(Integer destEventType, String syncDataId, Integer errCode, String errMsg) {
        CompleteDataWriteArg arg = new CompleteDataWriteArg();
        arg.setDestEventType(destEventType);
        WriteResult writeResult = new WriteResult();
        writeResult.setSyncDataId(syncDataId);
        writeResult.setErrCode(errCode);
        writeResult.setErrMsg(errMsg);
        arg.setWriteResult(writeResult);
        return arg;
    }
}
