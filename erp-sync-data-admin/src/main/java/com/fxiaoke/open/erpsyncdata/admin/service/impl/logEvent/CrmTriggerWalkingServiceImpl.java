package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.admin.result.CrmTriggerLogResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SyncLogNode;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogDetailArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 11:15 2024/8/8
 * @Desc:
 */
@Service
@Slf4j
@SyncLogNode(SyncLogTypeEnum.CRM_TRIGGER)
public class CrmTriggerWalkingServiceImpl extends AbstractLogService {
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    public ObjectDataService objectDataService;

    @Override
    public Result queryListLogByType(String tenantId, StreamLogQueryArg streamLogQueryArg, String lang) {
        List<String> filterSyncLog = null;
        if (ObjectUtils.isNotEmpty(streamLogQueryArg.getSyncLogId())) {
            List<SyncLog> syncLogs = syncLogManager.listByLogId(tenantId, streamLogQueryArg.getSyncLogId(),streamLogQueryArg.getStartTime(),streamLogQueryArg.getEndTime());
            if (CollectionUtils.isEmpty(syncLogs)) {
                return Result.newSuccess(new Page<>());
            }
            filterSyncLog = syncLogs.stream().filter(item -> item.getType().equals(SyncLogTypeEnum.CRM_TRIGGER)).map(SyncLog::getLogId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterSyncLog)) {
                return Result.newSuccess(new Page<>());
            }
        }
        String realObjApiName = streamLogQueryArg.getObjApiName();
        streamLogQueryArg.setPloyDetailId(null);//不筛选集成流
        Page<SyncLog> syncLogPage = getSyncLogPage(tenantId, realObjApiName, filterSyncLog, streamLogQueryArg);
        return convert2PageResult(syncLogPage);
    }

    @Override
    public Result<?> queryListByLogIds(final String tenantId, final String realObjApiName, final List<String> logIds, final StreamLogQueryArg streamLogQueryArg, String lang) {
        String realObjApiName2 = streamLogQueryArg.getObjApiName();
        Page<SyncLog> syncLogPage = getSyncLogPage(tenantId, realObjApiName2, logIds, streamLogQueryArg);
        return convert2PageResult(syncLogPage);
    }

    private static Result<Page<CrmTriggerLogResult>> convert2PageResult(final Page<SyncLog> syncLogPage) {
        List<CrmTriggerLogResult> crmTriggerLogResultResults = Lists.newArrayList();
        if(syncLogPage.getData()!=null){
            for (SyncLog syncLog : syncLogPage.getData()) {
                ErpTempData erpTempData = Optional.ofNullable(syncLog.getErpTempData()).orElseGet(() -> new ErpTempData());
                //直接返回synclog的数据
                CrmTriggerLogResult crmTriggerLogResultResult = new CrmTriggerLogResult();
                crmTriggerLogResultResult.setSyncLogId(syncLog.getLogId());
                crmTriggerLogResultResult.setDataId(erpTempData.getDataId());
                crmTriggerLogResultResult.setCreateTime(syncLog.getCreateTime().getTime());
                crmTriggerLogResultResult.setDataNumber(erpTempData.getDataNumber());
                crmTriggerLogResultResult.setId(String.valueOf(syncLog.getId()));
                crmTriggerLogResultResults.add(crmTriggerLogResultResult);
            }
        }
        Page<CrmTriggerLogResult> crmTriggerLogResultPage = new Page<>();
        crmTriggerLogResultPage.setData(crmTriggerLogResultResults);
        crmTriggerLogResultPage.setTotalNum(syncLogPage.getTotalNum());
        crmTriggerLogResultPage.setHasNext(syncLogPage.isHasNext());
        return Result.newSuccess(crmTriggerLogResultPage);
    }


    @Override
    public Result<?> queryLogDataById(String tenantId, StreamLogQueryArg.SyncLogDataArg syncLogDataArg,String lang,SyncLog syncLog) {
        if(syncLog==null){
            syncLog = syncLogManager.getLogById(tenantId, new ObjectId(syncLogDataArg.getId()), syncLogDataArg.getNodeEnum());
        }
        if (ObjectUtils.isNotEmpty(syncLog)) {
            StreamLogDetailArg streamLogDetailArg = new StreamLogDetailArg();
            streamLogDetailArg.setSyncLogId(syncLog.getLogId());
            streamLogDetailArg.setTenantId(tenantId);
            ErpTempData erpTempData=Optional.ofNullable(syncLog.getErpTempData()).orElseGet(() ->new ErpTempData());
            CrmTriggerLogResult crmTriggerLogResultResult = new CrmTriggerLogResult();
            crmTriggerLogResultResult.setSyncLogId(syncLog.getLogId());
            crmTriggerLogResultResult.setDataId(erpTempData.getDataId());
            crmTriggerLogResultResult.setCreateTime(syncLog.getCreateTime().getTime());
            crmTriggerLogResultResult.setDataNumber(erpTempData.getDataNumber());
            crmTriggerLogResultResult.setId(String.valueOf(syncLog.getId()));
            crmTriggerLogResultResult.setData(erpTempData.getDataBody());
            return Result.newSuccess(crmTriggerLogResultResult);
        }
        return Result.newSuccess();
    }

}
