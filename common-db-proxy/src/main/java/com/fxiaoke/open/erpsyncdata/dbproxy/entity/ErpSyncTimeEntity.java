package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * erp策略同步时间
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ObjApiName("objectApiName")
@Table(name = "erp_sync_time")
public class ErpSyncTimeEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;

    /**
     * 对象apiName
     */
    private String objectApiName;

    /**
     * 操作类型
     *
     * @see EventTypeEnum
     * ExtraEventTypeEnum
     * 只包含这部分数据 {@link com.fxiaoke.open.erpsyncdata.preprocess.constant.ExtraEventTypeEnum#permittedType}
     */
    private Integer operationType;

    /**
     * 最后同步时间
     */
    private Long lastSyncTime;

    /**
     * 最后轮询mongo时间
     */
    private Long lastQueryMongoTime ;

    /**
     * 优先级，值越小越优先，默认值100
     */
    private Integer priority;

    /**
     * 轮询时间间隔，分钟，注意这个轮询时间间隔只能是最上层统一调度时间间隔的倍数
     */
    private String pollingInterval;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}