#YAML
# 插入和更新 sqlserver
insertSql: |
  insert into t7_1 (id, pid, name, "date")
  values (${id}, ${pid}, ${name}, ${date__d});
updateSql: |
  MERGE INTO t7_1 AS target
  USING (VALUES
  (${id}, ${pid}, ${name}, ${date__d})
  ) AS source (id, pid, name, date)
  ON target.id = source.id
  WHEN MATCHED THEN
  UPDATE SET
  target.pid = source.pid,
  target.name = source.name,
  target.date = source.date
  WHEN NOT MATCHED THEN
  INSERT (id, pid, name, date)
  VALUES (source.id, source.pid, source.name, source.date);
queryIdSql: |
  select ${id}
