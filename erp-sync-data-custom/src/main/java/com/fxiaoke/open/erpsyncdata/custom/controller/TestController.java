package com.fxiaoke.open.erpsyncdata.custom.controller;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by fengyh on 2021/2/1.
 */

@RestController
//100, 101 分别指定为112和生产的tanentID
@RequestMapping(path = {"inner/erp/syncdata/customfunction/100", "inner/erp/syncdata/customfunction/101"})
@Slf4j
public class TestController {

    @RequestMapping("/test")
    @ResponseBody
    public String test(){
        return "hello world";
    }

    /**
     * curl --location --request POST 'localhost:8080/inner/erp/syncdata/customfunction/100/test2' \
     --header 'k1: v1' \
     --header 'Content-Type: application/json' \
     --header 'x-fs-ei: 100' \
     --data-raw '{
     "b1":"bv1",
     "b2":"bv2"
     }'
     */
    @ApiOperation(value = "创建数据映射，必须开启策略")
    @RequestMapping(value = "/test2",method = RequestMethod.POST)
    public Result<String> test2(@RequestHeader(value="x-fs-ei") Integer tenantId,
                                @RequestHeader Map headerMap,
                                @RequestBody Map bodyMap) {
        log.info("trace test2 ei:{}, header:{}, body:{} ", tenantId, headerMap, bodyMap);
        return Result.newSuccess("succ");
    }
}
