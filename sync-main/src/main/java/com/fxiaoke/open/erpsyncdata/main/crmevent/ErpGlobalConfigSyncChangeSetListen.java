package com.fxiaoke.open.erpsyncdata.main.crmevent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.change.set.component.handler.module.Filter;
import com.facishare.change.set.listener.ChangeSetListener;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.TENANT_NEED_PASS_DATASOURCE;

/**
 * <AUTHOR>
 * @date 2023/9/12 11:35:25
 * <p>
 * 更改集配置key为tenant_id
 */
@Slf4j
public class ErpGlobalConfigSyncChangeSetListen extends ChangeSetListener {

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private I18NStringManager i18NStringManager;

    public ErpGlobalConfigSyncChangeSetListen() {
        super("erpSync-globalConfig-changeSet", "erpGlobalConfigSync");
    }

    private final List<ConfigProcess> configProcess = Lists.newArrayList(
            new TenantIdProcess(),
            new TenantListConfigProcess(),
            new TenantListConfigWithKeyProcess(),
            new TenantNeedPassDatasourceProcess()
    );

    /**
     * 出站数据为{"tenant_id":"xxx", process.key(): process.outBound(tenantId)}
     */
    @Override
    public void innerOutBoundPart(Integer enterpriseId, String tableName, List<Filter> filters, Consumer<Map<String, Object>> consumer) throws Throwable {

        String tenantId = String.valueOf(enterpriseId);

        final Map<String, Object> collect = configProcess.stream()
                .filter(ConfigProcess::isOutBound)
                .map(process -> {
                    final String s = process.outBound(tenantId);
                    if (StringUtils.isBlank(s)) {
                        return null;
                    }
                    return Pair.of(process.key(), s);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        consumer.accept(collect);

        log.info("{} outbound tenantId:{} value:{}", this.getClass().getSimpleName(), tenantId, collect);
    }

    private final Map<String, ConfigProcess> processMap = configProcess.stream().collect(Collectors.toMap(ConfigProcess::key, Function.identity()));

    /**
     * uniqueKeys 为企业id,不需要使用
     * data 为入站数据:{"tenant_id":"xxx", process.key(): process.inBound(tenantId, value)}
     */
    @Override
    public void innerInBoundPart(Integer enterpriseId, String tableName, List<String> uniqueKeys, JSONObject data, List<Filter> defaultFilter) throws Throwable {
        String tenantId = String.valueOf(enterpriseId);

        data.forEach((key, value) -> {
            final ConfigProcess process = processMap.get(key);
            if (Objects.isNull(process)) {
                return;
            }
//            防止入站多个企业导致覆盖
            RLock rLock = redissonClient.getLock(key);
            try {
                if (!rLock.tryLock(10, 60, TimeUnit.SECONDS)) {
                    throw new RuntimeException(i18NStringManager.getByEi(I18NStringEnum.s973, enterpriseId + ""));
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            try {
                process.inbound(tenantId, value.toString());
            } finally {
                rLock.unlock();
            }
        });

        log.info("{} inbound ei:{} data:{}", this.getClass().getSimpleName(), enterpriseId, data);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!ConfigCenter.ENABLE_CONSUME_SANDBOX_EVENT) {
            log.info("this env not consume changeSet event");
            return;
        }
        super.afterPropertiesSet();
    }


    interface ConfigProcess {
        /**
         * 出入站key
         */
        String key();

        default boolean isOutBound() {
            return true;
        }

        String outBound(String tenantId);

        void inbound(String tenantId, String value);
    }

    public abstract class AbstractConfigProcess implements ConfigProcess {

        public abstract TenantConfigurationTypeEnum type();

        public abstract String getValue(String tenantId, String config);

        /**
         * @param config   当前的配置
         * @param outValue 出站数据
         * @return 需要修改为什么配置(返回null表示不修改)
         */
        public abstract String newValue(String tenantId, String config, String outValue);

        @Override
        public String key() {
            return type().name();
        }

        @Override
        public String outBound(String tenantId) {
            final ErpTenantConfigurationEntity global = tenantConfigurationManager.findGlobal(type().name());
            if (Objects.nonNull(global) && StringUtils.isNotBlank(global.getConfiguration())) {
                return getValue(tenantId, global.getConfiguration());
            }
            return null;
        }

        @Override
        public void inbound(String tenantId, String value) {
            final ErpTenantConfigurationEntity global = tenantConfigurationManager.findGlobal(type().name());
            final String oldValue = Optional.ofNullable(global).map(ErpTenantConfigurationEntity::getConfiguration).orElse(null);
            final String newValue = newValue(tenantId, oldValue, value);
            if (StringUtils.isBlank(newValue)) {
                return;
            }
            tenantConfigurationManager.updateConfig("0", "0", getChannel(), type().name(), newValue);
        }

        protected String getChannel() {
            return "ALL";
        }
    }

    public class TenantNeedPassDatasourceProcess extends AbstractConfigProcess {
        @Override
        public TenantConfigurationTypeEnum type() {
            return TENANT_NEED_PASS_DATASOURCE;
        }

        @Override
        public String getValue(String tenantId, String config) {
            final Map<String, List<String>> map = JSON.parseObject(config, new TypeReference<Map<String, List<String>>>() {
            });
            final String prefix = tenantId + "_";
            final Map<String, List<String>> value = map.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith(prefix) || entry.getKey().equals(tenantId))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            if (MapUtils.isEmpty(value)) {
                return null;
            }
            return JSON.toJSONString(value);
        }

        /**
         * 出入站企业都有配置,只保留都有的配置
         * 出站有 入站没有, 以出站数据为准
         */
        @Override
        public String newValue(String tenantId, String config, String outValue) {
            final Map<String, List<String>> map;
            if (StringUtils.isBlank(config)) {
                map = new HashMap<>();
            } else {
                map = JSON.parseObject(config, new TypeReference<Map<String, List<String>>>() {
                });
            }

            final Map<String, List<String>> outMap = JSON.parseObject(outValue, new TypeReference<Map<String, List<String>>>() {
            });

            final Map<String, List<String>> addValue = outMap.entrySet().stream()
                    .map(entry -> {
                        final String key = entry.getKey();
                        String newKey = key.contains("_") ? tenantId + "_" + StringUtils.substringAfter(key, "_") : tenantId;

                        final List<String> newValue = getNewValue(newKey, map, entry.getValue());
                        return Pair.of(newKey, newValue);
                    })
                    .filter(pair -> Objects.nonNull(pair.getValue()))
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue));

            map.putAll(addValue);
            return JSON.toJSONString(map);
        }

        private List<String> getNewValue(String newKey, Map<String, List<String>> map, List<String> value) {
            final List<String> strings = map.get(newKey);
            if (Objects.isNull(strings)) {
                return value;
            }

            return ListUtils.retainAll(strings, value);
        }

        @Override
        protected String getChannel() {
            return "0";
        }
    }

    public class TenantListConfigWithKeyProcess implements ConfigProcess {
        private final Splitter on = Splitter.on(";");

        //    需要出入站的type和处理方式
        public List<String> getTenantListType() {
            return ConfigCenter.globalTenantListConfigType;
        }

        @Override
        public String key() {
            return "TenantListConfigWithKey";
        }

        @Override
        public String outBound(String tenantId) {
            final List<TenantConfig> collect = getTenantListType().stream()
                    .map(type -> tenantConfigurationManager.findGlobal(type))
                    .filter(entity -> Objects.nonNull(entity) && StringUtils.isNotBlank(entity.getConfiguration()) && on.splitToList(entity.getConfiguration()).contains(tenantId))
                    .map(entity -> new TenantConfig(entity.getDataCenterId(), entity.getChannel(), entity.getType()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(collect)) {
                return null;
            }
            return JSON.toJSONString(collect);
        }

        @Override
        public void inbound(String tenantId, String value) {
            final List<TenantConfig> tenantConfigs = JSON.parseArray(value, TenantConfig.class);
            if (CollectionUtils.isEmpty(tenantConfigs)) {
                return;
            }
            for (TenantConfig tenantConfig : tenantConfigs) {
                final ErpTenantConfigurationEntity entity = tenantConfigurationManager.findOne("0", tenantConfig.getDcId(), tenantConfig.getChannel(), tenantConfig.getType());
                final ArrayList<String> strings = Optional.ofNullable(entity)
                        .map(ErpTenantConfigurationEntity::getConfiguration)
                        .map(s -> Lists.newArrayList(on.split(s)))
                        .orElseGet(ArrayList::new);
                if (strings.contains(tenantId)) {
                    continue;
                }
                strings.add(tenantId);
                final String join = strings.stream().filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(";"));
                tenantConfigurationManager.updateConfig("0", tenantConfig.getDcId(), tenantConfig.getChannel(), tenantConfig.getType(), join);
            }
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TenantConfig {
        private String dcId;
        private String channel;
        private String type;
    }

    /**
     * 不能删除,兼容旧的更改集
     */
    public class TenantListConfigProcess implements ConfigProcess {

        private final Splitter on = Splitter.on(";");

        @Override
        public String key() {
            return "TenantList";
        }

        //    需要出入站的type和处理方式
        public List<String> getTenantListType() {
            return ConfigCenter.globalTenantListConfigType;
        }

        /**
         * 更改集使用,有部分全局配置没有按 0,0,ALL 保存,但都使用 0,0 保存
         * 所以可以使用findOne(findOne内部没有使用channel,只用来做日志)
         * <p>
         * 只需要确定有没有的配置
         *
         * @see com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.FormatType#tenantList
         */
        @Override
        public String outBound(String tenantId) {
            final List<String> hasConfig = getTenantListType().stream()
                    .map(type -> tenantConfigurationManager.findGlobal(type))
                    .filter(entity -> Objects.nonNull(entity) && StringUtils.isNotBlank(entity.getConfiguration()) && on.splitToList(entity.getConfiguration()).contains(tenantId))
                    .map(ErpTenantConfigurationEntity::getType)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(hasConfig)) {
                return null;
            }
            return JSON.toJSONString(hasConfig);
        }

        @Override
        public void inbound(String tenantId, String value) {
            final List<String> keys = JSON.parseArray(value, String.class);

            keys.stream()
                    .map(key -> {
                        final ErpTenantConfigurationEntity entity = tenantConfigurationManager.findGlobal(key);
                        if (Objects.nonNull(entity)) {
                            return entity;
                        }

                        final ErpTenantConfigurationEntity configuration = new ErpTenantConfigurationEntity();
                        configuration.setTenantId("0");
                        configuration.setChannel("ALL");
                        configuration.setDataCenterId("0");
                        configuration.setType(key);
                        configuration.setConfiguration("");
                        return configuration;
                    })
                    .map(entity -> Pair.of(Lists.newArrayList(on.split(entity.getConfiguration())), entity))
                    .filter(pair -> !pair.getKey().contains(tenantId))
                    .forEach(pair -> {
                        final List<String> key = pair.getKey();
                        key.add(tenantId);
                        final String join = key.stream().filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.joining(";"));

                        final ErpTenantConfigurationEntity entity = pair.getValue();
                        tenantConfigurationManager.updateConfig(entity.getTenantId(), entity.getDataCenterId(), entity.getChannel(), entity.getType(), join);
                    });
        }

        @Override
        public boolean isOutBound() {
            return false;
        }
    }

    /**
     * 更改集主键,必须要有
     */
    public class TenantIdProcess implements ConfigProcess {

        @Override
        public String key() {
            return "tenant_id";
        }

        @Override
        public String outBound(String tenantId) {
            return tenantId;
        }

        @Override
        public void inbound(String tenantId, String value) {

        }
    }
}
