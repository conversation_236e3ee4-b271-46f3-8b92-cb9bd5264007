package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 12:01 2021/1/19
 * @Desc:
 */
@Data
@ApiModel
public class QuerySyncDataMappingArg implements Serializable {
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("源企业数据ids")
    private List<String> sourceDataId;
    @ApiModelProperty("目标企业数据ids")
    private List<String> destDataId;
}
