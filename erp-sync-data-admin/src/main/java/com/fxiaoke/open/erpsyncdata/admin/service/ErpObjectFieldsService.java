package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.model.K3CSaveType;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.result.DeleteErpObjFieldResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.*;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 13:55 2020/8/19
 * @Desc:
 */
public interface ErpObjectFieldsService {

    Result<List<ErpObjectFieldResult>> queryErpObjectFieldsByObjApiNameAndDcId(String tenantId, int userId, ErpObjectApiNameArg arg,String dataCenterId);
    Result<QueryResult<List<ErpObjectFieldResult>>> pageErpObjectFieldsByObjApiName(String tenantId,String dataCenterId, int userId, QueryErpObjectFieldsArg arg);
    Result<Void> processErpObjectField(ErpObjectFieldResult erpObjectField);
    Result<ErpObjectFieldResult> updateErpObjectField(String tenantId,String dataCenterId,int userId,ErpObjectFieldResult erpObjectFieldResult);
    Result<String> deleteErpObjectFields(String tenantId, int userId, DeleteErpObjectFieldsArg deleteArg);

    /**
     * 批量删除ERP对象字段，同时删除对应的字段扩展表数据
     * @param tenantId
     * @param userId
     * @param idList
     * @return
     */
    Result<List<DeleteErpObjFieldResult>> batchDeleteErpObjectFields(String tenantId, int userId, List<String> idList,String lang);
    //Result<List<ErpObjectFieldResult>> setUpPrimaryKeyField(int tenantId,int userId,ErpObjectFieldResult erpObjectFieldResult);
    Result<ErpObjectFieldResult> updateErpObjectFields(String tenantId,String dataCenterId, int userId, UpsertErpObjectFieldArg arg,String lang);

    /**
     * 按字段顺序批量更新ERP对象字段信息，仅用于编辑对象字段页面，保存ERP对象排序后的字段信息
     * @param tenantId
     * @param dataCenterId
     * @param erpObjectRelationshipResult
     * @return
     */
    Result<Void> updateErpObjectFieldsInOrder(String tenantId,String dataCenterId, ErpObjectRelationshipResult erpObjectRelationshipResult);

    /**
     * 根据真实对象和批次 查询对象信息
     * @param tenantId
     * @param userId
     * @param arg
     * @param dataCenterId
     * @return
     */
    Result<ErpObjectRelationshipResult> queryErpObjectAndFieldsByActualObjAndDcId(String tenantId, int userId, ErpObjectDescResult arg,String dataCenterId,String lang);

    Result<ErpObjectRelationshipResult> queryErpObjectAndFieldsByActualObjAndDcId(String tenantId, String dataCenterId, String erpObjectApiName, Integer splitSeq, String lang);

    Result<String> checkErpObjectFields(String tenantId, int userId, ErpObjectDescResult arg,String dataCenterId,String lang);
    Result<List<ErpFieldItemResult>> queryErpFieldItem(String tenantId, String dataCenterId, ErpFieldItemArg arg);
    Result<ErpObjectFieldResult> queryK3cloudErpField(String tenantId, String dataCenterId, String erpObjectApiName, String erpObjectFieldName,String lang);

    Result<ImportExcelFile.FieldImportResult> batchImportErpObjectFields(ImportExcelFile.FieldImportArg arg,String lang);

    /**
     * 初始化ERP对象扩展表数据，根据当前字段扩展表数据，分别生成不同数据中心的字段扩展表数据
     * @return
     */
    Result<Void> initFieldExtendData();

    /**
     * 初始化ERP对象扩展表数据，根据当前字段扩展表数据，分别生成不同数据中心的字段扩展表数据
     * @param eiList
     * @return
     */
    Result<Void> initFieldExtendData(List<String> eiList);

    /**
     * 初始化ERP对象扩展表数据，根据当前字段扩展表数据，分别生成不同数据中心的字段扩展表数据
     * @param tenantId
     * @param erpObjApiName
     * @return
     */
    Result<Void> initFieldExtendData(String tenantId,String erpObjApiName);

    IdSaveExtend getK3ObjectSaveExtend(String tenantId, String dcId, String objectApiName);

    Result<Void> setK3ObjectAddStatus(String tenantId, String dcId, String objectApiName, K3CSaveType k3CSaveType,String lang);

    Result<Void> setK3ObjectModifyStatus(String tenantId, String dcId, String objectApiName, K3CSaveType k3CSaveType,String lang);

    Result<Map<String, Map<String, String>>> importObjectFiledApiNameData(String tenantId,
                                                             String dataCenterId,
                                                             ErpChannelEnum channel,
                                                             String lang,
                                                             Map<String, String> erpObjectMap,
                                                             List<ErpIntegrationStreamExcelVo> integrationStreamExcelVos);
}
