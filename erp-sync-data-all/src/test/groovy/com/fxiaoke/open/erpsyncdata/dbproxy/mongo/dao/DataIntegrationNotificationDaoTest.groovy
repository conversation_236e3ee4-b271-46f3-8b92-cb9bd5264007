package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class DataIntegrationNotificationDaoTest extends BaseSpockTest {
    @Autowired
    private DataIntegrationNotificationDao dataIntegrationNotificationDao
    @Autowired
    private I18NStringManager i18NStringManager

    def tenantId = "88521"
    def dcId = "643f7322b54ea80001767d86"
    def ployDetailId = "10e860b3be0e4261898c5b7ec8fd7237"

    def userIdList = Lists.newArrayList(1069)

    @Test
    void insert() {
        dataIntegrationNotificationDao.insert(tenantId,
                dcId,
                Lists.newArrayList(ployDetailId),
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId),
                AlarmType.OTHER,
                AlarmLevel.GENERAL,
                "test only",
                userIdList)
        println("ok")
        getDataList()
    }

    @Test
    void getDataList() {
        def dataList = dataIntegrationNotificationDao.getDataListByPage(tenantId,
                dcId,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                20,
                0)
        println(dataList)
    }

    @Test
    void getDataListByDcId() {
        def dataList = dataIntegrationNotificationDao.getDataListByDcId("88521")
        println(dataList)
    }

    @Test
    void deleteMany() {
        def count = dataIntegrationNotificationDao.deleteMany(null)
        assert count > 0
    }

}
