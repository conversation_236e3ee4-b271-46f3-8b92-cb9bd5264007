package com.fxiaoke.open.erpsyncdata.probe.service;

import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ReTrySendMq;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/20 19:37
 * 重试相关记录失败的数据
 * @desc
 */
public interface RetryFailDataService {

    Result<Integer> retryDataService(String tenantId,String retryType,Long startTime,Long endTime,List<Integer> status,Integer limit,Integer offset);

    Result<Void> retryDataServiceHandler();

    Result<Void> retryDataServiceHandlerData(List<ReTrySendMq> reTrySendMqs);

    Result<Integer> retryDataByIds(List<ObjectId> ids);

    Result<List<ReTrySendMq>> queryTryData(String tenantId,String retryType,Long startTime,Long endTime,List<Integer> status,Integer limit,Integer offset);
}
