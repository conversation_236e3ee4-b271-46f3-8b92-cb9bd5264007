package com.fxiaoke.open.erpsyncdata.web.factory;

import com.fxiaoke.open.erpsyncdata.web.service.customFunction.BaseCustomFunctionCommonServiceImpl;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.CustomFunctionCommonService;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.api.FuncApiService;
import com.google.common.collect.Maps;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:15 2021/3/18
 * @Desc:
 */
@Component
public class CustomFunctionFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Autowired
    private Map<String, CustomFunctionCommonService> customFunctionCommonServiceFactory;


    private Map<String, FuncApiService> funcApiServiceMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
        Map<String, ? extends FuncApiService> map = applicationContext.getBeansOfType(FuncApiService.class);
        funcApiServiceMap = map.values().stream().collect(Collectors.toMap(FuncApiService::getApiType, val -> val));
    }

    public CustomFunctionCommonService getHandlerByType(String type) {
        CustomFunctionCommonService customFunctionCommonService = customFunctionCommonServiceFactory.get(type);
        return customFunctionCommonService == null ? new BaseCustomFunctionCommonServiceImpl() : customFunctionCommonService;
    }

    public FuncApiService getFuncApiService(String type) {
        return funcApiServiceMap.get(type);
    }
}
