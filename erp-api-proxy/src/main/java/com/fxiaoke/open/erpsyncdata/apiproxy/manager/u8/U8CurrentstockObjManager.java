package com.fxiaoke.open.erpsyncdata.apiproxy.manager.u8;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.U8ConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service("currentstock")
public class U8CurrentstockObjManager extends U8DefaultMananger {

    @Override
    protected void listErpObjDataByTimeAfter(Result<StandardListData> standardListDataResult,
                                             String tenantId,
                                             String dataCenterId,
                                             String snapshotId) {
        if (standardListDataResult.getData() != null && standardListDataResult.getData().getDataList() != null) {
            for (StandardData standardData : standardListDataResult.getData().getDataList()) {
                ObjectData masterFieldVal = standardData.getMasterFieldVal();
                Object batch = masterFieldVal.get("batch");
                if (batch != null && StringUtils.isNotEmpty((String) batch)) {
                    masterFieldVal.put("currentStockId", masterFieldVal.get("invcode") + "#" + masterFieldVal.get("whcode") + "#" + batch);
                } else {
                    masterFieldVal.put("currentStockId", masterFieldVal.get("invcode") + "#" + masterFieldVal.get("whcode"));
                }
                masterFieldVal.put("currentStockBatchId",masterFieldVal.get("currentStockId"));
                masterFieldVal.put("productBatchId",masterFieldVal.get("currentStockId"));
            }
        }
    }


    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        String connectParam = connectInfo.getConnectParams();
        U8ConnectParam U8ConnectParam = GsonUtil.fromJson(connectParam, U8ConnectParam.class);
        String queryPath = getBatchQueryUrl(erpIdArg.getObjAPIName());
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(erpIdArg.getTenantId(),
                connectInfo.getId(),
                erpIdArg.getObjAPIName(),
                ErpObjInterfaceUrlEnum.queryMasterById.name());

        Map<String, String> params = new HashMap<>();
        String dataId = erpIdArg.getDataId();
        if (dataId.contains("#")) {
            String[] split = dataId.split("#");
            String invcode = split[0];
            String whcode = split[1];
            params.put("invcode_begin", invcode);
            params.put("invcode_end", invcode);
            params.put("whcode_begin", whcode);
            params.put("whcode_end", whcode);
            if (split.length == 3) {
                String batch = split[2];
                params.put("batch", batch);
            }
        } else {
            params.put("id", dataId);
        }
        //发送请求
        Result<StandardData> result = get(U8ConnectParam, queryPath, params, interfaceMonitorData);
        getErpObjDataAfter(result.getData());
        return result;
    }

    @Override
    protected void getErpObjDataAfter(StandardData standardData) {
        ObjectData masterFieldVal = standardData.getMasterFieldVal();
        Object batch = masterFieldVal.get("batch");
        if (batch != null && StringUtils.isNotEmpty((String) batch)) {
            masterFieldVal.put("currentStockId", masterFieldVal.get("invcode") + "#" + masterFieldVal.get("whcode") + "#" + batch);
        } else {
            masterFieldVal.put("currentStockId", masterFieldVal.get("invcode") + "#" + masterFieldVal.get("whcode"));
        }
        masterFieldVal.put("currentStockBatchId",masterFieldVal.get("currentStockId"));
        masterFieldVal.put("productBatchId",masterFieldVal.get("currentStockId"));
    }

    @Override
    protected Result handleGetResponse(String objApiName, String url, String response) {
        JSONObject resultObj = JSONObject.parseObject(response);
        String resultCode = resultObj.getString("errcode");
        if ("0".equals(resultCode)) {
            StandardData standardData = new StandardData();
            standardData.setObjAPIName(objApiName);
            JSONArray jsonArray = resultObj.getJSONArray(objApiName);
            if(jsonArray != null && !url.contains("batch=")){
                jsonArray = jsonArray.stream().filter(jsonObject->StringUtils.isEmpty(((JSONObject)jsonObject).getString("batch"))).collect(Collectors.toCollection(JSONArray::new));
            }
            if(jsonArray != null &&jsonArray.size() > 0){
                standardData = transferU8ToStandardData(objApiName, jsonArray.getJSONObject(0));
            }else{
                log.info("No matched currentStock, objApiName:{}, url:{}, response:{}",objApiName,url,response);
            }
            return new Result(standardData);
        }
        if ("20002".equals(resultCode)) {
            StandardData standardData = new StandardData();
            standardData.setObjAPIName(objApiName);
            return new Result(standardData);
        } else {
            String resultMsg = resultObj.getString("errmsg");
            //请求失败
            log.warn("post failed,url:{},response:{}", url, response);
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
        }
    }

}
