package com.fxiaoke.open.erpsyncdata.common.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import lombok.Getter;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/3.
 */
@Getter
public enum CheckAndUpdatePloyValidErrorTypeEnum implements I18nBase {
    API_NAMES_NOT_EXISTS(1,"对象不存在或者被禁用"),
    FUNCTION_NOT_VALID(2,"[{0}]函数校验失败"),
//    REQUIRE_FIELD_NOT_MAPPING(3,"对象[%s]必填值[%s]未配置数据映射"),
    MAPPING_COMMON_FIELDS_ERROR(4,"数据映射公共字段校验失败,对象[%s]字段[%s]已被作废或者删除"),
    MAPPING_NOT_EXISTS(5,"对象[{0}]->[{1}]字段映射为空"),
//    RELATION_IS_NOT_EXISTS(6,"企业关联关系不存在"),
    SYNC_RULES_NOT_EXISTS(7,"同步规则未配置"),
    SYNC_RULES_VALID(8,"轮询方式获取数据，不支持勾选删除动作"),
//    OBJECT_REFERENCE_NOT_OPEN_PLOY_DETAIL(8,"查找关联类型字段对应的 [%s] -> [%s] 策略没有启用"),
//    OBJECT_REFERENCE_NOT_CLOSE_PLOY_DETAIL(9,"关联该对象的 [%s] -> [%s] 策略未停用"),
//    SYNC_CONDITIONS_DATA_IS_NOT_EXISTS(10,"对象[%s]数据范围不合法"),
    PLOY_BREAK_BY_SYSTEM(11,"策略由于出现异常被系统停用，解决异常后可直接启用策略，异常如下：{0}"),
//    FAILED_SYNC_DATA_MAPPING_NUM_TOO_MUCH(12,"该集成流待处理主对象（[%s] -> [%s]）同步失败数据超过阈值：%s,请处理完成后,再开启集成流"),
    ;
    private final Integer type;
    private final String message;

    /**
     * @deprecated 请使用 {@link #indexedFormat(Object...)}
     */
    @Deprecated
    public String formatMessage(String... objs){
        return String.format(this.message,objs);
    }

    CheckAndUpdatePloyValidErrorTypeEnum(Integer type, String message){
        this.type = type;
        this.message = message;
    }

    @Override
    public String getI18nKey() {
        return I18nUtil.buildKey("CheckAndUpdatePloyValidErrorTypeEnum",name());
    }

    @Override
    public String getI18nValue() {
        return message;
    }
}