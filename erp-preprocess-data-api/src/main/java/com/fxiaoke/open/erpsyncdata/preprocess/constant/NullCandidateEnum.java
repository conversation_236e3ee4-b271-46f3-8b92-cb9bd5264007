package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 */
public enum NullCandidateEnum {
    IS_NULL,
    IS_EMPTY_IF_STR,
    IS_BLANK_IF_STR,
    NOT_EXIST,

    ;

    public static Set<NullCandidateEnum> fromStrList(Collection<String> strList) {
        if (CollUtil.isEmpty(strList)) {
            return new HashSet<>();
        }
        Set<NullCandidateEnum> enumSet = strList.stream().map(v -> EnumUtil.fromStringQuietly(NullCandidateEnum.class, v))
                .filter(e -> e != null).collect(Collectors.toSet());
        return enumSet;
    }
}
