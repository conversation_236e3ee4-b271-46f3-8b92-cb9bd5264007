package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel
public class U8EaiConnectParam extends BaseLangConnectParam {

    private static final long serialVersionUID = 5918765114938725275L;

    /**
     * 基础路径
     */
    @ApiModelProperty("基础路径")
    private String baseUrl;

    @ApiModelProperty("调用方")
    private String sender;

    @ApiModelProperty("提供方")
    private String reciver="u8";

    @ApiModelProperty(value = "授权类型",allowableValues = "1,2",notes = "1：（默认）；2：token授权（暂时不支持）")
    private Integer authType = 1;

    @ApiModelProperty("账号")
    private String userName;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

}
