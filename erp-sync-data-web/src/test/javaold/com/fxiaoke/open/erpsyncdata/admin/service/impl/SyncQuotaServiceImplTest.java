package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.model.SyncQuota;
import com.fxiaoke.open.erpsyncdata.admin.result.CheckQuotaResult;
import com.fxiaoke.open.erpsyncdata.admin.service.SyncQuotaService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/21
 */
@Ignore
public class SyncQuotaServiceImplTest extends BaseTest {
    @Autowired
    private SyncQuotaService syncQuotaService;

    @Test
    public void testCheck() {
        Result<List<CheckQuotaResult>> listResult = syncQuotaService.checkQuotaTask(null,true,true,true,null);
        System.out.println(listResult);
    }

    @Test
    public void testGetQuota() {
//        Result<SyncQuota> result = syncQuotaService.getQuota("83952", false);
//        System.out.println(JacksonUtil.toJson(result));
    }
}
