package com.fxiaoke.open.erpsyncdata.common.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/5/28 15:11
 * 配置中心的配置项，在common-db，commondb又依赖着sync-common。
 * @desc
 */
public class NeedRetryCenterUtils {
    /**
     * 区分需要重试的异常
     */
    public static List<String> needRetryException= Lists.newArrayList();
    static {
        ConfigFactory.getInstance().getConfig("erp-sync-data-all", config -> {
            needRetryException = ImmutableList.copyOf(Splitter.on(";").split(config.get("needRetryException", "com.alibaba.druid.pool.DataSourceNotAvailableException;com.alibaba.druid.pool.GetConnectionTimeoutException")));
        }, true);
    }
}
