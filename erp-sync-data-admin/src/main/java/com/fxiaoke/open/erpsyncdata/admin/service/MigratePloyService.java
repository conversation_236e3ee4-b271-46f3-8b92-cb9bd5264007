package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.MigrateProductCateArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import javax.validation.Valid;

/**
 * 用于自动化迁移策略
 * 如：产品分类 迁移
 * <AUTHOR> (^_−)☆
 * @date 2022/1/12
 */
public interface MigratePloyService {

    /**
     * 迁移产品分类
     * @param migrateProductCateArg
     * @return
     */
    Result<String> migrateProductCate(@Valid MigrateProductCateArg migrateProductCateArg,String lang);
}
