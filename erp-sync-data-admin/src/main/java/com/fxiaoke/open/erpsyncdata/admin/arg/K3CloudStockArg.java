package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Splitter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * K3CLOUD库存查询参数
 * <AUTHOR>
 * @date 2021/08/31
 */

@Data
@ApiModel
@NoArgsConstructor
public class K3CloudStockArg implements Serializable {
    @Data
    public static class StockArg implements Serializable {
        @ApiModelProperty(value = "仓库编码,必填")
        //仓库编码类似：CK009.1||001.03
        private String warehouseNumber;
        @ApiModelProperty(value = "产品编码,必填")
        private String productNumber;
        @ApiModelProperty(value = "产品名称,必填")
        private String productName;
        @ApiModelProperty(value = "批次编码,可为空")
        //批次编码类似：CH566z/zsl2021bbhh/1
        private String batchNumber;
    }
    @ApiModelProperty(value = "企业EI,必填")
    private String tenantId;
    @ApiModelProperty(value = "库存查询参数列表")
    private List<StockArg> stockArgList;

    @ApiModelProperty(value = "仓库编码,仅内部使用")
    //仓库编码类似：CK009.1||001.03
    private List<String> stockNumberList;

    @ApiModelProperty(value = "物料编码,仅内部使用")
    private List<String> materialNumberList;

    @ApiModelProperty(value = "物料名称,仅内部使用")
    private List<String> materialNameList;

    @ApiModelProperty(value = "批次编码,仅内部使用,如果没值，传空值")
    //批次编码类似：CH566z/zsl2021bbhh/1
    private List<String> lotNumberList;

    public void init() {
        stockNumberList = new ArrayList<>();
        materialNumberList = new ArrayList<>();
        materialNameList = new ArrayList<>();
        lotNumberList = new ArrayList<>();

        for(StockArg arg : stockArgList) {
            String productNumber = arg.getProductNumber();
            if(StringUtils.isEmpty(productNumber)) {
                if(StringUtils.isEmpty(arg.getProductName())) continue;
                if(StringUtils.containsNone(arg.getProductName(),"#")) continue;

                List<String> names = Splitter.on("#").splitToList(arg.getProductName());
                productNumber = names.get(0);
            }
            materialNumberList.add(productNumber);
            materialNameList.add(arg.getProductName());

            stockNumberList.add(arg.getWarehouseNumber());

            String batchNumber = arg.getBatchNumber();
            if(StringUtils.isEmpty(batchNumber)) {
                batchNumber="";
            }
            lotNumberList.add(batchNumber);
        }
    }

    public String getStockNumbers() {
        return stockList2String(stockNumberList);
    }

    public String getMaterialNumbers() {
        return list2String(materialNumberList);
    }

    public String getLotNumbers() {
        return lotList2String(lotNumberList);
    }

    private String list2String(List<String> list) {
        if(list ==null || list.isEmpty()) return null;
        StringBuilder sb = new StringBuilder();
        for(String item : list) {
            sb.append(item);
            sb.append(",");
        }
        sb.deleteCharAt(sb.toString().length()-1);
        return sb.toString();
    }

    private String lotList2String(List<String> list) {
        if(list ==null || list.isEmpty()) return null;
        long count = list.stream().filter(item->StringUtils.isNotEmpty(item)).count();
        if(count<1) return "";

        StringBuilder sb = new StringBuilder();
        for(String item : list) {
            String lotNumber = item;
            List<String> lotItemList = Splitter.on("/").splitToList(lotNumber);
            if(lotItemList.size()>1) {
                lotNumber = lotItemList.get(1);
            }
            sb.append(lotNumber);
            sb.append(",");
        }
        sb.deleteCharAt(sb.toString().length()-1);
        return sb.toString();
    }

    private String stockList2String(List<String> list) {
        if(list ==null || list.isEmpty()) return null;
        StringBuilder sb = new StringBuilder();
        for(String item : list) {
            String stockNumber = item;
            List<String> stockItemList = Splitter.on("||").splitToList(stockNumber);
            if(stockItemList.size()>1) {
                stockNumber = stockItemList.get(0);
            }
            sb.append(stockNumber);
            sb.append(",");
        }
        sb.deleteCharAt(sb.toString().length()-1);
        return sb.toString();
    }

    public static String getLotNumber(String lotNumber) {
        List<String> lotItemList = Splitter.on("/").splitToList(lotNumber);
        if(lotItemList.size()>1) {
            lotNumber = lotItemList.get(1);
        }
        return lotNumber;
    }

    public static void main(String[] args) {
        K3CloudStockArg arg = new K3CloudStockArg();
        arg.setTenantId("81243");

        List<K3CloudStockArg.StockArg> stockArgList = new ArrayList<>();
        for(int i=0;i<3;i++) {
            K3CloudStockArg.StockArg stockArg = new K3CloudStockArg.StockArg();
            stockArg.setWarehouseNumber("warehouse"+(i+1));
            stockArg.setProductNumber("product"+(i+1));
            stockArg.setBatchNumber("batch"+(i+1));
            stockArgList.add(stockArg);
        }

        arg.setStockArgList(stockArgList);
        arg.init();
        System.out.println(arg);
    }
}
