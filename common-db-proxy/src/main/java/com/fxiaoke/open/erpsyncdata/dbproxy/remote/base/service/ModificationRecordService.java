package com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.service;

import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.base.arg.AddRecordLog;
import com.fxiaoke.retrofit2.http.Body;
import com.fxiaoke.retrofit2.http.HeaderMap;
import com.fxiaoke.retrofit2.http.POST;
import com.fxiaoke.retrofit2.http.Path;
import com.fxiaoke.retrofitspring.annotation.RetrofitConfig;

/**
 * <AUTHOR>
 * @date 2023/10/23 11:45:21
 */
@RetrofitConfig(baseUrl = "PaasMetadata", desc = "添加crm 修改日志")
public interface ModificationRecordService {
    @POST("/API/v1/rest/object/{apiName}/action/RecordLog")
    Result<Void> addLog(@HeaderMap HeaderObj headers, @Path("apiName") String apiName, @Body AddRecordLog arg);
}
