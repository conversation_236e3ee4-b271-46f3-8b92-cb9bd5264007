package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 15:54 2021/9/1
 * @Desc:
 */
@AllArgsConstructor
@Getter
public enum ErpHistoryDataTaskTypeEnum {
    TYPE_TIME(1, TenantType.ERP, "按照时间", I18NStringEnum.s889.getI18nKey()),
    TYPE_IDS(2, TenantType.ERP, "按照ids", I18NStringEnum.s890.getI18nKey()),//手工输入的ids
    TYPE_K3C_FILTER_STRING(3, TenantType.ERP, "按照k3自定义条件", I18NStringEnum.s891.getI18nKey()),
    TYPE_CRM_FILTER(4, TenantType.CRM, "按照crm数据范围", I18NStringEnum.s3619.getI18nKey()),
    TYPE_ERP_IDS_BY_CRM_FILTER(5, TenantType.ERP, "erp->crm 按照crm数据范围", I18NStringEnum.s3619.getI18nKey()),
    TYPE_TIME_INVALID(6, TenantType.ERP, "按照时间查询作废", I18NStringEnum.kQueryInvalidByTime.getI18nKey()),
    TYPE_FILE_IDS(7, TenantType.ERP, "按照ids", I18NStringEnum.s890.getI18nKey()),//文件导入的ids,给前端的都是2，但是增加了idsInputType区分
    ;

    private final Integer status;
    private final Integer sourceType;
    private final String desc;
    private final String i18nKey;


    private static Map<Integer, ErpHistoryDataTaskTypeEnum> statusMap = Arrays.stream(ErpHistoryDataTaskTypeEnum.values()).collect(Collectors.toMap(ErpHistoryDataTaskTypeEnum::getStatus, Function.identity()));

    public static ErpHistoryDataTaskTypeEnum valueOf(Integer status) {
        return statusMap.get(status);
    }
}
