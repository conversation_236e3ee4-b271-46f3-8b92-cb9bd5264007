package com.fxiaoke.open.erpsyncdata.common.constant;

public enum SyncPloyTypeEnum {
    INPUT(1, "ERP往CRM"),
    OUTPUT(2, "CRM往ERP")
    ;
    private Integer type;
    private String description;

    SyncPloyTypeEnum(int type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static SyncPloyTypeEnum getByType(Integer type) {
        for (SyncPloyTypeEnum syncPloyTypeEnum : values()) {
            if (syncPloyTypeEnum.getType().equals(type)) {
                return syncPloyTypeEnum;
            }
        }
        return null;
    }
}
