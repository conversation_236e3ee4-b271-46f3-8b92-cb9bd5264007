package com.fxiaoke.open.oasyncdata.model;

import lombok.Data;

import java.io.Serializable;

/**
 * oa对接Api
 *
 * <AUTHOR>
 * @date 2021/3/9
 */
@Data
public class OASyncApiVO implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    private String id;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 对象apiName
     *
     */
    private String objApiName;

    /**
     * 描述
     */
    private String description = "";

    /**
     * 事件类型
     */
    private String eventType = "";

    /**
     * 数据模板
     */
    private String dataTemplate = "";

    /**
     * 开启状态（1。开启 2.关闭）
     */
    private String status;

    /**
     * url
     */
    private String url = "";


    /**
     * 请求模式
     */
    private String requestMode = "";


    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;
}