package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.ViewArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

public interface SpecialBusiness {


    /**
     * 创建erp对象前置动作
     *
     * @param standardData
     * @param apiClient
     * @param saveArg
     * @param saveExtend
     * @return
     */
    default Result<String> beforeRunCreateErpObjData(StandardData standardData, K3CloudApiClient apiClient, SaveArg saveArg, IdSaveExtend saveExtend) {
        return Result.newSuccess();
    }

    default void afterRunCreateErpObjData(ErpIdResult erpIdResult,StandardData standardData,SaveArg saveArg,K3CloudApiClient apiClient) {

    }

    /**
     * 查看erp数据前置动作
     *
     * @param viewArg
     * @param erpIdArg
     * @return
     */
    default void beforeRunView(ViewArg viewArg, ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
    }

    /**
     * 查看erp数据后置动作
     * 一些单据增加虚拟字段
     *
     * @param erpIdArg
     * @param standardData
     * @param apiClient
     */
    default void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
    }

    /**
     * 去重
     */
    default void distinctByNumber(List<List<Object>> list) {
    }


    /**
     * 查看对象数据的前置动作。
     */
    default void beforeRunBillQuery(QueryArg queryArg, TimeFilterArg timeFilterArg, K3CloudApiClient k3CloudApiClient) {
    }

    /**
     * 获取列表数据的后动作。
     */
    default void afterRunListData(TimeFilterArg timeFilterArg, List<StandardData> standardDataList,
                                  K3CloudApiClient k3CloudApiClient) {
    }

    /**
     * 更新erp数据前置动作
     *  @param saveArg
     * @param standardData
     * @param apiClient
     * @param saveExtend
     */
    default void beforeRunUpdate(SaveArg saveArg, StandardData standardData, K3DataConverter k3DataConverter, K3CloudApiClient apiClient,IdSaveExtend saveExtend) {
    }

    /**
     * 独立获取数据接口
     * 目前仅库存需要使用
     *
     * @param key
     * @return
     */
    default boolean needSpecialGetAndQuery(String key) {
        return false;
    }

    /**
     * 独立获取数据接口
     *
     * @param erpIdArg
     * @param apiClient
     * @return
     */
    default Result<StandardData> specialGetErpObjData(ErpIdArg erpIdArg, K3CloudApiClient apiClient) {
        return Result.newError(ResultCodeEnum.UNIMPLEMENTED_METHOD);
    }

    /**
     * 独立获取列表数据接口
     *
     * @param timeFilterArg
     * @param apiClient
     * @return
     */
    default Result<StandardListData> specialListErpObjData(TimeFilterArg timeFilterArg, K3CloudApiClient apiClient) {
        return Result.newError(ResultCodeEnum.UNIMPLEMENTED_METHOD);
    }
    default void afterRunGetReSyncObjDataById(ErpIdArg erpIdArg, List<StandardData> allData) {
    }
    /**
     * 通过ExecuteBillQuery接口获取单条数据前逻辑
     */
    default void beforeGetDataByBillQuery(String tenantId, QueryArg queryArg, K3CloudApiClient k3CloudApiClient) {
    }
}
