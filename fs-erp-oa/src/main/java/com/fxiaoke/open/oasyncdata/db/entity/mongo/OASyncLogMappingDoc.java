package com.fxiaoke.open.oasyncdata.db.entity.mongo;


import lombok.*;
import lombok.experimental.Accessors;
import org.bson.Document;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;

/**
 * oa对接日志
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class OASyncLogMappingDoc  implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    @BsonId
    private ObjectId id;

    /**
     * 企业id
     */

    private String tenantId;

    /**
     * 数据id
     */
    private String dataId;

    /**
     * 数据id
     */
    private String dataName;

    /**
     * 业务对象名
     */
    private String objectName;


    /**
     * 状态
     * @see com.fxiaoke.open.oasyncdata.constant.OASyncLogEnum
     */
    private String status;


    /**
     * 错误信息
     */
    private String message;

    /**
     * 对象信息
     */
    private String objApiName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 标题
     */
    private String title;

    /**
     * 接收人
     */
    private String receiverId;

    /**
     * 业务对象id的数据
     */
    private String businessDataId;

    /**
     * 数据中心id
     */
    private String dataCenterId;


    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务类型
     */
    private String messageType;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * lastSyncObjectId 对应snapshot的数据
     */
    private ObjectId lastSyncLogId;
}