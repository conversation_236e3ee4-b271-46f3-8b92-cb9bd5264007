package com.fxiaoke.open.erpsyncdata.web.controller.devtool;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.jdy.JDYDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpListArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController("devToolsController")
@RequestMapping("erp/syncdata/devtool")
public class DevToolController extends BaseController {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private JDYDataManager jdyDataManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoManager;

    final String domain = "https://api.kingdee.com";

    @GetMapping({"/"})
    public ModelAndView index() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/erp/syncdata/devtool/index.jsp");
        UserVo userVo = getUserVo();
        String ea = userVo.getEnterpriseAccount();
        Integer employeeId = userVo.getEmployeeId();
        String name = getName();
        modelAndView.addObject("cdnBaseUrl", ConfigCenter.CDN_BASE_URL);
        modelAndView.addObject("userName", String.format("%s(%s.%s)", name, ea, employeeId));
        return modelAndView;
    }

    @RequestMapping(value = "/listDcInfos", method = RequestMethod.GET)
    public Result<List<ErpConnectInfoEntity>> listDcInfos() {
        String tenantId = getLoginUserTenantId();
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.listErpDcByTenantId(tenantId);
        List<ErpConnectInfoEntity> infoEntities = erpConnectInfoEntities.stream()
                .filter(info -> ErpChannelEnum.ERP_JDY.equals(info.getChannel()))
                .collect(Collectors.toList());
        return Result.newSuccess(infoEntities);
    }

    @GetMapping(value = "/listFields")
    public Result<Amis.Crud<Dict>> listFieldsByUrl(@RequestParam(required = false) Map<String,String> requestParam) {
        String dcId = requestParam.get("dcId");
        if (StrUtil.isBlank(dcId)) {
            return Result.newSystemError(I18NStringEnum.s3675);
        }
        String url = requestParam.get("url");
        String processedUrl;
        if (StrUtil.isNotBlank(url) && url.indexOf(domain) == 0) {
            processedUrl = url.substring(domain.length());
        } else {
            return Result.newSystemError(I18NStringEnum.s3676);
        }
        int perPage = Convert.toInt(requestParam.get("perPage"), 50);
        int page = Convert.toInt(requestParam.get("page"), 1);
        String fieldNames = requestParam.get("fieldNames");
        Set<String> fields = new HashSet<String>();
        fields.add("id");
        if (StrUtil.isNotBlank(fieldNames)) {
            fields.addAll(StrUtil.split(fieldNames, ",")
                                  .stream()
                                  .map(String::trim)
                                  .collect(Collectors.toSet()));
        } else {
            // 默认字段是number
            fields.add("number");
        }
        ErpListArg arg = new ErpListArg(processedUrl, new ArrayList<>(fields), page, perPage);
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(getLoginUserTenantId(), dcId);
        if (connectInfo == null) {
            return Result.newSystemError(I18NStringEnum.s3706, dcId);
        }

        Result<StandardListData> result = jdyDataManager.listErpObjFieldsByUrl(arg, connectInfo);
        if (!result.isSuccess()) {
            log.info("调用jdyDataManager出错：" + result);
            return Result.newSystemError(I18NStringEnum.s3707, result.getErrMsg());
        }
        //构建columns
        StandardListData resultData = result.getData();
        List<Dict> dicts = resultData.getDataList()
                .stream()
                .map(objData -> {
                    ObjectData data = objData.getMasterFieldVal();
                    Dict dict = new Dict();
                    fields.forEach(field -> dict.put(field, data.get(field)));
                    return dict;
                })
                .collect(Collectors.toList());
        Amis.ColHelper<Dict> colHelper = Amis.ColHelper.parseMap(dicts);
        Amis.Crud<Dict> crud = colHelper.getCrud();
        crud.setTotal(resultData.getTotalNum());
        return Result.newSuccess(crud);
    }

}
