package com.fxiaoke.open.erpsyncdata.preprocess.result.base;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 异常断言,抛出业务异常
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/12/2
 */
public class AssertUtil {
    /**
     * 支持null,CharSequence,Array,Collection,Map
     * @param object
     * @param msg
     * @param <T>
     * @return
     */
    public static <T> T notEmpty(T object, String msg, String i18nKey, String tenantId) {
        if (ObjectUtils.isEmpty(object)) {
            throw new ErpSyncDataException(msg,i18nKey,tenantId);
        }
        return object;
    }

    public static <T> T notEmpty(T object, I18NStringEnum i18NStringEnum, String tenantId) {
        return notEmpty(object, i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), tenantId);
    }

    public static <T> T notEmpty(T object, String tenantId) {
        if (ObjectUtils.isEmpty(object)) {
            throw new ErpSyncDataException(I18NStringEnum.s233,tenantId);
        }
        return object;
    }

    public static <T> T notNull(T object, I18NStringEnum i18NStringEnum, String tenantId) {
        return notNull(object, i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), tenantId);
    }

    public static <T> T notNull(T object, String msg, String i18nKey, String tenantId) {
        if (object == null) {
            throw new ErpSyncDataException(msg,i18nKey,tenantId);
        }
        return object;
    }


    public static void allNotNull(Object... objects) {
        for (Object object : objects) {
            if (object == null) {
                throw new ErpSyncDataException("arg can not be null",null,null);
            }
        }
    }

    public static <T> T in(T object, Object... cols) {
        for (Object col : cols) {
            if (object.equals(col)){
                return object;
            }
        }
        throw new ErpSyncDataException("object not in valid list",null,null);
    }

    public static void isTrue(boolean b, I18NStringEnum i18NStringEnum, String tenantId){
        isTrue(b, i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), tenantId);
    }

    public static void isTrue(boolean b,String msg, String i18nKey, String tenantId){
        if (!b){
            throw new ErpSyncDataException(msg,i18nKey,tenantId);
        }
    }
}
