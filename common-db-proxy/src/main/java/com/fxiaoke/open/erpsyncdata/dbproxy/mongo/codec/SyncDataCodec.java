package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.codec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.MapListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.mongodb.MongoClient;
import com.mongodb.client.model.Updates;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonReader;
import org.bson.BsonString;
import org.bson.BsonValue;
import org.bson.BsonWriter;
import org.bson.Document;
import org.bson.codecs.Codec;
import org.bson.codecs.CollectibleCodec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.EncoderContext;
import org.bson.types.ObjectId;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/27
 */
@Slf4j
public class SyncDataCodec implements CollectibleCodec<SyncDataEntity> {
    private final Codec<Document> documentCodec;

    /**
     * Default constructor.
     */
    public SyncDataCodec() {
        this.documentCodec = MongoClient.getDefaultCodecRegistry().get(Document.class);
    }


    private SyncDataEntity convert(Document document) {
        if (document == null) {
            return null;
        }
        SyncDataEntity syncData = BeanUtil.toBean(document, SyncDataEntity.class,
                CopyOptions.create().ignoreError().setIgnoreProperties("_id", "sourceData", "destData", "sourceDetailSyncDataIds", "createTime", "updateTime"));
        syncData.convertAndSetId(document.getObjectId("_id"));
        syncData.setSourceData(convert2ObjectData(document.get("sourceData")));
        syncData.setDestData(convert2ObjectData(document.get("destData")));
        syncData.setCreateTime(safeGetTime(document, "createTime"));
        syncData.setUpdateTime(safeGetTime(document, "updateTime"));
        syncData.setSourceDetailSyncDataIds(convert2MapListStringData(document.get("sourceDetailSyncDataIds")));
        return syncData;
    }

    private Long safeGetTime(Document document, String key) {
        try {
            Date date = document.getDate(key);
            if (date != null) {
                return date.getTime();
            }
        } catch (Exception e) {
            log.warn("syncData codec exist error date,doc:{}", document);
        }
        return null;
    }

    private ObjectData convert2ObjectData(Object doc) {
        if (doc instanceof String) {
            String str = (String) doc;
            if (StringUtils.isBlank(str)){
                return null;
            }
            try {
                ObjectData objectData = JacksonUtil.fromJson(str,ObjectData.class);
                return objectData;
            }catch (Exception e){
                log.error("decode Object Data failed,",e);
            }
        }
        return null;
    }

    private MapListStringData convert2MapListStringData(Object doc) {
        if (doc instanceof String) {
            String str = (String) doc;
            if (StringUtils.isBlank(str)){
                return null;
            }
            try {
                MapListStringData objectData = JacksonUtil.fromJson(str,MapListStringData.class);
                return objectData;
            }catch (Exception e){
                log.error("decode Object Data failed,",e);
            }
        }
        return null;
    }

    @Override
    public SyncDataEntity decode(BsonReader reader, DecoderContext decoderContext) {
        Document document = documentCodec.decode(reader, decoderContext);
        SyncDataEntity syncData = convert(document);
        return syncData;
    }

    @Override
    public void encode(BsonWriter writer, SyncDataEntity value, EncoderContext encoderContext) {
        Map<String, Object> map = BeanUtil.beanToMap(value);
        map.remove("id");
        //这两个字段在语句使用currentDate函数生成
        map.remove("createTime");
        map.remove("updateTime");
        map.put("_id", new ObjectId(value.getId()));
        Document document = new Document(map);
        if (value.getCreateTime()!=null){
            document.put("createTime",new Date(value.getCreateTime()));
        }
        document.put("updateTime",new Date());
        Object sourceData = document.remove("sourceData");
        Object destData = document.remove("destData");
        Object sourceDetailSyncDataIds = document.remove("sourceDetailSyncDataIds");
        document.put("sourceData", JacksonUtil.toJson(sourceData));
        document.put("destData", JacksonUtil.customToJson(destData));
        document.put("sourceDetailSyncDataIds", JacksonUtil.toJson(sourceDetailSyncDataIds));
        documentCodec.encode(writer, document, encoderContext);
    }

    @Override
    public Class<SyncDataEntity> getEncoderClass() {
        return SyncDataEntity.class;
    }

    @Override
    public SyncDataEntity generateIdIfAbsentFromDocument(SyncDataEntity document) {
        if (!documentHasId(document)) {
            document.convertAndSetId(new ObjectId());
        }
        return document;
    }

    @Override
    public boolean documentHasId(SyncDataEntity document) {
        return document.getId() != null;
    }

    @Override
    public BsonValue getDocumentId(SyncDataEntity document) {
        if (!documentHasId(document)) {
            throw new IllegalStateException("The document does not contain an _id");
        }
        return new BsonString(document.getId());
    }
}
