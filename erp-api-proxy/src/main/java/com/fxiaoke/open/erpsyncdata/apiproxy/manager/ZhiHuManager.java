package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.common.Pair;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.Connector;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.HeaderManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HeaderScriptUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HeaderFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ZhiHuConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类描述：知乎连接器
 *
 * @author: zhangshf9284
 * @date: 2022/5/617:24
 */
@Slf4j
@Connector(handlerType = ConnectorHandlerType.INNER, channel = ErpChannelEnum.YXT_MARKETING_ZHIHU)
public class ZhiHuManager extends BaseErpDataManager {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private SpecialWayDataService specialWayDataService;
    @Autowired
    private HeaderManager headerManager;
    /**
     * 构建参数，发送请求，并保存日志，解析返回结果转换为标准格式
     *
     * @param connectParam         连接参数
     * @param servicePath          服务地址
     * @param interfaceMonitorData 接口日志
     * @return 标准结果
     */
    @LogLevel()
    private Result<StandardListData> get(String tenantId,String dataCenterId,ZhiHuConnectParam connectParam,
                                         String servicePath,
                                         InterfaceMonitorData interfaceMonitorData,
                                         Long rspReadLimitLenByte) {
        String url = connectParam.getBaseUrl() + "?" + servicePath;
        Map<String, String> headerMap= Maps.newHashMap();
        if(ObjectUtils.isNotEmpty(connectParam.getHeaderFunctionName())){
            //前端header函数
            HeaderFunctionArg headerFunctionArg= HeaderFunctionArg.builder().tenantId(tenantId).dataCenterId(dataCenterId).functionName(connectParam.getHeaderFunctionName()).interfaceUrl(ErpObjInterfaceUrlEnum.valueOf(interfaceMonitorData.getType()))
                    .params(null).build();
            Result<Map<String, String>> headerMapByFunction = headerManager.getHeaderMapByFunction(headerFunctionArg);
            if(!headerMapByFunction.isSuccess()){
                saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(), interfaceMonitorData.getObjApiName(),interfaceMonitorData.getType(),new ProxyRequest(url, headerMap, null), headerMapByFunction.getErrMsg(),2,System.currentTimeMillis(), System.currentTimeMillis(),
                        "", TraceUtil.get(),0L, null);
                throw new ErpSyncDataException(I18NStringEnum.s153,tenantId);
            }
            headerMap=headerMapByFunction.getData();
        }else{
            headerMap = HeaderScriptUtil.getHeaderMap(tenantId,connectParam.getHeaderScript(), url);
        }
        HttpRspLimitLenUtil.ResponseBodyModel response = null;
        StopWatch stopWatch = new StopWatch();
        Long callTime = System.currentTimeMillis();
        stopWatch.start();
        String rspStr = "";
        Integer status = 1;
        try {
            response = proxyHttpClient.getUrl(url, headerMap,rspReadLimitLenByte);
            if(response.isReachLengthLimit()) {
                log.error("erp data length is too large,get CONTENT_LENGTH_LIMIT_ERROR, ei:{}",tenantId);
                rspStr = String.format(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR.getErrMsg(), "");
                return new Result<>(ResultCodeEnum.CONTENT_LENGTH_LIMIT_ERROR);
            }
            rspStr = response.getBody();
            ZhiHuConnectParam.ResultFormat resultObj =
              JSONObject.parseObject(response.getBody(), ZhiHuConnectParam.ResultFormat.class);
            connectParam.setResultFormat(resultObj);
            String resultCode = resultObj.getCode();
            String resultMsg = resultObj.getMsg();
            if (StringUtils.equalsIgnoreCase(resultCode, resultObj.getSuccessCode())) {
                //调用接口成功
                String dataJson = resultObj.getData();
                List<ObjectData> objectDataList = JSONObject.parseArray(dataJson, ObjectData.class);
               // List<String> list = Arrays.asList(resultObj.getData());
                List<StandardData> standardListDataArrayList = new ArrayList<>();
                Result<StandardListData> result = new Result<>();
                objectDataList.forEach(l -> {
//                    StandardData standardData = JSONObject.parseObject(l, StandardData.class);
//                    if (standardData != null) {
//                        standardListDataArrayList.add(standardData);
//                    }
                    StandardData data = new StandardData();
                    l.putId(l.getString("phone"));
                    data.setMasterFieldVal(l);
                    standardListDataArrayList.add(data);
                });
                StandardListData data = new StandardListData();
                data.setDataList(standardListDataArrayList);
                result.setErrMsg(resultMsg);
                result.setData(data);
                return result;
            } else {
                status = 2;
                //请求失败
                log.warn("get failed,url:{},headerMap:{},response:{}", url, headerMap, response);
                return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, resultCode + "," + resultMsg);
            }
        } catch (Exception e) {
            status = 2;
            log.error("get error,connectParam:{},url:{},headerMap:{}", connectParam, url, headerMap, e);
            rspStr = e + " " + rspStr;
            if (e.getCause() instanceof SocketTimeoutException) {//超时
                return new Result<>(ResultCodeEnum.SOCKETTIMEOUT, e.getMessage());
            }
            return new Result<>(ResultCodeEnum.CALL_OUT_HTTP_FAILED, e.getMessage());
        } finally {
            stopWatch.stop();
            saveErpInterfaceMonitor(interfaceMonitorData.getTenantId(), interfaceMonitorData.getDcId(), interfaceMonitorData
              .getObjApiName(), interfaceMonitorData.getType(), new ProxyRequest(url, headerMap, null), rspStr, status, callTime, System
              .currentTimeMillis(), "", TraceUtil.get(), stopWatch.getTotalTimeMillis(), interfaceMonitorData.getTimeFilterArg());
        }
    }


    @Override
    public Result<StandardData> getErpObjData(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<List<StandardData>> getReSyncObjDataById(ErpIdArg erpIdArg, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<StandardListData> listErpObjDataByTime(TimeFilterArg timeFilterArg,
                                                         ErpConnectInfoEntity connectInfo) {
        ErpObjInterfaceUrlEnum type = ErpObjInterfaceUrlEnum.queryMasterBatch;
        syncLogManager.initLogId(timeFilterArg.getTenantId(),timeFilterArg.getObjAPIName());
        if (timeFilterArg.getOperationType()== EventTypeEnum.INVALID.getType()){
            type=ErpObjInterfaceUrlEnum.queryInvalid;
            return Result.newSuccess(new StandardListData());
        }
        if (timeFilterArg.getOffset()>=timeFilterArg.getLimit()){
            //接口没有分页所以第二次查询就直接返回
            return Result.newSuccess(new StandardListData());
        }

        if (timeFilterArg.getEndTime()-timeFilterArg.getStartTime()>1000*60*60*24*10){
            return Result.newSystemError(I18NStringEnum.s43);
        }

        String connectParam = connectInfo.getConnectParams();
        ZhiHuConnectParam zhihuConnectParam = ConnectParamUtil.parseZhiHu(timeFilterArg.getTenantId(),connectParam);
        //String viewPath = zhihuConnectParam.getServicePath().getView();
        //构建接口日志
        InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitor(timeFilterArg.getTenantId(),
                connectInfo.getId(),
                timeFilterArg.getObjAPIName(),
                type.name());
        //构建param
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        long signStamp = System.currentTimeMillis() / 1000;
        //查询15天内的线索
        String startTime = formatter.format(signStamp * 1000 - 1000 * 60 * 60 * 24 * 15);
        String endTime = formatter.format(signStamp * 1000);
        String string = zhihuConnectParam.getUserId() + zhihuConnectParam.getToken() + signStamp + "";
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        byte[] bytes = md5.digest(string.getBytes(StandardCharsets.UTF_8));
        StringBuilder builder = new StringBuilder();

        for (byte aByte : bytes) {
            builder.append(Integer.toHexString((0x000000FF & aByte) | 0xFFFFFF00).substring(6));
        }

        List<Pair<String, Object>> pairs =
          ImmutableList.of(Pair.build("from", startTime), Pair.build("to", endTime), Pair.build("userId", zhihuConnectParam
            .getUserId()), Pair.build("signStamp", signStamp + ""), Pair.build("signature", builder.toString()));
        String viewPath = pairs.stream().map(v -> v.first + "=" + v.second).collect(Collectors.joining("&"));
        //发送请求
        Result<StandardListData> result = get(timeFilterArg.getTenantId(), connectInfo.getId(),zhihuConnectParam,
                viewPath, interfaceMonitorData, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        if (result.isSuccess()) {
            result.getData().getDataList().forEach(r -> {
                r.setObjAPIName(timeFilterArg.getObjAPIName());
            });
        }
        LogIdUtil.clear();
        return result;
    }

    @Override
    public Result<ErpIdResult> createErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }


    /**
     * 新建erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> createErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjData(StandardInvalidData standardInvalidData, ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, true);
    }
    @Override
    public Result<String> deleteErpObjData(StandardInvalidData standardDeleteData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<String> invalidErpObjDetailData(StandardInvalidData standardInvalidData,
                                                  ErpConnectInfoEntity connectInfo) {
        return comInvalid(standardInvalidData, connectInfo, false);
    }

    @Override
    public Result<String> recoverErpObjData(StandardRecoverData standardRecoverData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    private Result<String> comInvalid(StandardInvalidData standardInvalidData,
                                      ErpConnectInfoEntity connectInfo,
                                      boolean isMaster) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    @Override
    public Result<ErpIdResult> updateErpObjData(StandardData standardData, ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }

    /**
     * 更新erp对象明细数据
     *
     * @param doWriteMqData
     * @param connectInfo
     * @return
     */
    @Override
    public Result<StandardDetailId> updateErpObjDetailData(SyncDataContextEvent doWriteMqData,
                                                           StandardDetailData standardDetailData,
                                                           ErpConnectInfoEntity connectInfo) {
        return new Result<>(ResultCodeEnum.UNSUPPORTED_MANAGER);
    }
}
