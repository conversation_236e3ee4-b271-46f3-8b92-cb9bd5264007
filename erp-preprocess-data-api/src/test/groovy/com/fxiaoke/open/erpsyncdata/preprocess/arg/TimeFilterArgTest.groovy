package com.fxiaoke.open.erpsyncdata.preprocess.arg

import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class TimeFilterArgTest extends Specification {

    def "clone arg not modify the original obj"() {
        given:
        def arg1 = new TimeFilterArg()
        arg1.setTenantId("1")
        def arg2 = arg1.clone()
        arg2.setTenantId("2")
        expect:
        arg1.getTenantId() == "1"
        arg2.getTenantId() == "2"
    }

    def "clone QueryTempTimeFilterArg"() {
        given:
        def arg1 = new QueryTempTimeFilterArg()
        arg1.setTenantId("1")
        def arg2 = arg1.clone()
        arg2.setTenantId("2")
        expect:
        arg1.getTenantId() == "1"
        arg2.getTenantId() == "2"
        arg1 instanceof QueryTempTimeFilterArg
        arg2 instanceof QueryTempTimeFilterArg
    }
}
