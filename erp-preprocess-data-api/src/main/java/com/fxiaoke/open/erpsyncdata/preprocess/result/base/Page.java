package com.fxiaoke.open.erpsyncdata.preprocess.result.base;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/14
 */
@Setter
@Getter
@ToString
public class Page<E>{
    private List<E> data;
    /**
     * 总数，可能为概数
     */
    private long totalNum;
    /**
     * 是否存在下一页
     */
    private boolean hasNext;
    /**
     * 查询唯一标识
     */
    private String queryId;
}
