package com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/3/29 16:37:10
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ErpInterfaceMonitor {
    /**
     * spel
     */
    String objApiName();
    ErpObjInterfaceUrlEnum type();
}
