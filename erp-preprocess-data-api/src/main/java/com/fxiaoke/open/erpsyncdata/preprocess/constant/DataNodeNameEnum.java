package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 20:28 2023/1/9
 * @Desc:
 */
@Getter
public enum DataNodeNameEnum implements I18nBase {
    //erp->crm
    EnterTempData("EnterTempData", "数据进入临时库", I18NStringEnum.s1200.getI18nKey()),
    OutTempData("OutTempData", "数据出临时库", I18NStringEnum.s1201.getI18nKey()),

    //crm->erp
    ConsumeCrmMq("ConsumeCrmMq", "消费paas的mq节点", I18NStringEnum.s1202.getI18nKey()),
    DataReWriteSource("DataReWriteSource", "回写crm节点", I18NStringEnum.s1203.getI18nKey(),false,140,"reverseWrite2Crm"),

    //common

    //触发节点
    DataTriggerProcess("DataTriggerProcess", "数据触发(分发)", I18NStringEnum.s1204.getI18nKey(), true,60,"streamBegin"),
    DataBeforeFunc("DataBeforeFunc", "同步前函数", I18NStringEnum.s844.getI18nKey(), true, 60, "completeEventTrigger"),
    @Deprecated
    CheckSyncDataMapping("CheckSyncDataMapping", "校验中间表", I18NStringEnum.s1205.getI18nKey()),
    DataFieldMapping("DataFieldMapping", "字段转换", I18NStringEnum.s1206.getI18nKey(), true,80,"doProcess"),
    QueryCrmObject2DestNode("QueryCrmObject2DestNode", "查询crm赋值给目标", I18NStringEnum.s1207.getI18nKey(), true,80,null),
    DataDuringFunc("DataDuringFunc", "同步中函数", I18NStringEnum.s846.getI18nKey(), true,100,"completeProcess"),
    DataWriteDest("DataWriteDest", "写目标系统", I18NStringEnum.s1208.getI18nKey(), true,120,"doWrite"),
    DataIdMapping("DataIdMapping", "更新映射", I18NStringEnum.s1209.getI18nKey(),false,160,null),
    DataAfterFunc("DataAfterFunc", "同步后函数", I18NStringEnum.s848.getI18nKey(), true,180,"completeDataWrite"),

    SyncStepException("SyncStepException", "SyncStepException异常", I18NStringEnum.s1210.getI18nKey()),
    Exception("Exception", "异常", I18NStringEnum.s883.getI18nKey()),
    DataEnd("DataEnd", "结束", I18NStringEnum.s810.getI18nKey()),
    ObjOverDetailLimit("ObjOverDetailLimit", "数据明细数量超出限制，不同步该数据", I18NStringEnum.s840.getI18nKey()),
    ;

    private final String name;//节点名称
    private final String msg;//节点信息描述
    private final String i18nKey;
    /**
     * 需要统计耗时
     */
    private final boolean needStatCount;

    private final int order;

    /**
     * 有则上报耗时节点，，，后加节点一般不需要设置
     */
    private final String timePointName;

    DataNodeNameEnum(String name, String msg, String i18nKey) {
        this.name = name;
        this.msg = msg;
        this.i18nKey = i18nKey;
        this.needStatCount = false;
        this.order = 100;
        this.timePointName = null;
    }

    DataNodeNameEnum(String name, String msg, String i18nKey, boolean needStatCount, int order, String timePointName) {
        this.name = name;
        this.msg = msg;
        this.i18nKey = i18nKey;
        this.needStatCount = needStatCount;
        this.order = order;
        this.timePointName = timePointName;
    }

    public String getMsg(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey, lang, tenantId, msg);
    }


    @Override
    public String getI18nValue() {
        return msg;
    }
}
