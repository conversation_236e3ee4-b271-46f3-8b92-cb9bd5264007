package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3CPQOrderManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.DistributeArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component("SAL_SaleOrder")
public class SaleOrderSpecialBusinessImpl implements SpecialBusiness {
    @Autowired
    private K3CPQOrderManager k3XOrderManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    @Override
    public Result<String> beforeRunCreateErpObjData(StandardData standardData, K3CloudApiClient apiClient, SaveArg saveArg, IdSaveExtend saveExtend) {
        // 分配客户
        Result<String> result = distributeCustomer(standardData, apiClient);
        if (!result.isSuccess()) {
            log.warn("Distribute customer fail,data={},result={}", standardData, result);
            return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
        }
        // 分配物料
        result = distributeProduct(standardData, apiClient);
        if (!result.isSuccess()) {
            log.warn("Distribute product fail,data={},result={}", standardData, result);
            return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
        }
        // 订单可能有cpq结构，K3C侧不能批量添加
        saveArg.setIsEntryBatchFill(false);
        if (saveExtend.getIsDifferentCPQ()) {
            saveArg.getNeedReturnFields().add("FSaleOrderEntry.FRowType");
        }
        // 处理带cpq的订单
        result = k3XOrderManager.handleCpqSaleOrderProduct(standardData, apiClient,saveArg);
        if (!result.isSuccess()) {
            log.warn("handle cpqSaleOrderProduct fail,data={},result={}", standardData, result);
            return result;
        }
        return result;
    }

    @Override
    public void beforeRunUpdate(SaveArg saveArg, StandardData standardData, K3DataConverter k3DataConverter, K3CloudApiClient apiClient, IdSaveExtend saveExtend) {
        // 处理带cpq的订单
        k3XOrderManager.handleCpqSaleOrderProduct(standardData, apiClient,saveArg);
    }

    /**
     * 分配客户
     *
     * @param standardData
     * @param apiClient
     * @return
     */
    private Result<String> distributeCustomer(StandardData standardData, K3CloudApiClient apiClient) {
        String orgNumber = String.valueOf(standardData.getMasterFieldVal().get("FSaleOrgId.FNumber"));
        String accountNumber = String.valueOf(standardData.getMasterFieldVal().get("FCustId.FNumber"));
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("BD_Customer");
        queryArg.setFieldKeys("FName,FNumber,FCustId,FUseOrgId,FCreateOrgId.FNumber");
        queryArg.setFilterString(String.format("FUseOrgId.FNumber ='%s' and FNumber='%s'", orgNumber, accountNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if (!result.isSuccess()) {
            return Result.newError(ResultCodeEnum.RESULT_ERROR);
        }
        if (result.getData().size() != 0) {
            return Result.newSuccess();
        }
        // 查询ID，分配接口只能用ID分配
        queryArg = new QueryArg();
        queryArg.setFormId("BD_Customer");
        queryArg.setFieldKeys("FCUSTID");
        queryArg.setFilterString(String.format("FUseOrgId.FNumber = FCreateOrgId.FNumber and FNumber='%s'", accountNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> cusResult = apiClient.queryReturnMap(queryArg);
        List<K3Model> cusData = cusResult.getData();
        if (CollectionUtils.isEmpty(cusData)){
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s173.getI18nKey(),
                    apiClient.getTenantId(),
                    String.format(I18NStringEnum.s173.getI18nValue(), accountNumber),
                    Lists.newArrayList(accountNumber)),
                    null,
                    null);
        }
        queryArg = new QueryArg();
        String accountId = cusData.get(0).getString("FCUSTID");
        queryArg.setFormId("ORG_Organizations");
        queryArg.setFieldKeys("FOrgId");
        queryArg.setFilterString(String.format("FNumber='%s'", orgNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> orgResult = apiClient.queryReturnMap(queryArg);
        List<K3Model> orgData = orgResult.getData();
        if (CollectionUtils.isEmpty(orgData)){
            throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s174.getI18nKey(),
                    apiClient.getTenantId(),
                    String.format(I18NStringEnum.s174.getI18nValue(), accountNumber),
                    Lists.newArrayList(accountNumber)),
                    null,
                    null);
        }
        String orgId = orgData.get(0).getString("FOrgId");
        DistributeArg distributeArg = new DistributeArg();
        distributeArg.setDataIds(accountId);
        distributeArg.setOrganizationIds(orgId);
        return apiClient.distribute("BD_Customer", distributeArg);
    }

    /**
     * 分配物料
     *
     * @param standardData
     * @param apiClient
     * @return
     */
    private Result<String> distributeProduct(StandardData standardData, K3CloudApiClient apiClient) {
        String orgNumber = String.valueOf(standardData.getMasterFieldVal().get("FSaleOrgId.FNumber"));
        Set<String> productNumberSet = Sets.newHashSet();
        if(CollectionUtils.isEmpty(standardData.getDetailFieldVals().get("SAL_SaleOrder.SaleOrderEntry"))){//没有明细，不处理
            return Result.newSuccess();
        }
        for (ObjectData objectData : standardData.getDetailFieldVals().get("SAL_SaleOrder.SaleOrderEntry")) {
            productNumberSet.add(String.valueOf(objectData.get("FMaterialId.FNumber")));
        }
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId("BD_MATERIAL");
        queryArg.setFieldKeys("FNumber");
        String productNumber = productNumberSet.stream().collect(Collectors.joining("','"));
        queryArg.setFilterString(String.format("FUseOrgId.FNumber ='%s' and FNumber in ('%s')", orgNumber, productNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if (!result.isSuccess()) {
            return Result.newError(ResultCodeEnum.RESULT_ERROR);
        }
        if (result.getData().size() == productNumberSet.size()) {
            return Result.newSuccess();
        }
        for (K3Model data : result.getData()) {
            productNumberSet.remove(data.get("FNumber"));
        }
        productNumber = productNumberSet.stream().collect(Collectors.joining("','"));
        queryArg = new QueryArg();
        queryArg.setFormId("BD_MATERIAL");
        queryArg.setFieldKeys("FMATERIALID");
        queryArg.setFilterString(String.format("FUseOrgId.FNumber = FCreateOrgId.FNumber and FNumber in ('%s')", productNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> productResult = apiClient.queryReturnMap(queryArg);
        Set<String> productIdSet = Sets.newHashSet();
        for (K3Model data : productResult.getData()) {
            productIdSet.add(String.valueOf(data.get("FMATERIALID")));
        }
        queryArg = new QueryArg();
        queryArg.setFormId("ORG_Organizations");
        queryArg.setFieldKeys("FOrgId");
        queryArg.setFilterString(String.format("FNumber='%s'", orgNumber));
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> orgResult = apiClient.queryReturnMap(queryArg);
        String orgId = orgResult.getData().get(0).getString("FOrgId");
        DistributeArg distributeArg = new DistributeArg();
        distributeArg.setOrganizationIds(orgId);
        for (String id : productIdSet) {
            distributeArg.setDataIds(id);
            Result<String> distributeResult = apiClient.distribute("BD_MATERIAL", distributeArg);
            if (!distributeResult.isSuccess()) {
                log.warn("Distribute product fail,id={},orgId={}", id, orgId);
                return Result.newError(ResultCodeEnum.DISTRIBUTE_ERROR);
            }
        }
        return Result.newSuccess();
    }


}
