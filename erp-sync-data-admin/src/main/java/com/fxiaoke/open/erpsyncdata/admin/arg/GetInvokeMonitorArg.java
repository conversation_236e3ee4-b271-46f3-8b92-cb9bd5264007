package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class GetInvokeMonitorArg implements Serializable {
    @ApiModelProperty("数据中心id")
    private String dcId;
    @ApiModelProperty("ERP对象apiName")
    private List<String> erpObjApiName;
    @ApiModelProperty("开始统计时间")
    private Long startTime;
    @ApiModelProperty("间隔时间 分:m,小时:h,天:d,周:w,月:M,季度:q,年:y")
    private String interval;
    @ApiModelProperty("动作")
    private Integer action;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("需要查看的监控图")
    private String monitor;
}
