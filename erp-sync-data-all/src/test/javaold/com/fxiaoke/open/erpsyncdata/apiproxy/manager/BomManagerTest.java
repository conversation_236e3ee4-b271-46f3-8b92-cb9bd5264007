package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Ignore
public class BomManagerTest extends BaseTest {
    @Autowired
    private BomManager bomManager;

    @Test
    public void getMaterialUnitNumber() {
        K3CloudConnectParam connectParam = K3CloudConnectParam.newUserParam(
                "http://*************/k3cloud/",
                "5ec229fad54306", "ces2", "8888888");
        K3CloudApiClient apiClient = K3CloudApiClient.newInstance("84801", connectParam, "665097193209561088");
        String unitNumber = bomManager.getMaterialUnitNumber(apiClient, "CH1055");
        System.out.println(unitNumber);
    }

    @Test
    public void getBomInstanceList() {
        List<ObjectData> list = bomManager.getBomInstanceList("82777", "62de1614edca4c0001b2f08c");
        System.out.println(list);
    }

    @Test
    public void querySaleOrderRelatedList() {
        List<ObjectData> list = bomManager.querySaleOrderRelatedList("82777", "62dcf4102085fc0001548bcb");
        System.out.println(list);
    }

    @Test
    public void getaOrderProductList() {
        List<ObjectData> list = bomManager.getOrderProductList("82777", "62dcf413b3a8d50001befa51");
        System.out.println(list);
    }
}
