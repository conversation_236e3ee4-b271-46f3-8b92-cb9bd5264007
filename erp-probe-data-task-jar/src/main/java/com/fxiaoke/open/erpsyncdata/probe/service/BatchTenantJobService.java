package com.fxiaoke.open.erpsyncdata.probe.service;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 18:24 2021/2/26
 * @Desc:批量处理企业错误消息通知,批量超时同步数据,批量触发等待数据
 */
public interface BatchTenantJobService {
    /**
     * 执行任务
     * @param tenantIdList 企业ei集合
     * @return
     */
    Result<Void> executeTasks(List<String> tenantIdList);


    Result<Void> asyncExecuteSingleTenantTasks(String tenantId);

    Result<Void> reSyncDataExecuteSingleTenantTasks(String tenantId);
}
