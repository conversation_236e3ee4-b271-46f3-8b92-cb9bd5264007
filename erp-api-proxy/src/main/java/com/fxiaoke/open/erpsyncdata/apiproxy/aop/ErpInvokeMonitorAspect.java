package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.log.dto.ErpSyncMonitorLogDTO;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.MonitorLogModule;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ObjectInvokeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ObjectInvokeStatus;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;

import static com.fxiaoke.open.erpsyncdata.dbproxy.aop.AspectSpelUtil.getSpelValue;
import static com.fxiaoke.open.erpsyncdata.dbproxy.aop.AspectSpelUtil.getStandardEvaluationContext;

/**
 * <AUTHOR>
 * @date 2022/12/6 19:53:01
 */
@Aspect
@Component
@Slf4j
public class ErpInvokeMonitorAspect {
    @Autowired
    private SyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;

    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;

    @Around("execution(* com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager.erpPushDataToDss(..)) || execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseErpDataManager.*(..)) || execution(* com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService.currencyFunction(..)) || execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager.*(..)) || execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.SapDataManager.*(..)) || execution(* com.fxiaoke.open.erpsyncdata.preprocess.impl.ErpDataPreprocessServiceImpl.listErpObjDataFromMongo(..)) ||  execution(* com.fxiaoke.open.erpsyncdata.main.dispatcher.processor.DispatcherEventListen.listen(..)) ||  execution(* com.fxiaoke.open.erpsyncdata.writer.manager.DoWrite2CrmManager.batchDoWriteAndAfter(..)) || execution(* com.fxiaoke.open.erpsyncdata.apiproxy.manager.SpecialWayDataService.*(..))")
    public Object invokeMonitor2(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        final MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        final InvokeMonitor annotation = AnnotationUtils.findAnnotation(signature.getMethod(), InvokeMonitor.class);
        if (Objects.nonNull(annotation)) {
            return invokeMonitor(proceedingJoinPoint, annotation);
        }
        return proceedingJoinPoint.proceed();
    }

    // 有其他aop导致实现类获取不到interface上注解
    // @Around("@annotation(invokeMonitor)")
    public Object invokeMonitor(ProceedingJoinPoint proceedingJoinPoint, InvokeMonitor invokeMonitor) throws Throwable {
        final long start = System.currentTimeMillis();

        final MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        final ObjectInvokeEntity objectInvokeEntity = new ObjectInvokeEntity();
        Object proceed = null;
        boolean exception = false;
        try {
            proceed = proceedingJoinPoint.proceed();
            setMonitorStatus(objectInvokeEntity, proceed);
            return proceed;
        } catch (Throwable e) {
            exception = true;
            setMonitorStatus(objectInvokeEntity, e);
            throw e;
        } finally {
            try {
                final StandardEvaluationContext context = getStandardEvaluationContext(proceedingJoinPoint, proceed, signature);
                if (exception || BooleanUtils.isTrue(getSpelValue(invokeMonitor.condition(), context))) {
                    objectInvokeEntity.setCost((int) (System.currentTimeMillis() - start));
                    objectInvokeEntity.setCreateTime(System.currentTimeMillis());
                    objectInvokeEntity.setAction(invokeMonitor.action().getAction());
                    objectInvokeEntity.setInvokeType(invokeMonitor.invokeType().getType());
                    // objectInvokeEntity.setLogId(LogIdUtil.get());
                    ObjectInvokeEntity.setPloyDetailIdByThreadLocal(objectInvokeEntity);

                    fillObjectInvokeEntity(objectInvokeEntity, invokeMonitor, context);
                    sendBizLog(invokeMonitor, objectInvokeEntity);
//                    if (configCenterConfig.isMonitorTenant(objectInvokeEntity.getTenantId())) {
//                        MonitorUtil.send(objectInvokeEntity, MonitorType.INVOKE_MONITOR);
//                    }
                }
            } catch (Throwable e) {
                log.warn("上报监控失败, logId:{} method:{} args:{} objectInvokeEntity:{}", LogIdUtil.get(), signature.getName(), proceedingJoinPoint.getArgs(), JSON.toJSONString(objectInvokeEntity), e);
            }
        }
    }

    private static void sendBizLog(InvokeMonitor invokeMonitor, ObjectInvokeEntity objectInvokeEntity) {
        try {
            //后面再观察下是否需要增加抽样上报逻辑。
            if (InvokeFeature.isEnabled(invokeMonitor.invokeFeatures(), InvokeFeature.sendBizLog)) {
                String status = ObjectInvokeStatus.success.getStatus().equals(objectInvokeEntity.getStatus()) ? "0" : "-1";
                //上报
                ErpSyncMonitorLogDTO.ErpSyncMonitorLogDTOBuilder builder = MonitorBizLogUtil.builder(MonitorLogModule.invoke_monitor, invokeMonitor.invokeType() + ":" + invokeMonitor.action(), objectInvokeEntity.getTenantId(), status);
                builder.label1(invokeMonitor.invokeType().name());
                builder.label2(invokeMonitor.action().name());
                builder.num1(MonitorBizLogUtil.safeLong(objectInvokeEntity.getCount()));
                builder.num2(MonitorBizLogUtil.safeLong(objectInvokeEntity.getResultObjSize()));
                builder.cost1(MonitorBizLogUtil.safeLong(objectInvokeEntity.getCost()));
                MonitorBizLogUtil.send(builder.build());
            }
        } catch (Exception e) {
            log.error("sendBizLog exception", e);
        }
    }

    public void setPloyDetailId(final ObjectInvokeEntity objectInvokeEntity, final String snapshotId) {
        if (StringUtils.isNotBlank(objectInvokeEntity.getPloyDetailId()) || StringUtils.isBlank(snapshotId)) {
            return;
        }
        // objectInvokeEntity.setSnapshotId(snapshotId);

        final SyncPloyDetailSnapshotEntity syncPloyDetailSnapshotEntity = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(objectInvokeEntity.getTenantId())).get(objectInvokeEntity.getTenantId(), snapshotId);
        // 转换为PloyDetailId
        objectInvokeEntity.setPloyDetailId(syncPloyDetailSnapshotEntity.getSyncPloyDetailId());
    }

    public static void setMonitorStatus(final ObjectInvokeEntity objectInvokeEntity, final Throwable e) {
        objectInvokeEntity.setStatus(ObjectInvokeStatus.error.getStatus());
        objectInvokeEntity.setErrorMessage(e.getClass().getSimpleName() + ":" + e.getMessage());
    }

    public static void setMonitorStatus(final ObjectInvokeEntity objectInvokeEntity, final Object proceed) {
        if (proceed instanceof Result) {
            final Result result = (Result) proceed;
            objectInvokeEntity.setStatus(result.isSuccess() ? ObjectInvokeStatus.success.getStatus() : ObjectInvokeStatus.error.getStatus());
            objectInvokeEntity.setErrorCode(result.getErrCode());
            objectInvokeEntity.setErrorMessage(result.getErrMsg());
        } else {
            objectInvokeEntity.setStatus(ObjectInvokeStatus.success.getStatus());
        }
    }

    private void fillObjectInvokeEntity(final ObjectInvokeEntity objectInvokeEntity, final InvokeMonitor erpMonitor, final StandardEvaluationContext context) {
        objectInvokeEntity.setTenantId(getSpelValue(erpMonitor.tenantId(), context));
        objectInvokeEntity.setDcId(getSpelValue(erpMonitor.dcId(), context));
        objectInvokeEntity.setObjectApiName(getSpelValue(erpMonitor.objAPIName(), context));
        objectInvokeEntity.setSourceEventType(getSpelValue(erpMonitor.sourceEventType(), context));
        objectInvokeEntity.setDataId(getSpelValue(erpMonitor.dataId(), context));
        final Integer count = getSpelValue(erpMonitor.count(), context);
        objectInvokeEntity.setCount(Objects.isNull(count) ? 0 : count);

        setPloyDetailId(objectInvokeEntity, getSpelValue(erpMonitor.snapshotId(), context));

        //计算结果对象大小
        if (InvokeFeature.isEnabled(erpMonitor.invokeFeatures(), InvokeFeature.calculateResultSize)) {
            final Object data  = getSpelValue(erpMonitor.data(), context);
            objectInvokeEntity.setResultObjSize(RamUsageEstimateUtil.sizeOfObjectIgnoreException(data));
        }
    }

    /**
     * InvokeMonitor 使用,不能删除
     *
     * @see K3DataManager#listAndLog(String, String, K3CloudApiClient, QueryArg, TimeFilterArg)
     */
    public static Integer size(Collection<?> collection) {
        return CollectionUtils.emptyIfNull(collection).stream()
                .map(c -> {
                    if (c instanceof Collection) {
                        return size((Collection<?>) c);
                    } else {
                        return 1;
                    }
                })
                .reduce(Integer::sum)
                .orElse(0);
    }
}
