package com.fxiaoke.open.erpsyncdata.web.controller.customFunction;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.annotation.CustomFunctionRateLimit;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncByCrmDataIdArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import com.fxiaoke.open.erpsyncdata.web.factory.CustomFunctionFactory;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.CustomFunctionCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date: 10:20 2025/3/19
 * @Desc:
 */
@Slf4j
@Api(tags = "自定义函数操作中心")
@RestController("CustomFunctionActionCenterController")
@RequestMapping("inner/erp/syncdata/customfunction/actioncenter")
public class CustomFunctionActionCenterController {
    @Autowired
    private CustomFunctionFactory customFunctionFactory;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "通过crm主键手动同步（crm->erp）")
    @RequestMapping(value = "/manualSyncCrm2Erp",method = RequestMethod.POST)
    @ControllerMethodProxy
    @CustomFunctionRateLimit
    public Result<?> manualSyncCrm2Erp(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") Integer tenantId, @RequestHeader(value="x-fs-locale", required = false) String lang, @RequestBody SyncByCrmDataIdArg arg) {
        if (arg == null||tenantId==null) {
            log.info("tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        log.info("CustomFunctionActionCenterController.manualSyncCrm2Erp.tenantId={},lang={}，arg={}",tenantId, lang,arg);
        TraceUtil.setLocale(StringUtils.isNotEmpty(lang) ? lang : i18NStringManager.getDefaultLang(String.valueOf(tenantId)));
        CustomFunctionCommonArg customFunctionCommonArg=new CustomFunctionCommonArg();
        customFunctionCommonArg.setTenantId(String.valueOf(tenantId));
        customFunctionCommonArg.setParams(JsonUtil.toJson(arg));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType("manualSyncCrm2Erp");
        return customFunctionCommonService.executeLogic(customFunctionCommonArg);
    }
}
