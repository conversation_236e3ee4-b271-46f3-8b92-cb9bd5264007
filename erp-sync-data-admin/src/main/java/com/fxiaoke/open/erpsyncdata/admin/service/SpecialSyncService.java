package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.model.excel.CategoryFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpProductCategory;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ProductCategorySyncResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/8/6
 */
public interface SpecialSyncService {

    /**
     * @param tenantId 企业Id
     * @param userId 接受同步结果的user
     * @param dcId 数据中心ID
     * @param categories ERP产品分类
     * @return 导入的结果
     */
    Result<List<CategoryFieldDataMappingExcelVo>> syncErpProductCategory(String tenantId,
                                                                         @Nullable Integer userId,
                                                                         String dcId,
                                                                         List<ErpProductCategory> categories,
                                                                         String lang);

    /**
     * 同步K3Cloud物料分组到CRM产品分类
     * @param tenantId
     * @param userId
     * @param dcId
     * @return
     */
    Result<List<CategoryFieldDataMappingExcelVo>> syncKcMaterialGroup2Cate(String tenantId, @Nullable Integer userId, String dcId,String lang);

    /**
     * 查询K3C物料分组同步日志
     * @param tenantId
     * @param userId
     * @param dcId
     * @return
     */
    Result<ProductCategorySyncResult> getSyncKcMaterialGroup2CateLog(String tenantId, @Nullable Integer userId, String dcId);
}
