package com.fxiaoke.open.erpsyncdata.admin.data;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2020/7/2.
 */
@Data
@EqualsAndHashCode
public class ListObjectFieldsData implements Serializable {
    private String objectApiName;
    private String objectDisplayName;
    private String fieldApiName;
    private String label;
    private String type;
    private Boolean isRequired;

    private ListObjectFieldsData() {
    }

    public static ListObjectFieldsData newData(String objectApiName, String objectDisplayName, String fieldApiName, String label, String type, Boolean isRequired) {
        ListObjectFieldsData data = new ListObjectFieldsData();
        data.objectApiName = objectApiName;
        data.objectDisplayName = objectDisplayName;
        data.fieldApiName = fieldApiName;
        data.label = label;
        data.type = type;
        data.isRequired = isRequired;
        return data;
    }
}
