package com.fxiaoke.open.erpsyncdata.web.service.customFunction;


import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AttachmentsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.AttachmentsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AttachmentsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("k3DownAndUploadFile")
public class K3DownAndUploadFileServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private AttachmentsService attachmentsService;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId = commonArg.getTenantId();
        AttachmentsArg arg = JsonUtil.fromJson(commonArg.getParams(), AttachmentsArg.class);
        arg.setTenantId(tenantId);
        Result2<AttachmentsResult> attachmentsResult = attachmentsService.downAndUploadAttachments(arg);
        if(!attachmentsResult.isSuccess()){
            Result<String> result=Result.newError(ResultCodeEnum.ERROR_MSG);
            result.setErrMsg(attachmentsResult.getErrMsg());
            result.setTraceMsg(result.getTraceMsg());
            return result;
        }
        return Result.newSuccess(JsonUtil.toJson(attachmentsResult.getData()));
    }
}
