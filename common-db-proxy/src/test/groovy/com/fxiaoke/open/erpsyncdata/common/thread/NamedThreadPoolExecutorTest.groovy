package com.fxiaoke.open.erpsyncdata.common.thread

import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil
import com.github.trace.TraceContext
import spock.lang.Specification

import java.util.concurrent.RejectedExecutionHandler
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.atomic.AtomicInteger

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/1/13
 */
class NamedThreadPoolExecutorTest extends Specification {

    class TestExecutor extends NamedThreadPoolExecutor {
        AtomicInteger executedTasks = new AtomicInteger(0)
        AtomicInteger rejectedTasks = new AtomicInteger(0)

        TestExecutor(String name, int corePoolSize, int maximumPoolSize, int queueSize, RejectedExecutionHandler handler) {
            super(name, corePoolSize, maximumPoolSize, queueSize, handler)
        }

        @Override
        void execute(Runnable command) {
            executedTasks.incrementAndGet()
            super.execute(command)
        }
    }

    def "测试线程池"() {
        given:
        TestExecutor executor = new TestExecutor("test",3,3,100, new RejectedExecutionHandler(){
            @Override
            void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                def test = (TestExecutor)executor
                def rejected = test.rejectedTasks.incrementAndGet()
                println("第" + rejected + "个线程触发拒绝: thread pool full")
            }
        })

        when:
        TraceContext context = TraceContext.get();
        context.setTraceId("test-111")
        for (i in 0..<500) {
            executor.submit {
                sleep(10)
                println(Thread.currentThread().getName()+" "+TraceContext.get().getTraceId())
            }
        }
        // 等待任务全部运行完成
        executor.shutdown()
        while (!executor.isTerminated()) {}

        then:
        // 验证已执行的任务数和被拒绝的任务数
        def numExe = executor.executedTasks.get()
        def numRej = executor.rejectedTasks.get()
        println(numExe + "个线程被执行，" +  numRej + "个线程被拒绝")
        numExe == 500
    }
}
