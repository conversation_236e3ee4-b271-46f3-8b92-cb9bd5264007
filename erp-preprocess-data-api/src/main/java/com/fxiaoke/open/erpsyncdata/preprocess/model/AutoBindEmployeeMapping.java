package com.fxiaoke.open.erpsyncdata.preprocess.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 10:08:59
 */
public interface AutoBindEmployeeMapping {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg implements Serializable {
        private String tenantId;
        private String objectId;
        private List<String> dataCenterIds;
    }
}
