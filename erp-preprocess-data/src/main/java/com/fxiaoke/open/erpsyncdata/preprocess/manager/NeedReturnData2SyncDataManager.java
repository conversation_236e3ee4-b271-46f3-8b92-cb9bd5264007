package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldTypeContants;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailData2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataMonitoredException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Component
@Slf4j
public class NeedReturnData2SyncDataManager {
    @Autowired
    private SyncDataManager syncDataManager;
    public void doDetailNeedReturnData2SyncData(String tenantId, Map<String, List<ObjectData>> detailDataResultsMap,
                                                Map<String, Map<String, String>> apiNameAndDestDataIdSyncDataIdMap, SyncPloyDetailData2 syncPloyDetailData) {
        if (syncPloyDetailData.getIntegrationStreamNodes() != null && syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName() != null) {
            Map<String, List<String>> objApiName2NeedReturnFieldApiName = syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName();
            for (Map.Entry<String, List<ObjectData>> detailEntry : detailDataResultsMap.entrySet()) {
                String detailObjectApiName = detailEntry.getKey();
                List<String> needReturnFieldApiNames = objApiName2NeedReturnFieldApiName.get(detailObjectApiName);
                if(CollectionUtils.isNotEmpty(needReturnFieldApiNames)&&apiNameAndDestDataIdSyncDataIdMap.containsKey(detailObjectApiName)){
                    List<ObjectData> detailObjectResults = detailEntry.getValue();
                    Map<String, String> destDataIdSyncDataIdMap = apiNameAndDestDataIdSyncDataIdMap.get(detailObjectApiName);
                    for(ObjectData detail:detailObjectResults){
                        String syncDataId=destDataIdSyncDataIdMap.get(detail.getId());
                        if(StringUtils.isNotBlank(syncDataId)){
                            setNeedReturnData2SyncData(tenantId, syncDataId, detail, needReturnFieldApiNames);
                        }
                    }
                }
            }
        }
    }

    public void doMasterNeedReturnData2SyncData(String tenantId, String syncDataId, ObjectData objectData, SyncPloyDetailData2 syncPloyDetailData) {
        if(objectData==null){
            return;
        }
        if (this.hasNeedReturnData2SyncDataNode(syncPloyDetailData,objectData.getApiName())) {
            List<String> needReturnFieldApiNames = syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName().get(objectData.getApiName());
            setNeedReturnData2SyncData(tenantId, syncDataId, objectData, needReturnFieldApiNames);
        }
    }
    public boolean hasNeedReturnData2SyncDataNode(SyncPloyDetailData2 syncPloyDetailData,String objApiName){
        if (syncPloyDetailData.getIntegrationStreamNodes() != null && syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName() != null
                && syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName().containsKey(objApiName)) {
            List<String> needReturnFieldApiNames = syncPloyDetailData.getIntegrationStreamNodes().getObjApiName2NeedReturnFieldApiName().get(objApiName);
            if(CollectionUtils.isNotEmpty(needReturnFieldApiNames)){
                return true;
            }
        }
        return false;
    }

    public void setNeedReturnData2SyncData(String tenantId, String syncDataId, ObjectData objectData, List<String> needReturnFieldApiNames) {
        com.fxiaoke.open.erpsyncdata.common.data.ObjectData needReturnData = new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
        for (String field : needReturnFieldApiNames) {
            needReturnData.put(field, objectData.get(field));
        }
        syncDataManager.updateNeedReturnData(tenantId, syncDataId, needReturnData);
    }

}
