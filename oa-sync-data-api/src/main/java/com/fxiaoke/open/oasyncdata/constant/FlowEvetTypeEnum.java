package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;

public enum FlowEvetTypeEnum {
    INSTANCE_CHANGE("instance_change", "实例变更", I18NStringEnum.s1229.getI18nKey(), "instance"),
    INSTANCE_TRIGGER_RESULT("instance_trigger_result", "审批触发成功", I18NStringEnum.s1230.getI18nKey(), "bizObject"),
    INSTANCE_COMPLETE("instance_complete", "实例审批完成", I18NStringEnum.s1231.getI18nKey(), "instance"),
    INSTANCE_TRIGGER("instance_trigger", "批量触发审批流之前", I18NStringEnum.s1232.getI18nKey(), "bizObject"),
    INSTANCE_TRIGGER_SUCCESS("instance_trigger_success", "触发审批流成功", I18NStringEnum.s1233.getI18nKey(), "instance"),
    INSTANCE_DELETE("instance_delete", "流程实例删除", I18NStringEnum.s1234.getI18nKey(), "instance"),
    INSTANCE_RECOVERY("instance_recovery", "流程实例恢复", I18NStringEnum.s1235.getI18nKey(), "instance"),
    TASK_CHANGE("task_change", "任务变更", I18NStringEnum.s1236.getI18nKey(), "task"),
    TASK_DELETE("task_delete", "任务删除", I18NStringEnum.s1237.getI18nKey(), "task"),
    TASK_RECOVERY("task_recovery", "任务恢复", I18NStringEnum.s1238.getI18nKey(), "task");

    String type;
    String name;
    String i18nKey;
    String target;

    FlowEvetTypeEnum(String type, String name, String i18nKey, String target) {
        this.type = type;
        this.name = name;
        this.i18nKey = i18nKey;
        this.target = target;
    }

    public String getType() {
        return type;
    }

    public String getName(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,name);
    }

    public String getTarget() {
        return target;
    }


}
