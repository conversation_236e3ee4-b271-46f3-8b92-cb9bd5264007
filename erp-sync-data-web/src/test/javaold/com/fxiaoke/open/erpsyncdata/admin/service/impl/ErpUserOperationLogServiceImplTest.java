package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpUserOperationLogService;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 21:02 2021/12/7
 * @Desc:
 */
@Ignore
public class ErpUserOperationLogServiceImplTest extends BaseTest {
    @Autowired
    private ErpUserOperationLogService erpUserOperationLogService;

    @Test
    public void buildNewErpUserOperationLog() {
    }

    @Test
    public void addErpUserOperationLog() {
    }

    @Test
    public void buildAndAddLog() {
        erpUserOperationLogService.buildAndAddLog("11","11","11","11","11","11","11",System.currentTimeMillis());
        System.out.println("");
    }
}