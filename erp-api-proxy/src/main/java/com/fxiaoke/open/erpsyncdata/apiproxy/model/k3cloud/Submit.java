package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/23
 */
public class Submit {

    @Getter
    @Setter
    @ToString
    public static class BaseArg extends HashMap<String,Object> implements Serializable {

        public BaseArg() {
            //原来的逻辑默认是0
            this.put("CreateOrgId", 0);
        }
        public void setIds(String ids) {
            this.put("Ids",ids);
        }
        public void setNumbers(List<String> numbers) {
            this.put("Numbers",numbers);
        }
    }


    @Getter
    @Setter
    @ToString
    public static class Result {
        @SerializedName("Result")
        @JsonProperty("Result")
        private SubmitResult result;
    }

    public static class SubmitResult extends K3BaseResult{

    }

    public static String checkResult(com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<Result> submit){
        if (!submit.isSuccess()){
            return submit.getErrMsg();
        }else {
            Submit.Result submitResult = submit.getData();
            if (submitResult == null || submitResult.getResult().getResponseStatus() == null) {
                return I18NStringManager.getByTraceLang(I18NStringEnum.s3797);
            }else {
                ResponseStatus responseStatus = submitResult.getResult().getResponseStatus();
                if (!responseStatus.getIsSuccess()) {
                    return responseStatus.printErrors();
                }
            }
        }
        return null;
    }
}
