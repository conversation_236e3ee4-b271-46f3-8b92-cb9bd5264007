package com.fxiaoke.open.erpsyncdata.preprocess.model;


import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;


/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/10
 * @Desc:
 */
public class CrmRequestBaseParam {

    /**
     * 企业ei
     */
    private int enterpriseId;

    /**
     * 当前请求人的员工id
     */
    private int currentEmployeeId;

    /**
     * 对象名称
     */
    private String apiName;

    public CrmRequestBaseParam(int enterpriseId, int currentEmployeeId, String apiName) {
        this.enterpriseId = enterpriseId;
        this.currentEmployeeId = currentEmployeeId;
        this.apiName = apiName;
    }

    public int getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(int enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public int getCurrentEmployeeId() {
        return currentEmployeeId;
    }

    public void setCurrentEmployeeId(int currentEmployeeId) {
        this.currentEmployeeId = currentEmployeeId;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CrmRequestBaseParam that = (CrmRequestBaseParam)o;
        return enterpriseId == that.enterpriseId && currentEmployeeId == that.currentEmployeeId
            && Objects.equal(apiName, that.apiName);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(enterpriseId, currentEmployeeId, apiName);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("enterpriseId", enterpriseId)
            .add("currentEmployeeId", currentEmployeeId).add("apiName", apiName).toString();
    }
    /**
     * 构造调用CRM接口的基本参数
     *
     * *@param enterpriseAccount
     * @param currentEmployeeId
     * @param apiName
     * @return
     */
    public static CrmRequestBaseParam build(int enterpriseId, Integer currentEmployeeId, String apiName) {
        int employeeId;
        if (null != currentEmployeeId) {
            employeeId = currentEmployeeId;
        } else {
            employeeId = -10000;
        }
        return new CrmRequestBaseParam(enterpriseId, employeeId, apiName);
    }

}
