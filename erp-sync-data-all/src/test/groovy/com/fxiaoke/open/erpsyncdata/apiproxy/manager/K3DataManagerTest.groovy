package com.fxiaoke.open.erpsyncdata.apiproxy.manager

import com.alibaba.fastjson.JSONObject
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum
import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3CPQOrderManager
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.SaveArg
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldExtendDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.google.common.base.Joiner
import com.google.common.collect.Lists
import groovy.util.logging.Slf4j
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

import java.util.stream.Collectors

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/13
 */
@Ignore
@Slf4j
class K3DataManagerTest extends BaseSpockTest {
    @Autowired
    private K3DataManager k3DataManager
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private K3CPQOrderManager k3CPQOrderManager;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;

    @Test
    public void createErpObjData() {
        String json = "{\"detailFieldVals\":{},\"masterFieldVal\":{\"FBILLTYPE.FNumber\":\"WLQD01_SYS\",\"FBOMCATEGORY\":\"1\",\"FBOMUSE\":\"99\",\"FCreateOrgId.FNumber\":\"000\",\"FMATERIALID.FNumber\":\"CH1055\",\"FUNITID.FNumber\":\"Pcs\",\"FUseOrgId.FNumber\":\"000\",\"object_describe_api_name\":\"ENG_BOM\",\"tenant_id\":\"82777\",\"_id\":\"788928494032814080\"},\"objAPIName\":\"ENG_BOM\"}";
        StandardData standardData = JSONObject.parseObject(json,StandardData.class)

        String json2 = "{\"channel\":\"ERP_K3CLOUD\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://172.31.100.60/k3cloud/\\\",\\\"dbId\\\":\\\"5ec229fad54306\\\",\\\"dbName\\\":\\\"接口环境\\\",\\\"authType\\\":1,\\\"userName\\\":\\\"ces2\\\",\\\"password\\\":\\\"8888888\\\",\\\"lcid\\\":2052,\\\"pushDataApiNames\\\":[],\\\"useFsHttpClient\\\":true}\",\"createTime\":1627629713607,\"dataCenterName\":\"金蝶K3Cloud\",\"enterpriseName\":\"(82777)zsl测试企业027\",\"id\":\"696453487420604416\",\"number\":0,\"tenantId\":\"82777\",\"updateTime\":1655207300973}"
        ErpConnectInfoEntity entity = JSONObject.parseObject(json2,ErpConnectInfoEntity.class)

        def data = k3DataManager.createErpObjData(standardData, entity)
        println(data)
    }

    @Test
    public void handleCpqSaleOrderProduct() {
        String dataValue="{\"detailFieldVals\":{\"SAL_SaleOrder.SaleOrderEntry\":[{\"FRowType\":null,\"FMaterialId.FNumber\":\"CH1362\",\"FQty\":\"1.00\",\"fake_master_detail\":\"799118177207484416\",\"FDiscountRate\":\"100\",\"root_prod_pkg_key\":\"1658222945830248\",\"FTaxPrice\":\"20.00\",\"prod_pkg_key\":\"1658222945830248\",\"parent_prod_pkg_key\":null,\"bom_instance_tree_id\":\"62d6796bdd1cdf22607afecb\",\"bom_id\":\"20220719000272\",\"FUnitID.FNumber\":null,\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"82777\",\"_id\":\"799118177274593280\"},{\"FRowType\":null,\"FMaterialId.FNumber\":\"CH1364\",\"FQty\":\"1.00\",\"fake_master_detail\":\"799118177207484416\",\"FDiscountRate\":\"100\",\"root_prod_pkg_key\":\"1658222945830248\",\"FTaxPrice\":\"0.00\",\"prod_pkg_key\":\"1658222939953241\",\"parent_prod_pkg_key\":\"1658222945830248\",\"bom_instance_tree_id\":\"62d6796bdd1cdf22607afecb\",\"bom_id\":\"20220719000270\",\"FUnitID.FNumber\":null,\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"82777\",\"_id\":\"799118177341702144\"},{\"FRowType\":null,\"FMaterialId.FNumber\":\"CH1365\",\"FQty\":\"1.00\",\"fake_master_detail\":\"799118177207484416\",\"FDiscountRate\":\"100\",\"root_prod_pkg_key\":\"1658222945830248\",\"FTaxPrice\":\"0.00\",\"prod_pkg_key\":\"1658222939953240\",\"parent_prod_pkg_key\":\"1658222945830248\",\"bom_instance_tree_id\":\"62d6796bdd1cdf22607afecb\",\"bom_id\":\"20220719000271\",\"FUnitID.FNumber\":null,\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"82777\",\"_id\":\"799118177408811008\"}]},\"masterFieldVal\":{\"FSaleOrgId.FNumber\":\"000\",\"FCustId.FNumber\":\"CUST00972\",\"FSalerId.FNumber\":\"00665_GW000226_1\",\"FNote\":null,\"FChangeReason\":\"编辑了订单\",\"FDate\":\"2022-07-19\",\"FSaleOrderFinance.FExchangeRate\":\"1\",\"FBillTypeID.FNumber\":\"XSDD01_SYS\",\"FSaleOrderFinance.FSettleCurrId.FNumber\":\"PRE001\",\"object_describe_api_name\":\"SAL_SaleOrder\",\"tenant_id\":\"82777\",\"_id\":\"799118177207484416\"},\"objAPIName\":\"SAL_SaleOrder\",\"syncLogId\":null}";
        StandardData standardData=JSONObject.parseObject(dataValue,StandardData.class);
        K3CloudApiClient k3CloudApiClient=K3CloudApiClient.newInstance(tenantId, "{\"baseUrl\":\"http://172.31.100.60/k3cloud/\",\"dbId\":\"5ec229fad54306\",\"dbName\":\"接口环境\",\"authType\":1,\"userName\":\"ces2\",\"password\":\"8888888\",\"lcid\":2052,\"pushDataApiNames\":[],\"useFsHttpClient\":true}", "696453487420604416")
        String saveArg="{\n" +
                "\t\"isAutoSubmitAndAudit\": false,\n" +
                "\t\"isEntryBatchFill\": false,\n" +
                "\t\"model\": {\n" +
                "\t\t\"FSaleOrgId\": {\n" +
                "\t\t\t\"FNumber\": \"000\"\n" +
                "\t\t},\n" +
                "\t\t\"FCustId\": {\n" +
                "\t\t\t\"FNumber\": \"CUST00972\"\n" +
                "\t\t},\n" +
                "\t\t\"FSalerId\": {\n" +
                "\t\t\t\"FNumber\": \"00665_GW000226_1\"\n" +
                "\t\t},\n" +
                "\t\t\"FChangeReason\": \"编辑了订单\",\n" +
                "\t\t\"FDate\": \"2022-07-19\",\n" +
                "\t\t\"FSaleOrderFinance\": {\n" +
                "\t\t\t\"FExchangeRate\": \"1\",\n" +
                "\t\t\t\"FSettleCurrId\": {\n" +
                "\t\t\t\t\"FNumber\": \"PRE001\"\n" +
                "\t\t\t}\n" +
                "\t\t},\n" +
                "\t\t\"FBillTypeID\": {\n" +
                "\t\t\t\"FNumber\": \"XSDD01_SYS\"\n" +
                "\t\t},\n" +
                "\t\t\"FSaleOrderEntry\": [{\n" +
                "\t\t\t\"FMaterialId\": {\n" +
                "\t\t\t\t\"FNumber\": \"CH1285\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"FTaxPrice\": \"28.00\",\n" +
                "\t\t\t\"FQty\": \"10.00\",\n" +
                "\t\t\t\"FDiscountRate\": \"100\",\n" +
                "\t\t\t\"FRowId\": \"1657019209880318\"\n" +
                "\t\t}, {\n" +
                "\t\t\t\"FMaterialId\": {\n" +
                "\t\t\t\t\"FNumber\": \"CH1287\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"FTaxPrice\": \"0.00\",\n" +
                "\t\t\t\"FQty\": \"10.00\",\n" +
                "\t\t\t\"FDiscountRate\": \"100\",\n" +
                "\t\t\t\"FParentRowId\": \"1657019209880318\",\n" +
                "\t\t\t\"FRowId\": \"1657019195733311\"\n" +
                "\t\t}, {\n" +
                "\t\t\t\"FMaterialId\": {\n" +
                "\t\t\t\t\"FNumber\": \"CH1286\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"FTaxPrice\": \"0.00\",\n" +
                "\t\t\t\"FQty\": \"30.00\",\n" +
                "\t\t\t\"FDiscountRate\": \"100\",\n" +
                "\t\t\t\"FParentRowId\": \"1657019209880318\",\n" +
                "\t\t\t\"FRowId\": \"1657019195733309\"\n" +
                "\t\t}]\n" +
                "\t},\n" +
                "\t\"needReturnFields\": [\"FSaleOrderEntry.FEntryID\"],\n" +
                "\t\"subSystemId\": null\n" +
                "}";
        SaveArg saveArg1=JSONObject.parseObject(saveArg,SaveArg.class);
        def result = k3CPQOrderManager.handleCpqSaleOrderProduct(standardData, k3CloudApiClient, saveArg1)
        println(result)
    }

    @Test
    public void testView() {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId("81961");
        erpIdArg.setObjAPIName("BD_MATERIALUNITCONVERT");
        erpIdArg.setDataId("100027");
        ErpConnectInfoEntity entity=erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("81961")).getByIdAndTenantId("81961","650085855625084928");
        def result = k3DataManager.getErpObjData(erpIdArg, entity)
        println(JacksonUtil.toJson(result))
    }

    @Test
    public void testSave() {
        String tenantId = "81138"
        SaveArg saveArg = new SaveArg();
        StandardData standardData = JacksonUtil.fromJson(dataJson, StandardData.class)
        K3DataConverter saveConverter = k3DataManager.buildSaveConverter(tenantId,null, standardData);
        //转换参数
        saveConverter.fillSaveArg(standardData, saveArg,tenantId);
        println(JacksonUtil.toJson(saveArg))
    }

    @Test
    public void testQuery() {
        String ei="81243";
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).listByTenantId(ei).get(0);
        TimeFilterArg timeFilterArg=new TimeFilterArg();
        timeFilterArg.setTenantId(ei);
        timeFilterArg.setObjAPIName("BD_STOCK");
        timeFilterArg.setStartTime(System.currentTimeMillis()-1000*3600*24*30L);
        timeFilterArg.setEndTime(System.currentTimeMillis());
        timeFilterArg.setOperationType(1)
        timeFilterArg.setLimit(5)
        ErpObjectEntity query=new ErpObjectEntity()
        query.setTenantId(ei)
        query.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT)
        List<ErpObjectEntity> objs=erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(ei)).queryList(query)
        List<String> list= Lists.newArrayList()
        for(ErpObjectEntity objectEntity:objs){
            if(!objectEntity.getErpObjectApiName().contains("_")){
                continue
            }
            timeFilterArg.setObjAPIName(objectEntity.getErpObjectApiName())
            Result<StandardListData> result = k3DataManager.listErpObjDataByTime(timeFilterArg, connectInfo);
            if(result.isSuccess()){
                list.add(objectEntity.getErpObjectApiName())
            }
            println ""
        }
String sss= Joiner.on(",").join(list)



        String tenantId = "81138"
        List<FilterData> filterDatas = [
                FilterData.builder()
                        .fieldApiName("FCustId")
                        .operate(FilterOperatorEnum.IN.value)
                        .fieldValue([715926, 716188]).build(),
                FilterData.builder()
                        .fieldApiName("FUseOrgId.FNumber")
                        .operate(FilterOperatorEnum.IS.value)
                        .fieldValue(["000"]).build(),
        ]
        QueryArg queryArg = new QueryArg()
        queryArg.setFilterString("FNumber = '20200706-000125'")
        queryArg.addAndFilters(filterDatas)
        queryArg.setFormId("BD_Customer")
        queryArg.setFieldKeys("FCustId,FNumber,FName,FUseOrgId.FNumber,FUseOrgId,FCreateOrgId,FDocumentStatus,FForbidStatus,FInvoiceType");
        def data = k3DataManager.queryK3ObjData(tenantId, "",queryArg)
        println(GsonUtil.toJson(data))
    }

    @Test
    void testBuildConverter(){
        String tenantId='81961';
        String objApiName='BD_MATERIAL';
        List<ErpFieldExtendEntity> result2 = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByObjQueryCode(tenantId,null, objApiName,Lists.newArrayList("FNumber","FName"));
        List<ErpFieldExtendEntity> result = erpFieldExtendDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getAllNeedQueryFieldExtend(tenantId,null, objApiName);

        ErpFieldExtendEntity erpFieldExtendEntity= result.get(0);
        result.add(erpFieldExtendEntity);
        List<ErpFieldExtendEntity> distinctResult = result.stream().distinct().collect(Collectors.toList());




        K3DataConverter converter = k3DataManager.buildConverter("82777","BD_STOCK")
        println converter
    }

    @Test
    void test222() {
        String json = "{\n" +
                "    \"objAPIName\": \"SAL_SaleOrder\",\n" +
                "    \"masterFieldVal\": {\n" +
                "        \"ComId\": \"103365#XSDD002184\",\n" +
                "        \"FChangerId.Id\": \"1\",\n" +
                "        \"id\": \"103365\",\n" +
                "        \"FSaleDeptId.FNumber\": \"6046\",\n" +
                "        \"FSaleOrderFinance.FBillAllAmount\": \"1.00\",\n" +
                "        \"FSalerId.FNumber\": \"00665_GW000226_1\",\n" +
                "        \"FNote\": \"dfvdf\",\n" +
                "        \"FDate\": \"2022-08-02\",\n" +
                "        \"FBillTypeID.FNumber\": \"XSDD01_SYS\",\n" +
                "        \"FSaleOrgId.FNumber\": \"000\",\n" +
                "        \"FSaleOrderFinance.FExchangeRate\": \"1.00\",\n" +
                "        \"FChangeReason\": \"103365#XSDD002184\",\n" +
                "        \"FCustId.FNumber\": \"CUST00977\",\n" +
                "        \"object_describe_api_name\": \"SAL_SaleOrder\",\n" +
                "        \"tenant_id\": \"81243\",\n" +
                "        \"_id\": \"103365#XSDD002184\",\n" +
                "        \"salesOrderId2XOrderId\": {\n" +
                "            \"SaleOrderPlan\": {\n" +
                "                103202: 102933\n" +
                "            },\n" +
                "            \"SaleOrderEntry\": {\n" +
                "                107125: 106746\n" +
                "            },\n" +
                "            \"order\": {\n" +
                "                103365: 102920\n" +
                "            }\n" +
                "        }\n" +
                "    },\n" +
                "    \"detailFieldVals\": {\n" +
                "        \"SAL_SaleOrder.SaleOrderEntry\": [\n" +
                "            {\n" +
                "                \"DetailId\": \"107125\",\n" +
                "                \"fake_master_detail\": \"103365#XSDD002184\",\n" +
                "                \"FDiscountRate\": 100.0,\n" +
                "                \"FMaterialId.FNumber\": \"CH1221\",\n" +
                "                \"FAllAmount\": \"1.00\",\n" +
                "                \"FEntryNote\": \"可同步产品\",\n" +
                "                \"FTaxPrice\": \"1.0000\",\n" +
                "                \"FUnitID.FNumber\": \"Pcs\",\n" +
                "                \"FQty\": \"1.00\",\n" +
                "                \"object_describe_api_name\": \"SAL_SaleOrder.SaleOrderEntry\",\n" +
                "                \"tenant_id\": \"81243\",\n" +
                "                \"_id\": \"107125\",\n" +
                "                \"id\": \"107125\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"SAL_SaleOrder.SaleOrderPlan\": [\n" +
                "            {\n" +
                "                \"fake_master_detail\": \"103365#XSDD002184\",\n" +
                "                \"FMustDate\": \"2022-08-04\",\n" +
                "                \"DetailId\": \"103202\",\n" +
                "                \"FRecAdvanceAmount\": \"1.00\",\n" +
                "                \"FRecAdvanceRate\": \"100.00\",\n" +
                "                \"FNeedRecAdvance\": \"0\",\n" +
                "                \"object_describe_api_name\": \"SAL_SaleOrder.SaleOrderPlan\",\n" +
                "                \"tenant_id\": \"81243\",\n" +
                "                \"_id\": \"103202\",\n" +
                "                \"id\": \"103202\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        StandardData standardData = JSONObject.parseObject(json,StandardData.class);
        json = "{\"channel\":\"ERP_K3CLOUD\",\"connectParams\":\"{\\\"baseUrl\\\":\\\"http://172.31.100.60/k3cloud/\\\",\\\"dbId\\\":\\\"5ec229fad54306\\\",\\\"dbName\\\":\\\"接口环境\\\",\\\"authType\\\":1,\\\"userName\\\":\\\"ces2\\\",\\\"password\\\":\\\"8888888\\\",\\\"lcid\\\":2052,\\\"useFsHttpClient\\\":true}\",\"createTime\":1627629713607,\"dataCenterName\":\"金蝶K3Cloud\",\"enterpriseName\":\"(82777)zsl测试企业027\",\"id\":\"628312575457230848\",\"tenantId\":\"81243\",\"updateTime\":1634108123414}"
        ErpConnectInfoEntity erpConnectInfoEntity = JSONObject.parseObject(json,ErpConnectInfoEntity.class)
        def result = k3DataManager.updateErpObjData(standardData, erpConnectInfoEntity)
        println(result)
    }
}
