package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 数据中心辅助
 * <AUTHOR> (^_−)☆
 * @date 2021/4/7
 */
@Service
@Slf4j
public class DataCenterManager {

    @Autowired
    private AdminSyncPloyDetailSnapshotDao syncPloyDetailSnapshotDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(cacheType = CacheType.LOCAL,timeUnit = TimeUnit.MINUTES,expire = 5)
    public String getDataCenterBySnapshotId(String tenantId,String snapshotId){
        SyncPloyDetailSnapshotEntity snapshot = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getById(tenantId, snapshotId);
        if (snapshot==null){
            log.error("get snapshot return null，apiName：{}",snapshotId);
            throw new ErpSyncDataException(I18NStringEnum.s128,tenantId);
        }
        String objApiName;
        if (snapshot.getSyncPloyDetailData().getSourceTenantType().equals(TenantType.ERP)){
            objApiName = snapshot.getSyncPloyDetailData().getSourceObjectApiName();
        }else {
            objApiName = snapshot.getSyncPloyDetailData().getDestObjectApiName();
        }
        String sourceTenantId = snapshot.getSourceTenantId();
        return getDataCenterByObjApiName(sourceTenantId,objApiName);
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Cached(cacheType = CacheType.LOCAL,timeUnit = TimeUnit.MINUTES,expire = 5)
    public Map<String,String> getResultBySnapshotId(String tenantId, String snapshotId){
        SyncPloyDetailSnapshotEntity snapshot = syncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getById(tenantId, snapshotId);
        if (snapshot==null){
            log.error("get snapshot return null，apiName：{}",snapshotId);
            throw new ErpSyncDataException(I18NStringEnum.s128,tenantId);
        }
        String objApiName;
        if (snapshot.getSyncPloyDetailData().getSourceTenantType().equals(TenantType.ERP)){
            objApiName = snapshot.getSyncPloyDetailData().getSourceObjectApiName();
        }else {
            objApiName = snapshot.getSyncPloyDetailData().getDestObjectApiName();
        }
        String sourceTenantId = snapshot.getSourceTenantId();
        String dataCenterByObjApiName = getDataCenterByObjApiName(sourceTenantId, objApiName);
        Map<String,String> resultMap= Maps.newHashMap();
        resultMap.put("streamId",snapshot.getSyncPloyDetailId());
        resultMap.put("dataCenterId",dataCenterByObjApiName);
        return resultMap;
    }

    @Cached(cacheType = CacheType.LOCAL,timeUnit = TimeUnit.MINUTES,expire = 5)
    public String getDataCenterBySyncDataId(String tenantId,String syncDataId){
        SyncDataEntity byId = adminSyncDataDao.setTenantId(tenantId).getSimple(tenantId, syncDataId);
        if (byId==null){
            log.error("get syncdata return null，syncDataId：{}",syncDataId);
            throw new ErpSyncDataException(I18NStringEnum.s129,tenantId);
        }
        return dataCenterManager.getDataCenterBySnapshotId(tenantId,byId.getSyncPloyDetailSnapshotId());
    }

    @LogLevel
    @Cached(cacheType = CacheType.LOCAL,localLimit = 2000,timeUnit = TimeUnit.MINUTES,expire = 5)
    public String getDataCenterByObjApiName(String tenantId,String objApiName){
        ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, objApiName);
        if (relation==null){
            log.error("get by obj return null，apiName：{}",objApiName);
            throw new ErpSyncDataException(I18NStringEnum.s130,tenantId);
        }
        return relation.getDataCenterId();
    }

    /**
     * @param tenantId
     * @param dcId
     * @return 0开始
     */
    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 5)
    public int getDataCenterSeq(String tenantId, String dcId) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if (connectInfo == null) {
            throw new ErpSyncDataException(I18NStringEnum.s131, tenantId);
        }
        Connector connector = AllConnectorUtil.getByChannelAndConnectParam(connectInfo.getChannel(), connectInfo.getConnectParams());
        //从number反向获取序号
        int seq = connectInfo.getNumber() - connector.getConnectorId() * 100;
        return seq;
    }

    @Cached(cacheType = CacheType.LOCAL, timeUnit = TimeUnit.MINUTES, expire = 5)
    public boolean onlyOneErpDataCenter(String tenantId) {
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoManager.listByTenantId(tenantId);
        long erpCount = erpConnectInfoEntities.stream().filter(v -> v.getChannel().getConnectorType() == ConnectorTypeEnum.ERP).count();
        return erpCount == 1;
    }
}
