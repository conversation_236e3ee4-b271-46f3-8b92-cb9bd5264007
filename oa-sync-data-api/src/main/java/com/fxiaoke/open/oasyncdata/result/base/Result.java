package com.fxiaoke.open.oasyncdata.result.base;


import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.oasyncdata.result.Result2;
import com.github.trace.TraceContext;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Getter
@Setter
@ApiModel
public class Result<T> extends BaseResult implements Serializable {
    private static final long serialVersionUID = -1407891263555853715L;

    @ApiModelProperty("数据")
    protected T data;

    public Result() {
        this.errCode = ResultCodeEnum.SUCCESS.getErrCode();
        this.errMsg = ResultCodeEnum.SUCCESS.getErrMsg();
        this.i18nKey = ResultCodeEnum.SUCCESS.getI18nKey();
        this.traceMsg = TraceContext.get().getTraceId();
    }

    public Result(T data) {
        this.errCode = ResultCodeEnum.SUCCESS.getErrCode();
        this.errMsg = ResultCodeEnum.SUCCESS.getErrMsg();
        this.i18nKey = ResultCodeEnum.SUCCESS.getI18nKey();
        this.data = data;
        this.traceMsg = TraceContext.get().getTraceId();
    }

    public Result(ResultCodeEnum resultCode) {
        this.errCode = resultCode.getErrCode();
        this.errMsg = resultCode.getErrMsg();
        this.i18nKey = resultCode.getI18nKey();
        this.traceMsg = TraceContext.get().getTraceId();
    }

    public Result(ResultCodeEnum resultCode, Object... formats) {
        this.errCode = resultCode.getErrCode();
        this.errMsg = String.format(resultCode.getErrMsg(), formats);
        this.i18nKey = resultCode.getI18nKey();
        this.i18nExtra = new ArrayList<>();
        for(Object format : formats) {
            if(format==null) continue;
            this.i18nExtra.add(format.toString());
        }
        this.traceMsg = TraceContext.get().getTraceId();
    }

    public Result(String errCode, String errMsg, T data) {
        this.errCode = errCode;
        this.errMsg = errMsg;
        this.data = data;
        this.traceMsg = TraceContext.get().getTraceId();
    }

    private static <R> Result<R> newInstance(String errCode, String errMsg, R data) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setData(data);
        result.setTraceMsg(TraceContext.get().getTraceId());
        return result;
    }

    private static <R> Result<R> newInstanceByI18N(String errCode, String errMsg, R data, String i18nKey, List<String> i18nExtra) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setI18nKey(i18nKey);
        result.setI18nExtra(i18nExtra);
        result.setData(data);
        result.setTraceMsg(TraceContext.get().getTraceId());
        return result;
    }

    private static <R> Result<R> newInstance(ResultCodeEnum resultCode, R data) {
        return newInstanceByI18N(resultCode.getErrCode(), resultCode.getErrMsg(), data, resultCode.getI18nKey(),null);
    }

    private static <R> Result<R> newInstanceByI18N(ResultCodeEnum resultCode, R data, String i18nKey, List<String> i18nExtra) {
        return newInstanceByI18N(resultCode.getErrCode(), resultCode.getErrMsg(), data, i18nKey, i18nExtra);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode) {
        return newInstanceByI18N(resultCode.getErrCode(), resultCode.getErrMsg(), null, resultCode.getI18nKey(), null);
    }

    public static <R> Result<R> newError(String resultMsg) {
        return newError(ResultCodeEnum.SYSTEM_ERROR, resultMsg);
    }

    public static <R> Result<R> newSystemError(I18NStringEnum resultCode, String... extra) {
        return newErrorWithData(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), resultCode, null, extra);
    }

    public static <R> Result<R> newErrorByI18N(String resultMsg, String i18nKey, List<String> i18nExtra) {
        return newErrorByI18N(ResultCodeEnum.SYSTEM_ERROR, resultMsg, i18nKey, i18nExtra);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, String msg) {
        return newErrorByI18N(resultCode.getErrCode(), msg,null,null);
    }

    public static <R> Result<R> newErrorByI18N(ResultCodeEnum resultCode, String msg, String i18nKey, List<String> i18nExtra) {
        return newErrorByI18N(resultCode.getErrCode(), msg, i18nKey, i18nExtra);
    }

    public static <R> Result<R> newError(ResultCodeEnum resultCode, R data) {
        return newInstanceByI18N(resultCode, data, resultCode.getI18nKey(), null);
    }

    public static <R> Result<R> newError(String errCode, String errMsg) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        return result;
    }

    public static <R> Result<R> newErrorByI18N(String errCode, String errMsg, String i18nKey, List<String> i18nExtra) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setI18nKey(i18nKey);
        result.setI18nExtra(i18nExtra);
        return result;
    }

    public static <R> Result<R> newError(String errCode, I18NStringEnum resultCode, String... extra) {
        return newErrorWithData(errCode, resultCode, null, extra);
    }

    private static <R> @NotNull Result<R> newErrorWithData(String errCode, I18NStringEnum resultCode, R data, String[] extra) {
        List<String> extraList = null;
        if (extra != null) {
            extraList = new ArrayList<>(Arrays.asList(extra));
        }

        String msg = resultCode.getI18nValue();
        if (Objects.nonNull(extra)) {
            msg = String.format(msg, extra);
        }

        return newInstanceByI18N(errCode, msg, data, resultCode.getI18nKey(), extraList);
    }

    public static <R> Result<R> newError(String errCode, String errMsg, String traceMsg) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setTraceMsg(traceMsg);
        return result;
    }

    public static <R> Result<R> newErrorByI18N(String errCode, String errMsg, String traceMsg, String i18nKey, List<String> i18nExtra) {
        Result<R> result = new Result<>();
        result.setErrCode(errCode);
        result.setErrMsg(errMsg);
        result.setI18nKey(i18nKey);
        result.setI18nExtra(i18nExtra);
        result.setTraceMsg(traceMsg);
        return result;
    }

    public static <R> Result<R> newSuccess() {
        return newInstance(ResultCodeEnum.SUCCESS, null);
    }

    public static <R> Result<R> newSuccess(R data) {
        return newInstance(ResultCodeEnum.SUCCESS, data);
    }

    public static <R> Result<R> newSuccessByI18N(R data,String i18nKey, List<String> i18nExtra) {
        return newInstanceByI18N(ResultCodeEnum.SUCCESS, data,i18nKey,i18nExtra);
    }

    public boolean isSuccess() {
        return ResultCodeEnum.SUCCESS.getErrCode().equals(this.errCode);
    }

    public T safeData(){
        if (isSuccess()){
            return data;
        }
        throw new ErpSyncDataException(this.errCode,this.errMsg,null,null);
    }


    /**
     * 复制错误结果，不复制数据
     *
     * @param result
     * @param <R>
     * @return
     */
    public static <R> Result<R> copy(BaseResult result) {
        return newInstanceByI18N(result.getErrCode(), result.getErrMsg(), null,result.getI18nKey(),result.getI18nExtra());
    }

    /**
     * 复制平台结果
     *
     * @param result
     * @param <R>
     * @return
     */
    public static <R> Result<R> copy(Result2<R> result) {
        if (result.isSuccess()) {
            return newSuccessByI18N(result.getData(),result.getI18nKey(),result.getI18nExtra());
        } else {
            return newInstanceByI18N(ResultCodeEnum.PLAT_FORM_ERROR.getErrCode(),
                    result.getErrMsg(),
                    null,
                    ResultCodeEnum.PLAT_FORM_ERROR.getI18nKey(),
                    null);
        }
    }

    @Override
    public String toString() {
        return "Result{" +
                "data=" + data +
                ", errCode='" + errCode + '\'' +
                ", errMsg='" + errMsg + '\'' +
                ", traceMsg='" + traceMsg + '\'' +
                '}';
    }
}
