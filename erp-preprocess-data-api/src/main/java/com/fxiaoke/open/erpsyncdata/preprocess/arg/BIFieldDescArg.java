package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/3/11 17:05
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BIFieldDescArg implements Serializable {
    private Map<String,String> descMap;
    @Data
    public static class CreateFieldDescArg implements Serializable{
        private Integer ei;
        private String enumTypeName;
        private Map<String,String> enumCodeToNameMap;
        private List<String> enumCodeList;

    }
    @Data
    public static class BIResult implements Serializable{
        private int statusCode;
        private String statusPhrase;

        public  boolean isSuccess(){
            if(0==this.statusCode){
                return true;
            }
            return false;
        }
    }

    @Data
    public static class BICRUDFiledResult implements Serializable{
        private int errCode;
        private String errMessage;
        private List<BICRUDFiledDataResult> result;
        public  boolean isSuccess(){
            if(200==this.errCode){
                return true;
            }
            return false;
        }
    }

    @Data
    public static class BICRUDFiledDataResult implements Serializable{
        //{
        //            "ei": 88521,
        //            "enumTypeName": "bi_erp_data_screen_dataCenterId",
        //            "enumCode": "653623d5b4660c00014c129a",
        //            "enumName": "云星辰"
        //        },
        private Integer ei;
        private String enumTypeName;
        private String enumCode;
        private String enumName;

    }

    /**
     * @see
     */
    @Data
    public static class FilterList implements Serializable{
        private String filterId;
        private String value1;//SelectValue
        private String value2;
        private String dateRangeId;

    }

    @Data
    public static class SelectValue implements Serializable{
        private String nodeName;//单选名称
        private Integer isSelected;//是否选择 0 未选择 1 选择
        private String enumId;//单选code
        private String optionCode;//单选code
        private String parentID;//父节点
        private Integer isHaveChild;//是否有子节点 0 无 1 有

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DashboardFilterEnum implements Serializable{
        private String filterId;
        private String type;
        private String label;
        private String operateType;
        private String fieldName;

    }
}
