package com.fxiaoke.open.erpsyncdata.preprocess.util;

import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.google.common.collect.Lists;

/**
 * 库存工具类
 * <AUTHOR>
 * @date 2021.11.11
 */
public class StockUtils {
    public static ObjectData queryStockData(MetadataControllerService metadataControllerService,
                                            String tenantId,
                                            String product_id,
                                            String warehouse_id,
                                            I18NStringManager i18NStringManager) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilter("warehouse_id", Lists.newArrayList(warehouse_id),"EQ");
        searchTemplateQuery.addFilter("product_id",Lists.newArrayList(product_id),"EQ");

        ControllerListArg listArg = new ControllerListArg();

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(1);
        searchQuery.addFilter("warehouse_id",Lists.newArrayList(warehouse_id),"EQ");
        searchQuery.addFilter("product_id",Lists.newArrayList(product_id),"EQ");

        listArg.setSearchQuery(searchQuery);

        Result<Page<ObjectData>> result = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_STOCK.getObjApiName(),listArg);
        if(result.isSuccess() && result.getData().getTotal()>0) {
            return result.getData().getDataList().get(0);
        }
        return null;
    }
}
