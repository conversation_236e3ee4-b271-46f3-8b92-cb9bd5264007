package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.DataVerificationTaskStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataVerificationTask;
import com.mongodb.client.MongoCollection;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:11 2022/11/9
 * @Desc:
 */
@Ignore
public class DataVerificationTaskDaoTest extends BaseTest {
    @Autowired
    private DataVerificationTaskDao dataVerificationTaskDao;

    private String tenantId="84801";
    private String streamId="a78b668ae39849b284f9e8e696d247cb";
    private String taskId="5b34771d7ef542e6a44550e46d39eaa8";
    @Test
    public void queryDataVerificationTasks() {
        List<DataVerificationTask> dataVerificationTasks = dataVerificationTaskDao.queryDataVerificationTasks(tenantId, streamId, taskId);
        List<DataVerificationTask> dataVerificationTasks1 = dataVerificationTaskDao.queryDataVerificationTasks(tenantId, streamId, null);
        System.out.println("");
    }

    @Test
    public void createDataVerificationTask() {
        String dataVerificationTask = dataVerificationTaskDao.createDataVerificationTask(null, tenantId, streamId, null, null,
                null, 0, 0, 0, 0,0,0,0, DataVerificationTaskStatusEnum.doing, new Date(), new Date());
        System.out.println("");
    }

    @Test
    public void createOrUpdateDataVerificationTaskById() {

    }

    @Test
    public void updateDataVerificationTaskById() {

    }

    @Test
    public void getOrCreateDataVerificationTaskCollection() {
        MongoCollection<DataVerificationTask> orCreateDataVerificationTaskCollection = dataVerificationTaskDao.getOrCreateDataVerificationTaskCollection();
        System.out.println("");
    }

}