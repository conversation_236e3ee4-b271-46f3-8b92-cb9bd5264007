package com.fxiaoke.open.erpsyncdata.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2023/4/23 17:32
 * 注解在需要加解密的实体类
 * @Version 1.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD,ElementType.PARAMETER})
public @interface SecurityField {
}
