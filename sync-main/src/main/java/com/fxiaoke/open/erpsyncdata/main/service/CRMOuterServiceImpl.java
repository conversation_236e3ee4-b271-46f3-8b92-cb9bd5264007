package com.fxiaoke.open.erpsyncdata.main.service;

import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.GetDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.arg.v3.FindV3Arg;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.PartnerFieldContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.FindNameByIdsResult;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.result.ProductCategoryResult;
import com.fxiaoke.crmrestapi.result.v3.ObjectDataGetByIdV3Result;
import com.fxiaoke.crmrestapi.service.*;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.result.BatchGetOuterTenantIdByEaResult;
import com.fxiaoke.enterpriserelation2.result.GetFsAccountByOuterResult;
import com.fxiaoke.enterpriserelation2.result.GetOuterAccountByFsResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.*;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.exception.SyncDataException;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorDataHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.node.NodeHelper;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ExecuteCustomFunctionArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.impl.AbsOuterServiceImpl;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;

@Service
@Slf4j
public class CRMOuterServiceImpl extends AbsOuterServiceImpl {
    @Autowired
    private NFileStorageService nFileStorageService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private FxiaokeAccountService fxiaokeAccountService;
    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    @Autowired
    private PublicEmployeeService publicEmployeeService;
    @Autowired
    private SkuSpuService skuSpuService;
    @Autowired
    private CustomFunctionService customFunctionService;
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CrmRemoteService crmRemoteService;

    private static Set<String> enterpriseWhiteSet = new HashSet<>();

    private static ThreadLocal<Map<String, ObjectData>> CRM_OBJECT_DATA_LOCAL = new ThreadLocal<>();

    @PostConstruct
    private void init() {
        ConfigFactory.getConfig("erp-sync-data-all", config -> {
            String enterpriseWhiteListJson = config.get("product.category.enterprise.white.list", "[]");
            enterpriseWhiteSet = JSON.parseObject(enterpriseWhiteListJson, Set.class);
        });
    }

    @Override
    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    public Result2<BatchGetOuterTenantIdByEaData> batchGetOuterTenantIdByEa(Integer ei, List<String> eas) {
        BatchGetOuterTenantIdByEaArg batchGetOuterTenantIdByEaArg = new BatchGetOuterTenantIdByEaArg();
        batchGetOuterTenantIdByEaArg.setEas(eas);
        BatchGetOuterTenantIdByEaResult result = fxiaokeAccountService.batchGetOuterTenantIdByEa(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(ei), batchGetOuterTenantIdByEaArg)
                .getData();
        return Result2.newSuccess(this.convert(result));
    }

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    @Override
    public Result2<String> getMasterObjectApiName(String tenantId, Integer tenantType, String objectApiName) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ObjectDescribe describeResult = objectDescribeService.getDescribe(headerObj, objectApiName).getData().getDescribe();
        for (FieldDescribe fieldDescribe : describeResult.getFields().values()) {
            if (fieldDescribe.getType().equals(FieldTypeContants.MASTER_DETAIL) && fieldDescribe.getIsActive()) {
                return Result2.newSuccess(fieldDescribe.getTargetApiName());
            }
        }
        return Result2.newSuccess();
    }

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    public Result2<String> getReferName(String tenantId, Integer tenantType, String objectApiName, String id) {
        if (StringUtils.isEmpty(id)) {
            return Result2.newSuccess();
        }
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        com.fxiaoke.crmrestapi.common.result.Result<FindNameByIdsResult> dataNamesResult = objectDataService.findNameByIds(headerObj, objectApiName, Lists.newArrayList(id));
        if (dataNamesResult.getData() == null) {
            return Result2.newSuccess();
        }
        String dataName = dataNamesResult.getData().getNameList().isEmpty() ? null : dataNamesResult.getData().getNameList().get(0).getName();
        return Result2.newSuccess(dataName);
    }

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    public Result2<String> getDeptName(String tenantId, Integer tenantType, String detpId) {
        GetDepartmentArg arg = new GetDepartmentArg();
        arg.setDepartmentId(Integer.valueOf(detpId));
        arg.setEnterpriseId(Integer.valueOf(tenantId));
        arg.setCurrentEmployeeId(1000);
        GetDepartmentResult getDepartmentResult = departmentService.getDepartment(arg);
        Department department = getDepartmentResult.getDepartment();
        if (department == null) {
            return Result2.newSuccess();
        }
        return Result2.newSuccess(department.getName());
    }

    @Override
    public Result2<List<ObjectData>> listObjectDatas(String tenantId, Integer tenantType, String objectApiName, Map<String, List<String>> filterFieldValues, Integer limit, Integer offset) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        SearchQuery searchQuery = new SearchQuery();
        for (Entry<String, List<String>> entry : filterFieldValues.entrySet()) {
            searchQuery.addFilter(entry.getKey(), entry.getValue(), FilterOperatorEnum.EQ);
        }
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setSearchQuery(searchQuery);
        List<ObjectData> dataList = new ArrayList<>();
        Page<com.fxiaoke.crmrestapi.common.data.ObjectData> detalPage = metadataControllerService.list(headerObj, objectApiName, controllerListArg).getData();
        if (detalPage != null && detalPage.getDataList() != null) {
            for (com.fxiaoke.crmrestapi.common.data.ObjectData crmObjectData : detalPage.getDataList()) {
                ObjectData objectData = new ObjectData();
                objectData.putAll(crmObjectData);
                dataList.add(objectData);
            }
        }
        return Result2.newSuccess(dataList);
    }

    private Filter buildFilter(String fieldName, String operator, List<String> fieldValues) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }

    private Wheres buildWheresByFilters(List<Filter> filters) {
        Wheres wheres = new Wheres();
        wheres.setFilters(filters);
        return wheres;
    }

    @Override
    public List<List<FilterData>> changeVariableFilter(List<List<FilterData>> filters, String sourceTenantId, String destTenantId) {
        List<List<FilterData>> filtersResult = new ArrayList<>();
        //隐藏的从对象可能是没有filter的，所以需判空
        if (filters == null) {
            return filtersResult;
        }
        for (List<FilterData> filterDatas : filters) {
            List<FilterData> filterDataResult = new ArrayList<>();
            for (FilterData filterData : filterDatas) {
                if ("variable".equals(filterData.getType())) {
                    filterData = (FilterData) convert(filterData, sourceTenantId, destTenantId);
                }
                filterDataResult.add(filterData);
            }
            filtersResult.add(filterDataResult);
        }
        return filtersResult;
    }

    private Object convert(FilterData filterData, String sourceTenantId, String destTenantId) {
        Integer srcEi = Integer.valueOf(sourceTenantId);
        Integer destEi = Integer.valueOf(destTenantId);
        List<String> fieldValues = filterData.getFieldValue();
        String fieldValue = fieldValues.get(0);
        if (org.springframework.util.StringUtils.isEmpty(fieldValue)) {
            return filterData;
        }
        String variableType = filterData.getVariableType();
        String result;
        if (org.springframework.util.StringUtils.isEmpty(variableType)) {
            return filterData;
        }
        if (variableType.equals(ConditionVariableEnum.REFERENCE_ACCOUNT.getValue())) {
            result = getMapperObjectId(srcEi, destEi, AccountFieldContants.API_NAME).getData();
        } else if (variableType.equals(ConditionVariableEnum.REFERENCE_PARTNER.getValue())) {
            result = getMapperObjectId(srcEi, destEi, PartnerFieldContants.API_NAME).getData();
        } else {
            throw new SyncDataException(ResultCodeEnum.CONDITION_TYPE_NOT_EXIST.getErrCode(), ResultCodeEnum.CONDITION_TYPE_NOT_EXIST.getErrMsg());
        }
        List<String> fieldValueResult = new ArrayList<>();
        fieldValueResult.add(result);
        filterData.setFieldValue(fieldValueResult);
        return filterData;
    }

    @Override
    public Result2<Long> getDownstreamRelationOwnerOuterUid(Integer upstreamEi, Integer downstreamEi) {
        String upstreamEa = eieaConverter.enterpriseIdToAccount(upstreamEi);
        String downstreamEa = eieaConverter.enterpriseIdToAccount(downstreamEi);
        Map<String, Long> ea2OuterTenantIdMap = this.batchGetOuterTenantIdByEa(upstreamEi, Lists.newArrayList(upstreamEa, downstreamEa)).getData().getEa2OuterTenantIdMap();
        Long upstreamOuterTenantId = ea2OuterTenantIdMap.get(upstreamEa);
        Long downstreamOuterTenantId = ea2OuterTenantIdMap.get(downstreamEa);
        GetOutUidArg getOutUidArg = new GetOutUidArg();
        getOutUidArg.setUpstreamOuterTenantId(upstreamOuterTenantId);
        getOutUidArg.setDownstreamOuterTenantId(downstreamOuterTenantId);
        return Result2.newSuccess(publicEmployeeService.getDownstreamRelationOwnerOuterUid(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(upstreamEi), getOutUidArg).getData());
    }

    @Override
    public Result2<ObjectData> getObjectData(String tenantId, Integer tenantType, String objectApiName, String id) {
        if (StringUtils.isEmpty(id)) {
            return Result2.newSuccess();
        }
        ObjectData localObjectData = getLocalObjectData(id, objectApiName, tenantId);
        if (ObjectUtils.isNotEmpty(localObjectData)) {
//            log.info("cache catch objectData ");
            return Result2.newSuccess(localObjectData);
        }
        //大部分情况下是catch的，不catch的时候来打印日志
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        //子阳这边说底层已经做过优化，会自动选择该走DB还是ES
        searchQueryInfo.setSearchSource("db");
        searchQueryInfo.addFilter("_id", Collections.singletonList(id), FilterOperatorEnum.EQ);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2", "-1", "0", "1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> resultResult = objectDataServiceV3.findOne(headerObj, findV3Arg);
        try {
            ObjectDataGetByIdV3Result data = resultResult.getData();
            if (data == null || data.getObjectData() == null) {
                log.warn("not found data,obj:{},id:{}", objectApiName, id);
                return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), i18NStringManager.getByEi(I18NStringEnum.s985,tenantId));
            }
            ObjectData objectData = new ObjectData();
            objectData.putAll(data.getObjectData());
            return Result2.newSuccess(objectData);
        } catch (CrmBusinessException e) {
            log.warn("getObjectData CrmBusinessException:{}", e.toString());
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), e.toString());
        }
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Override
    public Result2<ObjectData> getObjectDataByName(String tenantId, Integer tenantType, String objectApiName, String name) {
        if (StringUtils.isEmpty(name)) {
            return Result2.newSuccess();
        }
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(true);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        //子阳这边说底层已经做过优化，会自动选择该走DB还是ES
        searchQueryInfo.setSearchSource("db");
        searchQueryInfo.addFilter("name", Collections.singletonList(name), FilterOperatorEnum.EQ);
        //作废、正常、已删除数据都查询
        searchQueryInfo.addFilter("is_deleted", Lists.newArrayList("-2", "-1", "0", "1"), FilterOperatorEnum.IN);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> resultResult = objectDataServiceV3.findOne(headerObj, findV3Arg);
        ObjectData objectData = new ObjectData();
        try {
            ObjectDataGetByIdV3Result data = resultResult.getData();
            if (data == null || data.getObjectData() == null) {
                log.warn("not found data,obj:{},name:{}", objectApiName, name);
                return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), i18NStringManager.getByEi(I18NStringEnum.s985,tenantId));
            }
            objectData.putAll(data.getObjectData());
        } catch (CrmBusinessException e) {
            log.warn("getObjectData CrmBusinessException = " + e.toString());
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), e.toString());
        }
        return Result2.newSuccess(objectData);
    }
    @Override
    public Result2<ObjectData> getDetailById(String tenantId, String apiName, String id) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        SearchQuery searchQuery = new SearchQuery();
        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setObjectDataId(id);
        controllerDetailArg.setIncludeDescribe(false);
        controllerDetailArg.setIncludeLayout(false);
        controllerDetailArg.setObjectDescribeApiName(apiName);
        ObjectData objectData = new ObjectData();
        objectData.putAll(metadataControllerService.detail(headerObj, apiName, controllerDetailArg).getData().getData());
        return Result2.newSuccess(objectData);
    }

    @Override
    public Result2<Integer> getOwnerId(Integer tenantId, Integer tenantType, String objectApiName, String id) {
        com.fxiaoke.crmrestapi.common.data.ObjectData objectData = com.fxiaoke.crmrestapi.common.data.ObjectData
                .convert(this.getObjectData(String.valueOf(tenantId), tenantType, objectApiName, id).getData());
        return Result2.newSuccess(objectData.getOwner());
    }

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    @Override
    public Result2<Map<String, String>> batchGetEmployeeFieldValue(String tenantId, Integer tenantType, List<String> employeeIds, String fieldName) {
        Map<String, String> names = new HashedMap();
        for (String employeeId : employeeIds) {
            GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
            arg.setEnterpriseId(Integer.valueOf(tenantId));
            arg.setEmployeeId(Integer.valueOf(employeeId));
            GetEmployeeDtoResult getEmployeeDtoResult = employeeService.getEmployeeDto(arg);
            EmployeeDto employeeDto = getEmployeeDtoResult.getEmployee();
            if (employeeDto != null) {
                if (fieldName == null || fieldName.equals("name")) {
                    names.put(employeeId, employeeDto.getName());
                } else if (fieldName.equals("mobile")) {
                    names.put(employeeId, employeeDto.getMobile());
                }
            }
        }
        return Result2.newSuccess(names);
    }

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    @Override
    public Result2<GetOuterAccountByFsData> getOuterAccountByFs(Integer upstreamEi, Integer ei, Integer userId) {
        GetOuterAccountByFsArg arg = new GetOuterAccountByFsArg();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        arg.setEa(ea);
        arg.setFsUserId(userId);
        GetOuterAccountByFsResult result = fxiaokeAccountService.getOuterAccountByFs(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(upstreamEi), arg).getData();
        return Result2.newSuccess(this.convert(result));
    }

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    @Override
    public Result2<GetFsAccountByOuterData> getFsAccountByOuter(Long outerUid) {
        GetFsAccountByOuterArg arg = new GetFsAccountByOuterArg();
        arg.setOuterUid(outerUid);
        GetFsAccountByOuterResult result = fxiaokeAccountService.getFsAccountByOuter(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(0), arg).getData();
        return Result2.newSuccess(this.convert(result));
    }

    private GetFsAccountByOuterData convert(GetFsAccountByOuterResult source) {
        if (source == null) {
            return null;
        }
        GetFsAccountByOuterData result = new GetFsAccountByOuterData();
        result.setEa(source.getEa());
        result.setEmployeeId(source.getEmployeeId());
        result.setOuterTenantId(source.getOuterTenantId());
        return result;
    }

    private GetOuterAccountByFsData convert(GetOuterAccountByFsResult source) {
        if (source == null) {
            return null;
        }
        GetOuterAccountByFsData result = new GetOuterAccountByFsData();
        result.setOuterTenantId(source.getOuterTenantId());
        result.setOuterUid(source.getOuterUid());
        return result;
    }

    @Override
    @Cached(expire = 5, cacheType = CacheType.LOCAL)
    public Result2<String> getMapperObjectId(Integer upstreamEi, Integer downstreamEi, String objectApiName) {
        String upstreamEa = eieaConverter.enterpriseIdToAccount(upstreamEi);
        String downstreamEa = eieaConverter.enterpriseIdToAccount(downstreamEi);
        BatchGetOuterTenantIdByEaData batchGetOuterTenantIdByEaData = this.batchGetOuterTenantIdByEa(0, Lists.newArrayList(upstreamEa, downstreamEa)).getData();
        if (batchGetOuterTenantIdByEaData == null) {
            return null;
        }
        Long downstreamOuterTenantId = batchGetOuterTenantIdByEaData.getEa2OuterTenantIdMap().get(downstreamEa);
        UpstreamAndDownstreamOuterTenantIdOutArg arg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        arg.setUpstreamEa(upstreamEa);
        arg.setDownstreamOuterTenantId(downstreamOuterTenantId);
        arg.setObjectApiName(objectApiName);
        return Result2.newSuccess(enterpriseRelationService.getMapperObjectId(com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(upstreamEi), arg).getData());
    }

    @Override
    public Result2<List<Map<String, String>>> convertFiles(String sourceTenantId, Integer sourceTenantType, String type, List<Map<String, String>> sourceFiles, String destTenantId,
                                                           Integer destTenantType) {
        List<Map<String, String>> destFiles = new ArrayList<>(sourceFiles.size());
        String sourceEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(sourceTenantId));
        String destEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(destTenantId));
        for (Map<String, String> value : sourceFiles) {
            String path = value.get("path");
            String ext = value.get("ext");
            byte[] bytes = downloadFile(sourceEa, path);
            Map<String, String> destFile = uploadFile(destEa, type, ext, bytes);
            destFiles.add(destFile);
        }
        return Result2.newSuccess(destFiles);
    }

    private byte[] downloadFile(String ea, String path) {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setEa(ea);
        arg.setDownloadUser("E." + ea + ".1000");
        arg.setnPath(path);
        return nFileStorageService.nDownloadFile(arg, ea).getData();
    }

    private Map<String, String> uploadFile(String ea, String type, String ext, byte[] data) {
        NUploadFileDirect.Arg nFileArg = new NUploadFileDirect.Arg();
        nFileArg.setEa(ea);
        nFileArg.setSourceUser("E." + ea + ".1000");
        nFileArg.setData(data);
        nFileArg.setFileExt(ext);
        nFileArg.setFileSecurityGroup("");
        nFileArg.setBusiness("syncdata");
        String pathname = null;
        if (FieldType.IMAGE.equals(type)) {
            pathname = createPath();
            //+1 表示是图片类型
            nFileArg.setNamedFilePath(pathname + "1");
        }
        NUploadFileDirect.Result result = nFileStorageService.nUploadFileDirect(nFileArg, ea);
        Map<String, String> fileData = new HashedMap();
        fileData.put("ext", ext);
        if (pathname == null) {
            pathname = result.getFinalNPath();
        }
        fileData.put("path", pathname);
        return fileData;
    }

    /**
     * 翟付杰 给的逻辑，当是图片类型的时候需要自己加逻辑
     */
    private String createPath() {
        LocalDate localDate = LocalDate.now();
        String pathPrefix = "N";
        return Joiner.on('_')
                .join(pathPrefix, localDate.format(DateTimeFormatter.ofPattern("yyyyMM")), localDate.format(DateTimeFormatter.ofPattern("dd")), UUID.randomUUID().toString().replaceAll("-", ""));
    }

    private BatchGetOuterTenantIdByEaData convert(BatchGetOuterTenantIdByEaResult source) {
        if (source == null) {
            return null;
        }
        BatchGetOuterTenantIdByEaData result = new BatchGetOuterTenantIdByEaData();
        result.setEa2OuterTenantIdMap(source.getEa2OuterTenantIdMap());
        return result;
    }

    @Override
    public Result2<FunctionServiceExecuteReturnData> executeCustomFunction(String tenantId,
                                                                           String erpDataCenterId,
                                                                           ExecuteCustomFunctionArg arg,
                                                                           ErpObjInterfaceUrlEnum interfaceUrl,
                                                                           CustomFunctionTypeEnum functionType, final String ployDetailSnapshotId, String objectName) {
        FunctionServiceExecuteArg functionServiceExecuteArg = new FunctionServiceExecuteArg();
        functionServiceExecuteArg.setApiName(arg.getApiName());
        functionServiceExecuteArg.setNameSpace(arg.getNameSpace());
        // 函数做了校验,不能和配置的不一致
        functionServiceExecuteArg.setBindingObjectAPIName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        List<FunctionServiceParameterData> functionServiceParameterDataList = new ArrayList<>();
        FunctionServiceParameterData<Map> functionServiceParameterData = new FunctionServiceParameterData();
        functionServiceParameterData.setName(CustomFunctionConstant.SYNC_ARG_NAME);
        functionServiceParameterData.setType(CustomFunctionConstant.SYNC_ARG_TYPE_MAP);
        Map<String, Object> dataValueMap = new HashMap<>();
        dataValueMap.put(CustomFunctionParameterEnum.SYNC_DATA_ID.getArgName(), arg.getParameter().getSyncDataId());
        dataValueMap.put(CustomFunctionParameterEnum.SOURCE_TENANT_ID.getArgName(), arg.getParameter().getSourceTenantId());
        dataValueMap.put(CustomFunctionParameterEnum.SOURCE_OBJECT_APINAME.getArgName(), arg.getParameter().getSourceObjectApiName());
        dataValueMap.put(CustomFunctionParameterEnum.SOURCE_EVENT_TYPE.getArgName(), arg.getParameter().getSourceEventType());
        dataValueMap.put(CustomFunctionParameterEnum.DEST_TENANT_ID.getArgName(), arg.getParameter().getDestTenantId());
        dataValueMap.put(CustomFunctionParameterEnum.DEST_OBJECT_APINAME.getArgName(), arg.getParameter().getDestObjectApiName());
        dataValueMap.put(CustomFunctionParameterEnum.DEST_EVENT_TYPE.getArgName(), arg.getParameter().getDestEventType());
        dataValueMap.put(CustomFunctionParameterEnum.OBJECT_DATA.getArgName(), arg.getObjectData());
        dataValueMap.put(CustomFunctionParameterEnum.DETAILS.getArgName(), arg.getDetails());
        dataValueMap.put(CustomFunctionParameterEnum.COMPLETE_DATA_WRITE_RESULT.getArgName(), arg.getParameter().getCompleteDataWriteMqData());
        //从节点变量取值
        NodeContext<?> context = NodeHelper.getContext();
        if (context != null) {
            dataValueMap.put(CustomFunctionParameterEnum.DATA_CENTER_ID.getArgName(), context.getDcId());
            dataValueMap.put(CustomFunctionParameterEnum.STREAM_IDS.getArgName(), context.getStreamIds());
        }
        functionServiceParameterData.setValue(dataValueMap);
        functionServiceParameterDataList.add(functionServiceParameterData);
        functionServiceExecuteArg.setParameters(functionServiceParameterDataList);
        if (Objects.nonNull(functionType) && StringUtils.isNotBlank(objectName)) {
            //同步函数 添加函数主属性
            final String crmObjectApiName = Objects.equals(functionType, CustomFunctionTypeEnum.BEFORE_FUNCTION) ? arg.getParameter().getSourceObjectApiName() : arg.getParameter().getDestObjectApiName();
            functionServiceExecuteArg.setObjectData(ImmutableMap.of("name", objectName, "object_describe_api_name", crmObjectApiName));
        }
        com.fxiaoke.otherrestapi.function.data.HeaderObj headerObj = new com.fxiaoke.otherrestapi.function.data.HeaderObj(Integer.parseInt(tenantId), CrmConstants.SYSTEM_USER);
        Result2<FunctionServiceExecuteReturnData> result = customFunctionService.currencyFunction(tenantId,
                erpDataCenterId,
                headerObj,
                functionServiceExecuteArg,
                interfaceUrl, functionType, ployDetailSnapshotId);
        if (!result.isSuccess()) {
            return Result2.newError(result.getIntErrCode(), result.getErrMsg());
        }
        return Result2.newSuccess(result.getData());
    }

    @Override
    @Cached(expire = 5 * 60 * 60, cacheType = CacheType.LOCAL)
    public Result2<ProductCategoryData> getSourceProductCategoryValue(String tenentId, String code) {
        if (!(enterpriseWhiteSet.contains(tenentId))) {
            //没有白名单权限直接返回
            return Result2.newError(ResultCodeEnum.NOT_HAVE_AUTH.getErrCode(), ResultCodeEnum.NOT_HAVE_AUTH.getErrMsg());
        }
        Result2<Map<String, ProductCategoryData>> result = this.listProductCategory(tenentId);
        if (!result.isSuccess()) {
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), ResultCodeEnum.SYSTEM_ERROR.getErrMsg());
        }
        return Result2.newSuccess(result.getData().get(code));
    }

    @Override
    @Cached(expire = 5 * 60 * 60, cacheType = CacheType.LOCAL)
    public Result2<ProductCategoryData> getOrCreateDestProductCategoryValue(String tenentId, String categoryName, String categoryCode, Integer orderField) {
        Result2<Map<String, ProductCategoryData>> result = this.listProductCategory(tenentId);
        if (!result.isSuccess()) {
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), ResultCodeEnum.SYSTEM_ERROR.getErrMsg());
        }
        Map<String, ProductCategoryData> productCategoryDataMap = this.listProductCategory(tenentId).getData();
        for (ProductCategoryData productCategoryData : productCategoryDataMap.values()) {
            if (categoryCode.equals(productCategoryData.getCategoryCode())) {
                return Result2.newSuccess(productCategoryData);
            }
        }
        //如果没有拿到目标企业产品分类，那么就为目标企业新建产品分类
        HeaderObj headerObj = new HeaderObj(Integer.parseInt(tenentId), CrmConstants.SYSTEM_USER);
        com.fxiaoke.crmrestapi.arg.ProductCategoryArg crmProductCategoryArg = new com.fxiaoke.crmrestapi.arg.ProductCategoryArg();
        crmProductCategoryArg.setName(categoryName);
        crmProductCategoryArg.setCategoryCode(categoryCode);
        crmProductCategoryArg.setOrderField(orderField);
        com.fxiaoke.crmrestapi.common.data.ObjectData productCategoryObjectData;
        try {
            productCategoryObjectData = skuSpuService.addProductCategory(headerObj, crmProductCategoryArg).getResult();
        } catch (Exception e) {
            log.warn("skuSpuService#addProductCategory fail, tenentId = {}, crmProductCategoryArg={}", tenentId, crmProductCategoryArg);
            Result2<Map<String, ProductCategoryData>> destResult = this.listProductCategory(tenentId);
            if (!destResult.isSuccess()) {
                return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), ResultCodeEnum.SYSTEM_ERROR.getErrMsg());
            }
            Map<String, ProductCategoryData> destProductCategoryDataMap = this.listProductCategory(tenentId).getData();
            for (ProductCategoryData productCategoryData : destProductCategoryDataMap.values()) {
                if (categoryCode.equals(productCategoryData.getCategoryCode())) {
                    return Result2.newSuccess(productCategoryData);
                }
            }
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), ResultCodeEnum.SYSTEM_ERROR.getErrMsg());
        }
        ProductCategoryData productCategoryData = new ProductCategoryData();
        Double code = (Double) productCategoryObjectData.get(ProductCategoryConstant.CODE);
        productCategoryData.setCode(String.valueOf(code.intValue()));
        productCategoryData.setName((String) productCategoryObjectData.get(ProductCategoryConstant.NAME));
        productCategoryData.setCategoryCode((String) productCategoryObjectData.get(ProductCategoryConstant.CATEGORY_CODE));
        return Result2.newSuccess(productCategoryData);
    }

    @Override
    public Result2<Map<String, ProductCategoryData>> listProductCategory(String tenentId) {
        HeaderObj headerObj = new HeaderObj(Integer.parseInt(tenentId), CrmConstants.SYSTEM_USER);
        ProductCategoryResult<com.fxiaoke.crmrestapi.common.data.ObjectData> objectDataProductCategoryResult;
        try {
            objectDataProductCategoryResult = skuSpuService.listProductCategory(headerObj);
        } catch (Exception e) {
            log.warn("skuSpuService#listProductCategory is fail, tenentId = {}", tenentId);
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), ResultCodeEnum.SYSTEM_ERROR.getErrMsg());
        }
        Map<String, ProductCategoryData> productCategoryDataMap = new HashMap<>();
        if (objectDataProductCategoryResult.getResult() == null && objectDataProductCategoryResult.getResult().isEmpty()) {
            return Result2.newSuccess(productCategoryDataMap);
        }
        for (com.fxiaoke.crmrestapi.common.data.ObjectData objectData : objectDataProductCategoryResult.getResult()) {
            ProductCategoryData productCategoryData = new ProductCategoryData();
            productCategoryData.setCode((String) objectData.get(ProductCategoryConstant.CODE));
            productCategoryData.setName((String) objectData.get(ProductCategoryConstant.NAME));
            productCategoryData.setCategoryCode((String) objectData.get(ProductCategoryConstant.CATEGORY_CODE));
            productCategoryData.setOrderField(Integer.parseInt((String) objectData.get(ProductCategoryConstant.ORDER_FIELD)));
            productCategoryDataMap.putIfAbsent((String) objectData.get(ProductCategoryConstant.CODE), productCategoryData);
        }
        return Result2.newSuccess(productCategoryDataMap);
    }

    @Override
    @Cached(expire = 5 * 60 * 60, cacheType = CacheType.LOCAL)
    public Result2<FieldDescribeData> getFieldDescribe(String tenantId, String apiName, String fieldApiName) {
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ListObjectDescribeResult> result = objectDescribeService.list(headerObj, true, Lists.newArrayList(apiName));
        if (CollectionUtils.isEmpty(result.getData().getDescribe())) {
            return Result2.newError(ResultCodeEnum.HAD_NOT_OBJECT_FOR_TENANT);
        }
        ObjectDescribe objectDescribe = result.getData().getDescribe().get(0);
        FieldDescribe objectDataField = objectDescribe.getFields().get(fieldApiName);
        if (objectDataField == null) {
            return Result2.newError(ResultCodeEnum.HAD_NOT_OBJECT_FIELD_DESCRIPT_FOR_TENANT);
        }
        return Result2.newSuccess(BeanUtil2.deepCopy(objectDataField, FieldDescribeData.class));
    }

    @Override
    @Cached(expire = 5 * 60 * 60, cacheType = CacheType.LOCAL)
    public Result2<String> getObjectNameByApiName(String tenantId, String apiName) {
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId), CrmConstants.SYSTEM_USER);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(headerObj, apiName);
        if (describeResult.isSuccess()) {
            return Result2.newSuccess(describeResult.getData().getDescribe().getDisplayName());
        }
        return Result2.newError(ResultCodeEnum.THIRD_APPLICATION_ERROR);
    }

    @Override
    public Result2<SyncDataContextEvent> executeCompleteWriteHook(SyncDataContextEvent message) {
        return Result2.newSuccess(message);
    }

    @Override
    public Integer getTenantType() {
        return TenantTypeEnum.CRM.getType();
    }

    @Override
    public Result2<String> getIdByNumber(String tenantId, String objectApiName, String number) {
        return Result2.newSuccess();
    }

    @Override
    public Result2<String> batchGetObjectDataAndCache(String tenantId, String objectApiName, List<String> objectDataIds) {
        final Result2<List<ObjectData>> listResult2 = crmRemoteService.batchGetObjectData(tenantId, objectApiName, objectDataIds);

        Optional.ofNullable(listResult2.getData())
                .ifPresent(list -> list.forEach(objectData -> putObjectData(objectData.getId(), objectData.getApiName(), tenantId, objectData)));

        return Result2.newSuccess();
    }

    @Override
    public Result2<Void> removeBatchCache() {
        CRM_OBJECT_DATA_LOCAL.remove();
        return null;
    }

    private void putObjectData(String objectId, String apiName, String tenantId, ObjectData objectData) {
        Map<String, ObjectData> cache = getCache();
        String combineKey = new StringBuilder().append(tenantId).append("--").append(apiName).append("--").append(objectId).toString();
        cache.put(combineKey, objectData);

    }

    private ObjectData getLocalObjectData(String objectId, String apiName, String tenantId) {

        String combineKey = new StringBuilder().append(tenantId).append("--").append(apiName).append("--").append(objectId).toString();
        ObjectData localValue = getCache().get(combineKey);
        return localValue;

    }

    private Map<String, ObjectData> getCache() {
        Map<String, ObjectData> stringObjectDataMap = CRM_OBJECT_DATA_LOCAL.get();
        if (stringObjectDataMap == null) {
            stringObjectDataMap = new HashMap<>();
            CRM_OBJECT_DATA_LOCAL.set(stringObjectDataMap);
        }
        return stringObjectDataMap;
    }

    /**
     * 只查一条数据,同步过程中查crm数据，不查作废数据，暂时不查补充字段，会记录接口日志,
     * 任意一个查询的字段值为空，不调用接口，记录日志
     *
     * @param tenantId
     * @param tenantType
     * @param objectApiName
     * @param orFilters
     * @return FindV3Arg参数：
     * selectFields：需要返回的字段
     * includeInvalid：是否返回已作废数据（默认false）
     * includeRelevantTeam：是否返回相关团队（默认false）
     * calculateFormula：是否实时计算落地的计算字段（默认false）
     * calculateQuote：是否实时计算落地的引用字段（默认false）
     * convertQuoteForView：是否以页面展示的格式返回引用字段（默认false），该参数主要针对引用单选、多选、布尔、业务类型的字段，默认返回value，设置为true以后，返回的是label，并通过${字段apiName}__v返回value，以及${字段apiName}__o返回其他选项。
     * calculateCount：是否实时计算统计字段（默认false）
     * fillExtendInfo：是否补充各种字段的__r（默认false）
     * paginationOptimization：是否执行分页优化（默认false）
     */
    @LogLevel(LogLevelEnum.TRACE)
    @Override
    public Result2<ObjectData> queryObjectDataByEqFilter(String crmDcId, String tenantId, Integer tenantType, String objectApiName, List<List<FilterData>> orFilters) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(objectApiName)
                || orFilters == null || CollectionUtils.isEmpty(orFilters)) {
            return Result2.newSuccess();
        }
        Boolean needQuery = true;
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        FindV3Arg findV3Arg = new FindV3Arg();
        findV3Arg.setDescribeApiName(objectApiName);
        findV3Arg.setIncludeInvalid(false);
        SearchTemplateQuery searchQueryInfo = new SearchTemplateQuery();
        searchQueryInfo.setPermissionType(0);
        //searchQueryInfo.setSearchSource("db");
        List<Wheres> wheres = new ArrayList<>();
        for (List<FilterData> andFilterDataList : orFilters) {
            List<Filter> andFilters = new ArrayList<>();
            for (FilterData filterData : andFilterDataList) {
                if (CollectionUtils.isEmpty(filterData.getFieldValue())) {
                    needQuery = false;
                }
                andFilters.add(buildFilter(filterData.getFieldApiName(), filterData.getOperate(), filterData.getFieldValue()));
            }
            wheres.add(this.buildWheresByFilters(andFilters));
        }
        searchQueryInfo.setWheres(wheres);
        findV3Arg.setSearchQueryInfo(GsonUtil.toJson(searchQueryInfo));
        Long callTime = System.currentTimeMillis();
        Long returnTime = System.currentTimeMillis();
        ObjectData objectData = new ObjectData();
        Result2<ObjectData> result = null;
        Integer status = 1;
        try {
            if (needQuery) {
                com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdV3Result> resultResult = objectDataServiceV3.findOne(headerObj, findV3Arg);
                ObjectDataGetByIdV3Result data = resultResult.getData();
                if (data == null || data.getObjectData() == null) {
                    log.info("not found data,tenantId:{},obj:{},searchQueryInfo:{}", tenantId, objectApiName, searchQueryInfo);
                    result = Result2.newError(ResultCodeEnum.PARAM_ERROR.getErrCode(), i18NStringManager.getByEi(I18NStringEnum.s985,tenantId));
                } else {
                    objectData.putAll(data.getObjectData());
                    result = Result2.newSuccess(objectData);
                }
            } else {
                status = 2;
                log.info("not need queryObjectDataByEqFilter  arg={}", findV3Arg);
                result = Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), i18NStringManager.getByEi(I18NStringEnum.s986,tenantId));
            }
        } catch (CrmBusinessException e) {
            status = 2;
            log.warn("queryObjectDataByEqFilter CrmBusinessException ={} arg={}", e, findV3Arg);
            result = Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), e.toString());
        } catch (Exception e) {
            status = 2;
            log.warn("queryObjectDataByEqFilter Exception ={} arg={}", e, findV3Arg);
            result = Result2.newError(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), e.toString());
        } finally {
            InterfaceMonitorData interfaceMonitorData = InterfaceMonitorDataHelper.buildInterfaceMonitorData(tenantId, crmDcId, objectApiName, ErpObjInterfaceUrlEnum.crmQuery.name(), JacksonUtil.toJson(findV3Arg), JacksonUtil.toJson(result), status,
                    callTime, returnTime, "v3/inner/rest/object_data/find_one", TraceUtil.get(), returnTime - callTime);
            interfaceMonitorManager.saveInterfaceLogAndSyncLog(interfaceMonitorData);
        }
        return result;
    }
}
