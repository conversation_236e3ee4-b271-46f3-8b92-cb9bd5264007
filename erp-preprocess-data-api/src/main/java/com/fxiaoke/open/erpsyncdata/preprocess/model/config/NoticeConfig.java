package com.fxiaoke.open.erpsyncdata.preprocess.model.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/8/5
 */
@Getter
@Setter
@ToString
public class NoticeConfig {
    @ApiModelProperty("配置id")
    private String id;
    /**
     * 通知人
     */
    @ApiModelProperty("通知人列表")
    private List<Integer> users;
    /**
     * 角色
     */
    @ApiModelProperty("通知角色列表")
    private List<String> roles;

    public boolean isOpen() {
        //判断是否启用，通过是否配置了通知人判断
        return users != null && !users.isEmpty()
                || (roles != null && !roles.isEmpty());
    }
}
