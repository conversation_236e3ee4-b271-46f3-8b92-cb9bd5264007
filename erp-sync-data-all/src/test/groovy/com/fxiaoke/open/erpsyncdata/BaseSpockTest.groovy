package com.fxiaoke.open.erpsyncdata

import lombok.extern.slf4j.Slf4j
import org.junit.runner.RunWith
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner
import spock.lang.Specification

@ContextConfiguration(["classpath:spring/spring-test.xml"])
@Slf4j
class BaseSpockTest extends Specification {

//    static String tenantId = "84801"

    static  {
        System.setProperty("process.profile", "fstest")
        System.setProperty("process.profile.candidates", "fstest")
        System.setProperty("process.name", "fs-erp-sync-data")
    }
}
