package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjDataResult;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;

import java.util.List;

public class SyncDataContextUtils {

    /**
     * 旧的发送事件结构转换成统一的结构
     * @param batchSendEventDataArg
     * @return
     */
    public static List<SyncDataContextEvent> convertEventByBatchSendEventDataArg(BatchSendEventDataArg batchSendEventDataArg) {
        List<SyncDataContextEvent> syncDataContextEvents = Lists.newArrayList();
        for (BatchSendEventDataArg.EventData eventData : batchSendEventDataArg.getEventDatas()) {
            SyncDataContextEvent syncDataContextEvent = new SyncDataContextEvent();
            BeanUtils.copyProperties(eventData, syncDataContextEvent);
            syncDataContextEvents.add(syncDataContextEvent);
        }
        return syncDataContextEvents;
    }

    public static SyncDataContextEvent convertEventByBatchSendEventDataArg(BatchSendEventDataArg.EventData eventData) {
        SyncDataContextEvent syncDataContextEvent=new SyncDataContextEvent();
        BeanUtils.copyProperties(eventData,syncDataContextEvent);
        return syncDataContextEvent;
    }

    public static List<SyncDataContextEvent> convertEventByErpObjDataResult(List<ErpObjDataResult> erpObjDataResults) {
        List<SyncDataContextEvent> syncDataContextEvents = Lists.newArrayList();
        for (ErpObjDataResult erpObjDataResult : erpObjDataResults) {
            SyncDataContextEvent syncDataContextEvent = new SyncDataContextEvent();
            BeanUtils.copyProperties(erpObjDataResult, syncDataContextEvent);
            syncDataContextEvents.add(syncDataContextEvent);
        }

        return syncDataContextEvents;
    }

    public static ErpObjDataResult convertErpObjDataResultByContext(SyncDataContextEvent contextEvent){
        ErpObjDataResult erpObjDataResult=new ErpObjDataResult();
        BeanUtils.copyProperties(contextEvent,erpObjDataResult);
        return erpObjDataResult;
    }
    public static List<ErpObjDataResult> convertErpObjDataResultByContext(List<SyncDataContextEvent> contextEvent){

        List<ErpObjDataResult> erpObjDataResults = BeanUtil.deepCopyList(contextEvent, ErpObjDataResult.class);
        return erpObjDataResults;
    }
    //BatchSendEventDataArg.EventData
    public static BatchSendEventDataArg.EventData convertSendArgResultByContext(SyncDataContextEvent contextEvent){
        BatchSendEventDataArg.EventData eventData=new BatchSendEventDataArg.EventData();
        BeanUtils.copyProperties(contextEvent,eventData);
        return eventData;
    }
}
