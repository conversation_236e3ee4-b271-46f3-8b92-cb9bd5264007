package com.fxiaoke.open.erpsyncdata.probe.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 轮询数据服务
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
public interface ProbeDataTaskService {


    /**
     * 对某企业 执行所有策略,需独立线程调用，会加锁
     * @param tenantId 企业ei
     * @param objApiName
     * @return
     */
    Result<Void> executeProbeErpDataJob(String tenantId, String objApiName);

    /**
     * 对某企业 执行所有策略,从mongo库获取需要同步的erp数据，需独立线程调用，会加锁
     * @param tenantId 企业ei
     * @return
     */
    Result<Void> executeRollingErpDataFromMongoJob(String tenantId);

    /**
     * 对某企业 执行历史数据同步任务
     * @param tenantId 企业ei
     * @return
     */
    Result<Void> executeRollingErpHistoryDataJob(String tenantId);

    /**
     * 执行策略，会进入队列，并尝试触发轮询。
     * 无论定时任务还是主动触发的轮询，轮询结束后会重新检查队列。
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    Result<Void> enqueueAndTryExecute(String tenantId, String objApiName);

    /**
     * 触发轮询临时库，会进入队列，并尝试出队触发轮询临时库。
     * 轮询结束后会重新检查队列。
     *
     * @param triggerPollingData
     * @return
     */
    Result<Void> enqueueAndTryExecutePollingTempData(TriggerPollingData triggerPollingData);

    /**
     * 重试失败的轮询临时库任务
     */
    void retryPollingTempFail(@ContextEi() String tenantId);
}
