package com.fxiaoke.open.oasyncdata.service;

import com.fxiaoke.open.oasyncdata.model.BuildExcelFile;
import com.fxiaoke.open.oasyncdata.model.ImportExcelFile;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Validated
public interface FileService {

    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     *
     * @param <R>
     * @param buildExcelArg
     * @return
     */
    <R> Result<BuildExcelFile.Result> buildExcelFile(BuildExcelFile.Arg<R> buildExcelArg,String lang);


    /**
     * 构建excel表格，并上传到临时文件，返回路径
     * 仅支持一次传输数据，单个sheet
     *
     * @param ea
     * @param arg
     * @return
     */
    Result<BuildExcelFile.Result> buildExcelTemplate(@NotNull String ea, @NotNull ImportExcelFile.FieldDataMappingArg arg, String lang);



    /**
     * 导入excel文件
     * @param arg
     * @return
     * @throws IOException
     */
    Result<ImportExcelFile.Result> importExcelFile(@Valid ImportExcelFile.FieldDataMappingArg arg, String lang) throws IOException;

    }


