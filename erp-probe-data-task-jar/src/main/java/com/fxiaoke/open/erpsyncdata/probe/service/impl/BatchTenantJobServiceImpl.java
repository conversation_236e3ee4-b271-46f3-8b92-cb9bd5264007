package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpReSyncDataService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpSyncService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.probe.service.BatchTenantJobService;
import com.fxiaoke.open.erpsyncdata.probe.utils.RemoveUnNormalTenantUtil;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 18:27 2021/2/26
 * @Desc:
 */
@Service
@Slf4j
@Data
public class BatchTenantJobServiceImpl implements BatchTenantJobService {

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private ErpSyncService erpSyncService;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private RemoveUnNormalTenantUtil removeUnNormalTenantUtil;
    @Autowired
    private ErpReSyncDataService erpReSyncDataService;


    @Override
    public Result<Void> executeTasks(List<String> tenantIdList) {
        return Result.newSuccess();
    }

    @Override
    public Result<Void> asyncExecuteSingleTenantTasks(String tenantId) {
        //结束超时的同步任务
        syncDataTimeOut(tenantId);
        //扫描临时库任务 暂时不发
        scanTemp(tenantId);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> reSyncDataExecuteSingleTenantTasks(String tenantId) {
        return erpReSyncDataService.reSyncDataExecuteSingleTenantTasks(tenantId);
    }

    /**
     * 结束超时的同步任务
     */
    private void syncDataTimeOut(String tenantId) {
        long endUpdateTime = DateTime.now().minusSeconds(ConfigCenter.END_UPDATE_TIME_OUT_MIN * 60).getMillis();
        //为了减少sql执行时间，增加开始范围,为了预防特殊情况，把开始范围调到了*10
        long startUpdateTime = DateTime.now().minusSeconds(ConfigCenter.END_UPDATE_TIME_OUT_MIN * 60 * 10).getMillis();
        if(DateTime.now().getHourOfDay()%5==0){
            startUpdateTime= 0;
        }
        Result<Void> result = erpSyncService.syncDataTimeOutUpdateStatusToError(tenantId,startUpdateTime, endUpdateTime);
        log.debug("syncDataTimeOut syncService syncDataTimeOutUpdateStatusToError,arg:{},result:{}", endUpdateTime, result);
    }


    /**
     * 结束超时的同步任务
     */
    private void scanTemp(String tenantId) {
        DateTime now = DateTime.now();
        if (now.getMinuteOfDay() != ConfigCenter.MIN_OF_DAY_2_SCAN_TEMP) {
            //两点执行
            return;
        }
        log.info("execute scan temp,tenantId:{},now:{}", tenantId, now);
        ErpSyncDataBackStageEnvironmentEnum tenantAllModelEnv = tenantEnvManager.getTenantAllModelEnv(tenantId);
        if (ConfigCenter.ENABLE_SCAN_TEMP_ENVS.contains(tenantAllModelEnv.name())) {
            //相应环境才调用
            Result<Void> result = erpSyncService.scanTempNotTrigger(tenantId);
        }
        //更新
        if (ConfigCenter.ENABLE_SCAN_TEMP_ENVS.contains(tenantAllModelEnv.name())) {
            //相应环境才调用
            Result<Void> result = erpSyncService.scanTempNotTrigger(tenantId);
        }

    }

}
