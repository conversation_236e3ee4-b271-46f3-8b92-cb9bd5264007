package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.K3DataManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售退货单特殊逻辑
 * <AUTHOR>
 * @date 2020/12/11
 */

@Slf4j
@Component(K3CloudForm.SAL_RETURNSTOCK)
public class ReturnStockSpecialBusinessImpl extends BaseSpecialBusiness {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private K3DataManager k3DataManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    @Override
    public void afterRunView(ErpIdArg erpIdArg, StandardData standardData, K3Model erpData, K3CloudApiClient apiClient) {
        super.afterRunView(erpIdArg,standardData,erpData,apiClient);
        log.info("ReturnStockSpecialBusinessImpl.afterRunView,standardData={}",JSONObject.toJSONString(standardData));

        standardData.getDetailFieldVals().forEach((key,objectDataList)->{
            objectDataList.forEach((objectData ->
            {
                if(StringUtils.isNotEmpty(standardData.getMasterFieldVal().getString("return_warehouse"))) return;
                //crm的退货仓库字段在主对象上，k3c的退货单的仓库在明细字段上，所以，这里取k3c明细字段上一个不为空的仓库作为发货仓库，
                //但是还有一个问题，如果k3c的退货单的仓库字段全部为空，就无法创建crm的退货单，因为crm的退货单的退货仓库是必填字段
                String warehouseNumber = objectData.getString("FStockId.FNumber");
                if(StringUtils.isNotEmpty(warehouseNumber)) {
                    log.info("ReturnStockSpecialBusinessImpl,afterRunView,warehouseNumber={}",warehouseNumber);
                    standardData.getMasterFieldVal().put("return_warehouse",warehouseNumber);
                }
            }));
        });

        /**
         * 销售退货单明细需要添加字段：序列号列表：SAL_ReturnSerial
         */
        ErpTenantConfigurationEntity serialNumberConfig = tenantConfigurationManager.findOne(erpIdArg.getTenantId(),
                apiClient.getDataCenterId(),
                ErpChannelEnum.ERP_K3CLOUD.name(),
                TenantConfigurationTypeEnum.returnStockSerialNumber.name());
        log.info("ReturnStockSpecialBusinessImpl.afterRunView,serialNumberConfig={}",serialNumberConfig);
        if (serialNumberConfig != null) {//处理序列号
            //配置
            ErpTenantConfigurationEntity config = tenantConfigurationManager.findOne(erpIdArg.getTenantId(),
                    apiClient.getDataCenterId(),
                    ErpChannelEnum.ERP_K3CLOUD.name(),
                    TenantConfigurationTypeEnum.USE_BILLQUERY_INTERFACE_TO_VIEW.name());
            log.info("ReturnStockSpecialBusinessImpl.afterRunView,config={}",config);
            List<String> objList = Lists.newArrayList();
            if (config != null && config.getConfiguration() != null) {
                objList = JSONObject.parseArray(config.getConfiguration(), String.class);
            }
            K3DataConverter converter = k3DataManager.buildConverter(erpIdArg.getTenantId(),apiClient.getDataCenterId(), K3CloudForm.SAL_RETURNSTOCK);
            dealWithSerialNo(serialNumberConfig, standardData, objList,converter,apiClient);
            log.info("ReturnStockSpecialBusinessImpl.afterRunView,standardData2={}",JSONObject.toJSONString(standardData));
        }
    }

    /**
     * 分页获取退货单序列号数据
     * @param idFieldKey
     * @param dataId
     * @param apiClient
     * @return
     */
    private List<K3Model> queryAllReturnStockData(String idFieldKey,String dataId,K3CloudApiClient apiClient) {

        int pageSize = 2000;

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.SAL_RETURNSTOCK);
        queryArg.setFieldKeysByList(ImmutableList.of("FID","FEntity_FENTRYID","FSerialSubEntity_FDetailID","FSerialNo","FSerialId","FSerialNote"));
        queryArg.setFilterString(String.format("%s='%s'", idFieldKey, dataId));
        queryArg.appendFilterString("FSerialNo!=''");
        queryArg.setOrderString(idFieldKey + " ASC");

        queryArg.setLimit(pageSize);//页面大小为2000
        queryArg.setStartRow(0);
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        List<K3Model> dataList = new ArrayList<>();
        while (true) {
            Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
            log.info("TransferDirectSpecialBusinessImpl.queryAllReturnStockData,result={}",JSONObject.toJSONString(result));
            if(result.isSuccess()==false) break;

            dataList.addAll(result.getData());
            if(result.getData().size()==pageSize) {
                queryArg.setStartRow(queryArg.getStartRow()+pageSize);
                log.info("TransferDirectSpecialBusinessImpl.queryAllReturnStockData,queryArg={}",JSONObject.toJSONString(queryArg));
            } else {
                break;
            }
        }

        log.info("TransferDirectSpecialBusinessImpl.queryAllTransferDirectData,dataList.size={},dataList={}",
                dataList.size(),
                JSONObject.toJSONString(dataList));
        return dataList;
    }

    public void dealWithSerialNo(ErpTenantConfigurationEntity serialNumberConfig, StandardData standardData, List<String> objList, K3DataConverter converter, K3CloudApiClient apiClient) {
        String detailKey="SAL_RETURNSTOCK.SAL_RETURNSTOCKENTRY";
        for( ErpFieldExtendEntity entity:converter.getMasterDetailFieldMap().values()){
            if("SAL_RETURNSTOCKENTRY".equals(entity.getViewCode())){
                detailKey=entity.getFieldApiName();
            }
        }
        log.info("ReturnStockSpecialBusinessImpl.dealWithSerialNo,detailKey={}",detailKey);
        List<ObjectData> detailList = standardData.getDetailFieldVals().get(detailKey);
        if (CollectionUtils.isNotEmpty(detailList)) {
            if (!objList.contains("*") && !objList.contains(K3CloudForm.SAL_RETURNSTOCK)) {//StandardData是走ExecuteBillQuery接口
                String dataId=standardData.getMasterFieldVal().get("id")!=null?standardData.getMasterFieldVal().get("id").toString():standardData.getMasterFieldVal().getId();
                String idFieldKey=converter.getIdFieldExtend().getQueryCode()==null?converter.getIdFieldExtend().getSaveCode():converter.getIdFieldExtend().getQueryCode();
//                QueryArg queryArg = new QueryArg();
//                queryArg.setFormId(K3CloudForm.SAL_RETURNSTOCK);
//                queryArg.setFieldKeysByList(ImmutableList.of("FID","FEntity_FENTRYID","FSerialSubEntity_FDetailID","FSerialNo","FSerialId","FSerialNote"));
//                queryArg.setFilterString(String.format("%s='%s'", idFieldKey, dataId));
//                queryArg.appendFilterString("FSerialNo!=''");
//                queryArg.setOrderString(idFieldKey + " ASC");
//                Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
                List<K3Model> dataList = queryAllReturnStockData(idFieldKey,dataId,apiClient);
                if(CollectionUtils.isNotEmpty(dataList)){
                    Map<String,List<K3Model>> serialList=dataList.stream()
                            .filter(v-> StringUtils.isNotBlank(v.getString("FEntity_FENTRYID")))
                            .collect(Collectors.groupingBy(v->v.getString("FEntity_FENTRYID")));
                    if ("1".equals(serialNumberConfig.getConfiguration())) {//序列号拆分多条明细
                        List<ObjectData> newDetailList = Lists.newArrayList();
                        for (ObjectData detail : detailList) {
                            String detailId = detail.get("DetailId").toString();
                            Object outStockSerial = serialList.get(detailId);
                            if (outStockSerial != null && outStockSerial instanceof List && CollectionUtils.isNotEmpty((List) outStockSerial)) {
                                detail.remove("SAL_ReturnSerial");//去掉防止数据过大
                                List<Map<String, Object>> serialNoList = (List) outStockSerial;
                                for (Map<String, Object> serialNoMap : serialNoList) {
                                    String id = serialNoMap.get("FSerialSubEntity_FDetailID") == null ? null : serialNoMap.get("FSerialSubEntity_FDetailID").toString();
                                    String serialId = serialNoMap.get("FSerialId") == null ? null : serialNoMap.get("FSerialId").toString();
                                    String serialNo = serialNoMap.get("FSerialNo") == null ? null : serialNoMap.get("FSerialNo").toString();
                                    String serialNote = serialNoMap.get("FSerialNote") == null ? null : serialNoMap.get("FSerialNote").toString();
                                    ObjectData newDetail = BeanUtil.deepCopy(detail, ObjectData.class);
                                    newDetail.put("serialId", serialId);
                                    newDetail.put("DetailId", detailId + "#" + id);//明细id#序列号id
                                    newDetail.put("crmSerialNo", serialNo);//序列号
                                    newDetail.put("crmSerialNote", serialNote);//序列号备注
                                    newDetail.put("FRealQty", 1);//数量改成1
                                    fillDetailData(detail,newDetail,serialNoList.size());
                                    newDetailList.add(newDetail);
                                }
                            } else {
                                newDetailList.add(detail);
                            }
                        }
                        standardData.getDetailFieldVals().put(detailKey, newDetailList);
                    } else if ("2".equals(serialNumberConfig.getConfiguration())) {//序列号拼接为一个字段
                        for (ObjectData detail : detailList) {
                            String detailId = detail.get("DetailId").toString();
                            Object outStockSerial = serialList.get(detailId);
                            if (outStockSerial != null && outStockSerial instanceof List) {
                                StringBuffer serialIds = new StringBuffer();
                                StringBuffer serialNos = new StringBuffer();
                                StringBuffer serialNotes = new StringBuffer();
                                List<Map<String, Object>> serialNoList = (List) outStockSerial;
                                for (Map<String, Object> serialNoMap : serialNoList) {
                                    String id = serialNoMap.get("FSerialSubEntity_FDetailID") == null ? null : serialNoMap.get("FSerialSubEntity_FDetailID").toString();
                                    String serialId = serialNoMap.get("FSerialId") == null ? null : serialNoMap.get("FSerialId").toString();
                                    String serialNo = serialNoMap.get("FSerialNo") == null ? "" : serialNoMap.get("FSerialNo").toString();
                                    String serialNote = serialNoMap.get("FSerialNote") == null ? "" : serialNoMap.get("FSerialNote").toString();
                                    serialIds.append(serialId).append(";");
                                    serialNos.append(serialNo).append(";");
                                    serialNotes.append(serialNote).append(";");
                                }
                                if (serialIds.lastIndexOf(";") > 0) {
                                    serialIds.delete(serialIds.lastIndexOf(";"), serialIds.length());
                                }
                                if (serialNos.lastIndexOf(";") > 0) {
                                    serialNos.delete(serialNos.lastIndexOf(";"), serialNos.length());
                                }
                                if (serialNotes.lastIndexOf(";") > 0) {
                                    serialNotes.delete(serialNotes.lastIndexOf(";"), serialNotes.length());
                                }
                                detail.put("serialId", serialIds.toString());//序列号id
                                detail.put("crmSerialNo", serialNos.toString());//序列号
                                detail.put("crmSerialNote", serialNotes.toString());//序列号备注
                            }
                        }
                    }
                }
            } else {
                if ("1".equals(serialNumberConfig.getConfiguration())) {//序列号拆分多条明细
                    List<ObjectData> newDetailList = Lists.newArrayList();
                    for (ObjectData detail : detailList) {
                        String detailId = detail.get("DetailId").toString();
                        Object objSerial = detail.get("SAL_ReturnSerial");
                        if (objSerial != null && objSerial instanceof List && CollectionUtils.isNotEmpty((List) objSerial)) {
                            detail.remove("SAL_ReturnSerial");//去掉防止数据过大
                            List<Map<String, Object>> serialNoList = (List) objSerial;
                            for (Map<String, Object> serialNoMap : serialNoList) {
                                String id = serialNoMap.get("Id") == null ? null : serialNoMap.get("Id").toString();
                                String serialId = serialNoMap.get("SerialId_Id") == null ? null : serialNoMap.get("SerialId_Id").toString();
                                String serialNo = serialNoMap.get("SerialNo") == null ? null : serialNoMap.get("SerialNo").toString();
                                String serialNote = serialNoMap.get("SerialNote") == null ? null : serialNoMap.get("SerialNote").toString();
                                ObjectData newDetail = BeanUtil.deepCopy(detail, ObjectData.class);
                                newDetail.put("serialId", serialId);
                                newDetail.put("DetailId", detailId + "#" + id);//明细id#序列号id
                                newDetail.put("crmSerialNo", serialNo);//序列号
                                newDetail.put("crmSerialNote", serialNote);//序列号备注
                                newDetail.put("FRealQty", 1);//数量改成1
                                fillDetailData(detail,newDetail,serialNoList.size());
                                newDetailList.add(newDetail);
                            }
                        } else {
                            newDetailList.add(detail);
                        }
                    }
                    standardData.getDetailFieldVals().put(detailKey, newDetailList);
                } else if ("2".equals(serialNumberConfig.getConfiguration())) {//序列号拼接为一个字段
                    for (ObjectData detail : detailList) {
                        Object outStockSerial = detail.get("SAL_ReturnSerial");
                        if (outStockSerial != null && outStockSerial instanceof List) {
                            StringBuffer serialIds = new StringBuffer();
                            StringBuffer serialNos = new StringBuffer();
                            StringBuffer serialNotes = new StringBuffer();
                            List<Map<String, Object>> serialNoList = (List) outStockSerial;
                            for (Map<String, Object> serialNoMap : serialNoList) {
                                String id = serialNoMap.get("Id") == null ? null : serialNoMap.get("Id").toString();
                                String serialId = serialNoMap.get("SerialId_Id") == null ? null : serialNoMap.get("SerialId_Id").toString();
                                String serialNo = serialNoMap.get("SerialNo") == null ? "" : serialNoMap.get("SerialNo").toString();
                                String serialNote = serialNoMap.get("SerialNote") == null ? "" : serialNoMap.get("SerialNote").toString();
                                serialIds.append(serialId).append(";");
                                serialNos.append(serialNo).append(";");
                                serialNotes.append(serialNote).append(";");
                            }
                            if (serialIds.lastIndexOf(";") > 0) {
                                serialIds.delete(serialIds.lastIndexOf(";"), serialIds.length());
                            }
                            if (serialNos.lastIndexOf(";") > 0) {
                                serialNos.delete(serialNos.lastIndexOf(";"), serialNos.length());
                            }
                            if (serialNotes.lastIndexOf(";") > 0) {
                                serialNotes.delete(serialNotes.lastIndexOf(";"), serialNotes.length());
                            }
                            detail.put("serialId", serialIds.toString());//序列号id
                            detail.put("crmSerialNo", serialNos.toString());//序列号
                            detail.put("crmSerialNote", serialNotes.toString());//序列号备注
                        }
                    }
                }
            }
        }
    }

    private void fillDetailData(ObjectData detail,ObjectData newDetail,int serialCount) {
        Double amount = detail.getDouble("FAmount");
        Double amountLC = detail.getDouble("FAmount_LC");
        Double allAmount = detail.getDouble("FAllAmount");
        Double allAmountLC = detail.getDouble("FAllAmount_LC");

        Double billAmount = detail.getDouble("FBillAmount");
        Double billAmountLC = detail.getDouble("FBillAmount_LC");
        Double billAllAmount = detail.getDouble("FBillAllAmount");
        Double billAllAmountLC = detail.getDouble("FBillAllAmount_LC");

        if(amount!=null && amount>0) {
            Double avgAmount = amount / serialCount;
            newDetail.put("FAmount",avgAmount);
        }
        if(amountLC!=null && amountLC>0) {
            Double avgAmountLC = amountLC / serialCount;
            newDetail.put("FAmount_LC",avgAmountLC);
        }
        if(allAmount!=null && allAmount>0) {
            Double avgAllAmount = allAmount / serialCount;
            newDetail.put("FAllAmount",avgAllAmount);
        }
        if(allAmountLC!=null && allAmountLC>0) {
            Double avgAllAmountLC = allAmountLC / serialCount;
            newDetail.put("FAllAmount_LC",avgAllAmountLC);
        }

        if(billAmount!=null && billAmount>0) {
            Double avgBillAmount = billAmount / serialCount;
            newDetail.put("FBillAmount",avgBillAmount);
        }
        if(billAmountLC!=null && billAmountLC>0) {
            Double avgBillAmountLC = billAmountLC / serialCount;
            newDetail.put("FBillAmount_LC",avgBillAmountLC);
        }
        if(billAllAmount!=null && billAllAmount>0) {
            Double avgBillAllAmount = billAllAmount / serialCount;
            newDetail.put("FBillAllAmount",avgBillAllAmount);
        }
        if(billAllAmountLC!=null && billAllAmountLC>0) {
            Double avgBillAllAmountLC = billAllAmountLC / serialCount;
            newDetail.put("FBillAllAmount_LC",avgBillAllAmountLC);
        }
    }
}
