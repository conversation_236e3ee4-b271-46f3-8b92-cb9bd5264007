package com.fxiaoke.open.oasyncdata.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/3/2 15:32
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class ConnectorModuleResult {

    @ApiModelProperty("渠道,包括钉钉插件，企业微信插件")
    public String channel;
    @ApiModelProperty("渠道连接信息id")
    public String id;
    @ApiModelProperty("是否已经购买")
    public boolean hasBuy = false;
}
