package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.data.BaseResult;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;

import java.util.LinkedHashMap;

@Data
public class CompleteDataProcessMqData extends BaseResult {
    private static final long serialVersionUID = 2915117068128061985L;

    private String tenantId;
    private String syncDataId;
    private String sourceTenantId;
    private String destTenantId;
    private Integer destEventType;
    private String destObjectApiName;
    private String destDataId;
    private ObjectData destData;
    /**
     * key:中间表映射id
     * value: 从对象数据
     */
    private LinkedHashMap<String, ObjectData> destDetailSyncDataIdAndDestDataMap;
    private LinkedHashMap<String,String> destDetailObjMasterDetailFieldApiName;//目标明细对象对应的主从字段:<对象apiName,主从字段apiName>
    private String syncPloyDetailSnapshotId;
    /** 终止向后继续执行 */
    private Boolean stop;
    private Integer destTenantType;
//    /** 目标主对象数据id */
//    private String destMasterDataId;
//    /**
//     * 目标主对象apiName
//     */
//    private String destMasterObjectApiName;
    /**
     * 主对象对应的mappings data
     */
    private MasterMappingsData masterMappingsData;
}
