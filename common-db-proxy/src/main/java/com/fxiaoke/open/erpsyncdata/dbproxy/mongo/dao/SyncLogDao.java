package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base.TenantLimitableMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store.SyncLogMongoStore;
import com.google.common.collect.Sets;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static com.mongodb.client.model.Filters.lt;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/2
 */
@Repository
@Slf4j
public class SyncLogDao implements TenantLimitableMongoDao {

    @Autowired
    private SyncLogMongoStore store;
    @Autowired
    private CHSyncLogManager chSyncLogManager;

    @Override
    public String getCollPrefix() {
        return "sync_log";
    }

    @Override
    public CollStat getCollStat(String tenantId) {
        CollStat collStat = new CollStat();
        long count = chSyncLogManager.countByTenantId(tenantId);
        collStat.setCount(count);
        return collStat;
    }

    @Override
    public DateTime findMinDate(String tenantId) {
        Long first = chSyncLogManager.findMinDate(tenantId);
        if (first != null) {
            return DateUtil.date(first);
        }
        return DateUtil.date();
    }

    @Override
    public Long deleteBetween(String tenantId, Date beginDate, Date endDate) {
        return chSyncLogManager.deleteBetween(tenantId, beginDate, endDate);
    }

    public List<SyncLog> listSyncLogLimit1000(String tenantId, String lastId) {
        List<SyncLog> result = new ArrayList<>();
        MongoCollection<SyncLog> collection = store.getOrCreateCollection(tenantId);
        if (StringUtils.isNotBlank(lastId)) {
            collection.find()
                    .filter(lt("_id", new ObjectId(lastId)))
                    .skip(0)
                    .limit(1000)
                    .sort(Sorts.orderBy(Sorts.descending("_id")))
                    .into(result);
        } else {
            collection.find()
                    .skip(0)
                    .limit(1000)
                    .sort(Sorts.orderBy(Sorts.descending("_id")))
                    .into(result);
        }
        return result;
    }

    public Set<String> getAllCollection(String dbIndex) {
        Set<String> result = Sets.newHashSet();
        try {
            MongoDatabase mongoDatabase = store.getDatabase(dbIndex);
            if (mongoDatabase == null) {
                return result;
            }
            MongoIterable<String> collection = mongoDatabase.listCollectionNames();
            for (String name : collection) {
                result.add(name);
            }
        } catch (Exception e) {
            log.error("getAllCollection ei={} error={}", dbIndex, e);
        }

        return result;
    }

}
