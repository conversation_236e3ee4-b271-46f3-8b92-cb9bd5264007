<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity">
        <!--@mbg.generated-->
        <!--@Table erp_object_relationship-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="erp_actual_object_apiname" jdbcType="VARCHAR" property="erpRealObjectApiname"/>
        <result column="erp_split_object_apiname" jdbcType="VARCHAR" property="erpSplitObjectApiname"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="split_seq" property="splitSeq"/>
        <result column="split_type" property="splitType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id, channel, erp_actual_object_apiname, erp_split_object_apiname, split_type,split_seq,
        data_center_id,create_time, update_time
    </sql>

<!--auto generated by MybatisCodeHelper on 2020-12-24-->
    <select id="findBySplit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR} and
        erp_split_object_apiname=#{erpSplitObjectApiname,jdbcType=VARCHAR}
    </select>
    <select id="findByRealObjectApiName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        and erp_actual_object_apiname=#{erpRealObjectApiname,jdbcType=VARCHAR}
    </select>

    <select id="findAllByRealObjectApiName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_actual_object_apiname=#{erpRealObjectApiname,jdbcType=VARCHAR}
    </select>

    <select id="findAllByTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        order by erp_split_object_apiname asc
    </select>

    <select id="findAllTenantId" resultType="string">
        select distinct tenant_id
        from erp_object_relationship
        order by tenant_id asc
    </select>

    <select id="findMasterByRealObjectApiName"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        and erp_actual_object_apiname=#{erpRealObjectApiname,jdbcType=VARCHAR}
        and split_type = 'NOT_SPLIT'
    </select>

    <delete id="deleteByTenantIdAndDcId">
        delete from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByTenantId">
        delete from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </delete>

    <select id="findNotSplit" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        and split_type='NOT_SPLIT'
    </select>
    <select id="getSplitObjApiName"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and erp_actual_object_apiname=#{actualApiName,jdbcType=VARCHAR}
        and split_type='NOT_SPLIT'
    </select>

    <select id="listByTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-13-->
    <select id="listByTenantIdAndDataCenterId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        <if test="dataCenterId != null and dataCenterId != ''">
            and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="ListBySplit"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_object_relationship
        where tenant_id=#{tenantId,jdbcType=VARCHAR}
        and data_center_id=#{dataCenterId,jdbcType=VARCHAR}
        <if test="erpObjApiNames!=null">
            and erp_split_object_apiname in
            <foreach item="item" index="index" collection="erpObjApiNames"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>

        and split_type = 'NOT_SPLIT'

    </select>
</mapper>