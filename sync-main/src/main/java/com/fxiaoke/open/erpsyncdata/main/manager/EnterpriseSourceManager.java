package com.fxiaoke.open.erpsyncdata.main.manager;

import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.TenantCleanupRecord;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.service.TenantCleanupService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 处理过期企业的资源处理
 */
@Component
@Slf4j
public class EnterpriseSourceManager {
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private TenantCleanupService tenantCleanupService;
    @Autowired
    private SandboxEventManager sandboxEventManager;



    public void collectExpireResourceData(){
        List<String> allTenants = erpConnectInfoDao.listTenantId();
        log.warn("expire resource data tenantIds size:{}", allTenants.size());
        List<TenantCleanupRecord> tenantCleanupRecords= Lists.newArrayList();
        //TODO 查询指定的license的过期资源
        for (String allTenant : allTenants) {
            //将需要删除的资源记录在mongo.
            TenantCleanupRecord tenantCleanupRecord=new TenantCleanupRecord();
            tenantCleanupRecord.setId(ObjectId.get());
            tenantCleanupRecord.setTenantId(allTenant);
            //TODO  查询指定租户的资源
            tenantCleanupRecords.add(tenantCleanupRecord);
        }
        tenantCleanupService.batchAddTenantCleanupRecords(tenantCleanupRecords);

    }

    public void deleteExpireResourceData(){
        //todo
        List<TenantCleanupRecord> tenantCleanupRecords = tenantCleanupService.getUndeletedRecords(100);
        for (TenantCleanupRecord tenantCleanupRecord : tenantCleanupRecords) {
            String tenantId=tenantCleanupRecord.getTenantId();
            sandboxEventManager.deleteErpSettingNotJudgeSandbox(tenantId);
             //删除连接器
            erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantCleanupRecord.getTenantId()));
            //删除连接器对象，集成流，字段管理，字段描述
        }

    }

}
