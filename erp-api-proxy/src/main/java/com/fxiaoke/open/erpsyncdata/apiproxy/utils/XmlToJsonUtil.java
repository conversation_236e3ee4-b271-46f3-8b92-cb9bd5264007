package com.fxiaoke.open.erpsyncdata.apiproxy.utils;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class XmlToJsonUtil {

    /**
     * 将xml字符串<STRONG>转换</STRONG>为JSON字符串
     *
     * @param xmlString xml字符串
     * @return JSON<STRONG>对象</STRONG>
     */
    public static String xml2json(String xmlString) throws DocumentException {
        JSONObject jsonObject = xml2jsonObj(xmlString);
        String jsonStr = jsonObject.toJSONString();
        return jsonStr;
    }

    /**
     * 将xml字符串<STRONG>转换</STRONG>为JSON字符串
     *
     * @param xmlString xml字符串
     * @return JSON<STRONG>对象</STRONG>
     */
    public static JSONObject xml2jsonObj(String xmlString) throws DocumentException {
        SAXReader reader = new SAXReader();
        InputStream is = new ByteArrayInputStream(xmlString.getBytes(StandardCharsets.UTF_8));//如果出现乱码，就用你的框架的编码如utf-8
        Document document = reader.read(is);
        JSONObject result = new JSONObject();
        Element root = document.getRootElement();
        dom4j2Json(root, result);
        return result;
    }

    public static void dom4j2Json(Element element, JSONObject json) {
        //如果是属性
        for (Object o : element.attributes()) {
            Attribute attr = (Attribute) o;
            if (StringUtils.isNotEmpty(attr.getValue())) {
                json.put("@" + attr.getName(), attr.getValue());
            }
        }
        List<Element> chdEl = element.elements();
        if (chdEl.size() == 0) {
            //如果没有子元素,只有一个值  null值也放入JSON

            json.put(element.getName(), element.getText());
        } else {
            for (Element e : chdEl) {
                if (!e.elements().isEmpty()) {
                    //子元素也有子元素
                    JSONObject chdjson = new JSONObject();
                    dom4j2Json(e, chdjson);
                    Object o = json.get(e.getName());
                    if (o != null) {
                        JSONArray jsona = null;
                        if (o instanceof JSONObject) {
                            //如果此元素已存在,则转为jsonArray
                            JSONObject jsono = (JSONObject) o;
                            json.remove(e.getName());
                            jsona = new JSONArray();
                            jsona.add(jsono);
                            jsona.add(chdjson);
                        }
                        if (o instanceof JSONArray) {
                            jsona = (JSONArray) o;
                            jsona.add(chdjson);
                        }
                        json.put(e.getName(), jsona);
                    } else {
                        if (!chdjson.isEmpty()) {
                            json.put(e.getName(), chdjson);
                        } else {
                            json.put(e.getName(), null);
                        }
                    }


                } else {
                    //子元素没有子元素
                    for (Object o : element.attributes()) {
                        Attribute attr = (Attribute) o;
                        if (StringUtils.isNotEmpty(attr.getValue())) {
                            json.put("@" + attr.getName(), attr.getValue());
                        }
                    }
                    json.put(e.getName(), e.getText());
                }
            }
        }

    }

    public static String json2xml(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        String result = json2xml(jsonObject);
        return result;
    }

    public static String json2xml(JSONObject jsonObj) {
        StringBuffer buff = new StringBuffer();
        JSONObject tempObj = null;
        JSONArray tempArr = null;
        for (String temp : jsonObj.keySet()) {
            buff.append("<" + temp.trim() + ">");
            jsonObj.get(temp);
            if (jsonObj.get(temp) instanceof JSONObject) {
                tempObj = (JSONObject) jsonObj.get(temp);
                buff.append(json2xml(tempObj));
            } else if (jsonObj.get(temp) instanceof JSONArray) {
                tempArr = (JSONArray) jsonObj.get(temp);
                if (tempArr.size() > 0) {
                    for (int i = 0; i < tempArr.size(); i++) {
                        tempObj = (JSONObject) tempArr.get(0);
                        buff.append(json2xml(tempObj));
                    }
                }
            } else {
                String tempStr = jsonObj.get(temp).toString();
                buff.append(tempStr.trim());
            }
            buff.append("</" + temp.trim() + ">");
        }
        return buff.toString();
    }

}
