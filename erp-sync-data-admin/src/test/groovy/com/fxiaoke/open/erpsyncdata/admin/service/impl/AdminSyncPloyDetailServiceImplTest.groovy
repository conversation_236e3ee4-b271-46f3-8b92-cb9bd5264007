package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.QueryObjectToDestObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> 
 * @date 2024/3/13 17:34:55
 */
class AdminSyncPloyDetailServiceImplTest extends Specification {


    @Unroll
    def "#name-获取crm节点字段信息"() {
        when:
        Map<String, Map<String, Set<Object>>> map = new HashMap<>()
        QueryObjectToDestObjectData query = BeanUtil.deepCopy(data, QueryObjectToDestObjectData)
        AdminSyncPloyDetailServiceImpl.addQueryCrmNodeFields(map, [query], sourceCrm)

        println map

        then:
        JSON.toJSONString(result) == JSON.toJSONString(map)

        where:
        name        | data                                                                                                                                                                                                 | sourceCrm || result
        "源-查询"   | ["queryObjectMappingData": [sourceObjectApiName: "SA1", destObjectApiName: "DA1", queryFieldMappings: [[[fieldApiName: "sa1", fieldValue: ["da1"]], [fieldApiName: "sa1a", fieldValue: ["da1a"]]]]]] | true      || [SA1: ["sa1": [], "sa1a": []], DA1: ["da1a": [], "da1": []]]
        "源-设置"   | [queryData2DestDataMapping: [sourceObjectApiName: "SA1", destObjectApiName: "DA1", fieldMappings: [[sourceApiName: "sa1", destApiName: "da1"], [sourceApiName: "sa1a", destApiName: "da1a"]]]]       | true      || [SA1: ["sa1": [], "sa1a": []], DA1: ["da1a": [], "da1": []]]

        "目标-查询" | ["queryObjectMappingData": [sourceObjectApiName: "SA1", destObjectApiName: "DA1", queryFieldMappings: [[[fieldApiName: "sa1", fieldValue: ["da1"]], [fieldApiName: "sa1a", fieldValue: ["da1a"]]]]]] | false     || [SA1: ["sa1": [], "sa1a": []]]
        "目标-设置" | [queryData2DestDataMapping: [sourceObjectApiName: "SA1", destObjectApiName: "DA1", fieldMappings: [[sourceApiName: "sa1", destApiName: "da1"], [sourceApiName: "sa1a", destApiName: "da1a"]]]]       | false     || [SA1: ["sa1": [], "sa1a": []]]
    }

    @Unroll
    def "#name-获取数据范围选项值"() {
        when:
        Map<String, Map<String, Set<Object>>> map = new HashMap<>()
        FilterData query = BeanUtil.deepCopy(data, FilterData)
        AdminSyncPloyDetailServiceImpl.addFieldMappingData(map, "xxx", [[query]])

        println map

        then:
        JSON.toJSONString(result) == JSON.toJSONString(map.get("xxx"))

        where:
        name     | data                                                                                              || result
        "非选择" | [fieldApiName: "sa1", fieldValue: ["da1"]]                                                        || ["sa1": []]
        "单选"   | [fieldApiName: "unit", fieldType: "select_one", label: "单位", operate: "EQ", fieldValue: ["10"]] || ["unit": ["10"]]
        "多选"   | [fieldApiName: "unit", fieldType: "select_one", label: "单位", operate: "EQ", fieldValue: ["10"]] || ["unit": ["10"]]
    }

    @Unroll
    def "#name-获取字段映射选项值"() {
        when:
        FieldMappingData query = BeanUtil.deepCopy(data, FieldMappingData)
        def options = AdminSyncPloyDetailServiceImpl.getFieldMappingCrmFieldWithOptions(query, sourceCrm)

        println options

        then:
        JSON.toJSONString(result) == JSON.toJSONString(options)

        where:
        name           | data                                                                                                                                                                                | sourceCrm || result
        "源-非选择"    | [sourceApiName: "sa1", destApiName: "da1"]                                                                                                                                          | true      || ["sa1": []]
        "源-单选"      | [sourceApiName: "sa1", sourceType: "select_one", destApiName: "da1", mappingType: 1, optionMappings: [[sourceOption: "1", destOption: "1"], [sourceOption: "3", destOption: "2"]]]  | true      || ["sa1": ["1", "3"]]
        "源-多选"      | [sourceApiName: "sa1", sourceType: "select_many", destApiName: "da1", mappingType: 1, optionMappings: [[sourceOption: "1", destOption: "1"], [sourceOption: "3", destOption: "2"]]] | true      || ["sa1": ["1", "3"]]
        "源-固定值"    | [sourceApiName: "sa1", sourceType: "select_one", destApiName: "da1", mappingType: 3]                                                                                                | true      || ["sa1": []]

        "目标-非选择"  | [sourceApiName: "sa1", destApiName: "da1"]                                                                                                                                          | false     || ["da1": []]
        "目标-单选"    | [sourceApiName: "sa1", destType: "select_one", destApiName: "da1", mappingType: 1, optionMappings: [[sourceOption: "1", destOption: "1"], [sourceOption: "3", destOption: "2"]]]    | false     || ["da1": ["1", "2"]]
        "目标-单选"    | [sourceApiName: "sa1", destType: "select_many", destApiName: "da1", mappingType: 1, optionMappings: [[sourceOption: "1", destOption: "1"], [sourceOption: "3", destOption: "2"]]]   | false     || ["da1": ["1", "2"]]
        "目标-固定值"  | [sourceApiName: "sa1", destType: "select_one", destApiName: "da1", mappingType: 3, value: "1", valueType: 1, defaultValue: "2"]                                                     | false     || ["da1": ["1"]]
        "目标-固定值2" | [sourceApiName: "sa1", destType: "select_one", destApiName: "da1", mappingType: 3, value: "1", valueType: 2, defaultValue: "2"]                                                     | false     || ["da1": ["2"]]
    }

    @Unroll
    def "#name-获取校验的字段信息"() {
        when:
        AdminSyncPloyDetailService service = new AdminSyncPloyDetailServiceImpl()

        SyncPloyDetailData ploy = BeanUtil.deepCopy(data, SyncPloyDetailData)
        def check = service.getAllObjectAndFieldApiNameByPloy(Lists.newArrayList(ploy))
        println check.getData()


        then:
        JSON.toJSONString(result) == JSON.toJSONString(check.getData())

        where:
        name                       | data                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                || result
        "无数据"                   | [sourceTenantType: 1]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               || [:]

        // 主对象

        "源主对象"                 | [sourceTenantType: 1, sourceObjectApiName: "object_6u9j9__c"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                       || [object_6u9j9__c: [:]]
        "目标无对象"               | [sourceTenantType: 1, destObjectApiName: "object_6u9j9__c"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                         || [:]
        "目标主对象"               | [sourceTenantType: 2, destObjectApiName: "object_6u9j9__c"]                                                                                                                                                                                                                                                                                                                                                                                                                                                                         || [object_6u9j9__c: [:]]

        "源主对象字段"             | [sourceTenantType: 1, sourceObjectApiName: "object_6u9j9__c", fieldMappings: [[sourceApiName: "testAbc__c"]]]                                                                                                                                                                                                                                                                                                                                                                                                                       || [object_6u9j9__c: ["testAbc__c": []]]
        "源主对象字段2"            | [sourceTenantType: 1, sourceObjectApiName: "object_6u9j9__c", fieldMappings: [[sourceApiName: "testAbc__c"], [sourceApiName: "test123__c"]]]                                                                                                                                                                                                                                                                                                                                                                                        || [object_6u9j9__c: ["test123__c": [], "testAbc__c": []]]

        // 从对象

        "源从对象字段"             | [sourceTenantType: 1, detailObjectMappings: [["sourceObjectApiName": "abc", fieldMappings: [[sourceApiName: "aaa"], [sourceApiName: "bbb"]]], ["sourceObjectApiName": "def", fieldMappings: [[sourceApiName: "fff"], [sourceApiName: "ddd"]]]]]                                                                                                                                                                                                                                                                                     || [abc: ["aaa": [], "bbb": []], def: ["ddd": [], "fff": []]]
        "目标无从对象字段"         | [sourceTenantType: 2, detailObjectMappings: [["sourceObjectApiName": "abc", fieldMappings: [[sourceApiName: "aaa"], [sourceApiName: "bbb"]]], ["sourceObjectApiName": "def", fieldMappings: [[sourceApiName: "fff"], [sourceApiName: "ddd"]]]]]                                                                                                                                                                                                                                                                                     || [:]

        // 主对象和从对象同时存在字段映射
        "主从对象同时有字段"       | [sourceTenantType: 1, sourceObjectApiName: "object_6u9j9__c", fieldMappings: [[sourceApiName: "testAbc__c"]], detailObjectMappings: [["sourceObjectApiName": "sub_object", fieldMappings: [[sourceApiName: "sub_field"]]]]]                                                                                                                                                                                                                                                                                                         || [sub_object: ["sub_field": []], object_6u9j9__c: ["testAbc__c": []]]


        // 数据范围节点
        "源主对象数据范围"         | [sourceTenantType: 1, sourceObjectApiName: "object_6u9j9__c", syncConditions: [apiName: "asd", filters: [[[fieldApiName: "qqq"], [fieldApiName: "www"]]]]]                                                                                                                                                                                                                                                                                                                                                                          || [asd: ["qqq": [], "www": []], object_6u9j9__c: [:]]
        "源从对象数据范围"         | [sourceTenantType: 1, detailObjectSyncConditions: [[apiName: "asd", filters: [[[fieldApiName: "qqq"], [fieldApiName: "www"]]]], [apiName: "zxc", filters: [[[fieldApiName: "zzz"], [fieldApiName: "ccc"]]]]]]                                                                                                                                                                                                                                                                                                                       || [asd: ["qqq": [], "www": []], zxc: ["ccc": [], "zzz": []]]

        // 源对象数据范围与字段映射结合
        "源对象数据范围与字段映射" | [sourceTenantType   : 1,
                                      sourceObjectApiName: "object_6u9j9__c",
                                      fieldMappings      : [[sourceApiName: "testField"]],
                                      syncConditions     : [apiName: "condition", filters: [[[fieldApiName: "filterField"]]]]
        ]                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                || [condition: ["filterField": []], object_6u9j9__c: ["testField": []]]


        // 反写节点
        "源主对象反写带字段"       | [sourceTenantType: 1, sourceObjectApiName: "object_6u9j9__c", integrationStreamNodes: [reverseWriteNode: [sourceObjectApiName: "object_6u9j9__c", fieldMappings: [[sourceApiName: "testAbc__c"], [sourceApiName: "test123__c"]]]]]                                                                                                                                                                                                                                                                                                  || [object_6u9j9__c: ["test123__c": [], "testAbc__c": []]]

        "源从对象反写带字段"       | [sourceTenantType: 1, integrationStreamNodes: [reverseWriteNode: [detailObjectMappings: [[sourceObjectApiName: "SA1", fieldMappings: [[sourceApiName: "sa1"], [sourceApiName: "sa1a"]]], [sourceObjectApiName: "SA2", fieldMappings: [[sourceApiName: "sa2"], [sourceApiName: "sa2a"]]]]]]]                                                                                                                                                                                                                                         || [SA1: ["sa1": [], "sa1a": []], SA2: ["sa2": [], "sa2a": []]]

        // 查询节点
        "源查询主Crm节点"          | [sourceTenantType: 1, integrationStreamNodes: [queryCrmObject2DestNodeBySource: [queryObjectToDestObject: [["queryObjectMappingData": [sourceObjectApiName: "SA1", destObjectApiName: "DA1", queryFieldMappings: [[[fieldApiName: "sa1", fieldValue: ["da1"]], [fieldApiName: "sa1a", fieldValue: ["da1a"]]]]]], ["queryObjectMappingData": [sourceObjectApiName: "SA2", destObjectApiName: "DA2", queryFieldMappings: [[[fieldApiName: "sa2", fieldValue: ["da2"]], [fieldApiName: "sa2a", fieldValue: ["da2a"]]]]]]]]]]           || [SA1: ["sa1": [], "sa1a": []], DA2: ["da2a": [], "da2": []], SA2: ["sa2": [], "sa2a": []], DA1: ["da1a": [], "da1": []]]
        "源查询主Crm节点2"         | [sourceTenantType: 1, integrationStreamNodes: [queryCrmObject2DestNodeBySource: [queryObjectToDestObject: [[queryData2DestDataMapping: [sourceObjectApiName: "SA1", destObjectApiName: "DA1", fieldMappings: [[sourceApiName: "sa1", destApiName: "da1"], [sourceApiName: "sa1a", destApiName: "da1a"]]]], [queryData2DestDataMapping: [sourceObjectApiName: "SA2", destObjectApiName: "DA2", fieldMappings: [[sourceApiName: "sa2", destApiName: "da2"], [sourceApiName: "sa2a", destApiName: "da2a"]]]]]]]]                       || [SA1: ["sa1": [], "sa1a": []], DA2: ["da2a": [], "da2": []], SA2: ["sa2": [], "sa2a": []], DA1: ["da1a": [], "da1": []]]

        "源查询从Crm节点"          | [sourceTenantType: 1, integrationStreamNodes: [queryCrmObject2DestNodeBySource: [detailQueryData2DestDataMapping: [[["queryObjectMappingData": [sourceObjectApiName: "SA1", destObjectApiName: "DA1", queryFieldMappings: [[[fieldApiName: "sa1", fieldValue: ["da1"]], [fieldApiName: "sa1a", fieldValue: ["da1a"]]]]]], ["queryObjectMappingData": [sourceObjectApiName: "SA2", destObjectApiName: "DA2", queryFieldMappings: [[[fieldApiName: "sa2", fieldValue: ["da2"]], [fieldApiName: "sa2a", fieldValue: ["da2a"]]]]]]]]]]] || [SA1: ["sa1": [], "sa1a": []], DA2: ["da2a": [], "da2": []], SA2: ["sa2": [], "sa2a": []], DA1: ["da1a": [], "da1": []]]
        "源查询从Crm节点2"         | [sourceTenantType: 1, integrationStreamNodes: [queryCrmObject2DestNodeBySource: [detailQueryData2DestDataMapping: [[[queryData2DestDataMapping: [sourceObjectApiName: "SA1", destObjectApiName: "DA1", fieldMappings: [[sourceApiName: "sa1", destApiName: "da1"], [sourceApiName: "sa1a", destApiName: "da1a"]]]], [queryData2DestDataMapping: [sourceObjectApiName: "SA2", destObjectApiName: "DA2", fieldMappings: [[sourceApiName: "sa2", destApiName: "da2"], [sourceApiName: "sa2a", destApiName: "da2a"]]]]]]]]]             || [SA1: ["sa1": [], "sa1a": []], DA2: ["da2a": [], "da2": []], SA2: ["sa2": [], "sa2a": []], DA1: ["da1a": [], "da1": []]]

        "目标查询主Crm节点"        | [sourceTenantType: 1, integrationStreamNodes: [queryCrmObject2DestNodeByDest: [queryObjectToDestObject: [["queryObjectMappingData": [sourceObjectApiName: "SA1", destObjectApiName: "DA1", queryFieldMappings: [[[fieldApiName: "sa1", fieldValue: ["da1"]], [fieldApiName: "sa1a", fieldValue: ["da1a"]]]]]], ["queryObjectMappingData": [sourceObjectApiName: "SA2", destObjectApiName: "DA2", queryFieldMappings: [[[fieldApiName: "sa2", fieldValue: ["da2"]], [fieldApiName: "sa2a", fieldValue: ["da2a"]]]]]]]]]]             || [SA1: ["sa1": [], "sa1a": []], SA2: ["sa2": [], "sa2a": []]]
        "目标查询从Crm节点2"       | [sourceTenantType: 1, integrationStreamNodes: [queryCrmObject2DestNodeByDest: [detailQueryData2DestDataMapping: [[[queryData2DestDataMapping: [sourceObjectApiName: "SA1", destObjectApiName: "DA1", fieldMappings: [[sourceApiName: "sa1", destApiName: "da1"], [sourceApiName: "sa1a", destApiName: "da1a"]]]], [queryData2DestDataMapping: [sourceObjectApiName: "SA2", destObjectApiName: "DA2", fieldMappings: [[sourceApiName: "sa2", destApiName: "da2"], [sourceApiName: "sa2a", destApiName: "da2a"]]]]]]]]]               || [SA1: ["sa1": [], "sa1a": []], SA2: ["sa2": [], "sa2a": []]]


        "目标查询主Crm节点3"       | [sourceTenantType: 2, integrationStreamNodes: [queryCrmObject2DestNodeByDest: [queryObjectToDestObject: [["queryObjectMappingData": [sourceObjectApiName: "SA1", destObjectApiName: "DA1", queryFieldMappings: [[[fieldApiName: "sa1", fieldValue: ["da1"]], [fieldApiName: "sa1a", fieldValue: ["da1a"]]]]]], ["queryObjectMappingData": [sourceObjectApiName: "SA2", destObjectApiName: "DA2", queryFieldMappings: [[[fieldApiName: "sa2", fieldValue: ["da2"]], [fieldApiName: "sa2a", fieldValue: ["da2a"]]]]]]]]]]             || [SA1: ["sa1": [], "sa1a": []], DA2: ["da2a": [], "da2": []], SA2: ["sa2": [], "sa2a": []], DA1: ["da1a": [], "da1": []]]
        "目标查询从Crm节点4"       | [sourceTenantType: 2, integrationStreamNodes: [queryCrmObject2DestNodeByDest: [detailQueryData2DestDataMapping: [[[queryData2DestDataMapping: [sourceObjectApiName: "SA1", destObjectApiName: "DA1", fieldMappings: [[sourceApiName: "sa1", destApiName: "da1"], [sourceApiName: "sa1a", destApiName: "da1a"]]]], [queryData2DestDataMapping: [sourceObjectApiName: "SA2", destObjectApiName: "DA2", fieldMappings: [[sourceApiName: "sa2", destApiName: "da2"], [sourceApiName: "sa2a", destApiName: "da2a"]]]]]]]]]               || [SA1: ["sa1": [], "sa1a": []], DA2: ["da2a": [], "da2": []], SA2: ["sa2": [], "sa2a": []], DA1: ["da1a": [], "da1": []]]
    }
}
