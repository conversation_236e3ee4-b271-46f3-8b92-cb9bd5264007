package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.github.jedis.support.MergeJedisCmd;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.TestRule;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.InOrder;
import org.junit.rules.TestWatcher;
import org.junit.runner.Description;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * LevelLimiterManager测试类
 * 
 * 测试场景说明：
 * 1. 首次调用测试
 *    - 首次调用时返回初始速度
 *    - 验证Redis中正确设置了初始速度
 * 
 * 2. 高失败率降速测试
 *    - 失败率>10%时速度减半
 *    - 验证速度不会低于初始速度
 * 
 * 3. 低失败率加速测试
 *    - 失败率<=10%时速度翻倍
 *    - 验证速度不会超过目标速度
 * 
 * 4. 时间间隔测试
 *    - 两次调整之间必须间隔60秒
 *    - 未到间隔时间时保持当前速度
 * 
 * 5. Redis异常测试
 *    - Redis操作失败时返回当前速度
 *    - 数据解析异常时返回当前速度
 * 
 * 6. 请求记录测试
 *    - 成功请求：只增加总请求数
 *    - 失败请求：只增加失败数
 *    - 验证Redis key的过期时间设置
 * 
 * 7. 速度重置测试
 *    - 速度达到0时重置为初始速度
 *    - 验证重置后的速度计算正确
 * 
 * 8. 渐进式加速测试
 *    - 从初始速度开始逐步加速
 *    - 验证每次加速的幅度正确
 * 
 * 9. 完整速度周期测试
 *    - 从初始速度加速到3000
 *    - 再从3000减速到初始速度
 *    - 验证整个过程中的速度变化
 * 
 * 10. 10分钟无数据同步重置测试
 *     - 10分钟内无数据同步时重置速度
 *     - 验证重置后的状态正确
 */
@RunWith(MockitoJUnitRunner.class)
public class LevelLimiterManagerTest {

    @Rule
    public final TestRule watchman = new TestWatcher() {
        @Override
        protected void failed(Throwable e, Description description) {
            System.out.println("Test failed: " + description.getMethodName());
            System.out.println("Failure details: " + e.getMessage());
        }
    };

    private List<String> testResults;

    private LevelLimiterManager manager;

    @Mock
    private RedisDataSource redisDataSource;

    @Mock
    private MergeJedisCmd redisOperations;

    @Mock
    private Clock clock;

    // 常量定义，与LevelLimiterManager保持一致
    private static final String REDIS_KEY_PREFIX = "ERPDSS:LEVELLIMIT:";
    private static final String REDIS_LAST_ADJUST_PREFIX = "ERPDSS:LEVEL_LIMIT_LAST_ADJUST:";
    private static final int INITIAL_SPEED = 100;
    private static final int REDIS_KEY_EXPIRE_SECONDS = 24*60*60;
    private static final int SPEED_ADJUST_INTERVAL_SECONDS = 60;
    private static final int NO_SYNC_RESET_SECONDS = 1800;

    // 添加测试常量
    private static final String TEST_TENANT = "test_tenant";
    private static final String TEST_OBJECT = "test_object";
    private static final String TEST_KEY = TEST_TENANT + "_" + TEST_OBJECT;

    @Before
    public void setUp() throws Exception {
        // 重置所有mock
        reset(redisDataSource, redisOperations, clock);
        
        // 创建manager实例
        manager = new LevelLimiterManager();
        
        // 使用反射设置RedisDataSource
        Field redisDataSourceField = LevelLimiterManager.class.getDeclaredField("redisDataSource");
        redisDataSourceField.setAccessible(true);
        redisDataSourceField.set(manager, redisDataSource);
        
        // 使用反射设置Clock
        Field clockField = LevelLimiterManager.class.getDeclaredField("clock");
        clockField.setAccessible(true);
        clockField.set(manager, clock);
        
        // 设置RedisOperations的mock行为
        when(redisDataSource.get(anyString())).thenReturn(redisOperations);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(System.currentTimeMillis());

        // 初始化测试结果列表
        testResults = new ArrayList<>();
    }

    /**
     * 汇总测试方法，按顺序执行所有测试场景
     * 收集所有测试结果并输出汇总报告
     */
    @Test
    public void testAllScenarios() throws Exception {
        try {
            // 1. 首次调用测试
            runTest("首次调用测试", () -> {
                testCalculateAndUpdateSpeed_WhenFirstCall_ShouldReturnInitialSpeed();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 2. 高失败率降速测试
            runTest("高失败率降速测试", () -> {
                testCalculateAndUpdateSpeed_WhenHighFailureRate_ShouldDecreaseSpeed();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 3. 低失败率加速测试
            runTest("低失败率加速测试", () -> {
                testCalculateAndUpdateSpeed_WhenLowFailureRate_ShouldIncreaseSpeed();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 4. 时间间隔测试
            runTest("时间间隔测试", () -> {
                testCalculateAndUpdateSpeed_WhenWithinInterval_ShouldKeepCurrentSpeed();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
                     
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 6. 请求记录测试
            runTest("成功请求记录测试", () -> {
                testRecordRequest_WhenSuccess_ShouldIncrementTotalOnly();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            runTest("失败请求记录测试", () -> {
                testRecordRequest_WhenFailure_ShouldIncrementFailOnly();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 7. 速度重置测试
            runTest("速度重置测试", () -> {
                testCalculateAndUpdateSpeed_WhenSpeedZero_ShouldResetToInitialSpeed();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 8. 渐进式加速测试
            runTest("渐进式加速测试", () -> {
                testCalculateAndUpdateSpeed_WhenProgressiveAcceleration_ShouldIncreaseGradually();
            });

            runTest("完整渐进式加速测试", () -> {
                testCalculateAndUpdateSpeed_WhenFullProgressiveAcceleration_ShouldReachTargetSpeed();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 9. 完整速度周期测试
            runTest("完整速度周期测试", () -> {
                testCalculateAndUpdateSpeed_WhenFullSpeedCycle_ShouldCompleteCycle();
            });
            
            // 重置所有mock状态
            reset(redisDataSource, redisOperations, clock);
            setUp();
            
            // 10. 10分钟无数据同步重置测试
            runTest("10分钟无数据同步重置测试", () -> {
                testCalculateAndUpdateSpeed_WhenNoSyncForLongTime_ShouldResetToInitialSpeed();
            });
            
            // 输出所有测试结果
            System.out.println("\n测试结果汇总:");
            for (String result : testResults) {
                System.out.println(result);
            }
            
        } catch (Exception e) {
            System.out.println("测试执行过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 运行单个测试场景并收集结果
     */
    private void runTest(String testName, Runnable test) {
        try {
            test.run();
            testResults.add("✓ " + testName + " - 通过");
        } catch (AssertionError e) {
            testResults.add("✗ " + testName + " - 失败: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            testResults.add("✗ " + testName + " - 异常: " + e.getMessage());
            throw new RuntimeException("测试执行异常: " + testName, e);
        }
    }

    /**
     * 测试场景1：首次调用测试
     * 功能说明：
     * 1. 验证首次调用时返回初始速度(100) * 2
     * 2. 验证Redis中正确设置了初始速度
     * 3. 验证last_speed_adjust时间戳被正确设置
     * 预期结果：
     * - 返回初始速度100*2
     * - Redis中speed key被设置为100*2
     * - last_speed_adjust被设置为当前时间
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenFirstCall_ShouldReturnInitialSpeed() {
        // 准备测试数据
        String key = TEST_KEY;
        int targetSpeed = 1000;
        long currentTime = System.currentTimeMillis();

        // 设置Redis mock行为 - 返回空Map模拟首次调用
        Map<String, String> emptyInfo = new HashMap<>();
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(emptyInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);

        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);

        // 执行测试
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);

        // 验证结果
        assertEquals("首次调用应该返回初始速度", INITIAL_SPEED*2, result);
        
    }

    /**
     * 测试场景2：高失败率降速测试
     * 功能说明：
     * 1. 验证当失败率超过10%时速度减半
     * 2. 验证速度不会低于初始速度
     * 预期结果：
     * - 当前速度200，失败率20%，新速度应为100
     * - Redis中speed key被更新为100
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenHighFailureRate_ShouldDecreaseSpeed() {
        // Given
        String key = TEST_KEY;
        int targetSpeed = 1000;
        int currentSpeed = 200;
        long currentTime = System.currentTimeMillis();
        
        // 模拟高失败率
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(currentSpeed));
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("total", "100");
        initialInfo.put("fail", "20"); // 20%失败率
        initialInfo.put("last_sync", String.valueOf(currentTime/1000));
        initialInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // When
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);
        
        // Then
        assertEquals("高失败率时速度应该减半", currentSpeed / 2, result);
    }

    /**
     * 测试场景3：低失败率加速测试
     * 功能说明：
     * 1. 验证当失败率低于10%时速度翻倍
     * 2. 验证速度不会超过目标速度
     * 预期结果：
     * - 当前速度100，失败率5%，新速度应为200
     * - Redis中speed key被更新为200
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenLowFailureRate_ShouldIncreaseSpeed() {
        // 准备测试数据
        String key = TEST_KEY;
        int targetSpeed = 1000;
        int currentSpeed = 100;
        long currentTime = System.currentTimeMillis();

        // 设置Redis mock行为
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(currentSpeed));
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("total", "100");
        initialInfo.put("fail", "5"); // 5% 失败率
        initialInfo.put("last_sync", String.valueOf(currentTime/1000));
        // 确保可以调整速度 - 设置上次调整时间为61秒前
        initialInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);

        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);

        // 执行测试
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);

        // 验证结果
        assertEquals("低失败率时速度应该翻倍", currentSpeed * 2, result);
      
    }

    /**
     * 测试场景4：时间间隔测试
     * 功能说明：
     * 1. 验证两次调整之间必须间隔60秒
     * 2. 验证未到间隔时间时保持当前速度
     * 预期结果：
     * - 30秒内再次调用，速度保持不变
     * - 不更新Redis中的speed值
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenWithinInterval_ShouldKeepCurrentSpeed() {
        // Given
        String key = TEST_KEY;
        int targetSpeed = 1000;
        int currentSpeed = INITIAL_SPEED;  // 使用 INITIAL_SPEED 常量
        long currentTime = System.currentTimeMillis();
        
        // 模拟在调整间隔内
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(currentSpeed));
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("last_adjust", String.valueOf(currentTime - 30000)); // 30秒前
        initialInfo.put("total", "100");
        initialInfo.put("fail", "5");
        initialInfo.put("last_sync", String.valueOf(currentTime));
        
        System.out.println("Test setup:");
        System.out.println("Current time: " + currentTime);
        System.out.println("Last adjust time: " + (currentTime - 30000));
        System.out.println("Time difference: " + (currentTime - (currentTime - 30000)) + "ms");
        System.out.println("Initial state: " + initialInfo);
        
        String infoKey = buildRedisKey(key, "info");
        String lastAdjustKey = REDIS_LAST_ADJUST_PREFIX + key;
        
        // 使用Answer动态mock hgetAll，确保每次调用返回不同对象
        AtomicInteger callCount = new AtomicInteger(0);
        when(redisOperations.hgetAll(eq(infoKey))).thenAnswer(invocation -> {
            if (callCount.getAndIncrement() == 0) {
                return initialInfo;
            } else {
                return new HashMap<>(initialInfo);
            }
        });
        
        // 设置 last_adjust 的 mock 行为
        when(redisOperations.get(eq(lastAdjustKey))).thenReturn(String.valueOf(currentTime - 30000));
        
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // When
        clearInvocations(redisOperations); // 清除之前的mock调用记录
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);
        
        // Then
        System.out.println("Test result:");
        System.out.println("Expected speed: " + currentSpeed);
        System.out.println("Actual speed: " + result);
        assertEquals("在调整间隔内应该保持当前速度", currentSpeed, result);
    }

    /**
     * 测试场景5：Redis异常测试
     * 功能说明：
     * 1. 验证Redis操作失败时返回当前速度
     * 2. 验证数据解析异常时返回当前速度
     * 预期结果：
     * - Redis异常时返回当前速度100
     * - 不更新Redis中的speed值
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenRedisError_ShouldReturnCurrentSpeed() {
        // Given
        String key = TEST_KEY;
        int targetSpeed = 1000;
        int currentSpeed = 100;
        long currentTime = System.currentTimeMillis();
        
        // 设置Redis mock行为
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(currentSpeed));
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("last_adjust", String.valueOf(currentTime - TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1))); // 确保可以调整速度
        initialInfo.put("total", "100");
        initialInfo.put("fail", "5");
        initialInfo.put("last_sync", String.valueOf(currentTime));
        
        String infoKey = buildRedisKey(key, "info");
        String lastAdjustKey = REDIS_LAST_ADJUST_PREFIX + key;
        
        // 第一次调用返回正常数据
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.get(eq(lastAdjustKey))).thenReturn(String.valueOf(currentTime - TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1)));
        
        // 第二次调用抛出异常
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenThrow(new RuntimeException("Redis error"));
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // When
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);
        
        // Then
        assertEquals("Redis错误时应该返回当前速度", currentSpeed, result);        
     
    }

    /**
     * 测试场景6.1：成功请求记录测试
     * 功能说明：
     * 1. 验证成功请求只增加总请求数
     * 2. 验证不增加失败数
     * 3. 验证Redis key的过期时间设置
     * 预期结果：
     * - total计数器加1
     * - fail计数器不变
     * - total key的过期时间被设置为120秒
     */
    @Test
    public void testRecordRequest_WhenSuccess_ShouldIncrementTotalOnly() {
        // Given
        String key = TEST_KEY;
        long currentTime = System.currentTimeMillis();
        
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(INITIAL_SPEED));
        initialInfo.put("total", "0");
        initialInfo.put("fail", "0");
        initialInfo.put("last_sync", String.valueOf(currentTime));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // When
        manager.recordRequest(key, true);
        
        // Then
        // 验证Redis操作
        // InOrder inOrder = inOrder(redisOperations);
        // inOrder.verify(redisOperations).hmset(eq(infoKey), any(Map.class));
        // inOrder.verify(redisOperations).expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS));
    }

    /**
     * 测试场景6.2：失败请求记录测试
     * 功能说明：
     * 1. 验证失败请求只增加失败数
     * 2. 验证不增加总请求数
     * 3. 验证Redis key的过期时间设置
     * 预期结果：
     * - fail计数器加1
     * - total计数器不变
     * - fail key的过期时间被设置为120秒
     */
    @Test
    public void testRecordRequest_WhenFailure_ShouldIncrementFailOnly() {
        // Given
        String key = TEST_KEY;
        long currentTime = System.currentTimeMillis();
        
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(INITIAL_SPEED));
        initialInfo.put("total", "0");
        initialInfo.put("fail", "0");
        initialInfo.put("last_sync", String.valueOf(currentTime));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // When
        manager.recordRequest(key, false);
        
        // Then
        // 验证Redis操作
        // InOrder inOrder = inOrder(redisOperations);
        // inOrder.verify(redisOperations).hmset(eq(infoKey), any(Map.class));
        // inOrder.verify(redisOperations).expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS));
    }

    /**
     * 测试场景7：速度重置测试
     * 功能说明：
     * 1. 验证速度达到0时重置为初始速度
     * 2. 验证重置后的速度计算正确
     * 预期结果：
     * - 当前速度为0时，重置为初始速度100
     * - Redis中speed key被更新为100
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenSpeedZero_ShouldResetToInitialSpeed() {
        // Given
        String key = TEST_KEY;
        int targetSpeed = 1000;
        long currentTime = System.currentTimeMillis();
        
        // 模拟当前速度为0
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", "0");
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("last_adjust", String.valueOf(currentTime - TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1)));
        initialInfo.put("total", "0");
        initialInfo.put("fail", "0");
        initialInfo.put("last_sync", String.valueOf(currentTime));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // When
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);
        
        // Then
        assertEquals(INITIAL_SPEED, result);
    }

    /**
     * 测试场景8：渐进式加速测试
     * 功能说明：
     * 1. 验证从初始速度开始逐步加速
     * 2. 验证每次加速的幅度正确
     * 预期结果：
     * - 速度从100逐步增加到3000
     * - 每次加速翻倍，直到达到目标速度
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenProgressiveAcceleration_ShouldIncreaseGradually() {
        // 设置初始状态
        long currentTime = System.currentTimeMillis();
        String key = TEST_KEY;
        int targetSpeed = 1000;
        
        // 第一次调用 - 应该返回初始速度
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(INITIAL_SPEED));
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        initialInfo.put("total", "100");  // 添加一些请求记录
        initialInfo.put("fail", "5");    // 添加一些失败记录，但保持在10%以下
        initialInfo.put("last_sync", String.valueOf(currentTime/1000));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // 第一次调用 - 应该返回初始速度*2
        System.out.println("First call - Initial state: " + initialInfo);
        int actualSpeed1 = manager.calculateAndUpdateSpeed(key, targetSpeed);
        System.out.println("First call - Expected: " + INITIAL_SPEED*2 + ", Actual: " + actualSpeed1);
        assertEquals("第一次调用应该返回初始速度*2", INITIAL_SPEED*2, actualSpeed1);
        
        // 更新状态 - 设置第一次调用后的Redis数据
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        Map<String, String> state1 = new HashMap<>();
        state1.put("current_speed", String.valueOf(INITIAL_SPEED*2));  // 当前速度应该是200
        state1.put("target_speed", String.valueOf(targetSpeed));
        state1.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        state1.put("total", "100");  // 添加一些请求记录
        state1.put("fail", "5");    // 添加一些失败记录，但保持在10%以下
        state1.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(state1);
        
        // 第二次调用 - 应该加速到400
        System.out.println("Second call - State: " + state1);
        int actualSpeed2 = manager.calculateAndUpdateSpeed(key, targetSpeed);
        System.out.println("Second call - Expected: 400, Actual: " + actualSpeed2);
        assertEquals("第二次调用应该加速到400", INITIAL_SPEED*4, actualSpeed2);
        
        // 更新状态 - 设置第二次调用后的Redis数据
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        Map<String, String> state2 = new HashMap<>();
        state2.put("current_speed", String.valueOf(INITIAL_SPEED*4));  // 当前速度应该是400
        state2.put("target_speed", String.valueOf(targetSpeed));
        state2.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        state2.put("total", "100");
        state2.put("fail", "5");
        state2.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(state2);
        
        // 第三次调用 - 应该加速到800
        System.out.println("Third call - State: " + state2);
        int actualSpeed3 = manager.calculateAndUpdateSpeed(key, targetSpeed);
        System.out.println("Third call - Expected: 800, Actual: " + actualSpeed3);
        assertEquals("第三次调用应该加速到800", INITIAL_SPEED*8, actualSpeed3);
    }

    /**
     * 测试场景8.1：完整渐进式加速测试
     * 功能说明：
     * 1. 验证从初始速度开始逐步加速到目标速度
     * 2. 验证每次加速的幅度正确
     * 预期结果：
     * - 速度从100逐步增加到3000
     * - 每次加速翻倍，直到达到目标速度
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenFullProgressiveAcceleration_ShouldReachTargetSpeed() {
        // 设置初始状态
        long currentTime = System.currentTimeMillis();
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(INITIAL_SPEED));
        initialInfo.put("target_speed", "1600");
        initialInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        initialInfo.put("total", "100"); // 添加一些请求记录，避免重置为初始速度
        initialInfo.put("fail", "5");    // 添加一些失败记录，但保持在10%以下
        initialInfo.put("last_sync", String.valueOf(currentTime/1000));
        
        String infoKey = buildRedisKey(TEST_KEY, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // 第一次调用 - 加速到200
        int speed1 = manager.calculateAndUpdateSpeed(TEST_KEY, 1600);
        System.out.println("First call - Expected: 200, Actual: " + speed1);
        assertEquals("第一次调用应该加速到200", INITIAL_SPEED*2, speed1);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        Map<String, String> updatedInfo = new HashMap<>(initialInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*2));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第二次调用 - 加速到400
        int speed2 = manager.calculateAndUpdateSpeed(TEST_KEY, 1600);
        System.out.println("Second call - Expected: 400, Actual: " + speed2);
        assertEquals("第二次调用应该加速到400", INITIAL_SPEED*4, speed2);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        updatedInfo = new HashMap<>(updatedInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*4));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第三次调用 - 加速到800
        int speed3 = manager.calculateAndUpdateSpeed(TEST_KEY, 1600);
        System.out.println("Third call - Expected: 800, Actual: " + speed3);
        assertEquals("第三次调用应该加速到800", INITIAL_SPEED*8, speed3);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        updatedInfo = new HashMap<>(updatedInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*8));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第四次调用 - 加速到1600
        int speed4 = manager.calculateAndUpdateSpeed(TEST_KEY, 1600);
        System.out.println("Fourth call - Expected: 1600, Actual: " + speed4);
        assertEquals("第四次调用应该加速到1600", 1600, speed4);
    }

    /**
     * 测试场景9：完整速度周期测试
     * 功能说明：
     * 1. 验证从初始速度加速到目标速度
     * 2. 验证从目标速度减速回初始速度
     * 3. 验证整个过程中的速度变化正确
     * 预期结果：
     * - 加速阶段：100 -> 200 -> 400 -> 800 -> 1600 -> 3000
     * - 减速阶段：3000 -> 1500 -> 750 -> 375 -> 187 -> 100
     * - 每个阶段的速度变化符合预期
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenFullSpeedCycle_ShouldCompleteCycle() {
        // 设置初始状态
        long currentTime = System.currentTimeMillis();
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", String.valueOf(INITIAL_SPEED));
        initialInfo.put("target_speed", "800");
        initialInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        initialInfo.put("total", "100"); // 添加一些请求记录，避免重置为初始速度
        initialInfo.put("fail", "5");    // 添加一些失败记录，但保持在10%以下
        initialInfo.put("last_sync", String.valueOf(currentTime/1000));
        
        String infoKey = buildRedisKey(TEST_KEY, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // 加速阶段
        // 第一次调用 - 加速到200
        int speed1 = manager.calculateAndUpdateSpeed(TEST_KEY, 800);
        manager.recordRequest(TEST_KEY, true);
        System.out.println("First call - Expected: 200, Actual: " + speed1);
        assertEquals("第一次调用应该加速到200", INITIAL_SPEED*2, speed1);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        Map<String, String> updatedInfo = new HashMap<>(initialInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*2));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第二次调用 - 加速到400
        int speed2 = manager.calculateAndUpdateSpeed(TEST_KEY, 800);
        manager.recordRequest(TEST_KEY, true);
        System.out.println("Second call - Expected: 400, Actual: " + speed2);
        assertEquals("第二次调用应该加速到400", INITIAL_SPEED*4, speed2);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        updatedInfo = new HashMap<>(updatedInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*4));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第三次调用 - 加速到800
        int speed3 = manager.calculateAndUpdateSpeed(TEST_KEY, 800);
        manager.recordRequest(TEST_KEY, true);
        System.out.println("Third call - Expected: 800, Actual: " + speed3);
        assertEquals("第三次调用应该加速到800", INITIAL_SPEED*8, speed3);
        
        // 降速阶段
        // 更新状态 - 增加时间间隔并添加失败记录
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        updatedInfo = new HashMap<>(updatedInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*8));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("total", "100");
        updatedInfo.put("fail", "20"); // 20%失败率
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第四次调用 - 降速到400
        int speed4 = manager.calculateAndUpdateSpeed(TEST_KEY, 800);
        manager.recordRequest(TEST_KEY, true);
        System.out.println("Fourth call - Expected: 400, Actual: " + speed4);
        assertEquals("第四次调用应该降速到400", INITIAL_SPEED*4, speed4);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        updatedInfo = new HashMap<>(updatedInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*4));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("total", "100");
        updatedInfo.put("fail", "20"); // 保持20%失败率
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第五次调用 - 降速到200
        int speed5 = manager.calculateAndUpdateSpeed(TEST_KEY, 800);
        manager.recordRequest(TEST_KEY, true);
        System.out.println("Fifth call - Expected: 200, Actual: " + speed5);
        assertEquals("第五次调用应该降速到200", INITIAL_SPEED*2, speed5);
        
        // 更新状态 - 增加时间间隔
        currentTime += TimeUnit.SECONDS.toMillis(SPEED_ADJUST_INTERVAL_SECONDS + 1);
        when(clock.millis()).thenReturn(currentTime);
        
        updatedInfo = new HashMap<>(updatedInfo);
        updatedInfo.put("current_speed", String.valueOf(INITIAL_SPEED*2));
        updatedInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        updatedInfo.put("total", "100");
        updatedInfo.put("fail", "20"); // 保持20%失败率
        updatedInfo.put("last_sync", String.valueOf(currentTime/1000));
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(updatedInfo);
        
        // 第六次调用 - 降速到100
        int speed6 = manager.calculateAndUpdateSpeed(TEST_KEY, 800);
        manager.recordRequest(TEST_KEY, true);
        System.out.println("Sixth call - Expected: 100, Actual: " + speed6);
        assertEquals("第六次调用应该降速到100", INITIAL_SPEED, speed6);
    }

    /**
     * 测试场景10：10分钟无数据同步重置测试
     * 功能说明：
     * 1. 验证10分钟内无数据同步时重置速度
     * 2. 验证重置后的状态正确
     * 预期结果：
     * - 当前速度2000，11分钟无同步，重置为初始速度100
     * - Redis中speed key被更新为100
     * - last_speed_adjust被更新为当前时间
     */
    @Test
    public void testCalculateAndUpdateSpeed_WhenNoSyncForLongTime_ShouldResetToInitialSpeed() {
        // Given
        String key = TEST_KEY;
        int targetSpeed = 1000;
        long currentTime = System.currentTimeMillis();
        
        // 重置所有mock
        reset(redisOperations);
        
        // 模拟getCurrentState方法中的Redis操作顺序
        Map<String, String> initialInfo = new HashMap<>();
        initialInfo.put("current_speed", "2000");
        initialInfo.put("target_speed", String.valueOf(targetSpeed));
        initialInfo.put("last_adjust", String.valueOf(currentTime/1000 - SPEED_ADJUST_INTERVAL_SECONDS - 1));
        initialInfo.put("total", "100");
        initialInfo.put("fail", "5");
        // 设置最后同步时间为31分钟前（超过30分钟无同步阈值）
        initialInfo.put("last_sync", String.valueOf(currentTime/1000 - 31*60));
        
        String infoKey = buildRedisKey(key, "info");
        when(redisOperations.hgetAll(eq(infoKey))).thenReturn(initialInfo);
        when(redisOperations.hmset(eq(infoKey), any(Map.class))).thenReturn("OK");
        when(redisOperations.expire(eq(infoKey), eq(REDIS_KEY_EXPIRE_SECONDS))).thenReturn(1L);
        
        // 设置Clock的mock行为
        when(clock.millis()).thenReturn(currentTime);
        
        // 验证NO_SYNC_RESET_SECONDS的值
        assertEquals("NO_SYNC_RESET_SECONDS should be 1800 (30 minutes)", 1800, NO_SYNC_RESET_SECONDS);
        
        // 验证时间差计算
        long timeDiff = currentTime/1000 - (currentTime/1000 - 31*60);
        assertTrue("Time difference should be greater than 30 minutes", 
            timeDiff > NO_SYNC_RESET_SECONDS);
        
        // When
        int result = manager.calculateAndUpdateSpeed(key, targetSpeed);
        
        // Then
        assertEquals("长时间无同步应该重置为初始速度", INITIAL_SPEED, result);
    }

    private String buildRedisKey(String key, String type) {
        return REDIS_KEY_PREFIX + key + ":" + type;
    }
} 