package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.model.SimpleEmployeeMapping;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BindEmpMappingByFsIdArg extends SimpleEmployeeMapping {
    /**
     * 后处理异常时返回错误，默认false
     */
    private boolean returnErrorWhenPostProcessFailed;

    /**
     * 后处理异常时根据FsId删除数据，默认false
     */
    private boolean deleteByFsIdWhenPostProcessFailed;
}
