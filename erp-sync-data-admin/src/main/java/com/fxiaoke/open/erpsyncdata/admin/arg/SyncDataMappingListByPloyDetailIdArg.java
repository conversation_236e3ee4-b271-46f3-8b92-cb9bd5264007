package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@ApiModel
public class SyncDataMappingListByPloyDetailIdArg implements Serializable {
    @ApiModelProperty("策略明细id")
    private String ployDetailId;
    @ApiModelProperty("源对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("目标对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("主或从对象的映射id")
    private String syncDataMappingId;
    @ApiModelProperty("按状态筛选")
    private int status;
    @ApiModelProperty("源数据dataId")
    private String sourceDataId;
    @ApiModelProperty("目标数据id")
    private String destDataId;
    @ApiModelProperty("源数据dataName")
    private String sourceDataName;
    @ApiModelProperty("目标数据dataId")
    private String destDataName;
    @ApiModelProperty("状态详情")
    private String remark;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("lastSyncDataId")
    private String lastSyncDataId;
//    @ApiModelProperty("搜索内容")
//    private String searchText;
    @ApiModelProperty("每页大小")
    private Integer pageSize;
    @ApiModelProperty("页码")
    private Integer pageNumber;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")//跨度限制时间一个月
    private Long endTime;
    @ApiModelProperty("只需要totalCount")
    private Boolean onlyTotalCount=false;

    public boolean requireNotEmptyCondition(){
        return !StringUtils.isAllEmpty(this.getSourceDataName(),this.getSourceDataId(),this.getDestDataName(),this.getDestDataId(),this.getRemark());
        
    }

    public boolean requireDataIdNotEmpty(){
        return !StringUtils.isAllEmpty(this.getSourceDataId(),this.getDestDataId());

    }

}
