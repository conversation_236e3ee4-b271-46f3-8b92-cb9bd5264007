package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/3/25
 */
@Data
@ApiModel
public class TenantEnvInfo implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "旧的属性，前端可根据这个判断页面")
    private boolean isGray;

    @ApiModelProperty(value = "前端应用：vip、urgent、normal、gray")
    private String webApp;

    @ApiModelProperty(value = "路径，直接增加到路径后，正式环境为normal,gray环境为gray")
    private String env;

    @ApiModelProperty(value = "路径")
    private String path = "erp/syncdata/";

    @ApiModelProperty(value = "cep路径")
    private String cepPath = "FHH/EM1HERP";

    public void setEnv(String env) {
        this.env = env;
        this.cepPath = "FHH/EM1HERPOA" + env.toUpperCase() + "/cep/";
        if (!"normal".equals(env)) {
            this.path = "erp/syncdata" + env + "/";
        }
    }
}
