package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult
import com.fxiaoke.open.erpsyncdata.admin.service.FsCrmObjectService
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import spock.lang.Specification

class AdminNodeSettingServiceImplTest extends Specification {
    def "importFieldMapping"() {
        def ea = "88521"
        def tenantId = 88521
        def dcId = "643f7322b54ea80001767d86"
        def npath = "test"
        def userId = -10000
        def crmDcId = "643f7326b54ea8000176a191"
        def integrationStreamId = "5cfaf43c31d14f05a4a1d31b511a96ee";
        def direction = "CRM"

        I18NStringManager i18NStringManager = new I18NStringManager()

        def erpObjectFieldDao = Mock(ErpObjectFieldDao) {
            setTenantId(_) >> it

            ErpObjectFieldEntity query = new ErpObjectFieldEntity();
            query.setTenantId(String.valueOf(tenantId));
            query.setDataCenterId(dcId);
            query.setErpObjectApiName("BD_CustomerD.BillHead_1");

            queryList(query) >> {
                return [ErpObjectFieldEntity.builder().erpObjectApiName("BD_CustomerD.BillHead_1").fieldApiName("name")
                                .build(),
                        ErpObjectFieldEntity.builder().erpObjectApiName("BD_CustomerD.BillHead_1").fieldApiName("id")
                                .build()]
            }

            ErpObjectFieldEntity query1 = new ErpObjectFieldEntity();
            query1.setTenantId(String.valueOf(tenantId));
            query1.setDataCenterId(dcId);
            query1.setErpObjectApiName("BD_CustomerD.BD_FININFO_1");

            queryList(query1) >> {
                return [ErpObjectFieldEntity.builder().erpObjectApiName("BD_CustomerD.BD_FININFO_1").fieldApiName("FEMail")
                                .build()]
            }

            ErpObjectFieldEntity query2 = new ErpObjectFieldEntity();
            query2.setTenantId(String.valueOf(tenantId));
            query2.setDataCenterId(dcId);
            query2.setErpObjectApiName("BD_CustomerE.BillHead_1");

            queryList(query2) >> {
                return [ErpObjectFieldEntity.builder().erpObjectApiName("BD_CustomerE.BillHead_1").fieldApiName("name")
                                .build(),
                        ErpObjectFieldEntity.builder().erpObjectApiName("BD_CustomerE.BillHead_1").fieldApiName("id")
                                .build()]
            }
        }

        def fsCrmObjectService = Mock(FsCrmObjectService) {
            listObjectFieldsWithFilterBlackList(String.valueOf(tenantId), "AccountObj", lang) >> {

                ListObjectFieldsResult fieldsResult = new ListObjectFieldsResult()
                ObjectFieldResult objectFieldResult = new ObjectFieldResult()
                objectFieldResult.setApiName("name")
                ObjectFieldResult objectFieldResult1 = new ObjectFieldResult()
                objectFieldResult1.setApiName("id")
                fieldsResult.setFields([objectFieldResult, objectFieldResult1])
                return Result.newSuccess(fieldsResult)
            }

            listObjectFieldsWithFilterBlackList(String.valueOf(tenantId), "AccountAddrObj", lang) >> {
                Result<ListObjectFieldsResult> result = new Result<ListObjectFieldsResult>()
                ListObjectFieldsResult fieldsResult = new ListObjectFieldsResult()
                ObjectFieldResult objectFieldResult = new ObjectFieldResult()
                objectFieldResult.setApiName("eMail")
                fieldsResult.setFields([objectFieldResult])
                return Result.newSuccess(fieldsResult)
            }
        }

        def adminSyncPloyDetailDao = Mock(AdminSyncPloyDetailDao) {
            setTenantId(_) >> it

            updateByIdSelective(*_) >> 1
        }

        def idGenerator = Mock(IdGenerator) {
            get() >> "*********"
        }

        Map<String, String> erpObjectMap = new HashMap<>()
        if (id == 1) {
            erpObjectMap.put("BD_CustomerD", "BD_CustomerD.BillHead_1")
        } else if (id == 2) {
            erpObjectMap.put("BD_CustomerD", "BD_CustomerD.BillHead_1")
            erpObjectMap.put("BD_CustomerD.BD_FININFO", "BD_CustomerD.BD_FININFO_1")
        } else if (id == 3) {
            erpObjectMap.put("BD_CustomerE", "BD_CustomerE.BillHead_1")
        }

        def adminNodeSettingService = new AdminNodeSettingServiceImpl(adminSyncPloyDetailDao:adminSyncPloyDetailDao,
                fsCrmObjectService:fsCrmObjectService,
                erpObjectFieldDao:erpObjectFieldDao,
                i18NStringManager:i18NStringManager)

        def result = adminNodeSettingService.importFieldMapping(String.valueOf(tenantId),
                lang,
                dcId,
                crmDcId,
                integrationStreamId,
                integrationStreamExcelVos,
                direction,
                new HashMap<String, String>(),
                new HashMap<String,
                Map<String, String>>(),
                erpObjectMap)

        expect:
        result.success

        where:
        id  |  erpChannelEnum        |  lang                |  integrationStreamExcelVos
        1  |  ErpChannelEnum.ERP_K3CLOUD |  "zh-CN" |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name1")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("id")
                        .thirdPartyFieldApiName("id")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        2  |  ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerD")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户-地址")
                        .crmObjectApiName("AccountAddrObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("eMail")
                        .thirdPartyObjectLabel("客户-地址信息")
                        .thirdPartyObjectApiName("BD_CustomerD.BD_FININFO")
                        .thirdPartyFieldLabel("邮件")
                        .thirdPartyFieldApiName("FEMail")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        3  |  ErpChannelEnum.ERP_K3CLOUD  |  "zh-CN"             |  [
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerE")
                        .thirdPartyFieldLabel("名称")
                        .thirdPartyFieldApiName("name")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(false)
                        .build(),
                ErpIntegrationStreamExcelVo.builder()
                        .crmObjectLabel("客户")
                        .crmObjectApiName("AccountObj")
                        .crmFileName("客户名称")
                        .crmFileApiName("name")
                        .thirdPartyObjectLabel("自定义客户对象")
                        .thirdPartyObjectApiName("BD_CustomerE")
                        .thirdPartyFieldLabel("id")
                        .thirdPartyFieldApiName("id")
                        .thirdPartyFieldType("text")
                        .thirdPartyFieldRequired(true)
                        .build()
        ]
        }
}
