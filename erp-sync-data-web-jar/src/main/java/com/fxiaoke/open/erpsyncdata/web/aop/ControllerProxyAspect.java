package com.fxiaoke.open.erpsyncdata.web.aop;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpSyncDataBackStageEnvironmentEnum;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerFsAuthArg;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 15:42 2022/10/19
 * @Desc: 代理切面，切点是加了ControllerMethodProxy注解的controller
 */
@Component
@Aspect
@Slf4j
@Order(4)
public class ControllerProxyAspect {
    @Autowired
    private TenantEnvManager tenantEnvManager;


    @Pointcut("@annotation(com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object proxy(ProceedingJoinPoint point) throws Throwable {
        RestController restController = point.getTarget().getClass().getAnnotation(RestController.class);
        RequestMapping requestMapping = point.getTarget().getClass().getAnnotation(RequestMapping.class);
        if(requestMapping==null&&restController==null){//如果这两个注解同时为空，直接执行
            log.error("class[{}] is not a controller ,no proxy required",point.getClass().getName());
            return point.proceed();
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        ControllerMethodProxy controllerMethodProxy = method.getAnnotation(ControllerMethodProxy.class);
        if(controllerMethodProxy==null||!controllerMethodProxy.needProxy()){//注解为false,不需要代理
            return point.proceed();
        }
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        // 允许设置header跳过
        if (request.getHeader("ERPDSS_DEV_MODE")!=null) {
            return point.proceed();
        }
        HttpServletResponse response = requestAttributes.getResponse();
        String tenantId=null;
        String requestBody=null;
        Annotation[][] annotations = method.getParameterAnnotations();
        Object[] params = point.getArgs();
        for (int i = 0; i < annotations.length; i++) {
            Object param = params[i];
            Annotation[] paramAnn = annotations[i];
            //参数为空，直接下一个参数
            if (param == null || paramAnn.length == 0) {
                continue;
            }
            for (Annotation annotation : paramAnn) {
                //这里判断当前注解是否为ControllerTenantIDArg.class
                if (annotation.annotationType().equals(ControllerTenantIDArg.class)) {
                    if(StringUtils.isEmpty(tenantId)){//取第一个,不为空了就不取了
                        tenantId=param.toString();
                        try{
                           Integer.valueOf(tenantId);//测试转换一下，如果失败说明 tenantId非法
                        }catch (Exception e){
                            tenantId=null;
                            log.error("get tenantId is Exception param={}",param);
                        }

                    }
                }else if(annotation.annotationType().equals(RequestBody.class)){
                    requestBody= JSONObject.toJSONString(param);
                }else if(annotation.annotationType().equals(ControllerFsAuthArg.class)){
                    String fsAuth=(String)param;
                    ControllerFsAuthArg controllerFsAuthArg=(ControllerFsAuthArg)annotation;
                    String rawStr = new String(Base64.getDecoder().decode(fsAuth),"utf-8");
                    Map<String,String> fsAuthMap =  GsonUtil.fromJson(rawStr,Map.class);
                    if(org.apache.commons.lang3.StringUtils.isNotBlank(fsAuthMap.get(controllerFsAuthArg.tenantIdField()))){
                        tenantId = fsAuthMap.get(controllerFsAuthArg.tenantIdField()) ;
                    }
                }
            }
        }
        Boolean hadProxy = doProxy(tenantId, request, response, requestBody);
        if(!hadProxy){//没有走代理
            return point.proceed();
        }
        return null;//只能返回null，如果想要返回其他，doProxy里面就不能操作response的OutputStream，否则会重复
    }
    public Boolean doProxy(String tenantId, HttpServletRequest request, HttpServletResponse response, String requestBody) throws IOException, URISyntaxException {
        if (StringUtils.isEmpty(tenantId)) {
            log.info("doProxy tenantId is null");
            return false;
        }
        String targetAddr = getTargetAddr(tenantId);
        if (targetAddr == null) {
            return false;
        }
        final String requestURI = request.getRequestURI();
        URI uri = new URI(requestURI);
        String path = uri.getPath();
        String query = request.getQueryString();
        String target = targetAddr + path;
        if (query != null && !query.equals("") && !query.equals("null")) {
            target = target + "?" + query;
        }
        URI newUri = new URI(target);
        // 执行代理查询
        String methodName = request.getMethod();

        if (requestURI.contains("erp/syncdatagray/open/objdata") || requestURI.contains("erp/syncdata/open/webhook")) {
            // 检查推送和webhook路由错误
            log.warn("nginx路由错误 tenantId:{} uri:{}", tenantId, requestURI);
        }

        HttpMethod httpMethod = HttpMethod.resolve(methodName);
        if (httpMethod == null) {
            log.error("doProxy httpMethod is null");
            return false;
        }
        log.info("tenantId={} doProxy start target={}", tenantId, target);
        ClientHttpRequest delegate = new SimpleClientHttpRequestFactory().createRequest(newUri, httpMethod);
        Enumeration<String> headerNames = request.getHeaderNames();

        // 设置请求头
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            Enumeration<String> v = request.getHeaders(headerName);
            List<String> arr = new ArrayList<>();
            while (v.hasMoreElements()) {
                arr.add(v.nextElement());
            }
            delegate.getHeaders().put(headerName, arr);
        }
        if (StringUtils.isEmpty(requestBody)) {//如果使用了@RequestBody，request.getInputStream()流应该被关闭了
            StreamUtils.copy(request.getInputStream(), delegate.getBody());
        } else {
            delegate.getBody().write(requestBody.getBytes(request.getCharacterEncoding()));
        }

        // 执行远程调用
        ClientHttpResponse clientHttpResponse = delegate.execute();

        response.setStatus(clientHttpResponse.getStatusCode().value());
        // 设置响应头
        clientHttpResponse.getHeaders().forEach((key, value) -> value.forEach(it -> {
            if (!StrUtil.equalsIgnoreCase(key, "Transfer-Encoding") && !StrUtil.equalsIgnoreCase(key, "Content-length")) {
                response.setHeader(key, it);
            }
        }));
        StreamUtils.copy(clientHttpResponse.getBody(), response.getOutputStream());
        log.info("tenantId={} doProxy end status={}", tenantId, clientHttpResponse.getStatusCode().value());
        return true;
    }

    private String getTargetAddr(String tenantId) {
        if (ConfigCenter.WEB_ENV_URL == null || CollectionUtils.isEmpty(ConfigCenter.WEB_ENV_URL)) {//为空不做转发
            return null;
        }
        ErpSyncDataBackStageEnvironmentEnum tenantAllModelEnv = tenantEnvManager.getTenantAllModelEnv(tenantId);
        String tenantWebModelEnv = ConfigCenter.ALL_ENV_TO_WEB_ENV.get(tenantAllModelEnv.getEnvironment());
        Boolean environment = ConfigCenter.ServiceEnvironment.equals(tenantWebModelEnv);
        if (environment) {//环境是对的，不用转发
            return null;
        }
        if (ConfigCenter.WEB_ENV_URL.containsKey(tenantWebModelEnv)) {
            return ConfigCenter.WEB_ENV_URL.get(tenantWebModelEnv);
        }
        return null;
    }
}
