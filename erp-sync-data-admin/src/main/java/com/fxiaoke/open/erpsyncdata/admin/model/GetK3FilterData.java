package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/4 10:35:54
 */
public interface GetK3FilterData {
    @Data
    class Arg {
        /**
         * 中间对象apiName
         */
        private String objectApiName;

        /**
         * 查询类型 1-正常列表 2-作废列表
         */
        private Integer type;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<List<FilterData>> filterData;
    }
}
