package com.fxiaoke.open.erpsyncdata.monitor.dao;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.SimpleCache;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpTenantConfigurationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.model.CompareObjectMapping;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/12
 */
@Repository
public class CompareDaoImpl implements CompareDao {
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;

    private final SimpleCache<String, Dict> dataSourceInfoCache = new SimpleCache<>();
    private final SimpleCache<String, CompareObjectMapping> mappingCache = new SimpleCache<>();

    @Override
    public Dict getDataSourceInfo(String tenantId, String objApiName, DataSourceType dataSourceType) {
        Dict dataSourceInfo = dataSourceInfoCache.get(tenantId + objApiName + dataSourceType);
        if (dataSourceInfo == null) {
            //缓存没有，刷一下
            listObjMappingAndRefreshCache(tenantId);
        }
        dataSourceInfo = dataSourceInfoCache.get(tenantId + objApiName + dataSourceType);
        if (dataSourceInfo == null) {
            throw new ErpSyncDataException(I18NStringEnum.s118,tenantId);
        }
        return dataSourceInfo;
    }

    @Override
    public List<CompareObjectMapping> listObjMappingAndRefreshCache(String tenantId) {
        ErpTenantConfigurationEntity arg2 = new ErpTenantConfigurationEntity();
        if (StrUtil.isNotBlank(tenantId)) {
            arg2.setTenantId(tenantId);
        }
        arg2.setType(TenantConfigurationTypeEnum.TENANT_COMPARE_INFO.name());
        List<ErpTenantConfigurationEntity> entities = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("0")).queryList(arg2);
        List<CompareObjectMapping> allMappings = new ArrayList<>();
        for (ErpTenantConfigurationEntity entity : entities) {
            if (entity != null) {
                List<CompareObjectMapping> mappings = JSON.parseObject(entity.getConfiguration(), new TypeReference<List<CompareObjectMapping>>() {
                });
                allMappings.addAll(mappings);
                //更新下缓存
                for (CompareObjectMapping mapping : mappings) {
                    dataSourceInfoCache.put(mapping.getTenantId() + mapping.getSourceObjApiName() + mapping.getSourceType(), mapping.getSourceDataSourceInfo());
                    dataSourceInfoCache.put(mapping.getTenantId() + mapping.getDestObjApiName() + mapping.getDestType(), mapping.getDestDataSourceInfo());
                    mappingCache.put(mapping.getTenantId() + mapping.getCompareName(), mapping);
                }
            }
        }
        return allMappings;
    }


    @Override
    public CompareObjectMapping getObjMapping(String tenantId, String compareName) {
        CompareObjectMapping mapping = mappingCache.get(tenantId + compareName);
        if (mapping == null) {
            //缓存没有，刷一下
            listObjMappingAndRefreshCache(tenantId);
        }
        mapping = mappingCache.get(tenantId + compareName);
        if (mapping == null) {
            throw new ErpSyncDataException(I18NStringEnum.s119,tenantId);
        }
        return mapping;
    }
}
