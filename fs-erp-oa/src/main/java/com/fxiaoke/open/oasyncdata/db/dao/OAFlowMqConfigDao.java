package com.fxiaoke.open.oasyncdata.db.dao;


import com.fxiaoke.open.oasyncdata.db.entity.OAFlowMqConfigEntity;
import com.github.mybatis.mapper.ITenant;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OAFlowMqConfigDao extends ErpBaseDao<OAFlowMqConfigEntity>, ITenant<OAFlowMqConfigDao> {

    /**
     * 获取所有企业tenantId字段
     * @return
     */
    List<String> listTenantId();
}
