package com.fxiaoke.open.erpsyncdata.custom.controller.common;

import com.fxiaoke.open.erpsyncdata.custom.arg.GetErpData2TableArg;
import com.fxiaoke.open.erpsyncdata.custom.service.GetErpData2TableService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date: 15:20 2020/9/22
 * @Desc:
 */
@Slf4j
@Api(tags = "自定义函数获取erp数据推送到表相关接口")
@RestController("GetErpData2TableController")
@RequestMapping("inner/erp/syncdata/customfunction")
public class GetErpData2TableController {

    @Autowired
    private GetErpData2TableService getErpData2TableService;

    /**
     * 仍有调用，如果去除，需要检查哪里函数调用的先去掉。
     */
    @ApiOperation(value = "根据指定条件获取erp数据推送到数据库表")
    @RequestMapping(value = "/getErpData2Table",method = RequestMethod.POST)
    public Result<String> getErpData2Table(@RequestHeader(value="x-fs-ei") Integer tenantId,@RequestHeader(value = "x-user-id") String userId,@RequestBody GetErpData2TableArg arg) {
        if (arg == null||tenantId==null||arg.getErpDataFilter()==null) {
            log.info("param is null tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return getErpData2TableService.pushErpData2Table(String.valueOf(tenantId),userId,arg);
    }

}
