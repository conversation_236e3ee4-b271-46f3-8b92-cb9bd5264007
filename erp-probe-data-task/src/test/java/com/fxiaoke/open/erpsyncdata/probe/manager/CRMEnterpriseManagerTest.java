package com.fxiaoke.open.erpsyncdata.probe.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.probe.manger.CRMEnterpriseManager;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Ignore
public class CRMEnterpriseManagerTest extends BaseTest {
    @Autowired
    private CRMEnterpriseManager crmEnterpriseManager;
    @Autowired
    private RedisDataSource redisDataSource;

    @Test
    public void test() {
        redisDataSource.get(this.getClass().getSimpleName()).del("KEY_EI_LIST_CACHE");
        List<String> list = crmEnterpriseManager.getRunningEnterpriseIdListByPage(Lists.newArrayList("82777", "81961","81962","84781"));
        System.out.println(list);
    }

}
