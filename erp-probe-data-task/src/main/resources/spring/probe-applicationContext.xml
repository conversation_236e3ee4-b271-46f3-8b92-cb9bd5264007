<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">
    <context:annotation-config/>
    <task:annotation-driven executor="taskExecutor"/>

    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <!--ea和ei互转-->
    <import resource="classpath:spring/ei-ea-converter.xml"/>

    <!--导入本地xml-->
    <import resource="classpath:spring/fs-paas-mq.xml"/>
    <import resource="classpath:spring/probe-job.xml"/>
    <import resource="classpath:spring/xxl-job.xml"/>
    <import resource="classpath:spring/erp-sync-data-task-rest-client.xml"/>
    <!--pg数据库-->
    <import resource="classpath*:spring/common-db-proxy.xml"/>


    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:pointcut id="pointCutAround"
                          expression="execution(* com.fxiaoke.open.erpsyncdata.*.service.*.*(..))
                          or execution(* com.fxiaoke.open.erpsyncdata.probe.job.*.*(..))"/>
            <aop:around method="profile" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>

    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="2"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>


</beans>