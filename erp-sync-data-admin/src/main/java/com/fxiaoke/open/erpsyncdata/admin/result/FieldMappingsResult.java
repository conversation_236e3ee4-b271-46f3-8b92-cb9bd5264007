package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class FieldMappingsResult implements Serializable {
    @ApiModelProperty("源企业id")
    private List<String> sourceTenantIds;
    @ApiModelProperty("目标企业id")
    private List<String> destTenantIds;
    @ApiModelProperty("主对象映射")
    private ObjectMappingResult masterObjectMappings;
    @ApiModelProperty("从对象映射")
    private List<ObjectMappingResult> detailObjectMappings;
}
