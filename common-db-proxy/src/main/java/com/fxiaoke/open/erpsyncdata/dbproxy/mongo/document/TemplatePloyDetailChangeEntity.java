package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.mongodb.morphia.annotations.*;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/7 19:15:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity(value = "template_ploy_detail_change", noClassnameStored = true)
@Indexes({@Index(fields = {@Field("update_time")}, options = @IndexOptions(background = true, expireAfterSeconds = 7 * 24 * 3600))
})
public class TemplatePloyDetailChangeEntity implements Serializable {

    public static final int STATUS_INIT = 1;
    public static final int STATUS_EXECUTING = 2;
    public static final int STATUS_SUCCESS = 3;
    public static final int STATUS_FAIL = 4;

    @Id
    private String id;
    /**
     * 上游企业ID,用于通知
     */
    @Property("tenant_id")
    private String tenantId;
    @Property("template_id")
    private String templateId;
    @Property("downstream_id")
    private String downstreamId;
    @Property("event")
    private String event;
    /**
     * @see Reason
     */
    @Property("reason")
    private Integer reason;
    @Property("trace_id")
    private String traceId;
    /**
     * 1.未执行 2.执行中 3.成功 4.失败满重试次数
     */
    @Property("status")
    private Integer status;
    @Property("update_time")
    private Long updateTime;
    @Property("create_time")
    private Long createTime;
    @Property("try_time")
    private int tryTime;
    @Property("err_msg")
    private String errMsg;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class EnabledPloyDetailEvent implements Serializable {
        private boolean needSyncDuringStop;
        private String syncPloyDetailResult;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class DisabledPloyDetailEvent implements Serializable {
        private String ployDetailId;
        private String erpObjectApiName;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class UpdatedPloyDetailEvent implements Serializable {
        private String dcId;
        private String erpObjectApiName;
        private SyncRulesData syncRulesData;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class DeletedPloyDetailEvent implements Serializable {
        private String ployDetailId;
    }

    @Data
    public static class InitEvent implements Serializable {
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class DeletedDownstreamEvent implements Serializable {
        private String groupId;
        private java.util.List<String> erpObjects;
        private List<String> ployDetailIds;
    }

    public enum Reason {
        ENABLED(1, EnabledPloyDetailEvent.class),
        DISABLED(2, DisabledPloyDetailEvent.class),
        //创建集成流不需要初始化时间,暂时没有发这个消息
        UPDATED(3, UpdatedPloyDetailEvent.class),
        DELETED_PLOY_DETAIL(4, DeletedPloyDetailEvent.class),
        INIT(5, InitEvent.class),
        DELETED_DOWNSTREAM(6, DeletedDownstreamEvent.class),
        ;
        @Getter
        private Integer value;
        private Class<?> clazz;

        Reason(Integer value, Class<?> clazz) {
            this.value = value;
            this.clazz = clazz;
        }

        private static Map<Integer, Reason> statusMap = Arrays.stream(Reason.values()).collect(Collectors.toMap(Reason::getValue, Function.identity()));

        public static Reason valueOf(int status) {
            return statusMap.get(status);
        }

        public <T> T getEvent(String event) {
            return (T) JSON.parseObject(event, clazz);
        }
    }
}
