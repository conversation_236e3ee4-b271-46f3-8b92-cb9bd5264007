package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class CustomFunctionCommonArg implements Serializable {
    @ApiModelProperty("标识,标识调用哪个实现")
    private String type;
    @ApiModelProperty("参数Map，json字符串")
    private String params;
    @ApiModelProperty("企业id")
    private String tenantId;
}
