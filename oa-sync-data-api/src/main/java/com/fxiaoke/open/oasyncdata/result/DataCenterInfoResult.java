package com.fxiaoke.open.oasyncdata.result;

import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/6
 */
@Data
@ApiModel
public class DataCenterInfoResult {
    @ApiModelProperty("数据id")
    public String id;
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel;
    @ApiModelProperty("数据中心名称")
    public String dataCenterName;
    @ApiModelProperty("是否当前选中的数据中心")
    public boolean checked = false;
    @ApiModelProperty("是否配置连接信息")
    public boolean hasConnect = false;
    @ApiModelProperty("是否购买")
    public boolean hasPurchased = true;
    @ApiModelProperty("是否是erp渠道")
    public boolean hasErpChannel=true;

}
