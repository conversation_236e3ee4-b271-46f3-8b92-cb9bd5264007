package com.fxiaoke.open.erpsyncdata.monitor.task

import com.fxiaoke.open.erpsyncdata.monitor.BaseSpockTest
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class SyncDataFailedTaskTest extends BaseSpockTest {
    @Autowired
    private SyncDataFailedTask syncDataFailedTask;

    @Test
    void test() {
        def tenantId = "81243"
        def dcId = "dcid100"
        def ployDetailId = "ployDetailId100"

        for(int i=0;i<10;i++) {
            syncDataFailedTask.insert(tenantId,dcId,ployDetailId,(10+i).toString())
        }
        println("ok")
    }
}
