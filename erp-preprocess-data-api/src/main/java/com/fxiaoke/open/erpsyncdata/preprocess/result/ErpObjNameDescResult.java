package com.fxiaoke.open.erpsyncdata.preprocess.result;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 15:35 2021/8/11
 * @Desc:
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class ErpObjNameDescResult implements Serializable {

    @ApiModelProperty("对象apiName")
    public String objApiName; //
    @ApiModelProperty("对象名称")
    public String objName; //

}
