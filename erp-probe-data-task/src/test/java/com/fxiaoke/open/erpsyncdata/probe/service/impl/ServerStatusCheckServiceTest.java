package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.probe.service.ErpServerStatusCheckService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
@Slf4j
public class ServerStatusCheckServiceTest extends BaseTest {

    @Autowired
    private ErpServerStatusCheckService serverStatusCheckService;

    @Test
    public void testCheck(){

        Result<Void> result = serverStatusCheckService.executeCheckErpServerStatus("82814");
        log.info("",result);


    }

}
