package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import com.fxiaoke.common.IpUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.monitor.BaseTest;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DataNodeMsg;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:14 2023/2/10
 * @Desc:
 */
@Ignore
public class DataNodeMsgProcessorTest extends BaseTest {
    @Autowired
    private DataNodeMsgProcessor dataNodeMsgProcessor;
    @Autowired
    private ErpTempDataDao erpTempDataDao;

    @Test
    public void process() {
//        String tenantId="88521";
//        String dataCenterId="643f7322b54ea80001767d86";
//        String objApiName="BD_MATERIAL";
//        String splitObjApiName=objApiName+".BillHead";
//        String dataId="CH3395";
//        String streamId="6b465b24c1e64582bc6b1c60ad7bb3cb";
//        List<Document> dataList = erpTempDataDao.listErpObjDataByIdsOrNumbers(tenantId, dataCenterId, objApiName, Lists.newArrayList(dataId), true);
//        Long version=dataList.get(0).getDate("new_last_sync_time").getTime();
//        String iddd=dataList.get(0).getObjectId("_id").toString();
//        DataNodeMsg msg = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp()).tenantId(tenantId)
//                .dataCenterId(dataCenterId).objApiName(objApiName).dataId(dataId)
//                .nodeType(DataNodeTypeEnum.start).nodeName(DataNodeNameEnum.EnterTempData).nodeTime(System.currentTimeMillis()).order(1).remark("入库")
//                .msgDetail(JacksonUtil.toJson(dataList.get(0))).traceId(TraceUtil.get()).build();
//        Document doc = JacksonUtil.fromJson(msg.getMsgDetail(), Document.class);
//        ObjectId mongoId = DataNodeMsgProcessor.getDocObjectId(doc);
//        List<Document> listss=erpTempDataDao.listErpObjDataByMongoIds(tenantId, Lists.newArrayList(mongoId.toString()));
//        //dataNodeMsgProcessor.process(msg);
//        DataNodeMsg msg1 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId)
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.OutTempData).nodeTime(System.currentTimeMillis()).order(20).remark("出库")
//                .msgDetail(JacksonUtil.toJson(dataList.get(0))).traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg1);
//        DataNodeMsg msg2 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version)
//                .streamId(streamId).remark("分发")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataTriggerProcess).nodeTime(System.currentTimeMillis()).order(40)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg2);
////        DataNodeMsg msg3 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
////                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("数据触发失败")
////                .nodeType(DataNodeTypeEnum.end).nodeName(DataNodeNameEnum.DataEnd).nodeTime(System.currentTimeMillis()).order(1000)
////                .traceId(TraceUtil.get()).build();
////        dataNodeMsgProcessor.process(msg3);
//        DataNodeMsg msg4 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始同步前")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataBeforeFunc).nodeTime(System.currentTimeMillis()).order(60)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg4);
//        DataNodeMsg msg5 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始字段转换")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataFieldMapping).nodeTime(System.currentTimeMillis()).order(80)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg5);
//        DataNodeMsg msg6 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始同步中")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataDuringFunc).nodeTime(System.currentTimeMillis()).order(100)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg6);
//        DataNodeMsg msg7 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始写")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataWriteDest).nodeTime(System.currentTimeMillis()).order(120)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg7);
////        DataNodeMsg msg8 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
////                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始反写crm")
////                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataReWriteSource).nodeTime(System.currentTimeMillis()).order(120)
////                .traceId(TraceUtil.get()).build();
////        dataNodeMsgProcessor.process(msg8);
//        DataNodeMsg msg9 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始完成写")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataIdMapping).nodeTime(System.currentTimeMillis()).order(120)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg9);
//        DataNodeMsg msg10 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("开始同步后")
//                .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataAfterFunc).nodeTime(System.currentTimeMillis()).order(120)
//                .traceId(TraceUtil.get()).build();
//        //dataNodeMsgProcessor.process(msg10);
//        DataNodeMsg msg11 = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
//                .tenantId(tenantId).objApiName(splitObjApiName).dataId(dataId).version(version).streamId(streamId).remark("同步完成")
//                .nodeType(DataNodeTypeEnum.end).nodeName(DataNodeNameEnum.DataEnd).nodeTime(System.currentTimeMillis()).order(120)
//                .traceId(TraceUtil.get()).build();
//        dataNodeMsgProcessor.batchProcess(Lists.newArrayList(msg,msg1,msg2,msg4,msg5,msg6,msg7,msg9,msg10,msg11));
//        boolean is=true;
//        while (is){
//
//        }
//        System.out.println("");
    }
}