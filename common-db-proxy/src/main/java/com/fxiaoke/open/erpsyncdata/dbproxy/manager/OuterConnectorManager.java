package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.HubConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.OuterConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 外部连接器管理
 *
 * <AUTHOR> (^_−)☆
 */
@Component
@Slf4j
@LogLevel()
public class OuterConnectorManager implements InitializingBean {
    @Autowired
    private OuterConnectorManager outerConnectorManager;
    @Autowired
    private PlusTenantConfigManager plusTenantConfigManager;
    @Autowired
    private RedissonClient redissonClient;


    @Override
    public void afterPropertiesSet() throws Exception {
        AllConnectorUtil._setOuterConnectorSup(() -> {
            List<HubInfo> hubInfoList = outerConnectorManager.getHubInfoList();
            //去重
            TreeSet<OuterConnector> allOuterConnectors = hubInfoList.stream()
                    .flatMap(v -> v.getOuterConnectors().stream())
                    .peek(v -> v.setConnectorHandlerType(ConnectorHandlerType.HUB))
                    .collect(Collectors.toCollection(() ->
                            //无论是怎么整出来的没有apiName数据，不让他影响整个方法执行
                            new TreeSet<>(Comparator.comparing(v -> StrUtil.blankToDefault(v.getApiName(), "ERROR")))
                    ));
            //apl连接器
            allOuterConnectors.addAll(getAPLClassConnectors());
            return ImmutableList.copyOf(allOuterConnectors);
        });
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 2, timeUnit = TimeUnit.MINUTES)
    public HubInfo getHubInfo(String tenantId, String connectorApiName) {
        List<HubInfo> hubInfoList = outerConnectorManager.getHubInfoList();
        HubInfo hubInfo = hubInfoList.stream()
                .filter(v -> v.supportInvoke(tenantId, connectorApiName))
                .findFirst()
                .orElseGet(this::getDefaultHubInfo);
        return hubInfo;
    }

    public List<HubInfo> getHubInfoList() {
        //从配置中心读取hub地址
        String hubInfoStr = HubConfigCenter.hubInfo;
        if (hubInfoStr == null) {
            return Collections.emptyList();
        }
        List<HubInfo> configHubInfoList = JacksonUtil.fromJson(hubInfoStr, new TypeReference<List<HubInfo>>() {
        });
        //按排序
        configHubInfoList.sort(Comparator.comparingInt(v -> v.getOrder()));
        if (CollUtil.isNotEmpty(configHubInfoList)) {
            //初始化一下supportConnectorApiNames
            configHubInfoList.forEach(v -> v.getSupportConnectorApiNames());
        }
        return configHubInfoList;
    }

    @Cached(cacheType = CacheType.BOTH, expire = 2, timeUnit = TimeUnit.MINUTES)
    public HubInfo getDefaultHubInfo() {
        HubInfo hubInfo = HubInfo.builder()
                .name("default")
                .baseUrl(ConfigCenter.DEFAULT_HUB_BASE_URL)
                .outerConnectors(ListUtil.of(OuterConnector.builder()
                        .apiName("Salesforce")
                        .defaultName("Salesforce")
                        .systemName("Salesforce")
                        .moduleCode("salesforce_data_sync_app")
                        .iconUrl("https://c1.sfdcstatic.com/content/dam/sfdc-docs/www/logos/logo-salesforce.svg")
                        .build())).build();
        return hubInfo;
    }

    public List<OuterConnector> getAPLClassConnectors() {
        List<OuterConnector> aplClassConnectors = plusTenantConfigManager.getGlobalObjConfig(TenantConfigurationTypeEnum.APL_CLASS_INFO, new TypeReference<List<OuterConnector>>() {
        });
        aplClassConnectors = CollUtil.emptyIfNull(aplClassConnectors);
        aplClassConnectors.forEach(v -> v.setConnectorHandlerType(ConnectorHandlerType.APL_CLASS));
        return aplClassConnectors;
    }

    /**
     * 增加OuterConnector，但是无绑定code
     */
    public void addAPLClassConnector(OuterConnector outerConnector) {
        RLock lock = redissonClient.getLock(CommonConstant.REDIS_LOCK_ADD_OUTER_CONNECTOR);
        try {
            if (lock.tryLock(3, TimeUnit.SECONDS)) {
                try {
                    List<OuterConnector> aplClassConnectors = plusTenantConfigManager.getGlobalObjConfig(TenantConfigurationTypeEnum.APL_CLASS_INFO, new TypeReference<List<OuterConnector>>() {
                    });
                    boolean exist = aplClassConnectors.stream().anyMatch(v -> Objects.equals(v.getApiName(), outerConnector.getApiName()));
                    if (exist) {
                        return;
                    }
                    int maxId = aplClassConnectors.stream().mapToInt(v -> v.getConnectorId()).max().orElse(9999);
                    outerConnector.setConnectorId(maxId + 1);
                    aplClassConnectors.add(outerConnector);
                    plusTenantConfigManager.upsertGlobalObjConfig(TenantConfigurationTypeEnum.APL_CLASS_INFO, aplClassConnectors);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new ErpSyncDataException("add APLClassConnector timeout");
            }
        } catch (Exception e) {
            throw new ErpSyncDataException("add APLClassConnector error " + e.getMessage());
        }
    }

}
