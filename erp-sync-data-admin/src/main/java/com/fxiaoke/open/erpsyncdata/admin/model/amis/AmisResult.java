package com.fxiaoke.open.erpsyncdata.admin.model.amis;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Joiner;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/8/1
 */
public class AmisResult extends Result<Dict> {
    private static final long serialVersionUID = 294372440321536243L;

    public AmisResult() {
        super(new Dict());
    }

    public AmisResult set(String key, Object value) {
        this.data.put(key, value);
        return this;
    }

    public static AmisResult of(Object obj) {
        return of("a", obj);
    }


    public static AmisResult of(String key, Object obj) {
        AmisResult result = new AmisResult();
        return result.set(key, obj);
    }

    public static AmisResult list(List<?> objs) {
        String a = Joiner.on("<br />").join(objs);
        return of(a);
    }


}
