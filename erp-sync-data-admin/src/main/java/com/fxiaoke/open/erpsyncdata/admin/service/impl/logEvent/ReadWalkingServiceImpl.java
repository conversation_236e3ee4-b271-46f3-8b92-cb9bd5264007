package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import cn.hutool.core.collection.CollUtil;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SyncLogNode;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.InterfaceDataDealEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/6 20:37 读节点的日志
 * @Version 1.0
 */
@Slf4j
@Service("readWalkingServiceImpl")
@SyncLogNode(SyncLogTypeEnum.READ)
public class ReadWalkingServiceImpl extends AbstractLogService {
    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;

    @Override
    public Result<Page<ErpInterfaceMonitorResult>> queryListLogByType(String tenantId, StreamLogQueryArg streamLogQueryArg,String lang) {

        String ployDetailId = streamLogQueryArg.getPloyDetailId();
        String realObjApiName = baseLinkService.getRealApiName(tenantId, ployDetailId,true,lang);
        if (StringUtils.isEmpty(realObjApiName)) {
            return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
        }
        Page<ErpInterfaceMonitorResult> pageResult = new Page<>();
        List<String> objectIds = null;
        if (StringUtils.isNotEmpty(streamLogQueryArg.getSyncLogId())) {
            List<SyncLog> syncLogs = syncLogManager.listByLogId(tenantId, streamLogQueryArg.getSyncLogId(),streamLogQueryArg.getStartTime(), streamLogQueryArg.getEndTime());
            if (CollectionUtils.isEmpty(syncLogs)) {
                return Result.newSuccess(pageResult);
            }
            List<SyncLog> filterSyncLog = syncLogs.stream().filter(item -> item.getType().equals(SyncLogTypeEnum.READ)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterSyncLog)) {
                return Result.newSuccess(pageResult);
            }
            objectIds = Lists.newArrayList();
            for (SyncLog syncLog : filterSyncLog) {
                List<String> list = JacksonUtil.fromJson(syncLog.getData(), List.class);
                objectIds.add(list.get(0));
            }
        }
        List<String> readInterface = CollUtil.isEmpty(streamLogQueryArg.getFilterTypes()) ? ErpObjInterfaceUrlEnum.getReadInterface() : streamLogQueryArg.getFilterTypes();
        return getErpInterfaceMonitorPageResult(tenantId, streamLogQueryArg.getDcId(), streamLogQueryArg.getStartTime(), streamLogQueryArg.getEndTime(),
                streamLogQueryArg.getPageNum(), streamLogQueryArg.getPageSize(), realObjApiName, objectIds, readInterface,
                streamLogQueryArg.getStatus(), streamLogQueryArg.getQueryTime(), lang, ObjectUtils.isNotEmpty(streamLogQueryArg.getResultCount()) ? streamLogQueryArg.getResultCount().name() : null);
    }

    @Override
    public Result<?> queryListByLogIds(final String tenantId, final String realObjApiName, final List<String> logIds, final StreamLogQueryArg arg, String lang) {
        List<String> readInterface = CollUtil.isEmpty(arg.getFilterTypes()) ? ErpObjInterfaceUrlEnum.getReadInterface() : arg.getFilterTypes();

        return queryListByLogIds(tenantId, realObjApiName, logIds, readInterface, arg, lang);
    }


    /**
     * 根据id查询数据
     */
    @Override
    public Result<?> queryLogDataById(String tenantId, StreamLogQueryArg.SyncLogDataArg syncLogDataArg,String lang,SyncLog syncLog) {
        if(syncLog==null){
            syncLog = syncLogManager.getLogById(tenantId, new ObjectId(syncLogDataArg.getId()), SyncLogTypeEnum.READ.getType());
        }
        if (ObjectUtils.isEmpty(syncLog)) {
            return Result.newSuccess();
        }
        Result<List<ErpInterfaceMonitorResult>> queryLogDataByIdResult = getData(Lists.newArrayList(syncLog), syncLogDataArg.getDcId(), tenantId,lang);
        if (ObjectUtils.isNotEmpty(queryLogDataByIdResult.getData())) {
            return Result.newSuccess(queryLogDataByIdResult.getData().get(0));
        }
        return Result.newSuccess();
    }

    public Result getData(List<SyncLog> syncLogs,String dcId,String tenantId,String lang){
        List<ObjectId> objectIdList = new ArrayList<>();
        for (SyncLog syncLog : syncLogs) {
            List<String> list = JacksonUtil.fromJson(syncLog.getData(), List.class);
            objectIdList.add(new ObjectId(list.get(0)));
        }
        //查询渠道
        Result<List<ErpInterfaceMonitorResult>> queryInterfaceResult = erpInterfaceMonitorService.getObjInterfaceMonitorByObjIds(tenantId, objectIdList,lang);
        if (CollectionUtils.isNotEmpty(queryInterfaceResult.getData())) {
            for (ErpInterfaceMonitorResult datum : queryInterfaceResult.getData()) {
                datum.setSyncLogId(syncLogs.get(0).getLogId());
            }
        }
        return queryInterfaceResult;
    }

}
