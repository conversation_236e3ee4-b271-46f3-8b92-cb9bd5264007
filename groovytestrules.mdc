---
description: groovy test
globs: 
alwaysApply: false
---
## 6. 最佳实践

### 6.1 测试覆盖 [必须]
- 正常流程测试
- 边界值测试
- 异常流程测试
- 并发测试（如需要）

### 6.1.1 代码覆盖率要求 [必须]
- 行覆盖率要求：
  - 每个代码行都必须至少被一个测试用例执行到
  - 对于复杂方法，需确保所有代码行都有测试用例覆盖
  - 避免出现未被测试执行的代码行
  - 对于数据处理方法，需覆盖不同数据类型和格式的情况

- 分支覆盖率要求：
  - 所有 if-else 分支必须都有对应测试用例覆盖
  - switch-case 语句的每个分支都需要测试用例覆盖，包括 default 分支
  - 三元运算符 (?:) 的两个分支都需要测试用例覆盖
  - 对于嵌套的条件语句，需要覆盖所有可能的组合

- 条件覆盖率要求：
  - 对于复合条件（使用 && 或 ||），需要测试每个子条件的真假情况
  - 示例：对于 if (a && b)，需要测试：
    * a=true, b=true
    * a=true, b=false
    * a=false, b=true
    * a=false, b=false
  - 对于多重条件（a && b && c），需要覆盖所有关键组合

### 6.1.2 测试数据设计 [必须]
```groovy
@Unroll
def "测试复合条件分支覆盖-#desc"() {
    given: "准备测试数据"
    def input = new Input(
        condition1: cond1,
        condition2: cond2
    )
    
    when: "执行测试方法"
    def result = service.process(input)
    
    then: "验证结果"
    result == expected
    
    where:
    desc          | cond1 | cond2 || expected
    "全为真"      | true  | true  || "success"
    "条件1为假"   | false | true  || "fail"
    "条件2为假"   | true  | false || "fail"
    "全为假"      | false | false || "fail"
}

// 多重条件测试示例
@Unroll
def "测试多重条件覆盖-#desc"() {
    given: "准备测试数据"
    def input = new Input(
        value1: val1,
        value2: val2,
        value3: val3
    )
    
    when: "执行测试方法"
    def result = service.validateInput(input)
    
    then: "验证结果"
    result == expected
    
    where:
    desc            | val1        | val2    | val3    || expected
    "全部有效"      | "valid"     | 10      | true    || true
    "值1无效"       | ""          | 10      | true    || false
    "值2超范围"     | "valid"     | 999     | true    || false
    "值3为假"       | "valid"     | 10      | false   || false
    "全部无效"      | ""          | -1      | false   || false
}
```

### 6.1.3 循环覆盖要求 [必须]
- for/while 循环测试要求：
  - 0次循环（空集合/立即退出）
  - 1次循环（单元素）
  - 多次循环（正常情况，建议3-5个元素）
  - 最大边界循环（如果有限制）
  - 包含特殊元素的循环（null、空字符串等）

```groovy
@Unroll
def "测试列表处理-#desc"() {
    given: "准备测试数据"
    def inputList = items
    
    when: "执行处理方法"
    def result = service.processItems(inputList)
    
    then: "验证结果"
    result == expected
    
    where:
    desc          | items                    || expected
    "空列表"      | []                      || []
    "单个元素"    | ["item1"]               || ["ITEM1"]
    "多个元素"    | ["a", "b", "c"]         || ["A", "B", "C"]
    "含空元素"    | ["a", null, "c"]        || ["A", null, "C"]
    "最大数量"    | ["a"] * 100             || ["A"] * 100
}
```

### 6.1.4 异常路径覆盖 [必须]
- try-catch 块中的代码：
  - 正常执行路径（不抛出异常）
  - 每种可能的异常路径
  - finally 块的执行
  - 异常的传播链路

```groovy
def "测试异常处理场景"() {
    given: "准备可能导致异常的数据"
    def invalidInput = new Input(value: invalidValue)
    
    when: "执行可能抛出异常的方法"
    service.processWithException(invalidInput)
    
    then: "验证异常"
    def ex = thrown(ExpectedException)
    with(ex) {
        message == expectedMessage
        cause instanceof ExpectedCause
    }
    
    where:
    invalidValue || expectedMessage
    null        || "输入不能为空"
    ""          || "输入不能为空字符串"
    "invalid"   || "输入格式错误"
}
```

### 6.1.5 代码覆盖率检查工具 [推荐]
- 使用 JaCoCo 或 Cobertura 等工具进行覆盖率检查
- 建议设置以下最低覆盖率指标：
  - 行覆盖率 ≥ 85%
  - 分支覆盖率 ≥ 80%
  - 条件覆盖率 ≥ 75%
  - 方法覆盖率 ≥ 90%

### 6.1.6 覆盖率报告要求 [必须]
- 在CI/CD流程中集成覆盖率检查
- 定期生成覆盖率报告并进行审查
- 对未达标的部分进行分析和改进
- 每次提交前确保覆盖率不低于基准线
- 定期进行覆盖率趋势分析，确保覆盖率不降低

### 6.1.7 特殊场景覆盖 [必须]
- 并发场景测试：
  - 多线程访问共享资源
  - 死锁预防
  - 竞态条件处理
- 性能边界测试：
  - 大数据量处理
  - 超时处理
  - 资源限制场景
- 数据库交互测试：
  - 事务回滚
  - 连接异常
  - 死锁处理 