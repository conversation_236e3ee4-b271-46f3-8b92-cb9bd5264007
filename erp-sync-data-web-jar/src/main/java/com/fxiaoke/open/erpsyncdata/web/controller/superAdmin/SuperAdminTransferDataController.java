package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.web.transfer.TransferMongoService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 批量刷库操作
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/8/23
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/transferdata")
@Slf4j
public class SuperAdminTransferDataController extends SuperAdminBaseController {
    @Autowired
    private TransferMongoService transferMongoService;

    /**
     * 删除35天前历史的syncData数据
     */
    @PostMapping("transferShardingMongo")
    public Map<String, List<String>> transferShardingMongo(@RequestBody TransferArg arg) throws Exception {
        return transferMongoService.transfer(arg.getEis(), arg.getVip(), arg.getNormal(), arg.getGray(), arg.getTransferHandlerNames(), arg.getForceTransfer(), arg.getEndTime());
    }

    @Data
    public static class TransferArg implements Serializable {
        private List<String> eis;
        private Boolean vip;
        private Boolean normal;
        private Boolean gray;
        private List<String> transferHandlerNames;

        private Boolean forceTransfer = true;

        private String endTime;
    }
}
