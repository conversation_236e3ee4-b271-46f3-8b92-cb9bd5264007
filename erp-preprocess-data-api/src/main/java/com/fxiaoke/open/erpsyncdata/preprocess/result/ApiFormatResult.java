package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:16 2022/10/8
 * @Desc:
 */
@Data
@ApiModel
public class ApiFormatResult implements Serializable {
    @ApiModelProperty("ID")
    public String id;
    @ApiModelProperty("接口类型")
    public ErpObjInterfaceUrlEnum apiType;
    @ApiModelProperty("接口类型名称")
    private String apiTypeName;
    @ApiModelProperty("对象apiName")
    public String objApiName;
    @ApiModelProperty("虚拟对象apiName")
    public String splitObjectApiName;
    @ApiModelProperty("对象名称")
    public String objName;
    @ApiModelProperty("请求方式")
    public String requestMethod;
    @ApiModelProperty("请求路径")
    public String url;
    @ApiModelProperty("真实请求路径")
    public String realUrl;
    @ApiModelProperty("星空旗舰版专用URL")
    public String k3ultimate_url;
    @ApiModelProperty("自定义函数apiName")
    public String funcApiName;
    @ApiModelProperty("Headers")
    public List<ApiHeaderMessage> headerList;
    @ApiModelProperty("请求参数说明")
    public List<ApiFieldMessage> argFieldList;
    @ApiModelProperty("JSON请求参数示例")
    public String argExample;
    @ApiModelProperty("返回数据说明")
    public List<ApiFieldMessage> resultFieldList;
    @ApiModelProperty("JSON返回数据示例")
    public String resultExample;

    @ApiModelProperty("自定义接口ID")
    public String customInterfaceId;

    @ApiModelProperty("webhookUrl")
    private String webhookUrl;


    @Data
    @ApiModel
    @Builder
    @AllArgsConstructor
    public static class ApiFieldMessage implements Serializable {
        @ApiModelProperty("字段apiName")
        public String fieldApiName;
        @ApiModelProperty("字段类型")
        private String fieldType;
        @ApiModelProperty("字段说明")
        public String fieldDesc;
    }

    @Data
    @ApiModel
    @Builder
    @AllArgsConstructor
    public static class ApiHeaderMessage implements Serializable {
        @ApiModelProperty("字段apiName")
        public String fieldApiName;
        @ApiModelProperty("字段值")
        private String fieldValue;
        @ApiModelProperty("字段说明")
        public String fieldDesc;
    }
}
