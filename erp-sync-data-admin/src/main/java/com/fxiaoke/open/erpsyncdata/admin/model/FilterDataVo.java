package com.fxiaoke.open.erpsyncdata.admin.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.Operate;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
/**
 * 前端Operate和后端定义不一致
 */
public class FilterDataVo implements Serializable {
    /**
     * 字段apiName
     */
    @ApiModelProperty("字段apiName")
    private String fieldApiName;
    /**
     * 枚举
     *
     * @see FieldType
     */
    @ApiModelProperty("字段类型")
    private String fieldType;
    @ApiModelProperty("字段标签")
    private String label;
    /**
     * 枚举
     *
     * @see Operate
     */
    @ApiModelProperty("操作符，IS ISN IN等")
    private String operate;
    @ApiModelProperty("字段值")
    private List fieldValue;
    /**
     * quote field type * 当fieldType == 'quote' 时，该字段表示字段的真正类型 当fieldType == 'formula'时，该字段表示计算字段的返回值类型
     */
    @ApiModelProperty("该字段表示字段的真正类型 当fieldType == 'formula'时，该字段表示计算字段的返回值类型")
    private String quoteFieldType;

    private String quoteRealField;
    private String quoteFieldTargetObjectApiName;
    private String quoteFieldTargetObjectField;
    @ApiModelProperty("类型,判断是否是变量,传variable表示为变量类型字段")
    private String type;
    @ApiModelProperty("变量类型,判断是哪种变量类型字段")
    private String variableType;
    @ApiModelProperty("是否是between，且需要替换开始结束时间")
    @Builder.Default
    private Boolean isVariableBetween=false;

    public static List<List<FilterData>> convert2Dto(List<List<FilterDataVo>> vo) {
        return JSON.parseObject(JSON.toJSONString(vo), new TypeReference<List<List<FilterData>>>() {});
    }
}
