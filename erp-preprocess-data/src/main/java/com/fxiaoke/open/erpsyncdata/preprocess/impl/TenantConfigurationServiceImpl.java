package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpTenantConfiguration;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.TenantConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 17:47 2020/12/22
 * @Desc:
 */
@Service("tenantConfigurationService")
@Slf4j
public class TenantConfigurationServiceImpl implements TenantConfigurationService {
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ConfigCenterManager configCenterManager;
    @Autowired
    private SpeedLimitManager speedLimitManager;


    @Override
    @LogLevel(LogLevelEnum.TRACE)
    public Result<ErpTenantConfiguration> queryConfig(String tenantId,String dataCenterId,String channel,String type) {
        ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne(tenantId,
                dataCenterId,
                channel,
                type);
        ErpTenantConfiguration configuration = BeanUtil.deepCopy(configurationEntity,ErpTenantConfiguration.class);
        return new Result<>(configuration);
    }

    @Override
    @LogLevel(LogLevelEnum.TRACE)
    public Result<ErpTenantConfiguration> queryConfig(String type) {
        return queryConfig("0","0","ALL",type);
    }

    @Override
    public Result<Void> updateConfig(String tenantId,String dataCenterId,String channel,String type,String configuration) {
        ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne(tenantId,
                dataCenterId,
                channel,
                type);
        if(configurationEntity!=null) {
            configurationEntity.setConfiguration(configuration);
            tenantConfigurationManager.updateById(tenantId,configurationEntity);
            return Result.newSuccess();
        }

        ErpTenantConfigurationEntity entity = ErpTenantConfigurationEntity.builder()
                .id(idGenerator.get())
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .channel(channel)
                .type(type)
                .configuration(configuration)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
        int count = tenantConfigurationManager.insert(tenantId,entity);
        return count==1 ? Result.newSuccess() : Result.newError("-1",i18NStringManager.getByEi(I18NStringEnum.s21,tenantId));
    }

    @Override
    public Result<Void> updateConfig(String type, String configuration) {
        return updateConfig("0","0","ALL",type,configuration);
    }

    @Override
    public Result<Dict> getAllConfigByKey(@NotNull String tenantId, @Nullable String dataCenterId) {
        List<ErpTenantConfigurationEntity> tenantConfigs = tenantConfigurationManager.findAll(tenantId);
        List<ErpTenantConfigurationEntity> configs0 = tenantConfigurationManager.findAll("0");
        List<ErpTenantConfigurationEntity> configsStar = tenantConfigurationManager.findAll("*");
        Map<String, String> globalConfigs = new HashMap<>();
        configs0.stream().filter(v -> StrUtil.isNotBlank(v.getConfiguration())).forEach(v -> globalConfigs.put(v.getType(), v.getConfiguration()));
        configsStar.stream().filter(v -> StrUtil.isNotBlank(v.getConfiguration())).forEach(v -> globalConfigs.put(v.getType(), v.getConfiguration()));
        Dict dict = new Dict();
        if (dataCenterId == null) {
            //获取所有企业级别的配置
            //单项的企业级配置
            tenantConfigs.stream().filter(v -> v.getDataCenterId() == null || StrUtil.equalsAny(v.getDataCenterId(), "0", "*"))
                    .forEach((v) -> {
                        dict.put(v.getType(), v.getConfiguration());
                    });
            //速度配置,统一返回tps,还没有key不是tenantId的
            for (SpeedLimitTypeEnum speedLimitTypeEnum : SpeedLimitTypeEnum.values()) {
                Double tps = speedLimitManager.getTps(speedLimitTypeEnum, tenantId);
                dict.put(speedLimitTypeEnum.getConfigType().name(), tps);
            }
            //tenant名单
            Arrays.stream(TenantConfigurationTypeEnum.values())
                    .filter(v -> TenantConfigurationTypeEnum.FormatType.tenantList.equals(v.getFormatType()))
                    .forEach(k -> {
                        dict.put(k.name(), false);
                        String s = globalConfigs.get(k.name());
                        if (s != null && (
                                s.equals("*") ||
                                        s.startsWith(tenantId + ";") ||
                                        s.endsWith(";" + tenantId) ||
                                        s.contains(";" + tenantId + ";"))) {
                            dict.put(k.name(), true);
                        }
                    });
        } else {
            //连接器级别配置
        }
        return Result.newSuccess(dict);
    }
}
