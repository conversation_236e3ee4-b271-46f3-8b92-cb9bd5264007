package com.fxiaoke.open.erpsyncdata.common.constant;

public enum ConditionExceptionCodeEnum {
  /***
   * common
   */
  SUCCESS(0, "请求成功"), SYSTEM_ERROR(301140001, "系统异常"),
  //INVALID_PARA(201130002, "invalid para"),
  INVALID_PARA(2011404002, "请求数据非法"),
  TENANT_POLICY_ERROR(2011404003, "系统数据库异常"),
  HTTP_REQUEST_ERROR(2011404004, "请求异常"),
  ENTITY_ID_BLANK(2011404005, "对象为空"),
  ENTITY_ID_TO_LONG(2011404006, "对象ID超出系统长度"),
  TENANTID_BLANK(2011304007, "请求企业账户为空"),
  APPID_BLANK(2011304008, "请求业务方标识为空"),
  REQUEST_USESRID_BLANK(2011304009, "请求用户为空"),
  CONTEXT_BLANK(2011304010, "请求上下文为空"),
  DATA_IDS_BLANK(2011304011, "数据ID为空"),


  /***
   * 规则组相关
   */
  RULE_CODE_IS_BLANK(2011404100, "规则唯一标识为空"),
  RULE_EXPRESSION_COMPILE_ERROR(2011404101, "系统异常,表达式编译解析失败"),
  RULE_SQL_COMPILE_ERROR(2011404102, "系统异常,sql解析失败"),
  RULE_RULE_PARSE_RULE_CONTAIN_UNSUPPORT_ORDER(2011404103, "系统异常,高级规则解析表达式中的序号不存在"),
  RULE_RULE_PARSE_RULE_COMPILE_FAIL(2011404104, "系统异常,高级规则解析表达式不正确"),
  RULE_CODE_IS_TOO_LONG(2011404105, "规则唯一标识超出系统建议的长度:64"),
  RULE_STATUS_UNSUPPORT(2011404106, "规则状态不支持"),
  RULE_NAME_IS_TOO_LONG(2011404107, "规则名超出系统建议的长度:64"),
  RULE_NAME_IS_BLANK(2011404108, "规则名为空"),
  RULE_EXIST(2011404109, "规则已经存在"),
  RULE_UNEXIST(2011404110, "规则不存在"),
  RULE_EXPRESSION_EXECUTE_ERROR(2011404111, "数据权限->条件共享->规则计算异常"),
  RULE_GROUP_RULES_EMPTY(2011404112, "规则为空"),
  /***
   * Rule Macro Group
   */
  RULE_MACRO_GROUP_MORE_THAN_ONE(2011404113, "对象匹配规则超过1个"),
  RULE_MACRO_GROUP_NOT_FOUND(2011404113, "can not find specified macro group"),
  RULE_MACRO_GROUP_API_NAME_EXISTS(2011404114, "apiName duplicated"),
  RULE_MACRO_GROUP_API_NAME_IS_EMPTY(2011404115, "apiName can not be null or empty"),
  RULE_MACRO_GROUP_STATUS_VALUE_INVALID(2011404116, "invalid status value.(valid:1 or 0)"),
  RULE_MACRO_GROUP_NAME_EXISTS(2011404117, "macro group name duplicated"),
  RULE_MACRO_GROUP_NAME_IS_EMPTY(2011404118, "macro group name is null or empty"),

  /***
   * 规则02
   */
  RULE_FIELD_TYPE_UNMATCH(2011404200, "系统异常,对象字段类型定义不匹配"),
  RULE_FIELD_NOT_EXIST(2011404201, "系统异常,对象字段不存在"),
  RULE_FIELD_TYPE_BLANK(2011404202, "对象类型为空"),
  RULE_FIELD_NAME_BLANK(2011404203, "对象名为空"),
  RULE_FIELD_NAME_TOO_LONG(2011404204, "对象字段长度超出系统允许的长度"),
  RULE_FIELD_TYPE_TOO_LONG(2011404205, "对象字段的类型长度超出系统允许的长度"),
  RULE_FIELD_TYPE_UNSUPPORT(2011404206, "对象字段的类型不支持"),
  RULE_FIELD_OPERATE_UNSUPPORT(2011404207, "对象字段的比较符不支持"),
  RULE_FIELD_TYPE_ERROR(2011404208, "对象字段的类型同定义不一致"),
  RULE_FIELD_TYPE_NUMBER_ERROR(2011404209, "字段的值不是数值"),
  RULE_FIELD_VALUES_EMPTY(2011404210, "对象字段的值为空"),
  RULE_FIELD_VALUES_IS_NULL(2011404211, "对象字段的值存在空值"),
  RULE_FIELD_VALUES_BOOLEAN_ERROR(2011404212, "对象字段的BOOL值不正确(支持:true/false)"),
  RULE_EMPTY(2011404213, "规则为空"),
  RULE_ORDER_REPEAT(2011404214, "规则序号重复"),
  RULE_FIELD_VALUE_NOT_VALID_FOR_BETWEEN(2011404215, "VALUE NOT VALID FOR (NOT)BETWEEN OPERATOR"),


  /***
   * 动作03
   */
  RULE_ACTION_REST_METHOD_UNSUPPORT(2011404300, "动作的请求方式不支持(支持: POST/GET)"),
  RULE_ACTION_REST_URL_BLANK(2011404301, "请求地址为空"),
  RULE_ACTION_REST_URL_TOO_LONG(2011404302, "请求地址不合法,超出系统长度设置"),


  /***
   * 自定义对象04
   */
  OBJECT_DESCRIBE_ERROR(2011404400, "对象定义获取失败"),
  OBJECT_DATA_ERROR(2011404401, "获取元数据失败");


  private int code;
  private String message;

  ConditionExceptionCodeEnum(int code, String message) {
    this.code = code;
    this.message = message;
  }

  public int getCode() {
    return code;
  }

  public String getMessage() {
    return message;
  }

}
