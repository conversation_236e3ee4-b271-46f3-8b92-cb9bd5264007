package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/4 16:20:19
 */
public interface DeleteRelationManage {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Arg implements Serializable {
        @ApiModelProperty("分组id")
        private String id;
    }

    @Data
    class Result implements Serializable {
    }
}
