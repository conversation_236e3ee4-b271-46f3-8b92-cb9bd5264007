package com.fxiaoke.open.erpsyncdata.admin.model.superadmin;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/6
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class AdminConfigInfo {
    /**
     * 主键
     */
    private String id;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 配置类型
     * 也存在动态type
     * TenantConfigurationTypeEnum
     */
    private String type;

    /**
     * 配置信息
     */
    private String configuration;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    private TenantConfigurationTypeEnum typeEnum;

    private String formatType;
    private String dataType;
}
