package com.fxiaoke.open.oasyncdata.util;

import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class SFAUtils {
    public static ControllerGetDescribeResult detail(String tenantIdStr,
                              String queryDataId,
                              String bizApiName,
                              I18NStringManager i18NStringManager,
                              MetadataControllerService metadataControllerService) {
        if(StringUtils.isEmpty(bizApiName) || StringUtils.isEmpty(queryDataId))
            return null;

        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantIdStr,i18NStringManager);
        headerObj.put(I18NStringManager.X_FS_LOCALE,i18NStringManager.getDefaultLang(tenantIdStr));
        ControllerDetailArg controllerDetailArg=new ControllerDetailArg();
        controllerDetailArg.setObjectDataId(queryDataId);
        controllerDetailArg.setObjectDescribeApiName(bizApiName);
        controllerDetailArg.setIsFromRecycleBin(Boolean.TRUE);
        Result<ControllerGetDescribeResult> detail = metadataControllerService.detail(headerObj, bizApiName, controllerDetailArg);
        return detail.getData();
    }

    /**
     * 获取人员字段的名称
     * @param objectData
     * @param fieldApiName
     * @return
     */
    public static String getEmpName(ObjectData objectData, String fieldApiName) {
        if(StringUtils.equalsIgnoreCase(fieldApiName,"owner__r") || StringUtils.equalsIgnoreCase(fieldApiName,"submitter__r")) {
            Map<String,Object> ownerMap = (Map<String,Object>)objectData.get(fieldApiName);
            String name = ownerMap.get("name")!=null ? ownerMap.get("name").toString() : null;
            if(StringUtils.isEmpty(name)) {
                name = ownerMap.get("nickname")!=null ? ownerMap.get("nickname").toString() : null;
            }
            return name;
        }
        return null;
    }
}
