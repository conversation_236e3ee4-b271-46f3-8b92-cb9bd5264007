package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Data
@ContentRowHeight(25)
@HeadRowHeight(25)
@ColumnWidth(16)
@AllArgsConstructor
@NoArgsConstructor
public class TestExcelMod implements Serializable {

    private static final long serialVersionUID = -7898050229665840983L;
    @ExcelProperty(value = {"erpdss.global.global.s6","学生姓名"})
    private String student;

    @ExcelProperty(value = {"erpdss.global.global.s7","学生姓名"})
    private String course;

    @ExcelProperty(value = {"erpdss.global.global.s19","学生姓名"})
    private Integer score;

}
