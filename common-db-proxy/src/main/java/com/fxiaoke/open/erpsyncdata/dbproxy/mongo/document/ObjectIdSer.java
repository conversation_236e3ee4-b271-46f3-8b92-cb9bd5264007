package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;
import org.bson.types.ObjectId;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/4/14
 */
public class ObjectIdSer implements ObjectSerializer, ObjectDeserializer {
    @Override
    @SuppressWarnings("unchecked")
    public ObjectId deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        Object parse = parser.parse();
        return new ObjectId(parse.toString());
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }

    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        SerializeWriter writer = serializer.getWriter();
        writer.write("\""+object.toString()+"\"");
    }
}
