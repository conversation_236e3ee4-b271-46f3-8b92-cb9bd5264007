package com.fxiaoke.open.erpsyncdata.preprocess.result.base;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.fxiaoke.open.erpsyncdata.common.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.github.trace.aop.IgnorableException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * erp exception
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/5
 */
public class ErpSyncDataException extends RuntimeException implements IgnorableException, I18nBase {
    private static final long serialVersionUID = 8719923886663136527L;
    /**
     * 错误码
     */
    @Getter
    private final String errCode;
    /**
     * 错误信息
     */
    @Getter
    private final String errMsg;
    /**
     * 国际化词条key
     */
    @Getter
    private final String i18nKey;
    /**
     * 国际化额外信息
     */
    @Getter
    private List<String> i18nExtra;
    /**
     * exception就别加tenantId了。。。
     */
    @Getter
    @Deprecated
    private final String tenantId;

    public ErpSyncDataException(String errCode, String errMsg, String i18nKey, String tenantId) {
        super("errCode=" + errCode + ",errMsg=" + errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
        this.i18nKey = i18nKey;
        this.tenantId = tenantId;
    }

    public ErpSyncDataException(ResultCodeEnum resultCode, String errMsg, String i18nKey, String tenantId) {
        super("errCode=" + resultCode.getErrCode() + ",errMsg=" + errMsg);
        this.errCode = resultCode.getErrCode();
        this.errMsg = errMsg;
        this.i18nKey = i18nKey;
        this.tenantId = tenantId;
    }

    public ErpSyncDataException(String errMsg, String i18nKey, String tenantId) {
        super("errCode=" + ResultCodeEnum.SYSTEM_ERROR.getErrCode() + ",errMsg=" + errMsg);
        this.errCode = ResultCodeEnum.SYSTEM_ERROR.getErrCode();
        this.errMsg = errMsg;
        this.i18nKey = i18nKey;
        this.tenantId = tenantId;
    }

    public ErpSyncDataException(I18NStringEnum i18NStringEnum, String tenantId) {
        this(i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), tenantId);
    }


    public ErpSyncDataException(String errMsg) {
        super("errCode=" + ResultCodeEnum.SYSTEM_ERROR.getErrCode() + ",errMsg=" + errMsg);
        this.errCode = ResultCodeEnum.SYSTEM_ERROR.getErrCode();
        this.errMsg = errMsg;
        this.i18nKey = null;
        this.tenantId = null;
    }

    public ErpSyncDataException(ResultCodeEnum resultCode, String tenantId) {
        super("errCode=" + resultCode.getErrCode() + ",errMsg=" + resultCode.getErrMsg());
        this.errCode = resultCode.getErrCode();
        this.errMsg = resultCode.getErrMsg();
        this.i18nKey = resultCode.getI18nKey();
        this.tenantId = tenantId;
    }


    /**
     * 尝试从context取tenantId。没有的，后台跑的无所谓了。
     * @param resultCode
     */
    public ErpSyncDataException(ResultCodeEnum resultCode) {
        super("errCode=" + resultCode.getErrCode() + ",errMsg=" + resultCode.getErrMsg());
        this.errCode = resultCode.getErrCode();
        this.errMsg = resultCode.getErrMsg();
        this.i18nKey = resultCode.getI18nKey();
        this.tenantId = TraceUtil.tryGetTenantId();
    }

    public ErpSyncDataException(Throwable e) {
        super(e);
        this.errCode = ResultCodeEnum.SYSTEM_ERROR.getErrCode();
        this.errMsg = ResultCodeEnum.SYSTEM_ERROR.getErrMsg() + ":" + e.getMessage();
        this.i18nKey = null;
        this.tenantId = null;
    }

    public ErpSyncDataException(Throwable e, ResultCodeEnum resultCodeEnum, String tenantId) {
        super(e);
        this.errCode = resultCodeEnum.getErrCode();
        this.errMsg = resultCodeEnum.getErrMsg();
        this.i18nKey = resultCodeEnum.getI18nKey();
        this.tenantId = tenantId;
    }

    public ErpSyncDataException(Throwable e, String errCode, String errMsg) {
        super(errMsg, e);
        this.errCode = errCode;
        this.errMsg = errMsg;
        this.i18nKey = null;
        this.tenantId = null;
    }

    public ErpSyncDataException(Throwable e, String errMsg) {
        super(errMsg, e);
        this.errCode = ResultCodeEnum.SYSTEM_ERROR.getErrCode();
        this.errMsg = errMsg;
        this.i18nKey = null;
        this.tenantId = null;
    }

    public ErpSyncDataException(Throwable e, String errMsg, String i18nKey, String tenantId) {
        super(e);
        this.errCode = ResultCodeEnum.SYSTEM_ERROR.getErrCode();
        this.errMsg = errMsg;
        this.i18nKey = i18nKey;
        this.tenantId = tenantId;
    }

    public static <T> void checkResult(Result<T> result, String tenantId) {
        if (result.isSuccess()) {
            return;
        }
        throw new ErpSyncDataException(result.getErrCode(), result.getErrMsg(), result.getI18nKey(), tenantId);
    }

    public ErpSyncDataException(BaseResult baseResult, String i18nKey, String tenantId, String... extras) {
        this.errCode = baseResult.getErrCode();
        this.errMsg = baseResult.getErrMsg();
        this.i18nKey = i18nKey;
        this.tenantId = tenantId;
        if (extras != null) {
            this.i18nExtra = new ArrayList<>(Arrays.asList(extras));
        }
    }

    /**
     * tenantId放首位防止出错
     */
    public ErpSyncDataException(String tenantId, ResultCodeEnum resultCode) {
        super("errCode=" + resultCode.getErrCode() + ",errMsg=" + resultCode.getErrMsg());
        this.errCode = resultCode.getErrCode();
        this.errMsg = resultCode.getErrMsg();
        this.i18nKey = resultCode.getI18nKey();
        this.tenantId = tenantId;
    }

    public ErpSyncDataException extra(String... extras) {
        if (extras != null) {
            this.i18nExtra = new ArrayList<>(Arrays.asList(extras));
        }
        return this;
    }

    public ErpSyncDataException extra(List<String> extras) {
        this.i18nExtra = extras;
        return this;
    }

    public static ErpSyncDataException wrap(String errCode, I18NStringEnum i18NStringEnum, String tenantId, String... extras) {
        return new ErpSyncDataException(errCode, i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), tenantId).extra(extras);
    }

    public static ErpSyncDataException wrap(Throwable e) {
        if (e instanceof ErpSyncDataException) {
            return (ErpSyncDataException) e;
        }
        return new ErpSyncDataException(e);
    }


    public static ErpSyncDataException wrapRoot(Throwable e) {
        //noinspection unchecked
        Throwable causedByErpSyncDataException = ExceptionUtil.getCausedBy(e, ErpSyncDataException.class);
        if (causedByErpSyncDataException != null) {
            return (ErpSyncDataException) causedByErpSyncDataException;
        }
        Throwable rootCause = ExceptionUtil.getRootCause(e);
        return new ErpSyncDataException(rootCause);
    }


    public static ErpSyncDataException wrap(Throwable e, I18NStringEnum i18NStringEnum) {
        return wrap(e, i18NStringEnum, null);
    }

    public static ErpSyncDataException wrap(Throwable e, I18NStringEnum i18NStringEnum, String tenantId) {
        if (e instanceof ErpSyncDataException) {
            return (ErpSyncDataException) e;
        }
        return new ErpSyncDataException(e, i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), tenantId);
    }

    public static ErpSyncDataException wrap(I18NStringEnum i18NStringEnum) {
        return new ErpSyncDataException(i18NStringEnum.getI18nValue(), i18NStringEnum.getI18nKey(), null);
    }

    public static ErpSyncDataException wrap(Throwable e, String msg) {
        if (e instanceof ErpSyncDataException) {
            return (ErpSyncDataException) e;
        }
        return new ErpSyncDataException(e, msg, null, null);
    }

    /**
     * 获取异常链上所有异常的集合，如果{@link Throwable} 对象没有cause，返回只有一个节点的List<br>
     * 如果传入null，返回空集合
     *
     * <p>
     * 此方法来自Apache-Commons-Lang3
     * </p>
     *
     * @param throwable 异常对象，可以为null
     * @return 异常链中所有异常集合
     * @since 4.6.2
     */
    public static List<Throwable> getThrowableList(Throwable throwable) {
        final List<Throwable> list = new ArrayList<>();
        while (throwable != null && !list.contains(throwable)) {
            list.add(throwable);
            throwable = throwable.getCause();
        }
        return list;
    }

    /**
     * 获取异常链中最尾端的异常，即异常最早发生的异常对象。<br>
     * 此方法通过调用{@link Throwable#getCause()} 直到没有cause为止，如果异常本身没有cause，返回异常本身<br>
     * 传入null返回也为null
     *
     * <p>
     * 此方法来自Apache-Commons-Lang3
     * </p>
     *
     * @param throwable 异常对象，可能为null
     * @return 最尾端异常，传入null参数返回也为null
     */
    public static Throwable getRootCause(final Throwable throwable) {
        final List<Throwable> list = getThrowableList(throwable);
        return list.size() < 1 ? null : list.get(list.size() - 1);
    }

    @Override
    public String getErrorCode() {
        //使这个异常不会上报失败，s306244开头不会上报
        return "s306244000";
    }

    @Override
    public String getI18nValue() {
        return getErrMsg();
    }
}
