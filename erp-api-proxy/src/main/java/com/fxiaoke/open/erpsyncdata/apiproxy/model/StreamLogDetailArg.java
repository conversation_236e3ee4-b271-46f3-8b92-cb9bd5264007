package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/6/17 17:08
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StreamLogDetailArg implements Serializable {
    @ApiModelProperty("集成流id")
    private String streamId;

    @ApiModelProperty("同步日志ID")
    private String syncLogId;

    @ApiModelProperty("同步快照ID")
    private String syncDataId;

    @ApiModelProperty("数据中心ID")
    private String dcId;

    @ApiModelProperty("集成流节点")
    private String syncNodeType;

    private String tenantId;
    @ApiModelProperty("日志开始时间")
    private Long logStartTime;
    @ApiModelProperty("日志结束时间")
    private Long logEndTime;

}
