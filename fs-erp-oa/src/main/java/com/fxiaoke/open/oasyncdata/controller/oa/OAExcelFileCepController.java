package com.fxiaoke.open.oasyncdata.controller.oa;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.SendTextNoticeArg;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.ParallelUtils;
import com.fxiaoke.open.oasyncdata.manager.FileManager;
import com.fxiaoke.open.oasyncdata.model.AsyncImportExcelData;
import com.fxiaoke.open.oasyncdata.model.ImportExcelFile;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.FileService;
import com.fxiaoke.open.oasyncdata.service.NotificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/9/24 11:17:56
 */
@Api(tags = "OA文件服务相关接口")
@RestController
@RequestMapping("cep/oa/excelFile")
@Slf4j
public class OAExcelFileCepController extends BaseController {

    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private FileService fileService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private FileManager fileManager;


    @ApiOperation(value = "导入数据")
    @RequestMapping(value = "/importExcelData", method = RequestMethod.POST)
    public DeferredResult<Result<ImportExcelFile.Result>> asyncImportExcelData(@RequestBody AsyncImportExcelData.Arg arg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE, required = false) String lang) throws IOException {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        final String dcId = getDcId();

        ImportExcelFile.Result timeOutResult = new ImportExcelFile.Result();
        timeOutResult.setPrintMsg(i18NStringManager.get(I18NStringEnum.s1088, lang, tenantId));
        DeferredResult<Result<ImportExcelFile.Result>> result = new DeferredResult<>(ConfigCenter.IMPORT_TIME_OUT, Result.newSuccess(timeOutResult));

        ParallelUtils.createBackgroundTask().submit(() -> {
            ImportExcelFile.FieldDataMappingArg importArg = new ImportExcelFile.FieldDataMappingArg();
            importArg.setDataCenterId(dcId);
            importArg.setDataType(arg.getDataType());
            importArg.setTenantId(tenantId);
            importArg.setUserId(userId);
            importArg.setExcelType(arg.getExcelType()); //  OBJ_DATA_MAPPING
            importArg.setLang(lang);
            importArg.setFileType(arg.getFileType());

            try (InputStream inputStream = fileManager.downloadByPath(tenantId, arg.getNpath(), arg.getFileType())) {
                importArg.setFileStream(inputStream);
                Result<ImportExcelFile.Result> resultResult = fileService.importExcelFile(importArg, lang);
                resultResult.setErrMsg(i18NStringManager.get2(resultResult.getI18nKey(), lang, tenantId, resultResult.getErrMsg(), resultResult.getI18nExtra()));
                if (!result.setResult(resultResult)) {
                    sendImportResult(tenantId, userId, resultResult);
                }
            } catch (IOException e) {
                log.error("importObjectDataMapping get error", e);
            }
        }).run();
        return result;
    }


    private void sendImportResult(String tenantId, Integer userId, Result<ImportExcelFile.Result> importResult) {
        String msg = importResult.getErrMsg();
        if (importResult.isSuccess()) {
            msg = importResult.getData().getPrintMsg();
        }
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(i18NStringManager.getByEi(I18NStringEnum.s1263, tenantId) + LocalDateTime.now().toString());
        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg);
    }
}
