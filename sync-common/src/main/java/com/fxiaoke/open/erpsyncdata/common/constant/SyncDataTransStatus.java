package com.fxiaoke.open.erpsyncdata.common.constant;

/**
 * 请查看pg函数
 * create function trans_sync_status(integer) returns integer
 *     language sql
 * as
 * $$
 * select CASE WHEN $1 = 6 THEN 1 WHEN $1 in (1, 3, 5) THEN 2 ELSE 3 END ;
 * $$;
 * <AUTHOR> (^_−)☆
 * @date 2023/6/8
 */
public interface SyncDataTransStatus {
    /**
     * 同步成功
     */
    int success = 1;
    /**
     * 同步失败
     */
    int failed = 2;
    /**
     * 其他，（只有进行中）
     */
    int other = 3;
}
