package com.fxiaoke.open.erpsyncdata.util

import com.alibaba.fastjson.JSON
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpSyncExtentDTO
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncTimeUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/2/6
 */

class SyncTimeUtilTest extends Specification {
    @Unroll
    def "MergeEventTypeByObj"(List<Integer> timeTypes, List<List<Integer>> openEventTypes, List<Integer> resultTypes) {
        given:
        //同时有新增和更新的时间，但是开启的策略只有新增。应该合并为新增的时间。
        //模拟ErpSyncTimeDao.listSyncExtentByTenantId的结果
        List<ErpSyncExtentDTO> syncTimeVos = new ArrayList<>();
        for (final def timeType in timeTypes) {
            for (i in 0..<openEventTypes.size()) {
                def eventTypes = openEventTypes.get(i)
                ErpSyncExtentDTO dto = new ErpSyncExtentDTO(
                        objectApiName: "testObj",
                        operationType: timeType,
                        syncPloyDetailData: new SyncPloyDetailData(
                                id: i.toString(),
                                syncRules: new SyncRulesData(
                                        events: eventTypes
                                )
                        )
                )
                syncTimeVos.add(dto)
            }
        }
        def syncTimesMerged = SyncTimeUtil.mergeEventTypeByObj(syncTimeVos)
        printJson(syncTimeVos)
        printJson(syncTimesMerged)
        expect:
        resultTypes == syncTimesMerged.collect { it -> it.operationType }
        println(resultTypes)
        where:
        timeTypes  | openEventTypes   | resultTypes
        [1, 2]     | [[1, 2], [1, 2]] | [2, 2]
        [1, 2]     | [[1]]            | [1]
        [1, 2]     | [[1], [1, 2]]    | [2, 2]
        [1]        | [[1, 2]]         | [1]
        [1]        | [[1]]            | [1]
        [2]        | [[2]]            | [2]
        [201, 202] | [[1, 2], [1, 2]] | [202, 202]
    }

    private static void printJson(def obj) {
        println(JSON.toJSONString(obj))
    }
}
