package com.fxiaoke.open.erpsyncdata.custom.controller.common;

import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HeaderScriptUtil;
import com.fxiaoke.open.erpsyncdata.custom.service.FeatureSyncService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 科顺使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/5/20
 */

@Slf4j
@Api(tags = "特征同步相关接口")
@RestController("FeatureSyncController")
@RequestMapping("inner/erp/syncdata/customfunction/feature")
public class FeatureSyncController {
    @Autowired
    private FeatureSyncService featureSyncService;

    @RequestMapping("/check.io")
    public String check(){
        Map<String, String> headerMap = HeaderScriptUtil.getHeaderMap(null,"return [\"header\":\"success\"]", "http://www.test.com");
        return headerMap.toString();
    }


    @ApiOperation(value = "科顺企业根据指定条件同步数据")
    @RequestMapping(value = "/featureSync", method = RequestMethod.POST)
    public Result<String> featureSync(@RequestHeader(value = "x-fs-ei") Integer tenantIdInt, @RequestHeader(value = "x-user-id") String userId) {
        String tenantId = tenantIdInt.toString();
        Result<String> result = featureSyncService.executeTenantFeatureSync(tenantId,"rest");
        return result;
    }


}
