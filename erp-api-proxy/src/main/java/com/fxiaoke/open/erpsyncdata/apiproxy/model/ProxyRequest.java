package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Getter
@Setter
@AllArgsConstructor
public class ProxyRequest {
    private String url;

    private Map<String,String> headers;

    private Object params;

    @Override
    public String toString() {
        return GsonUtil.toJson(this);
    }
}
