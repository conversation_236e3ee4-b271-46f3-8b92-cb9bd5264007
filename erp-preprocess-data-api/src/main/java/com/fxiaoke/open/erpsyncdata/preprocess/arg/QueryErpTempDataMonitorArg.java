package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.SearchTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date: 20:06 2021/8/0
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("获取具体erp对象数据监控参数")
public class QueryErpTempDataMonitorArg extends PageArg{
    @ApiModelProperty("策略明细id")
    public String ployDetailId;
    @ApiModelProperty("erp真实对象apiName")
    private String erpRealObjectApiName;
    @ApiModelProperty("erp中间对象apiName")
    private String erpFakeObjectApiName;
    @ApiModelProperty("源数据状态")
    private Integer sourceDataStatus;
    @ApiModelProperty("数据同步状态")
    private Integer tempDataSyncStatus;
    @ApiModelProperty("所属同步任务")
    private String taskNum;
    @ApiModelProperty("开始时间")
    private Long startTime;
    @ApiModelProperty("结束时间")
    private Long endTime;
    @ApiModelProperty("查询的值")
    private String idOrNum;
    @ApiModelProperty("查询的字段")
    private SearchTypeEnum searchType;

}
