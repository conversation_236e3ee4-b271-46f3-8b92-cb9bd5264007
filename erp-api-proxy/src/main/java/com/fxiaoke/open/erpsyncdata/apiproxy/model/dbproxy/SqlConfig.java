package com.fxiaoke.open.erpsyncdata.apiproxy.model.dbproxy;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 */
@Data
public class SqlConfig {
    private String querySql;
    private String idWhere;
    private List<String> batchWhere;
    private List<String> batchInvalidWhere;

    private String insertSql;
    private String updateSql;
    /**
     * 新增或更新后，查询Id的sql，可以为空
     */
    private String queryIdSql;
    /**
     * 单条作废
     */
    private String invalidSql;
    /**
     * 作废关联数据，可用于作废明细，多条
     */
    private List<String> invalidRelationSqls;
}
