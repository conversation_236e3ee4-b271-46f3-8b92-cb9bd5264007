package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpInterfaceMonitorService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryErpObjInterfaceMonitorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceSimpleMsgResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:24 2021/8/20
 * @Desc:
 */
@Ignore
public class ErpInterfaceMonitorServiceImplTest extends BaseTest {
    @Autowired
    private ErpInterfaceMonitorService erpInterfaceMonitorService;

    @Test
    public void queryObjInterfaceList() {
    }

    @Test
    public void queryAllObjInterfaceList() {
        QueryErpInterfaceMonitorArg arg=new QueryErpInterfaceMonitorArg();
        Result<List<ErpInterfaceSimpleMsgResult>> listResult = erpInterfaceMonitorService.queryAllObjInterfaceList("81961", "650085855625084928", 1001, arg,null);
        System.out.println(listResult);
    }

    @Test
    public void getObjInterfaceMonitor() {
        QueryErpObjInterfaceMonitorArg arg=new QueryErpObjInterfaceMonitorArg();
        arg.setErpObjectApiName("BD_MATERIAL");
        arg.setInterfaceType(ErpObjInterfaceUrlEnum.queryMasterBatch);
        arg.setTraceId("J-E.81243.-10000-erp202200-BD_MATERIAL.BillHead");
//        arg.setStartTime(2508L);
//        arg.setEndTime(1632918128089L);
//        arg.setStatus(1);
//        arg.setQueryTime(1634882400000L);
        Result<QueryResult<List<ErpInterfaceMonitorResult>>> queryResultResult = erpInterfaceMonitorService.queryObjInterfaceList("81961", "650085855625084928", 1000, arg,null);
        System.out.println(queryResultResult);
    }

    @Test
    public void queryNotActualCount() {
        QueryErpObjInterfaceMonitorArg queryErpObjInterfaceMonitorArg=new QueryErpObjInterfaceMonitorArg();
        queryErpObjInterfaceMonitorArg.setErpObjectApiName("SAL_SaleOrder");
        queryErpObjInterfaceMonitorArg.setPageSize(20);
        queryErpObjInterfaceMonitorArg.setPageNum(1);
        queryErpObjInterfaceMonitorArg.setStatus(1);

        Result<Page<ErpInterfaceMonitorResult>> pageResult = erpInterfaceMonitorService.queryObjInterfaceListNotActualAccount("81243", "628312575457230848", -1000, queryErpObjInterfaceMonitorArg,null);
        System.out.println("pageResult");

    }

    @Test
    public void downloadObjInterfaceMonitor() {
    }
}