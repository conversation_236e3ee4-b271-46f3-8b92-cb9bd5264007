package com.fxiaoke.open.erpsyncdata.web.controller.fs;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.admin.manager.analyse.AllTenantInfoManager;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.fstool.SystemSearch;
import com.fxiaoke.open.erpsyncdata.admin.model.fstool.SystemSearch.Arg;
import com.fxiaoke.open.erpsyncdata.admin.model.fstool.SystemSearch.TenantInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 纷享内部工具
 * 权限：所有纷享内部人员可以访问
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@RestController("FsEfficiencyToolController")
@RequestMapping("erp/syncdata/fstool")
public class FsToolController extends BaseController {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private AllTenantInfoManager allTenantInfoManager;

    //不要使用"",不然无法从fstool访问，别问为什么我也不知道。。。
    @GetMapping({"/"})
    public ModelAndView index() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/erp/syncdata/fstool/index.jsp");
        UserVo userVo = getUserVo();
        String ea = userVo.getEnterpriseAccount();
        Integer employeeId = userVo.getEmployeeId();
        String name = getName();
        modelAndView.addObject("cdnBaseUrl", ConfigCenter.CDN_BASE_URL);
        modelAndView.addObject("userName", String.format("%s(%s.%s)", name, ea, employeeId));
        return modelAndView;
    }

    @GetMapping("initPageData")
    public Result<Dict> initPageData(@RequestParam String page) {
        //初始化数据
        Dict dict = Dict.create();
        if (Objects.equals(page, "connectSystemSearch")) {
            SystemSearch.InfoHolder infoHolder = allTenantInfoManager.getInfoHolder(false);
            List<String> systemNames = CollUtil.newArrayList(infoHolder.getSystemNameKey2DcId().keySet());
            systemNames.sort(null);
            dict.put("systemNames", systemNames);
        }
        return Result.newSuccess(dict);
    }

    @PostMapping("searchTenantInfo")
    public Result<Amis.Crud<TenantInfo>> searchTenantInfo(@RequestBody Arg arg) {
        SystemSearch.InfoHolder infoHolder = allTenantInfoManager.getInfoHolder(false);
        if (StrUtil.isBlank(arg.getSystemName())) {
            //只支持搜索系统名称
            return Result.newSystemError(I18NStringEnum.s3684);
        }
        Set<String> dcIds = new HashSet<>(infoHolder.getSystemNameKey2DcId().get(arg.getSystemName()));
        List<TenantInfo> resultList = new ArrayList<>();
        if (!CollUtil.isEmpty(dcIds)) {
            //随机10个
            Set<String> dcIds10 = dcIds.size() > 10 ? RandomUtil.randomEleSet(dcIds, 10) : dcIds;
            //获取企业,可能重复，就让他重复

            for (String dcId : dcIds10) {
                TenantInfo tenantInfo = infoHolder.getDc2TenantInfo().get(dcId);
                if (tenantInfo != null) {
                    resultList.add(tenantInfo);
                }
            }
        }
        Amis.ColHelper<TenantInfo> colHelper = Amis.ColHelper.parse(TenantInfo.class, resultList);
        return Result.newSuccess(colHelper.getCrud());
    }
}
