package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.ObjApiName;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * erp对象信息
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ObjApiName("erpObjectApiName")
@Table(name = "erp_object")
public class ErpObjectEntity {

    @Id
    private String id;

    /**
    * 企业id
    */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
    * 渠道
    */
    private ErpChannelEnum channel;

    /**
    * 对象类型
    */
    private ErpObjectTypeEnum erpObjectType;


    /**
    * 对象apiName
    */
    @Column(name = "erp_object_apiname")
    private String erpObjectApiName;

    /**
    * 对象名称
    */
    private String erpObjectName;

    /**
     * 对象扩展信息
     */
    private String erpObjectExtendValue;

    /**
    * 删除状态
    */
    private Boolean deleteStatus;

    /**
    * 创建时间
    */
    private Long createTime;

    /**
    * 修改时间
    */
    private Long updateTime;
}