package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DownstreamRelationManage;
import com.fxiaoke.open.erpsyncdata.preprocess.model.OrderBy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4 16:20:19
 */
public interface GetDownstreamEnterprise {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Arg extends PageArg {
        @ApiModelProperty("分组id")
        private String id;
        @ApiModelProperty("查看类型 1-正常 2-取消代管")
        private Integer status;
    }

    @Data
    @ApiModel
    class Result extends QueryResult<List<DownstreamRelationManage>> {
    }


    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class StatArg extends PageArg {
        @ApiModelProperty("分组id")
        private String id;
        @ApiModelProperty("排序字段")
        private List<OrderBy> orderByList;
        @ApiModelProperty("告警状态，1-正常，2-告警中，不传查所有")
        private Integer alertStatus;
        @ApiModelProperty("数据状态，1-全部成功，2-有异常，不传查所有")
        private Integer syncDataStatus;
        /**
         * 填充企业信息
         */
        private boolean fillEnterpriseInfo = true;
    }


    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    @Data
    @ApiModel
    class StatResult extends QueryResult<List<DownstreamRelationManage>> {

        @ApiModelProperty("统计任务是否完成 1-完成 2-未完成 3-无数据")
        private Integer statStatus = 1;
        @ApiModelProperty("统计时间")
        private Long lastStatTime;
        @ApiModelProperty("所有需要统计的企业数")
        private Integer expectTotal;
        @ApiModelProperty("任务异常信息")
        private String exceptionMsg;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class ResyncArg {
        @ApiModelProperty("分组id")
        private String id;
        @ApiModelProperty("下游企业Id，为空重试所有")
        private List<String> downstreamIds;
    }

}
