package com.fxiaoke.open.erpsyncdata.custom.service.impl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.custom.service.ErpDataInterfaceService;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:32 2021/2/4
 * @Desc:
 */
@Service
@Slf4j
public class ErpDataInterfaceServiceImpl implements ErpDataInterfaceService {
    @Override
    public Result<List<StandardData>> getAllErpObjectData(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorDataHandler dataHandler = ConnectorHandlerFactory.getDataHandler(channel, connectInfo.getConnectParams());
        Integer limit=100;
        Integer offset=0;
        List<StandardData> allDataList= Lists.newArrayList();
        while (true){
            timeFilterArg.setLimit(limit);
            timeFilterArg.setOffset(offset);
            List<StandardData> dataList=Lists.newArrayList();
            Result<StandardListData> result=dataHandler.listErpObjDataByTime(timeFilterArg,connectInfo);
            if (result != null && result.isSuccess() && result.getData() != null && result.getData().getDataList() != null) {
                dataList=result.getData().getDataList();
            }else{
                log.info("objectDataService.queryBySearchTemplate timeFilterArg={}, connectInfo={}，result={}",timeFilterArg,connectInfo,result);
            }
            allDataList.addAll(dataList);
            if(dataList.size()!=limit){
                break;
            }
            offset+=limit;
        }
        return Result.newSuccess(allDataList);
    }
    @Override
    public Result<List<StandardData>> getLimitErpObjectData(TimeFilterArg timeFilterArg, ErpConnectInfoEntity connectInfo) {
        ErpChannelEnum channel = connectInfo.getChannel();
        ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(channel, connectInfo.getConnectParams());
        Result<StandardListData> result=erpDataManager.listErpObjDataByTime(timeFilterArg,connectInfo);
        if (result != null && result.isSuccess() && result.getData() != null && result.getData().getDataList() != null) {
            return Result.newSuccess(result.getData().getDataList());
        }else{
            log.info("objectDataService.queryBySearchTemplate timeFilterArg={}, connectInfo={}，result={}",timeFilterArg,connectInfo,result);
        }
        return Result.newSuccess(Lists.newArrayList());
    }
}
