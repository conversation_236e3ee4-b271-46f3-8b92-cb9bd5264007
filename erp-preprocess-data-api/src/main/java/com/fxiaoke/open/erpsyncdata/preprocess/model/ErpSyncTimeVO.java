package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * erp同步时间
 * <AUTHOR>
 */
@ApiModel(value = "同步时间")
@Data
public class ErpSyncTimeVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "企业ei")
    private String tenantId;

    @ApiModelProperty(value = "对象apiName")
    private String objectApiName;

    @ApiModelProperty(value = "操作类型")
    private Integer operationType;

    @ApiModelProperty(value = "最后同步时间")
    private Long lastSyncTime;

    @ApiModelProperty(value = "最后轮询mongo时间")
    private Long lastQueryMongoTime ;

    @ApiModelProperty(value = "当前时间")
    private Long currentTime;

    @ApiModelProperty(value = "优先级，值越小越优先，默认值100")
    private Integer priority;

    @ApiModelProperty(value = "策略快照id")
    private String snapshotId;
    /**
     * 是否需要发送明细MQ
     */
    private Boolean needSendDetailEvent = true;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "更新时间")
    private Long updateTime;

    private static final long serialVersionUID = 1L;
}
