package com.fxiaoke.open.oasyncdata.model;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import io.netty.util.internal.ObjectUtil;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.io.Serializable;

/**
 * oa对接日志
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Data
public class OASyncLogVO implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    private String id;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 数据id
     */
    private String dataId;
    /**
     * 数据名称
     *
     */
    private String dataName;
    /**
     * 业务类型 代办或者提醒
     */
    private String businessType;
    /**
     * 业务对象名
     */
    private String objectName;
    /**
     * 数据
     */
    private String dataJson;

    /**
     * 状态
     */
    private String status;


    /**
     * 错误信息
     */
    private String message;

    /**
     * 对象信息
     */
    private String objApiName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 标题
     */
    private String title;

    /**
     * 接收人Id
     */
    private String receiverId;


    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;

    //请求接口信息---------------------------------
    /**
     * 请求地址
     */
    private String url;
    /**
     * apl api name
     */
    private String aplApiName;
    /**
     * 请求方式，GET,POST等等
     */
    private String method;
    /**
     * 请求头
     */
    private String header;
    /**
     * 请求体
     */
    private String body;
    /**
     * 响应体
     */
    private String response;
    //---------------------------------

    public String getBusinessType(I18NStringManager i18NStringManager, String lang, String tenantId) {
       if(this.businessType!=null){
           return businessType;
       }else if(ObjectUtils.isNotEmpty(ObjectApiEnum.getObjApiEnumByApiName(this.objApiName))){
           return ObjectApiEnum.getObjApiEnumByApiName(this.objApiName).getBusinessTypeName(i18NStringManager,lang,tenantId);
       }
       return i18NStringManager.get(I18NStringEnum.s1227,lang,tenantId);
    }
    public void setBusinessType(I18NStringManager i18NStringManager, String lang, String tenantId) {
        if(ObjectUtils.isNotEmpty(ObjectApiEnum.getObjApiEnumByApiName(this.objApiName))){
            this.businessType= ObjectApiEnum.getObjApiEnumByApiName(this.objApiName).getBusinessTypeName(i18NStringManager,lang,tenantId);
        }else {
            this.businessType=i18NStringManager.get(I18NStringEnum.s1227,lang,tenantId);
        }
    }

    public static void main(String[] args) {
        OASyncLogVO oaSyncLogVO=new OASyncLogVO();
        oaSyncLogVO.setObjApiName("crmNotify");
        System.out.printf(JSONObject.toJSONString(oaSyncLogVO));

    }

}