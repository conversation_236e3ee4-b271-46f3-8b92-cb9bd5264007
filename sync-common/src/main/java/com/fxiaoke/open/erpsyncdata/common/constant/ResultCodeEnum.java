package com.fxiaoke.open.erpsyncdata.common.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import lombok.Getter;

public enum ResultCodeEnum implements I18nBase {
    /**
     * 成功
     **/
    SUCCESS(0, "成功", I18NStringEnum.s6.getI18nKey()),
    /**
     * 系统繁忙
     */
    SERVER_BUSY(-1, "系统繁忙", I18NStringEnum.s431.getI18nKey()),
    /**
     * 系统错误
     */
    SYSTEM_ERROR(-2, "系统错误", I18NStringEnum.s392.getI18nKey()),
    /**
     * 第三方服务出错
     */
    THIRD_APPLICATION_ERROR(-3, "第三方服务出错", I18NStringEnum.s432.getI18nKey()),
    /**
     * 参数错误
     */
    PARAM_ERROR(-3, "参数错误", I18NStringEnum.s433.getI18nKey()),
    /**
     * 无权限
     */
    NOT_HAVE_AUTH(-5, "权限不足", I18NStringEnum.s434.getI18nKey()),
    /**
     * 数据不存在或已被删除
     */
    DATA_NOT_FOUND(-5, "数据不存在或已被删除", I18NStringEnum.s435.getI18nKey()),
    /**
     * 未知错误
     */
    UNKNOWN(-100, "未知异常", I18NStringEnum.s436.getI18nKey()),

    /**
     * 无权限操作此数据
     */
    NOT_HAVE_DATA_AUTH(-110, "无权限操作此数据", I18NStringEnum.s437.getI18nKey()),

    /**
     * 策略重复
     */
    PLOY_DUPLICATE(-111, "已存在相同策略", I18NStringEnum.s438.getI18nKey()),

    /**
     * 从对象不能单独创建策略
     */
    CAN_NOT_ADD_PLOY_BY_DETAIL(-112, "从对象不能单独创建策略", I18NStringEnum.s439.getI18nKey()),

    /**
     * 策略重复
     */
    PLOY_DETAIL_DUPLICATE(-113, "已存在相同策略明细", I18NStringEnum.s440.getI18nKey()),

    /**
     * 无法启用策略
     */
    CAN_NOT_OPEN_PLOY(-114, "无法启用策略，请先配置字段映射和同步规则", I18NStringEnum.s441.getI18nKey()),

    /**
     * 对象没有设置字段映射
     */
    NOT_SET_FIELD(-115, "对象没有设置字段映射", I18NStringEnum.s442.getI18nKey()),
    /**
     * 数据规则执行失败
     */
    DATA_RULE_EXECUTE_FAILED(-116, "数据规则执行失败", I18NStringEnum.s443.getI18nKey()),
    /**
     * 数据处理失败
     */
    DATA_PROCESS_FAILDED(-117, "数据处理失败", I18NStringEnum.s444.getI18nKey()),
    /**
     * 不支持的类型映射
     */
    UNSUPPORTED_TYPE_MAPPING(-118, "不支持的字段类型映射",  I18NStringEnum.s445.getI18nKey()),

    /**
     * 查找关联类型的字段没有对应的策略
     */
    OBJECT_REFERENCE_NOT_HAVE_PLOY_DETAIL(-119, "查找关联类型的字段没有对应的策略", I18NStringEnum.s446.getI18nKey()),

    /**
     * 关联对象对应的策略没有启用
     */
    OBJECT_REFERENCE_NOT_OPEN_PLOY_DETAIL(-120, "查找关联类型字段对应的策略没有启用", I18NStringEnum.s447.getI18nKey()),

    /**
     * 请先停用关联该对象的策略
     */
    OBJECT_REFERENCE_NOT_CLOSE_PLOY_DETAIL(-121, "请先停用关联该对象的策略", I18NStringEnum.s448.getI18nKey()),

    /**
     * 无法启用策略
     */
    NOT_HAS_FIELD_MAPPINGS(-123, "无法启用策略，请先配置字段映射", I18NStringEnum.s449.getI18nKey()),
    /**
     * 无法启用策略
     */
    NOT_HAS_DETAIL_FIELD_MAPPINGS(-124, "无法启用策略，请先配置从对象字段映射", I18NStringEnum.s450.getI18nKey()),

    /**
     * 无法启用策略
     */
    NOT_HAS_SYNC_RULES(-125, "无法启用策略，请先配置同步规则", I18NStringEnum.s451.getI18nKey()),
    /**
     * 无法启用策略
     */
    NOT_HAS_SYNC_CONDITIONS(-126, "无法启用策略，请先配置同步范围", I18NStringEnum.s452.getI18nKey()),

    /**
     * 无法启用策略
     */
    PLOY_EXCEPTION_OBJECT_NOT_FOUND(-127, "同步策略异常,对象已作废或已删除",  I18NStringEnum.s453.getI18nKey()),

    /**
     * 已启用策略无法删除
     */
    ENABLED_PLOY_CAN_NOT_DELETE(-128, "已启用策略无法删除，请先停用策略", I18NStringEnum.s454.getI18nKey()),
    /**
     * 无法删除，请先删除该对象的所有策略
     */
    PLOY_CAN_NOT_DELETE(-129, "策略无法删除，请先删除该对象的所有策略明细", I18NStringEnum.s455.getI18nKey()),
    /**
     * 新状态不能和当前状态一致
     */
    NEW_STATUS_IS_OLD_STATUS(-130, "新状态不能和当前状态一致", I18NStringEnum.s456.getI18nKey()),
    /**
     * 同步超时
     */
    SYNC_DATA_TIMEOUT(-131, "同步超时",I18NStringEnum.s457.getI18nKey()),
    /**
     * 策略已经被停用
     */
    PLOY_WAS_DISABLED(-132, "策略已经被停用",I18NStringEnum.s458.getI18nKey()),
    /**
     * 不支持的对象
     */
    UNSUPPORTED_OBJECT(-133, "不支持的对象",I18NStringEnum.s459.getI18nKey()),
    /**
     * 同步策略类型不存在
     */
    SYNC_PLOY_TYPE_NOT_EXIST(-134, "同步策略类型不存在",I18NStringEnum.s460.getI18nKey()),
    /**
     * 无此自定义函数的apiName类型
     */
    CUSTOM_FUNC_APINAME_TYPE_NOT_EXIST(-135,"不存在的函数apiName",  I18NStringEnum.s461.getI18nKey()),
    /**
     * 自定义函数包含新增/修改/作废等操作
     */
    CUSTOM_FUNC_CONTAIN_ILLEGAL_OPERATE_TYPE(-136,"自定义函数包含新增/修改/作废等操作", I18NStringEnum.s462.getI18nKey()),
    /**
     * 自定义函数体为空
     */
    CUSTOM_FUNC_BODY_NOT_EXIST(-137,"自定义函数体为空", I18NStringEnum.s463.getI18nKey()),
    /**
     * 策略校验不合法
     */
    PLOY_NOT_VALID(-138, "策略校验不合法,请查看运行状态",I18NStringEnum.s464.getI18nKey()),
    /**
     * 自定义函数执行失败
     */
    CUSTOM_FUNC_EXECUTE_FAIL(-139,"自定义函数执行失败", I18NStringEnum.s465.getI18nKey()),
    /**
     * 该自定义函数禁止执行
     */
    CUSTOM_FUNC_FORBID_EXECUTE(-140,"该自定义函数禁止执行", I18NStringEnum.s466.getI18nKey()),
    /**
     * 数据范围类型不存在
     */
    CONDITION_TYPE_NOT_EXIST(-141, "数据范围类型不存在",I18NStringEnum.s467.getI18nKey()),
    /** 依赖对象数据未存在映射关系 */
    DEPEND_DATA__NOT_SYNC(-142, "依赖对象数据未存在映射关系",I18NStringEnum.s468.getI18nKey()),

    /** 所选企业无此对象 */
    HAD_NOT_OBJECT_FOR_TENANT(-144, "所选企业无此对象", I18NStringEnum.s473.getI18nKey()),
    /** 所选企业无此对象字段描述 */
    HAD_NOT_OBJECT_FIELD_DESCRIPT_FOR_TENANT(-145, "所选企业无此对象描述", I18NStringEnum.s474.getI18nKey()),

    /**
     * 调用Http接口超时超时
     */
    SOCKET_TIMEOUT(-148, "调用Http接口超时",I18NStringEnum.s469.getI18nKey()),
    /**
     * K3Cloud版本不支持订单新变更单，请升级K3Cloud系或使用订单直接编辑功能
     */
    K3CLOUD_NOT_SUPPORT_XORDER(-149, "K3Cloud版本不支持订单新变更单，请升级K3Cloud系或使用订单直接编辑功能", I18NStringEnum.s470.getI18nKey()),

    DURING_FUNC_CHANGE_DETAIL_NUM(-150, "同步中函数修改了从对象数量", I18NStringEnum.s471.getI18nKey()),

    /**
     * 未获取到返回结果
     */
    NOT_GET_RESULT(-151, "未获取到返回结果", I18NStringEnum.s471.getI18nKey()),

    DATA_SYNCHRONIZING(-152, "数据正在同步中,请稍后再试", I18NStringEnum.s3799.getI18nKey()),
    ;

    /**
     * 错误码
     */
    @Getter
    private int errCode;
    /**
     * 错误信息
     */
    @Getter
    private String errMsg;
    /**
     * 错误描叙
     */
    @Getter
    private String i18nKey;

    ResultCodeEnum(int errCode, String errMsg, String i18nKey) {
        this.errCode = errCode;
        this.errMsg = errMsg;
        this.i18nKey = i18nKey;
    }

    @Override
    public String getI18nValue() {
        return errMsg;
    }
}
