package com.fxiaoke.open.erpsyncdata.preprocess.util;

import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SpeedLimitManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date: 14:12 2021/8/6
 * @Desc:
 */
@Slf4j
@Component
public class PollingDataSpeedRateLimitManager {

    @Autowired
    private SpeedLimitManager speedLimitManager;


    public void acquireQueryErpData(String tenantId, Long counts) {
        if (counts == null || counts < 1) {
            return;
        }
        speedLimitManager.countAndSleep(tenantId, SpeedLimitTypeEnum.QUERY_ERP_DATA, counts);
    }

    public void acquireQueryErpHistoryData(String tenantId, Long counts) {
        if (counts == null || counts < 1) {
            return;
        }
        speedLimitManager.countAndSleep(tenantId, SpeedLimitTypeEnum.QUERY_ERP_DATA_HISTORY, counts);
    }
}
