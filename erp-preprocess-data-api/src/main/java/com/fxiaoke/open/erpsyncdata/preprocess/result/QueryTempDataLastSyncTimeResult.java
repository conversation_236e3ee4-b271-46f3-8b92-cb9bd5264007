package com.fxiaoke.open.erpsyncdata.preprocess.result;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 16:35 2021/8/11
 * @Desc:
 */
@Data
@ApiModel
public class QueryTempDataLastSyncTimeResult implements Serializable {
    @ApiModelProperty("对象apiName")
    private String objApiName;
    @ApiModelProperty("数据体最后轮询更新时间")
    private Long lastSyncUpdateTime;
    @ApiModelProperty("数据体最后轮询作废时间")
    private Long lastSyncInvalidTime;
    @ApiModelProperty("数据体最后轮询删除时间")
    private Long lastSyncDeleteTime;
}
