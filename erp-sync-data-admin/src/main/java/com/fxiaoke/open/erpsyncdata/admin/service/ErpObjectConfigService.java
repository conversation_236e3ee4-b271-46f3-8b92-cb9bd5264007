package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.ErpDBProxyConfigArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.ErpEaiConfigArg;
import com.fxiaoke.open.erpsyncdata.admin.result.CheckDBProxyConfigResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.web.bind.annotation.RequestBody;

public interface ErpObjectConfigService {

    Result<ErpEaiConfigArg> loadErpEaiConfig(String tenantId, String dataCenterId, String objectApiName, String splitObjectApiName);

    Result<ErpEaiConfigArg> updateErpEaiConfig(ErpEaiConfigArg erpEaiConfigArg);

    Result<ErpDBProxyConfigArg> loadErpDBProxyConfig(String tenantId, String dataCenterId, String objectApiName, String splitObjectApiName);

    Result<ErpDBProxyConfigArg> updateErpDBProxyConfig(ErpDBProxyConfigArg erpDBProxyConfigArg);

    Result<CheckDBProxyConfigResult> checkDBProxyConfig(ErpDBProxyConfigArg erpDBProxyConfigArg);

}
