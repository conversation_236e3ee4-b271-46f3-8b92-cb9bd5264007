package com.fxiaoke.open.erpsyncdata.preprocess.result;


import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:35 2020/8/18
 * @Desc:
 */
@Data
@ApiModel
public class ErpObjectFieldResult implements Serializable {
    @ApiModelProperty("数据id")
    public String id; //
//    @ApiModelProperty("企业id")
//    public int tenantId; //
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel; //渠道，k3,sap,u8，其他
    @ApiModelProperty("对象apiName")
    public String erpObjectApiName ;
    @ApiModelProperty("字段apiName")
    public String fieldApiName ;
    @ApiModelProperty("字段Label")
    public String fieldLabel;
    @ApiModelProperty("是否必填")
    public boolean required = false;
    @ApiModelProperty("字段类型")
    public ErpFieldTypeEnum fieldDefineType;
    @ApiModelProperty("字段扩展值,格式根据字段类型变化")
    public Object fieldExtendValue;

    @ApiModelProperty("保存Code")
    public String saveCode;
    @ApiModelProperty("查看Code")
    public String viewCode;
    @ApiModelProperty("批量查询Code")
    public String queryCode;
    @ApiModelProperty("保存扩展")
    public Object saveExtend;
    @ApiModelProperty("查看扩展")
    public Object viewExtend;
    @ApiModelProperty("数据中心ID")
    private String dataCenterId;
}
