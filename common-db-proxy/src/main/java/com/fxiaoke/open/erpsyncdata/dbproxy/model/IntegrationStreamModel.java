package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntegrationStreamModel implements Serializable {
    /**
     * 集成流ID
     */
    private String id;
    /**
     * 集成流名称
     */
    private String name;

    private boolean isStop;
}
