package com.fxiaoke.open.oasyncdata.controller.oa.open;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.arg.SendTextNoticeArg;
import com.fxiaoke.open.oasyncdata.controller.oa.BaseController;
import com.fxiaoke.open.oasyncdata.controller.oa.OAExcelFileCepController;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.ParallelUtils;
import com.fxiaoke.open.oasyncdata.model.*;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.service.FileService;
import com.fxiaoke.open.oasyncdata.service.NotificationService;
import com.fxiaoke.open.oasyncdata.service.OAObjectFieldService;
import com.fxiaoke.open.oasyncdata.service.OAUserService;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA工具接口，无权限")
@RestController()
@RequestMapping("erp/syncdata/oa/tools")
@Slf4j
public class OAToolController extends BaseController {
    @Autowired
    private OAObjectFieldService oaObjectFieldService;
    @Autowired
    private OAUserService oaUserService;
    @Autowired
    private FileService fileService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;


    @GetMapping({"/viewRedirect"})
    public ModelAndView viewRedirect() {
        String redirectUrl="redirect:%s/erp/syncdata/open/oa/tools/index.html";
        redirectUrl= String.format(redirectUrl, ConfigCenter.ERP_DOMAIN_URL);
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        modelAndView.addObject("cdnBaseUrl", ConfigCenter.CDN_BASE_URL);
        return modelAndView;
    }

    @GetMapping({"/viewHtml"})
    public ModelAndView viewHtml() {
        String redirectUrl = ConfigCenter.ERROR_REDIRECT_URL;
        ModelAndView modelAndView = new ModelAndView(redirectUrl);
        return modelAndView;
    }


    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "field/addOrUpdateObjectField", method = RequestMethod.POST)
    public Result<List> addOrUpdateObjectField(@RequestBody List<OAObjectFieldVO> oaObjectFields,
                                               @RequestHeader(value = "secret")String secret,
                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        if(!"qSBMzZYpGQkvBQhP1".equals(secret)){
            return  Result.newSystemError(I18NStringEnum.s1264);
        }
        String tenantId = DataBaseBatchIndexUtil.notTenantId;
        return oaObjectFieldService.addOrUpdateObjectField(tenantId, oaObjectFields,lang);
    }

    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    public Result<String> addOrUpdateObjectField() {
        log.info("hello ....");
        return Result.newSuccess();
    }

    /**
     * @see OAExcelFileCepController#asyncImportExcelData(AsyncImportExcelData.Arg, String)
     */
    @Deprecated
    @ApiOperation(value = "导入数据")
    @RequestMapping(value = "/importExcelData", method = RequestMethod.POST)
    public DeferredResult<Result<ImportExcelFile.Result>> asyncImportExcelData(ImportExcelFile.FieldDataMappingArg arg,
                                                                               @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) throws IOException {

        ImportExcelFile.Result timeOutResult = new ImportExcelFile.Result();
        timeOutResult.setPrintMsg(i18NStringManager.get(I18NStringEnum.s1088,lang,arg.getTenantId()));
        DeferredResult<Result<ImportExcelFile.Result>> result = new DeferredResult<>(ConfigCenter.IMPORT_TIME_OUT, Result.newSuccess(timeOutResult));
        String loginUserTenantId = getLoginUserTenantId();
        arg.setTenantId(loginUserTenantId);

        Integer loginUserId = getLoginUserId();
        arg.setUserId(loginUserId);
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                Result<ImportExcelFile.Result> resultResult = fileService.importExcelFile(arg,lang);
                resultResult.setErrMsg(i18NStringManager.get2(resultResult.getI18nKey(),lang,loginUserTenantId,resultResult.getErrMsg(),resultResult.getI18nExtra()));
                if (!result.setResult(resultResult)) {
                    sendImportResult(loginUserTenantId, loginUserId, resultResult);
                }
            } catch (IOException e) {
                log.error("importObjectDataMapping get error", e);
            }
        }).run();
        return result;
    }

    @ApiOperation(value = "返回result")
    @RequestMapping(value = "/complexSuccessResult", method = RequestMethod.POST)
    public Result<Map<String,Object>> complexSuccessResult() {
        log.info("hello ....");
        Map<String,Object> resultMap=Maps.newHashMap();
        Map<String,Object> detailMap=Maps.newHashMap();
        detailMap.put("code","0");
        detailMap.put("result","请求成功oa的消息");   // ignoreI18n   实施和开发自用
        resultMap.put("resultMsg", JSONObject.toJSON(detailMap));
        return Result.newSuccess(resultMap);
    }
    @ApiOperation(value = "返回result")
    @RequestMapping(value = "/simpleSuccessResult", method = RequestMethod.POST)
    public Result<Map<String,Object>> simpleSuccessResult(@RequestBody Object object) {
        log.info("hello ....");
        Map<String,Object> resultMap=Maps.newHashMap();
        resultMap.put("code","0");
        resultMap.put("result","请求成功oa的消息");   // ignoreI18n   实施和开发自用
        return Result.newSuccess(resultMap);
    }

    @ApiOperation(value = "返回result")
    @RequestMapping(value = "/putSuccessResult", method = RequestMethod.PUT)
    public Result<Map<String,Object>> putSuccessResult(@RequestBody Object object) {
        log.info("hello ....",JSONObject.toJSONString(object));
        Map<String,Object> resultMap=Maps.newHashMap();
        resultMap.put("code","0");
        resultMap.put("result","请求成功oa的消息");   // ignoreI18n   实施和开发自用
        return Result.newSuccess(resultMap);
    }

    @ApiOperation(value = "返回delete result")
    @RequestMapping(value = "/deleteSuccessResult", method = RequestMethod.DELETE)
    public Result<Map<String,Object>> deleteSuccessResult(@RequestBody Object object) {
        log.info("hello ....",JSONObject.toJSONString(object));
        Map<String,Object> resultMap=Maps.newHashMap();
        resultMap.put("code","0");
        resultMap.put("result","请求成功oa的消息");   // ignoreI18n   实施和开发自用
        return Result.newSuccess(resultMap);
    }





    private void sendImportResult(String tenantId, Integer userId, Result<ImportExcelFile.Result> importResult) {
        String msg = importResult.getErrMsg();
        if (importResult.isSuccess()) {
            msg = importResult.getData().getPrintMsg();
        }
        //发送企信消息
        SendTextNoticeArg sendTextNoticeArg = new SendTextNoticeArg();
        sendTextNoticeArg.setTenantId(tenantId);
        sendTextNoticeArg.setReceivers(Collections.singletonList(userId));
        sendTextNoticeArg.setMsg(msg);
        sendTextNoticeArg.setMsgTitle(i18NStringManager.getByEi(I18NStringEnum.s1263,tenantId) + LocalDateTime.now().toString());
        notificationService.sendErpSyncDataAppNotice(sendTextNoticeArg);
    }


}
