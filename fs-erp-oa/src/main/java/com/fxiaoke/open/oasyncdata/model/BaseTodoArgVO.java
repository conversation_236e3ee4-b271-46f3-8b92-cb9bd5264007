package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BaseTodoArgVO {
    private String ea;

    private int ei;

    private int senderId;
    // 审核人信息
    private List<Integer> receiverIds;
    // id
    private String sourceId;

    private String bizType;

    private String url;

    private String title;

    private String content;

    private List<KeyValueItem> form;

    private Map<String, String> extraDataMap = new HashMap();
}
