package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DataCenterIntegration implements Serializable {
    private String value; // 源数据中心ID
    private String label; // 数据中心名称
    private List<IntegrationStreamChild> children; // 集成流名称列表

    // 内部类表示集成流名称
    @Data
    public static class IntegrationStreamChild {
        private String value; // 源对象apiName
        private String label; // 集成流名称
    }
}