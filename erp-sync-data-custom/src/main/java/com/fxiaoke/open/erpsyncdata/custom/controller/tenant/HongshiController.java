package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import cfca.internal.tool.Mechanism_Inside;
import cfca.util.CertUtil;
import cfca.util.KeyUtil;
import cfca.util.SignatureUtil2;
import cfca.util.cipher.lib.JCrypto;
import cfca.util.cipher.lib.Session;
import cfca.x509.certificate.X509Cert;
import cfca.x509.certificate.X509CertValidator;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.hongshi.Constants;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.hongshi.RSA;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.itrus.cryptorole.CryptoException;
import com.itrus.cryptorole.NotSupportException;
import com.itrus.cryptorole.Sender;
import com.itrus.cryptorole.SignatureVerifyException;
import com.itrus.cryptorole.bc.SenderBcImpl;
import com.itrus.svm.SVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 *
 *用友融联银企联云服务,
 * 对接过程需要引入几个客户的jar包计算签名。
 */

@Slf4j
@Api(tags = "宏石激光相关接口")
@RestController("HongshiController")
//78437是用来测试的ei, 457482 是正式环境的ei
@RequestMapping(path = {"inner/erp/syncdata/customfunction/80787", "inner/erp/syncdata/customfunction/457482","inner/erp/syncdata/customfunction/82814"})
public class HongshiController {

    @Autowired
    private I18NStringManager i18NStringManager;

    private static Session SESSION = null;

    @ApiOperation(value = "创建数据映射，必须开启策略")
    @RequestMapping(value = "/test2",method = RequestMethod.POST)
    public Result<String> test2(@RequestHeader(value="x-fs-ei") Integer tenantId,
                                @RequestHeader Map headerMap,
                                @RequestBody Map bodyMap) {
        log.info("trace test2 ei:{}, header:{}, body:{} ", tenantId, headerMap, bodyMap);



        return Result.newSuccess("succ");
    }



    @ApiOperation(value = "计算签名")
    @RequestMapping(value = "/getP7Sign",method = RequestMethod.POST)
    public Result<String> getP7Sign(HttpServletRequest request) {
            log.info("trace hongshi p7sign get request:{}",request);
           return Result.newSuccess("succ");
    }

    @ApiOperation(value = "计算签名")
    @RequestMapping(value = "/dowork",method = RequestMethod.POST)
    public Result<String> dowork(@RequestHeader(value="x-fs-ei") Integer tenantId,
                                @RequestHeader Map<String, String> headerMap,
                                @RequestBody Map<String, String> bodyMap) {
        log.info("trace dowork tenantId:{}, headerMap:{}, bodymap: {} ",tenantId, headerMap, bodyMap);
        String signData = bodyMap.get("param");
        String op = headerMap.get("op");
        String result=null;
        if(op == null || signData == null) {
            return Result.newError("fail", "param missing op:  " + op + ", signdata: "+signData);
        }
        try {
            log.info("trace begin hongshi  headerMap:{},bodyMap:{}, signData:{} ",
                    headerMap,bodyMap, signData);
            if(op.equals("encrypt")) {
                result = encrypt(signData);
            } else if (op.equals("decrypt")) {
                result = decrypt(signData);
            }else if (op.equals("addSign")) {
                result=sign(signData);
            } else if (op.equals("testSign")) {
                result=checkSign(signData);
            } else {
                return Result.newError("fail", "not supported op: " + op);
            }
            log.info("trace end hongshi p7sign headerMap:{},bodyMap:{}, signData:{}, op:{}, result:{} ",
                    headerMap,bodyMap, signData, op, result);
            return Result.newSuccess(result);
        }catch (Exception e) {
            log.error("trace hongshi   headerMap:{}, bodyMap:{} op:{}, get exception, ",headerMap, bodyMap, op, e);
            return Result.newError("fail", e.getMessage());
        }
    }

    //账户交易明细查询:40T22
    private static String encrypt(String signData) throws NotSupportException, com.itrus.cryptorole.CryptoException {
        String pfxPath = "/opt/tomcat/webapps/ROOT/WEB-INF/lib/C030.pfx";
        String keypassword = "12345678";
        Sender sender = new SenderBcImpl();
        sender.initCertWithKey(pfxPath, keypassword.toCharArray());
        byte[] p7dtach;
        try {
            p7dtach = sender.signMessage(signData.getBytes("utf-8"));
            return com.itrus.util.Base64.encode(p7dtach);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }


    private static Map<String,String> format(String res){
        if(res==null){
            return null;
        }
        if(res.length()<1){
            return null;
        }
        Map<String,String> map=new HashMap<String,String>();
        String[] params=res.split("&");
        for(String param:params){
//			String[] kv=param.split("=");
//			if(kv.length==2){
//				map.put(kv[0], kv[1]);
//			}else if(kv.length==1){
//				map.put(kv[0], "");
//			}

            if( param.startsWith( "data" ) ) {
                if( param.length() > 5 )
                    map.put( "data", param.substring( 5 ) );
                else
                    map.put( "data", "" );
            }
            if( param.startsWith( "signature" ) ) {
                if( param.length() > 10 )
                    map.put( "signature", param.substring( 10 ) );
                else
                    map.put( "signature", "" );
            }
        }
        return map;
    }

    private static String decrypt(String content){
        Map<String,String> res_map= format(content);
        String resp=res_map.get("data");
        String respSignData=res_map.get("signature")+"";

        try {
            boolean validRsp = RSA.verify(resp, respSignData, "", "utf-8");
            log.info("trace hongshi decrypt, validRsp:{}, resp:{}", validRsp, resp);
            if(validRsp) {
                return resp;
            } else {
                return null;
            }
        }catch (Exception e) {
            log.error("trace hongshi decrypt, content:{}, exception, ",content, e);
            return null;
        }
    }

    /**
     * 报文加签
     * @param resData   报文
     */
    public static String sign(String resData)  {
        String signVal = "";

        try {
            byte[] bts = resData.getBytes("utf-8");

            PrivateKey priKey = KeyUtil.getPrivateKeyFromPFX(Constants.getSignCertPath(), Constants.getSignCertPwd());
            X509Cert platformCert = CertUtil.getCertFromPfx(Constants.getSignCertPath(), Constants.getSignCertPwd());

            SignatureUtil2 signUtil = new SignatureUtil2();
            byte[] signature = signUtil.p7SignMessageDetach(Mechanism_Inside.SHA1_RSA, bts, priKey, platformCert,
                    SESSION);

            signVal = new String(signature);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return signVal;
    }

    /**
     * 报文验签
     * @param signData  参数
     */
    public String checkSign(String signData)  {

        //弄出报文、签名
        Map<String, String> map = JSONObject.parseObject(signData, new TypeReference<Map<String, String>>() {});
        String baowen="";
        String qianming="";
        if (map.containsKey("baowen")){
            baowen=map.get("baowen");
        }
        if (map.containsKey("qianming")){
            qianming=map.get("qianming");
        }
        log.info("报文:"+baowen+"签名:"+qianming);
        Boolean success = true;
        log.info("VERIFY_SIGN--------------------------------CFCA证书验签，客户签名：" +signData);
        try {
            byte[] signBytes = baowen.getBytes("utf-8");
            byte[] signDataByte = qianming.getBytes("utf-8");

            SignatureUtil2 signUtil = new SignatureUtil2();
            boolean verifySignResult = signUtil.p7VerifyMessageDetach(signBytes, signDataByte, SESSION);
            log.info("验签结果:"+verifySignResult);
            if (verifySignResult) {// 验签成功
                log.info("VERIFY_SIGN--------------------------------CFCA证书验签通过");
                // 获得客户证书
                X509Cert userCert = signUtil.getSignerCert();
                log.info("VERIFY_SIGN--------------------------------CFCA证书信息：" + userCert.getSubject());
                // 证书有效期校验
                if (!X509CertValidator.verifyCertDate(userCert)) {
                    log.info("VERIFY_SIGN-------------------------------证书已过有效期，请联系运营人员更换");
                }
                // 证书授信校验
                String trustCertPath = Constants.getTrustCertPath();
                X509CertValidator.updateTrustCertsMap(trustCertPath);
                if (!X509CertValidator.validateCertSign(userCert)) {
                    log.info("VERIFY_SIGN-------------------------------证书颁发者不合法，请核实");
                }
                // 证书是否已吊销校验
                String clrPath = Constants.getCrlCertPath();
                if (!X509CertValidator.verifyCertByCRLOutLine(userCert, clrPath)) {
                    log.info("VERIFY_SIGN-------------------------------证书已吊销");
                }
                log.info("VERIFY_SIGN-------------------------------CFCA证书验证有效！！！！！！！！！！！！！！！！！！！！！！！！！！");
            } else {
                // 验签失败
                success=false;
                log.info("VERIFY_SIGN-------------------------------CFCA证书验签不通过！！！！！！！！！！！！！！！！！！！！！！！！！！");
            }
        } catch (Exception e) {
            success=false;
            log.info("VERIFY_SIGN-------------------------------报文验签失败??????????????????????????????????????");
            e.printStackTrace();
        }

        if (success){
            return i18NStringManager.getByEi(I18NStringEnum.s3654, null);
        }else{
            return i18NStringManager.getByEi(I18NStringEnum.s3655, null);
        }

    }

    static{
        try {

            JCrypto jCrypto = JCrypto.getInstance();
            jCrypto.initialize(JCrypto.JSOFT_LIB, null);
            SESSION = jCrypto.openSession(JCrypto.JSOFT_LIB);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String args[]) {
        try {
           // String ret = encrypt("{\"request_head\":{\"version\":\"1.0.0\",\"request_seq_no\":\"RC002210000202103031945118610001\",\"cust_no\":\"C00221\",\"cust_chnl\":\"0000\",\"request_date\":\"20210303\",\"request_time\":\"194511\",\"tran_code\":\"40T22\"},\"request_body\":{\"acct_no\":\"755915678310502\",\"acct_name\":\"企业网银新20161073\",\"beg_date\":\"20210205\",\"end_date\":\"20210305\",\"beg_num\":\"1\",\"query_num\":\"2\"}}");
            String str2 = "abc\ndef\nccc";
            String str3 = str2.replaceAll("\\n", "");
            System.out.println("ret2: " + str2);
            System.out.println("ret3: " + str3);
        }catch (Exception e) {
            System.out.println("get exception: " + e);
        }
//        //加签
//        String ss="123456";
//        System.out.println(sign(ss));
//        //验签
//        String str="{\"baowen\":\"123456\",\"qianming\":\"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\"}";
//        System.out.println(checkSign(str));

    }

}
