<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao">
    <resultMap id="objectMapping" type="com.fxiaoke.open.erpsyncdata.preprocess.model.ObjectMappingVo">
        <result column="source_object_api_name" property="sourceObjectApiName"/>
        <result column="dest_object_api_name" property="destObjectApiName"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        tenant_id,
        integration_stream_name,
        source_data_center_id,
        dest_data_center_id,
        sync_ploy_id,
        "status",
        source_tenant_type,
        source_tenant_ids,
        source_object_api_name,
        dest_tenant_type,
        dest_tenant_ids,
        dest_object_api_name,
        field_mappings,
        detail_object_mappings,
        sync_rules,
        sync_conditions,
        detail_object_sync_conditions,
        integration_stream_nodes,
        before_func_api_name,
        during_func_api_name,
        after_func_api_name,
        is_valid,
        create_time,
        update_time,
        remark
        <!--@sql from sync_ploy_detail-->
    </sql>
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        <!--@Table sync_ploy_detail-->
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="integration_stream_name" property="integrationStreamName"/>
        <result column="source_data_center_id" property="sourceDataCenterId"/>
        <result column="dest_data_center_id" property="destDataCenterId"/>
        <result column="sync_ploy_id" property="syncPloyId"/>
        <result column="status" property="status"/>
        <result column="source_tenant_type" property="sourceTenantType"/>
        <result column="source_tenant_ids" property="sourceTenantIds"/>
        <result column="source_object_api_name" property="sourceObjectApiName"/>
        <result column="dest_tenant_type" property="destTenantType"/>
        <result column="dest_tenant_ids" property="destTenantIds"/>
        <result column="dest_object_api_name" property="destObjectApiName"/>
        <result column="field_mappings" property="fieldMappings"/>
        <result column="detail_object_mappings" property="detailObjectMappings"/>
        <result column="sync_rules" property="syncRules"/>
        <result column="sync_conditions" property="syncConditions"/>
        <result column="detail_object_sync_conditions" property="detailObjectSyncConditions"/>
        <result column="integration_stream_nodes" property="integrationStreamNodes"/>
        <result column="before_func_api_name" property="beforeFuncApiName"/>
        <result column="during_func_api_name" property="duringFuncApiName"/>
        <result column="after_func_api_name" property="afterFuncApiName"/>
        <result column="is_valid" property="isValid"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <select id="listByTenantIdAndSourceObjs"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where tenant_id=#{tenantId}
        <if test="sourceObjs !=null">
            and source_object_api_name in
            <foreach collection="sourceObjs" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="listByTenantIdAndStatus"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where tenant_id=#{tenantId}
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="listByStatus"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select tenant_id,source_object_api_name,dest_object_api_name,detail_object_mappings,integration_stream_nodes
        from sync_ploy_detail
        where status = #{status}
        and tenant_id in
        <foreach item="item" index="index" collection="tenantIds"
                                          open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
    </foreach>
    </select>

    <select id="listBySource" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">


                    select *
                    from sync_ploy_detail
                    where tenant_id=#{sourceTenantId}
                     and status = #{status} and source_tenant_type = #{sourceTenantType} and source_object_api_name like CONCAT(#{sourceObjectApiName},'%')


    </select>

    <select id="listBySourceTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        <if test="searchText != null and searchText != ''">
            inner join sync_ploy
            on sync_ploy_detail.sync_ploy_id = sync_ploy.id
        </if>
        WHERE tenant_id=#{sourceTenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and dest_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        <if test="sourceObjectApiName != null">
            and source_object_api_name = #{sourceObjectApiName}
        </if>
        <if test="searchText != null and searchText != ''">
            and sync_ploy.name like CONCAT('%',#{searchText},'%')
        </if>
        order by source_object_api_name offset #{offset}
        limit #{limit}
    </select>

    <select id="listByDestTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        <if test="searchText != null and searchText != ''">
            inner join sync_ploy
            on sync_ploy_detail.sync_ploy_id = sync_ploy.id
        </if>
        where tenant_id=#{destTenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and source_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        <if test="destObjectApiName != null">
            and dest_object_api_name = #{destObjectApiName}
        </if>
        <if test="searchText != null and searchText != ''">
            and sync_ploy.name like CONCAT('%',#{searchText},'%')
        </if>
        order by dest_object_api_name offset #{offset}
        limit #{limit}
    </select>

    <select id="countBySourceTenantId" resultType="java.lang.Integer">
        select count(sync_ploy_detail.id)
        from sync_ploy_detail
        <if test="searchText != null and searchText != ''">
            inner join sync_ploy
            on sync_ploy_detail.sync_ploy_id = sync_ploy.id
        </if>
        where tenant_id=#{sourceTenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and dest_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        <if test="sourceObjectApiName != null">
            and source_object_api_name = #{sourceObjectApiName}
        </if>
        <if test="searchText != null and searchText != ''">
            and sync_ploy.name like CONCAT('%',#{searchText},'%')
        </if>
    </select>

    <select id="countByDestTenantId" resultType="java.lang.Integer">
        select count(sync_ploy_detail.id)
        from sync_ploy_detail
        <if test="searchText != null and searchText != ''">
            inner join sync_ploy
            on sync_ploy_detail.sync_ploy_id = sync_ploy.id
        </if>
        where tenant_id=#{destTenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and source_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        <if test="destObjectApiName != null">
            and dest_object_api_name = #{destObjectApiName}
        </if>
        <if test="searchText != null and searchText != ''">
            and sync_ploy.name like CONCAT('%',#{searchText},'%')
        </if>
    </select>

    <select id="countDistinctApiNamesBySourceTenantId" resultType="java.lang.Integer">
        select count(sync_ploy_detail.id)
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="sourceObjectApiName != null">
            and source_object_api_name = #{sourceObjectApiName}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and dest_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        and source_tenant_type=1
    </select>

    <select id="countDistinctApiNamesByDestTenantId" resultType="java.lang.Integer">
        select count(sync_ploy_detail.id)
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="destObjectApiName != null">
            and dest_object_api_name = #{destObjectApiName}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and source_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        and dest_tenant_type=1
    </select>


    <select id="listDistinctApiNamesBySourceTenantId" resultType="java.lang.String">
        select distinct(source_object_api_name)
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="sourceObjectApiName != null">
            and source_object_api_name = #{sourceObjectApiName}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and dest_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        and source_tenant_type=1
        order by source_object_api_name offset #{offset}
        limit #{limit}
    </select>

    <select id="listDistinctApiNamesByDestTenantId" resultType="java.lang.String">
        select distinct(dest_object_api_name)
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="destObjectApiName != null">
            and dest_object_api_name = #{destObjectApiName}
        </if>
        <if test="erpFakeObjApiNames !=null">
            and source_object_api_name in
            <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
                #{objApiName}
            </foreach>
        </if>
        and dest_tenant_type=1
        order by dest_object_api_name offset #{offset}
        limit #{limit}
    </select>

    <select id="listByPloyIdAndErpObjApiNames"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where tenant_id=#{tenantId}
        and sync_ploy_id = #{ployId}
        and (
        source_object_api_name in
        <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
            #{objApiName}
        </foreach>
        or
        dest_object_api_name in
        <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
            #{objApiName}
        </foreach>
        )
        order by id offset #{offset}
        limit #{limit}
    </select>

    <select id="countByPloyIdAndErpObjApiNames" resultType="java.lang.Integer">
        select count(id)
        from sync_ploy_detail
        where tenant_id=#{tenantId}
        and sync_ploy_id = #{ployId}
        and (
        source_object_api_name in
        <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
            #{objApiName}
        </foreach>
        or
        dest_object_api_name in
        <foreach collection="erpFakeObjApiNames" item="objApiName" index="index" open="(" close=")" separator=",">
            #{objApiName}
        </foreach>
        )
    </select>

    <select id="listBySourceTenantIdAndDestTenantId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">


                    select * from sync_ploy_detail where   tenant_id=#{sourceTenantId}


    </select>

    <select id="listBy" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where status = #{status}
        and source_tenant_type = #{sourceTenantType}
        <if test="sourceObjectApiName != null">
            and source_object_api_name = #{sourceObjectApiName}
        </if>
        limit 20000
    </select>

    <update id="updateStatusById">
        update sync_ploy_detail
        set "status"    = #{status},
            update_time = (SELECT EXTRACT(epoch FROM now()) * 1000)
        where id = #{id}
          and "status" = #{oldStatus}
    </update>

    <update id="updateByIdSelective"
            parameterType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        update sync_ploy_detail
        <set>
            <if test="syncPloyId != null">
                sync_ploy_id = #{syncPloyId},
            </if>
            <if test="status != null">
                "status" = #{status},
            </if>
            <if test="sourceTenantType != null">
                source_tenant_type = #{sourceTenantType},
            </if>
            <if test="sourceTenantIds != null">
                source_tenant_ids = #{sourceTenantIds},
            </if>
            <if test="destTenantType != null">
                dest_tenant_type = #{destTenantType},
            </if>
            <if test="destTenantId != null">
                dest_tenant_ids = #{destTenantIds},
            </if>
            <if test="sourceObjectApiName != null">
                source_object_api_name = #{sourceObjectApiName},
            </if>
            <if test="destObjectApiName != null">
                dest_object_api_name = #{destObjectApiName},
            </if>
            <if test="fieldMappings != null">
                field_mappings = #{fieldMappings},
            </if>
            <if test="integrationStreamNodes != null">
                integration_stream_nodes = #{integrationStreamNodes},
            </if>
            <if test="detailObjectMappings != null">
                detail_object_mappings = #{detailObjectMappings},
            </if>
            <if test="syncRules != null">
                sync_rules = #{syncRules},
            </if>
            <if test="syncConditions != null">
                sync_conditions = #{syncConditions},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="detailObjectSyncConditions != null">
                detail_object_sync_conditions = #{detailObjectSyncConditions},
            </if>
            <if test="beforeFuncApiName != null">
                before_func_api_name = #{beforeFuncApiName},
            </if>
            <if test="duringFuncApiName != null">
                during_func_api_name = #{duringFuncApiName},
            </if>
            <if test="afterFuncApiName != null">
                after_func_api_name = #{afterFuncApiName},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateValid">
        update sync_ploy_detail
        <set>
            is_valid = #{isValid}
        </set>
        where id = #{id}
    </update>

    <update id="updateFuncApiName">
        update sync_ploy_detail
        <set>
            <if test="customFuncType == 1">
                before_func_api_name = #{customFuncApiName},
            </if>
            <if test="customFuncType == 2">
                during_func_api_name = #{customFuncApiName},
            </if>
            <if test="customFuncType == 3">
                after_func_api_name = #{customFuncApiName},
            </if>
            update_time = #{updateTime},
        </set>
        where id = #{id}
    </update>

    <!--auto generated by MybatisCodeHelper on 2021-12-08-->
    <update id="updateSyncRulesById">


                    update sync_ploy_detail
                    set sync_rules=#{updatedSyncRules}
                    where id=#{id}


    </update>

    <select id="listBySourceType" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">


                        select *
                        from sync_ploy_detail
                        WHERE tenant_id=#{tenantId}
                        and source_tenant_type= #{sourceTenantType}


    </select>
    <select id="listByDcIdAndObjApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        and source_data_center_id= #{sourceDcId}
        and dest_data_center_id= #{destDcId}
        and source_object_api_name= #{sourceObjectApiName}
        and dest_object_api_name= #{destObjectApiName}
    </select>
    <select id="countBySourceOrDestDcIdAndObjApiName" resultType="java.lang.Integer">
        select count(sync_ploy_detail.id)
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="dcId != null">
            and (source_data_center_id= #{dcId} or dest_data_center_id= #{dcId})
        </if>
        <if test="objApiName != null">
            and (source_object_api_name= #{objApiName} or dest_object_api_name= #{objApiName})
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
        <if test="queryStr != null">
            and integration_stream_name like CONCAT('%',#{queryStr},'%')
        </if>
    </select>
    <select id="listBySourceOrDestDcIdAndObjApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="dcId != null">
            and (source_data_center_id= #{dcId} or dest_data_center_id= #{dcId})
        </if>
        <if test="objApiName != null">
            and (source_object_api_name= #{objApiName} or dest_object_api_name= #{objApiName})
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
        <if test="queryStr != null">
            and integration_stream_name like CONCAT('%',#{queryStr},'%')
        </if>
        order by update_time DESC offset #{offset}
        limit #{limit}
    </select>
    <select id="listByDestTenantTypeAndObjApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        and dest_tenant_type= #{destTenantType}
        and dest_object_api_name= #{destObjApiName}
    </select>
    <select id="listBySourceTenantTypeAndObjApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        and source_tenant_type= #{sourceTenantType}
        and source_object_api_name= #{sourceObjApiName}
    </select>
    <select id="listByTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
    </select>
    <select id="listSomeFieldByTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select tenant_id,source_data_center_id,dest_data_center_id,update_time
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
    </select>

    <select id="listStreamByDCIDAndObjApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        <if test="sourceDataCenterId != null">
            and (source_data_center_id= #{sourceDataCenterId})
        </if>
        <if test="destDataCenterId != null">
            and (dest_data_center_id= #{destDataCenterId})
        </if>
        <if test="sourceApiName != null">
            and (source_object_api_name= #{sourceApiName})
        </if>
        <if test="destApiName != null">
            and (dest_object_api_name= #{destApiName})
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
    </select>

    <update id="updateNameAndDcIdById">
          update sync_ploy_detail
          set tenant_id=#{tenantId}
        <if test="name != null">
            ,integration_stream_name=#{name}
        </if>
          ,source_data_center_id=#{sourceDcId}
          ,dest_data_center_id=#{destDcId}
          where id=#{id}
    </update>
    <update id="updateNameById">
        update sync_ploy_detail set integration_stream_name=#{name}
        where id=#{id} and tenant_id=#{tenantId}
    </update>

    <select id="findOne" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select * from sync_ploy_detail
        where tenant_id=#{tenantId}
        and source_object_api_name = #{sourceObjectApiName}
        and dest_object_api_name = #{destObjectApiName}
        limit 1;
    </select>
    <select id="listByTenantIdAndId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select * from sync_ploy_detail
        where tenant_id=#{tenantId}
        and id in
        <foreach collection="streamIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-03-17-->
    <insert id="insertList">
        INSERT INTO sync_ploy_detail(
        id,
        tenant_id,
        integration_stream_name,
        source_data_center_id,
        dest_data_center_id,
        sync_ploy_id,
        status,
        source_tenant_type,
        source_tenant_ids,
        source_object_api_name,
        dest_tenant_type,
        dest_tenant_ids,
        dest_object_api_name,
        field_mappings,
        detail_object_mappings,
        sync_rules,
        sync_conditions,
        detail_object_sync_conditions,
        integration_stream_nodes,
        before_func_api_name,
        during_func_api_name,
        after_func_api_name,
        is_valid,
        remark,
        create_time,
        update_time
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.tenantId},
            #{element.integrationStreamName},
            #{element.sourceDataCenterId},
            #{element.destDataCenterId},
            #{element.syncPloyId},
            #{element.status},
            #{element.sourceTenantType},
            #{element.sourceTenantIds},
            #{element.sourceObjectApiName},
            #{element.destTenantType},
            #{element.destTenantIds},
            #{element.destObjectApiName},
            #{element.fieldMappings},
            #{element.detailObjectMappings},
            #{element.syncRules},
            #{element.syncConditions},
            #{element.detailObjectSyncConditions},
            #{element.integrationStreamNodes},
            #{element.beforeFuncApiName},
            #{element.duringFuncApiName},
            #{element.afterFuncApiName},
            #{element.isValid},
            #{element.remark},
            #{element.createTime},
            #{element.updateTime}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2022-03-17-->
    <delete id="deleteByTenantId">
        delete from sync_ploy_detail
        where tenant_id=#{tenantId}
    </delete>
    <delete id="deleteByTenantAndId">
        delete
        from sync_ploy_detail
        where tenant_id = #{tenantId}
          and id = #{id}
    </delete>

    <!--auto generated by MybatisCodeHelper on 2022-10-21-->
    <select id="listObjMappingByTenantId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select source_object_api_name, dest_object_api_name,detail_object_mappings
        from sync_ploy_detail
        where tenant_id=#{tenantId}
    </select>
    <select id="listBySourceTenantTypeAndObjApiNameList"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
         select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
        and source_tenant_type= #{sourceTenantType}
        and source_object_api_name in
        <foreach item="item" index="index" collection="sourceObjApiNameList"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="countByTenantId" resultType="java.lang.Integer">
        select count(*)
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
    </select>
    <select id="countEnableByTenantId" resultType="java.lang.Integer">
        select count(*)
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and status = 1;
    </select>
    <select id="countEnableByDcId" resultType="java.lang.Integer">
        select count(*)
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and (source_data_center_id= #{dcId} or dest_data_center_id= #{dcId})
          and status = 1;
    </select>

    <select id="countByDcId" resultType="java.lang.Integer">
        select count(*)
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and (source_data_center_id= #{dcId} or dest_data_center_id= #{dcId});
    </select>

    <select id="queryListEnableByDcIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        SELECT *
        FROM sync_ploy_detail
        WHERE tenant_id = #{tenantId,jdbcType=VARCHAR}
        <if test="dcIds != null and dcIds.size()>0">
            AND (
            <foreach collection="dcIds" item="dcId" index="index" separator=" OR " open="(" close=")">
                source_data_center_id = #{dcId}
                OR dest_data_center_id = #{dcId}
            </foreach>
            )
        </if>
        AND status = 1;
    </select>

    <select id="queryByDcId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and (source_data_center_id = #{dcId} or dest_data_center_id = #{dcId})
        <if test="status != null">
            and status= #{status}
        </if>
    </select>

    <delete id="deleteByTenantAndDcId">
        delete
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and (source_data_center_id = #{dcId} or dest_data_center_id = #{dcId})
    </delete>

<!--auto generated by MybatisCodeHelper on 2023-04-04-->
    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sync_ploy_detail
        where id=#{id} and tenant_id=#{tenantId}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-04-04-->
    <select id="countByErpDcIdAndCrmApiName" resultType="java.lang.Integer">
        select count(1)
        from sync_ploy_detail
        where tenant_id = #{tenantId}
          and ((source_data_center_id = #{erpDcId} and dest_object_api_name = #{crmObjApiName})
            or (dest_data_center_id = #{erpDcId} and source_object_api_name = #{crmObjApiName}))
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-08-->
    <select id="queryTenantIdByDestCRMObjectApiName" resultType="java.lang.String">
        select distinct tenant_id
        from sync_ploy_detail
        where dest_object_api_name=#{destObjectApiName}
        and dest_tenant_type = 1;
    </select>


    <select id="queryTenantIdBySourceCRMObjectApiName" resultType="java.lang.String">
        select distinct tenant_id
        from sync_ploy_detail
        where source_object_api_name=#{sourceObjectApiName}
          and source_tenant_type = 1;
    </select>

    <select id="listByIds" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-01-12-->
    <resultMap id="StreamSimpleMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo">
        <id column="id" property="streamId"/>
        <result column="tenantId" property="tenantId"/>
        <result column="status" property="status"/>
        <result column="erpDcId" property="erpDcId"/>
        <result column="crmObjApiName" property="crmObjApiName"/>
        <result column="erpObjApiName" property="erpObjApiName"/>
        <result column="sourceTenantType" property="sourceTenantType"/>
        <result column="integrationStreamName" property="integrationStreamName"/>
        <result column="updateTime" property="streamLastUpdateTime"/>
    </resultMap>
    <select id="list100SimpleByIdAfter" resultMap="StreamSimpleMap">
        select id,
               tenant_id          as tenantId,
               status,
               source_tenant_type as sourceTenantType,
               case
                   when source_tenant_type = 2 then source_data_center_id
                   else dest_data_center_id
                   end            as erpDcId,
               case
                   when source_tenant_type = 2 then source_object_api_name
                   else dest_object_api_name
                   end            as erpObjApiName,
               case
                   when source_tenant_type = 1 then source_object_api_name
                   else dest_object_api_name
                   end            as crmObjApiName,
               integration_stream_name as integrationStreamName,
               update_time        as updateTime
        from sync_ploy_detail
        <where>
            <if test="minId != null">
                and id <![CDATA[>]]> #{minId}
            </if>
        </where>
        order by id
        limit 100
    </select>
    <select id="findCrmMasterObjFuncUsagePloy"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where ((
        source_object_api_name = #{objApiName} and
        source_tenant_type = 1 and
        (before_func_api_name is not null and before_func_api_name != '')
        )
        or (
        dest_object_api_name = #{objApiName} and
        source_tenant_type = 2 and
        ((after_func_api_name is not null and after_func_api_name != '') or
        (during_func_api_name is not null and during_func_api_name != ''))
        ))
        <if test="tenantIds!= null">
            and tenant_id in
            <foreach collection="tenantIds" item="tenantId" index="index" open="(" close=")" separator=",">
                #{tenantId}
            </foreach>
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
        order by id
        offset #{offset}
        limit #{limit};
    </select>
    <select id="findErpMasterObjFuncUsagePloy"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where ((
        source_object_api_name like CONCAT( #{objApiName}, '%') and
        source_tenant_type = 2 and
        (before_func_api_name is not null and before_func_api_name != '')
        )
        or (
        dest_object_api_name like CONCAT( #{objApiName}, '%') and
        source_tenant_type = 1 and
        ((after_func_api_name is not null and after_func_api_name != '') or
        (during_func_api_name is not null and during_func_api_name != ''))
        ))
        <if test="tenantIds!= null">
            and tenant_id in
            <foreach collection="tenantIds" item="tenantId" index="index" open="(" close=")" separator=",">
                #{tenantId}
            </foreach>
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
        order by id
        offset #{offset}
        limit #{limit};
    </select>
    <select id="findErpDetailObjFuncUsagePloy"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where ((
        text(detail_object_mappings) like CONCAT( '%"sourceObjectApiName": "', CONCAT( #{objApiName}, '%')) and
        source_tenant_type = 2 and
        (before_func_api_name is not null and before_func_api_name != '')
        )
        or (
        text(detail_object_mappings) like CONCAT( '%"destObjectApiName": "', CONCAT( #{objApiName}, '%')) and
        source_tenant_type = 1 and
        ((after_func_api_name is not null and after_func_api_name != '') or
        (during_func_api_name is not null and during_func_api_name != ''))
        ))
        <if test="tenantIds!= null">
            and tenant_id in
            <foreach collection="tenantIds" item="tenantId" index="index" open="(" close=")" separator=",">
                #{tenantId}
            </foreach>
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
        order by id
        offset #{offset}
        limit #{limit};
    </select>
    <select id="findCrmDetailObjFuncUsagePloy"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where ((
        jsonb_path_match(detail_object_mappings, '$[*].sourceObjectApiName == $obj', jsonb_build_object('obj',
        #{objApiName})) and
        source_tenant_type = 1 and
        (before_func_api_name is not null and before_func_api_name != '')
        )
        or (
        jsonb_path_match(detail_object_mappings, '$[*].destObjectApiName == $obj', jsonb_build_object('obj',
        #{objApiName})) and
        source_tenant_type = 2 and
        ((after_func_api_name is not null and after_func_api_name != '') or
        (during_func_api_name is not null and during_func_api_name != ''))
        ))
        <if test="tenantIds!= null">
            and tenant_id in
            <foreach collection="tenantIds" item="tenantId" index="index" open="(" close=")" separator=",">
                #{tenantId}
            </foreach>
        </if>
        <if test="status != null">
            and status= #{status}
        </if>
        order by id
        offset #{offset}
        limit #{limit};
    </select>
    <select id="queryByDcIdByCrmObjApiNames"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and (source_data_center_id = #{dcId} or dest_data_center_id = #{dcId})
        <if test="crmObjApinames!= null and crmObjApinames.size()>0">
        and
            <foreach collection="crmObjApinames" item="crmObjApiname" index="index" open="(" close=")" separator="OR">
                 (source_object_api_name = #{crmObjApiname} or dest_object_api_name = #{crmObjApiname})
            </foreach>
        </if>
        <if test="status != null">
            and status= #{status}
        </if>

    </select>

    <select id="findSyncPloyDetailByStartWithName"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
        and (source_data_center_id = #{dcId} or dest_data_center_id = #{dcId})
        and integration_stream_name like #{integrationStreamName} || '%'
    </select>

<!--auto generated by MybatisCodeHelper on 2024-08-23-->
    <select id="listIdBySource" resultType="java.lang.String">
        select id
        from sync_ploy_detail
        where tenant_id=#{tenantId}
        and source_data_center_id=#{sourceDcId} and
        source_object_api_name=#{sourceObjectApiName}
        <if test="status != null">
            and status= #{status}
        </if>
    </select>

    <select id="listIdByCond" resultType="java.lang.String">
        select id
        from sync_ploy_detail
        where tenant_id = #{tenantId}
        <if test="erpDcId != null and erpDcId != '' != null">
            and (
                (source_data_center_id = #{erpDcId} and source_tenant_type = 2)
                    or
                (dest_data_center_id = #{erpDcId} and source_tenant_type = 1)
                )
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="listTenantIdList" resultType="java.lang.String">
        select distinct tenant_id
        from sync_ploy_detail
        <if test="minEi != null">
            where
            tenant_id > #{minEi}
        </if>
        order by tenant_id
        limit #{limit};
    </select>
    <select id="listBySourceApiName" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity">
        select *
        from sync_ploy_detail
        WHERE tenant_id=#{tenantId}
          and source_object_api_name= #{sourceObjApiName}
    </select>
    
</mapper>