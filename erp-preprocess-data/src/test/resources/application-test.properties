# ????????

# ?????
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2?????
spring.h2.console.enabled=true

# MyBatis??
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.fxiaoke.open.erpsyncdata.dbproxy.entity

# Redis??????????????
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=0

# MongoDB????????
spring.data.mongodb.host=localhost
spring.data.mongodb.port=27017
spring.data.mongodb.database=test_erp_sync

# ????
logging.level.com.fxiaoke.open.erpsyncdata=DEBUG
logging.level.org.springframework=INFO
logging.level.org.mybatis=DEBUG

# ????????
erpdss.skip.super.auth=true
