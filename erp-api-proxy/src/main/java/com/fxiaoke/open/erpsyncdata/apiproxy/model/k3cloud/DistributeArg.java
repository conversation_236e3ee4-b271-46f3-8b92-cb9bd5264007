package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Getter
@Setter
public class DistributeArg {
    /**
     * PkIds：必须,被分配的单据主键Id集合.
     * TOrgIds：必须，目标组织Id集合
     * IsAutoSubmitAndAudit: 分配后是否自动提交与审核（非必录）(默认不提交与审核)。
     */
    @SerializedName("PkIds")
    @JsonProperty("PkIds")
    private String dataIds;
    @SerializedName("TOrgIds")
    @JsonProperty("TOrgIds")
    private String organizationIds;
    @SerializedName("IsAutoSubmitAndAudit")
    @JsonProperty("IsAutoSubmitAndAudit")
    private boolean autoSubmit = true;
}
