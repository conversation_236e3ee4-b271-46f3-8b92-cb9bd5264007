package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant.PROBE_TEMP_DATA_SET_REDIS_KEY;

/**
 * <AUTHOR>
 * @Date: 15:26 2022/9/13
 * @Desc:
 */
@Component
@Slf4j
public class RedisDataSourceManager {
    @Autowired
    private RedisDataSource redisDataSource;

    public void addTriggerPollingTempDataObj(String tenantId, Set<String> objList, String redisVisitoerName){
        if(CollectionUtils.isEmpty(objList)){
            return;
        }
        String queueRedisKey = String.format(PROBE_TEMP_DATA_SET_REDIS_KEY, tenantId);
        String[] objApiNames=objList.toArray(new String[objList.size()]);
        redisDataSource.get(redisVisitoerName).sadd(queueRedisKey,objApiNames);
    }
    public Set<String> getTriggerPollingTempDataObj(String tenantId, String redisVisitoerName){
        String queueRedisKey = String.format(PROBE_TEMP_DATA_SET_REDIS_KEY, tenantId);
        Set<String> result= Sets.newHashSet();
        for(int i=0;i<30;i++){
            //低版本不支持spop(key,100L);
            String obj = redisDataSource.get(redisVisitoerName).spop(queueRedisKey);
            if(StringUtils.isBlank(obj)){
                break;
            }
            result.add(obj);
        }
        return result;
}
}
