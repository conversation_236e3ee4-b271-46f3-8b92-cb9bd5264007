package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.paas.pod.mybatis.MyBatisRoutePolicy;
import com.fxiaoke.open.erpsyncdata.admin.arg.CopyBetweenDbArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.CopyLog2ClickHouseArg;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.converter.manager.SyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.dao.CHSyncLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHInterfaceMonitorEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncLogEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHInterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpReSyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.InterfaceMonitorDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncDataMongoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.SyncLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.table.dao.ErpTableDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.mybatis.tenant.TenantContext;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 10:28 2021/11/30
 * @Desc:
 */
@Service
@Slf4j
@Data
public class SuperAdminBrushDataServiceImpl implements SuperAdminBrushDataService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private SyncDataManager syncDataManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private MigrateTableManager migrateTableManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpFieldExtendDao erpFieldExtendDao;
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    @Autowired
    private ErpTenantConfigurationDao erpTenantConfigurationDao;
    @Autowired
    private MyBatisRoutePolicy myBatisRoutePolicy;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ErpReSyncDataMongoDao erpReSyncDataMongoDao;
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private ErpTableDao tableDao;
    @Autowired
    private CHSyncLogDao clickHouseSyncLogDao;
    @Autowired
    private SyncLogDao syncLogDao;
    @Autowired
    private SyncDataMongoDao syncDataMongoDao;
    @Autowired
    private InterfaceMonitorDataDao interfaceMonitorDataDao;
    @Autowired
    private CHInterfaceMonitorManager chInterfaceMonitorManager;
    @Autowired
    private CHSyncLogManager chSyncLogManager;
    @Autowired
    private CHSyncDataManager chSyncDataManager;
    @Autowired
    private TenantInfoManager tenantInfoManager;

    private ThreadPoolExecutor executor = new ThreadPoolExecutor(60, 60, 600L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(8000),
            new NamedThreadFactory("data-to-clickhouse-", false),
            new BlockPolicy());

    @Override
    public Result<String> brushTableData(List<String> sourceTenantId, String destDataBaseTenantId) {
        StringBuilder stringBuilder = new StringBuilder();
        //增加双删除配置
        updateOrCreatedNeedBidirectionalWriting(sourceTenantId, destDataBaseTenantId);
        createUpdateIndex(sourceTenantId, stringBuilder);
        for (String tenantId : sourceTenantId) {
            try {
                stringBuilder.append("tenantId=").append(tenantId).append("开始时间：").append(new Date()).append(System.lineSeparator());   // ignoreI18n   实施和开发自用
                ErpTenantConfigurationEntity entity = getOrCreatedConfig(destDataBaseTenantId, stringBuilder, tenantId);
                Map<String, Object> config = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
                if (config.containsKey("isAllFinish") && (Boolean) config.get("isAllFinish")) {
                    stringBuilder.append("isAllFinish=true,直接跳过").append(System.lineSeparator());   // ignoreI18n   实施和开发自用
                    continue;
                }
                Boolean firstFinish = (Boolean) config.get("firstFinish");
                if (!firstFinish) {
                    brushSyncDataMappingsTableData(tenantId, destDataBaseTenantId, stringBuilder, entity);
                }
                config = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
                firstFinish = (Boolean) config.get("firstFinish");
                if (firstFinish) {//第一次已完成，开始校验时间
                    boolean result1 = brushSyncDataMappingsTableDataByTime(tenantId, tenantId, destDataBaseTenantId, stringBuilder, entity, true);
                    if (result1) {
                        Map<String, List<String>> configRoute = tenantConfigurationManager.getConfigRouteTenant();
                        String oldTenantRoute = null;
                        Boolean isChangeRoute = false;
                        for (String resourceId : configRoute.keySet()) {
                            List<String> tenantIds = configRoute.get(resourceId);
                            if (tenantIds != null && tenantIds.contains(tenantId)) {
                                oldTenantRoute = tenantIds.get(0);
                            }
                        }
                        for (String resourceId : configRoute.keySet()) {
                            if (configRoute.get(resourceId) != null && configRoute.get(resourceId).contains(destDataBaseTenantId)) {
                                String msg1 = migrateTableManager.initDestDataBaseTenantTable(destDataBaseTenantId, tenantId, false);
                                stringBuilder.append("initDestDataBaseTenantTable index ").append(msg1).append(System.lineSeparator());
                                brushSyncDataMappingsTableDataByTime(oldTenantRoute, tenantId, destDataBaseTenantId, stringBuilder, entity, false);
                                configRouteManager.configRoute(tenantId, resourceId);//创建索引后改路由
                                isChangeRoute = true;
                                if (oldTenantRoute != null) {//路由之后，再次检查是否还有更新数据，如果有，也直接覆盖（存在很小可能，路由之后被更新了，然后又被旧的覆盖了）
                                    brushSyncDataMappingsTableDataByTime(oldTenantRoute, tenantId, destDataBaseTenantId, stringBuilder, entity, true);
                                }
                                removeNeedBidirectionalWriting(tenantId);
                                log.info("configRoute tenantId={},resourceId={}", tenantId, resourceId);
                                break;
                            }
                        }
                        stringBuilder.append(System.lineSeparator()).append("结束时间：").append(new Date());   // ignoreI18n   实施和开发自用
                        config = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
                        config.put("isChangeRoute", isChangeRoute);
                        config.put("isAllFinish", true);
                        config.put("result", stringBuilder.toString());
                        entity.setConfiguration(JacksonUtil.toJson(config));
                        entity.setUpdateTime(System.currentTimeMillis());
                        erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(entity);
                    }
                    log.info("brushTableData result={},result1={}", stringBuilder.toString(), result1);
                }
            } catch (Exception e) {
                stringBuilder.append("Exception").append(e.getMessage());
            }
        }
        return Result.newSuccess(stringBuilder.toString());
    }

    private ErpTenantConfigurationEntity updateOrCreatedNeedBidirectionalWriting(List<String> sourceTenantId, String destDataBaseTenantId) {
        ErpTenantConfigurationEntity entity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, TenantConfigurationTypeEnum.NeedBidirectionalWriting.name());
        if (entity == null) {
            Map<String, Map<String, Object>> eis = Maps.newHashMap();
            for (String tenantId : sourceTenantId) {
                eis.put(tenantId, Maps.newHashMap());
                eis.get(tenantId).put("destRoute", destDataBaseTenantId);
            }
            entity = ErpTenantConfigurationEntity.builder()
                    .id(idGenerator.get())
                    .tenantId(CommonConstant.configUniformIdentifier)
                    .dataCenterId(CommonConstant.configUniformIdentifier)
                    .channel(CommonConstant.configUniformIdentifier)
                    .type(TenantConfigurationTypeEnum.NeedBidirectionalWriting.name())
                    .configuration(JacksonUtil.toJson(eis))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).insert(entity);
            entity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, TenantConfigurationTypeEnum.NeedBidirectionalWriting.name());
        } else {
            if (StringUtils.isBlank(entity.getConfiguration())) {
                Map<String, Map<String, Object>> eis = Maps.newHashMap();
                for (String tenantId : sourceTenantId) {
                    eis.put(tenantId, Maps.newHashMap());
                    eis.get(tenantId).put("destRoute", destDataBaseTenantId);
                }
                entity.setConfiguration(JacksonUtil.toJson(eis));
            } else {
                Map<String, Map<String, Object>> result = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
                for (String tenantId : sourceTenantId) {
                    if (!result.keySet().contains(tenantId)) {
                        result.put(tenantId, Maps.newHashMap());
                        result.get(tenantId).put("destRoute", destDataBaseTenantId);
                    }
                }
                entity.setConfiguration(JacksonUtil.toJson(result));
            }
            erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(entity);
            entity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, TenantConfigurationTypeEnum.NeedBidirectionalWriting.name());
        }
        return entity;
    }

    private void removeNeedBidirectionalWriting(String tenantId) {
        ErpTenantConfigurationEntity entity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, CommonConstant.configUniformIdentifier, TenantConfigurationTypeEnum.NeedBidirectionalWriting.name());
        if (entity != null && StringUtils.isNotBlank(entity.getConfiguration())) {
            Map<String, Map<String, Object>> eis = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
            if (eis.containsKey(tenantId)) {
                eis.remove(tenantId);
                entity.setConfiguration(JacksonUtil.toJson(eis));
                erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(entity);
            }
        }
    }

    private boolean brushSyncDataMappingsTableDataByTime(String oldTenantRoute, String sourceTenantId, String destDataBaseTenantId,
                                                         StringBuilder stringBuilder, ErpTenantConfigurationEntity entity, boolean needCount) {
        Long lastUpdateTime = null, now = System.currentTimeMillis();
        Map<String, Object> config = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
        if (config.containsKey("lastUpdateTime")) {
            lastUpdateTime = (Long) config.get("lastUpdateTime");
        } else {
            lastUpdateTime = (Long) config.get("firstTime");
        }
        int limit = 1000, count = 0;
        while (true) {
            List<SyncDataMappingsEntity> list = syncDataMappingsDao.setTenantId(oldTenantRoute).listOrderByUpdateTime(sourceTenantId, lastUpdateTime, limit);
            if (CollectionUtils.isEmpty(list)) {//查到空才结束
                break;
            }
            List<String> ids = list.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
            syncDataMappingsDao.setTenantId(destDataBaseTenantId).deleteByIds(sourceTenantId, ids);//先删除
            syncDataMappingsDao.setTenantId(destDataBaseTenantId).batchInsert(sourceTenantId, list);
            lastUpdateTime = list.get(list.size() - 1).getUpdateTime();
            count += list.size();
            log.info("update brushSyncDataMappingsTableData count={}", count);
            List<SyncDataMappingsEntity> list1 = syncDataMappingsDao.setTenantId(oldTenantRoute).listOrderByEqUpdateTime(sourceTenantId, lastUpdateTime);
            list1 = list1.stream().filter(item -> !ids.contains(item.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list1)) {
                List<List<SyncDataMappingsEntity>> split = ListUtil.split(list1, 100);
                for (List<SyncDataMappingsEntity> list2 : split) {
                    List<String> ids1 = list2.stream().map(SyncDataMappingsEntity::getId).collect(Collectors.toList());
                    syncDataMappingsDao.setTenantId(destDataBaseTenantId).deleteByIds(sourceTenantId, ids1);//先删除
                    syncDataMappingsDao.setTenantId(destDataBaseTenantId).batchInsert(sourceTenantId, list2);
                }
                count += list1.size();
                log.info("update brushSyncDataMappingsTableData eq updateTime count={}", count);
            }
        }
        if (count == 0) {
            config.put("lastUpdateTime", now);
        } else {
            config.put("lastUpdateTime", lastUpdateTime);
        }
        entity.setConfiguration(JacksonUtil.toJson(config));
        entity.setUpdateTime(System.currentTimeMillis());
        erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(entity);
        if (needCount) {
            Integer sourceSize = syncDataMappingsDao.setTenantId(oldTenantRoute).count(sourceTenantId);
            Integer destSize = syncDataMappingsDao.setTenantId(destDataBaseTenantId).count(sourceTenantId);
            stringBuilder.append("update check brushSyncDataMappingsTableData=").append("源数量：").append(sourceSize).append(",目标数量：").append(destSize).append(System.lineSeparator());   // ignoreI18n   实施和开发自用
            return sourceSize.equals(destSize);
        } else {
            return true;
        }

    }

    private ErpTenantConfigurationEntity getOrCreatedConfig(String destDataBaseTenantId, StringBuilder stringBuilder, String tenantId) throws Exception {
        ErpTenantConfigurationEntity entity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(tenantId, "0", "0", TenantConfigurationTypeEnum.brushTableDataConfig.name());
        if (entity == null) {
            String msg = migrateTableManager.initDestDataBaseTenantTable(destDataBaseTenantId, tenantId, true);
            stringBuilder.append("initDestDataBaseTenantTable ").append(msg);
            Map<String, Object> config = Maps.newHashMap();
            config.put("firstTime", System.currentTimeMillis());
            config.put("firstFinish", false);
            entity = ErpTenantConfigurationEntity.builder()
                    .id(idGenerator.get())
                    .tenantId(tenantId)
                    .dataCenterId("0")
                    .channel("0")
                    .type(TenantConfigurationTypeEnum.brushTableDataConfig.name())
                    .configuration(JacksonUtil.toJson(config))
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .build();
            erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).insert(entity);
            entity = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(tenantId, "0", "0", TenantConfigurationTypeEnum.brushTableDataConfig.name());
        }
        return entity;
    }

    private void createUpdateIndex(List<String> sourceTenantId, StringBuilder stringBuilder) {
        List<String> createdIndexFailed = Lists.newArrayList();
        for (String ei : sourceTenantId) {
            try {
                String sql = "CREATE index concurrently if not exists \"mappings_time_{tenantId}\" ON sync_data_mappings_{tenantId} (update_time);".replaceAll("\\{tenantId}", ei);
                tableDao.setTenantId(ei).superQuerySql(sql, ei);
            } catch (Exception e) {
                createdIndexFailed.add(ei);
                log.error("CREATE index e={}", e);
            }
        }
        stringBuilder.append("创建索引失败：").append(createdIndexFailed).append(System.lineSeparator());   // ignoreI18n   实施和开发自用
    }

    @Override
    public Result<String> brushDispatcherTime(List<String> sourceTenantIds, Integer time, Boolean cancel) {
        StringBuilder stringBuilder = new StringBuilder();
        if (cancel != null && cancel) {//删除
            for (String tenantId : sourceTenantIds) {
                ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne(tenantId, "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.toString());
                configurationEntity.setConfiguration("{}");
                tenantConfigurationManager.updateById(tenantId, configurationEntity);
            }
        } else {
            for (String tenantId : sourceTenantIds) {
                Map<String, Integer> config = Maps.newHashMap();
                List<SyncPloyDetailEntity> entities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
                for (SyncPloyDetailEntity entity : entities) {
                    config.put(entity.getSourceObjectApiName(), time);
                    if (CollectionUtils.isNotEmpty(entity.getDetailObjectMappings())) {
                        for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                            config.put(detailObjectMappingData.getSourceObjectApiName(), time);
                        }
                    }
                }
                ErpTenantConfigurationEntity configurationEntity = tenantConfigurationManager.findOne(tenantId, "0", ErpChannelEnum.ALL.name(), TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.toString());
                if (configurationEntity != null) {
                    stringBuilder.append(tenantId).append("   id").append(configurationEntity.getId()).append("已存在配置：").append(configurationEntity.getConfiguration());   // ignoreI18n   实施和开发自用
                    configurationEntity.setConfiguration(JSONObject.toJSONString(config));
                    tenantConfigurationManager.updateById(tenantId, configurationEntity);
                } else {
                    ErpTenantConfigurationEntity configuration = new ErpTenantConfigurationEntity();
                    configuration.setChannel(ErpChannelEnum.ALL.name());
                    configuration.setDataCenterId("0");
                    configuration.setTenantId(tenantId);
                    configuration.setType(TenantConfigurationTypeEnum.MONGO_DISPATCHER_DATA_AGGREGATION_TIME.name());
                    configuration.setId(idGenerator.get());
                    configuration.setConfiguration(JSONObject.toJSONString(config));
                    configuration.setCreateTime(System.currentTimeMillis());
                    configuration.setUpdateTime(System.currentTimeMillis());
                    int insert = tenantConfigurationManager.insert(tenantId, configuration);
                }
            }
        }
        return Result.newSuccess(stringBuilder.toString());
    }

    @Override
    public Result<String> copyBetweenDb(CopyBetweenDbArg arg) {
        if (StringUtils.isBlank(arg.getSourceDbRoute()) || StringUtils.isBlank(arg.getDestDbRoute()) || StringUtils.isBlank(arg.getTenantId())) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        StringBuilder msgBuilder = new StringBuilder();
        for (String tableName : arg.getTableNames()) {
            String msg = copyBetweenDbByTableName(arg.getDeleteDest(), arg.getSourceDbRoute(), arg.getDestDbRoute(), arg.getTenantId(), tableName);
            msgBuilder.append(tableName).append(msg).append("。完成").append(System.lineSeparator());   // ignoreI18n   实施和开发自用
        }
        return Result.newSuccess(msgBuilder.toString());
    }

    @Override
    @LogLevel(LogLevelEnum.DEBUG)
    public Result<Map<String, Set<String>>> getTenantIdRoute(Boolean onlySandBox, String destRoute) {
        Map<String, Set<String>> map = Maps.newHashMap();
        List<String> allTenantIds = Lists.newArrayList();
        Set<String> markDeletedTenantIds = Sets.newHashSet();
        Set<String> notIntEis = Sets.newHashSet();
        Map<String, String> tenantTableMap = new LinkedHashMap<>();
        if (StringUtils.isNotBlank(destRoute)) {
            String tablePrefix = "sync_data_mappings_";
            //所有mapping表
            List<String> allMappings = tableDao.setTenantId(destRoute).listAllTableLeftMatching(tablePrefix);
            for (String table : allMappings) {
                if (table.startsWith("deleted")) {
                    markDeletedTenantIds.add(table);
                }
                String tenantId = StrUtil.removePrefix(table, tablePrefix);
                if (NumberUtil.isInteger(tenantId)) {
                    tenantTableMap.put(tenantId, table);
                } else {
                    notIntEis.add(table);
                }
            }
            log.info("markDeletedTenantIds={}", markDeletedTenantIds);
            log.info("notIntEis={}", notIntEis);
            allTenantIds.addAll(tenantTableMap.keySet());
        } else {
            List<String> eis = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
            allTenantIds.addAll(eis);
        }
        if (onlySandBox != null && onlySandBox) {
            List<String> tenantIds = Lists.newArrayList();
            List<Integer> collect = allTenantIds.stream().map(tenantId -> Integer.valueOf(tenantId)).collect(Collectors.toList());
            Map<Integer, String> ei2ea = eieaConverter.enterpriseIdToAccount(collect);
            for (Integer ei : ei2ea.keySet()) {
                if (ei2ea.get(ei) != null && ei2ea.get(ei).endsWith("_sandbox")) {
                    tenantIds.add(ei.toString());
                }
            }
            allTenantIds = tenantIds;
        }
        map.put("markDeletedTenantIds", markDeletedTenantIds);
        map.put("notIntEis", notIntEis);
        for (String tenantId : allTenantIds) {
            try {
                TenantContext tenantContext = myBatisRoutePolicy.get(tenantId, false);
                if (tenantContext != null) {
                    String url = tenantContext.getUrl();
                    if (map.containsKey(url)) {
                        map.get(url).add(tenantId);
                    } else {
                        map.put(url, Sets.newHashSet(tenantId));
                    }
                } else {
                    if (map.containsKey("null")) {
                        map.get("null").add(tenantId);
                    } else {
                        map.put("null", Sets.newHashSet(tenantId));
                    }
                }
            } catch (Exception e) {
                log.info("myBatisRoutePolicy.get Exception={}", e);
                if (map.containsKey("Exception")) {
                    map.get("Exception").add(tenantId);
                } else {
                    map.put("Exception", Sets.newHashSet(tenantId));
                }
            }

        }
        log.info("getTenantIdRoute resultMap={}", JacksonUtil.toJson(map));
        if (!tenantTableMap.isEmpty()) {
            Map<String, Object> ei2TotalSize = Maps.newHashMap();
            String baseSql = "select * from v_get_table_stats where relname='sync_data_mappings_{tenantId}';";
            TenantContext tenantContext = myBatisRoutePolicy.get(destRoute, false);
            for (String key : map.keySet()) {
                if (key.equals(tenantContext.getUrl())) {
                    continue;
                }
                Set<String> tenantIds = map.get(key);
                for (String tenantId : tenantIds) {
                    String sql = baseSql.replaceAll("\\{tenantId}", tenantId);
                    List<Map<String, Object>> result = tableDao.setTenantId(destRoute).superQuerySql(sql, destRoute);
                    if (CollectionUtils.isNotEmpty(result) && result.get(0) != null) {
                        Object totalSize = result.get(0).get("total_size");
                        ei2TotalSize.put(tenantId, totalSize);
                    }
                }
                log.info("dbKey={},total_size={}", key, JacksonUtil.toJson(ei2TotalSize));
            }
        }
        return Result.newSuccess(map);
    }

    @Override
    public Result<String> copyLog2ClickHouse(CopyLog2ClickHouseArg arg) {
        Set<String> eis = Sets.newHashSet();
        if (StringUtils.isBlank(arg.getTenantIdStr())) {
            return Result.newError("tenantIdStr can not be null");
        } else {
            eis = ImmutableSet.copyOf(Splitter.on(",").splitToList(arg.getTenantIdStr()));
        }

        Set<String> tenantIds=Sets.newHashSet();
        for(String ei:eis){
            if(tenantIds.contains(ei)) {
                continue;
            }
            Set<String> cols=syncLogDao.getAllCollection(ei);
            for(String col:cols){
                if(col.startsWith("interface_monitor_")){
                    tenantIds.add(col.replace("interface_monitor_",""));
                }else if(col.startsWith("sync_data_")){
                    tenantIds.add(col.replace("sync_data_",""));
                }else if(col.startsWith("sync_log_")){
                    tenantIds.add(col.replace("sync_log_",""));
                }
            }
        }

        eis=eis.stream().filter(tenantIds::contains).collect(Collectors.toSet());
        for (String tenantId : eis) {
            executor.submit(() -> {
                batchInsert(tenantId);
            });
        }
        return Result.newSuccess();
    }

    private void batchInsert(String tenantId) {
        batchSyncDataInsert(tenantId);
        batchSyncLogInsert(tenantId);
        batchInterfaceMonitorInsert(tenantId);
        log.info("batchInsert finish ei={}",tenantId);
    }

    public void batchSyncLogInsert(String tenantId) {
        try {
            Long expireTime=(System.currentTimeMillis()-(tenantInfoManager.getExpireIntervalTimeByEi(tenantId)+1000*60*60*24L))/1000;//多24个小时
            String lastId=null;
            while(true){
                List<SyncLog> syncLogs = syncLogDao.listSyncLogLimit1000(tenantId, lastId);
                if(CollectionUtils.isEmpty(syncLogs)){
                    break;
                }
                lastId=syncLogs.get(syncLogs.size()-1).getId().toString();
                List<CHSyncLogEntity> chSyncLogEntities = chSyncLogManager.buildCHSyncLogFromSyncLog(tenantId, syncLogs);
                chSyncLogManager.sendBizLog(chSyncLogEntities);
                if(syncLogs.size()<1000){
                    break;
                }
                if(syncLogs.get(0).getId().getTimestamp()<expireTime){
                    break;
                }
            }
        } catch (Exception e) {
            log.info("batchSyncLogInsert tenantId={} Exception={}", tenantId,e);
        }



    }

    public void batchSyncDataInsert(String tenantId) {
        try {
            Long expireTime=(System.currentTimeMillis()-(tenantInfoManager.getExpireIntervalTimeByEi(tenantId)+1000*60*60*24L))/1000;//多24个小时
            String lastId=null;
            while(true){
                List<SyncDataEntity> syncDataEntities = syncDataMongoDao.listSyncDataLimit1000(tenantId, lastId);
                if(CollectionUtils.isEmpty(syncDataEntities)){
                    break;
                }
                lastId=syncDataEntities.get(syncDataEntities.size()-1).getId();
                List<CHSyncDataEntity> chSyncDataEntities = chSyncDataManager.buildCHSyncDataFromSyncDataList(syncDataEntities);
                chSyncDataManager.sendBizLog(chSyncDataEntities);
                if(syncDataEntities.size()<1000){
                    break;
                }
                if(new ObjectId(lastId).getTimestamp()<expireTime){
                    break;
                }
            }
        } catch (Exception e) {
            log.info("batchSyncLogInsert tenantId={} Exception={}", tenantId,e);
        }

    }

    public void batchInterfaceMonitorInsert(String tenantId) {
        try {
            Long expireTime=(System.currentTimeMillis()-(tenantInfoManager.getExpireIntervalTimeByEi(tenantId)+1000*60*60*24L))/1000;//多24个小时
            String lastId=null;
            while(true){
                List<InterfaceMonitorData> interfaceMonitorData = interfaceMonitorDataDao.listInterfaceMonitorDataLimit1000(tenantId, lastId);
                if(CollectionUtils.isEmpty(interfaceMonitorData)){
                    break;
                }
                lastId=interfaceMonitorData.get(interfaceMonitorData.size()-1).getId().toString();
                List<CHInterfaceMonitorEntity> chInterfaceMonitorEntities = chInterfaceMonitorManager.buildCHInterfaceMonitorFromInterfaceMoniter(interfaceMonitorData);
                chInterfaceMonitorManager.sendBizLog(chInterfaceMonitorEntities);
                if(interfaceMonitorData.size()<1000){
                    break;
                }
                if(interfaceMonitorData.get(0).getId().getTimestamp()<expireTime){
                    break;
                }
            }
        } catch (Exception e) {
            log.info("batchSyncLogInsert tenantId={} Exception={}", tenantId,e);
        }

    }

    private String copyBetweenDbByTableName(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId, String tableName) {
        switch (tableName) {//数据量不大，不分页
            case "erp_connect_info":
                return copyConnect(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_object":
                return copyObject(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_object_relationship":
                return copyObjectRelationship(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_object_field":
                return copyObjectField(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_field_extend":
                return copyObjectFieldExtend(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_obj_groovy":
                return copyGroovy(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_field_data_mapping":
                return copyFieldDataMapping(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "sync_ploy_detail":
                return copyPloyDetail(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "sync_ploy_detail_snapshot":
                return copyPloyDetailSnapshot(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_sync_time":
                return copySyncTime(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            case "erp_tenant_configuration":
                return copyConfiguration(deleteDest, sourceDbRoute, destDbRoute, tenantId);
            default:
                return i18NStringManager.getByEi(I18NStringEnum.s3647,tenantId);
        }
    }


    private String copyConfiguration(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpTenantConfigurationDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpTenantConfigurationEntity query = new ErpTenantConfigurationEntity();
            query.setTenantId(tenantId);
            List<ErpTenantConfigurationEntity> entities = erpTenantConfigurationDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpTenantConfigurationDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copySyncTime(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpSyncTimeDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpSyncTimeEntity query = new ErpSyncTimeEntity();
            query.setTenantId(tenantId);
            List<ErpSyncTimeEntity> entities = erpSyncTimeDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpSyncTimeDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyPloyDetailSnapshot(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = adminSyncPloyDetailSnapshotDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            List<SyncPloyDetailSnapshotEntity> entities = adminSyncPloyDetailSnapshotDao.setTenantId(sourceDbRoute).listByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(entities)) {
                for (SyncPloyDetailSnapshotEntity entity : entities) {
                    adminSyncPloyDetailSnapshotDao.setTenantId(destDbRoute).insertIgnore(entity);
                }
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyPloyDetail(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = adminSyncPloyDetailDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            List<SyncPloyDetailEntity> entities = adminSyncPloyDetailDao.setTenantId(sourceDbRoute).listByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(entities)) {
                adminSyncPloyDetailDao.setTenantId(destDbRoute).insertList(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyFieldDataMapping(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpFieldDataMappingDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpFieldDataMappingEntity query = new ErpFieldDataMappingEntity();
            query.setTenantId(tenantId);
            List<ErpFieldDataMappingEntity> entities = erpFieldDataMappingDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpFieldDataMappingDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyGroovy(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpObjGroovyDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpObjGroovyEntity query = new ErpObjGroovyEntity();
            query.setTenantId(tenantId);
            List<ErpObjGroovyEntity> entities = erpObjGroovyDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpObjGroovyDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyObjectFieldExtend(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpFieldExtendDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpFieldExtendEntity query = new ErpFieldExtendEntity();
            query.setTenantId(tenantId);
            List<ErpFieldExtendEntity> entities = erpFieldExtendDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpFieldExtendDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyObjectField(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpObjectFieldDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpObjectFieldEntity query = new ErpObjectFieldEntity();
            query.setTenantId(tenantId);
            List<ErpObjectFieldEntity> entities = erpObjectFieldDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpObjectFieldDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyObjectRelationship(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpObjectRelationshipDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
            query.setTenantId(tenantId);
            List<ErpObjectRelationshipEntity> entities = erpObjectRelationshipDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpObjectRelationshipDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyObject(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpObjectDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpObjectEntity query = new ErpObjectEntity();
            query.setTenantId(tenantId);
            List<ErpObjectEntity> entities = erpObjectDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpObjectDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }

    private String copyConnect(Boolean deleteDest, String sourceDbRoute, String destDbRoute, String tenantId) {
        try {
            StringBuilder msgBuilder = new StringBuilder();
            if (deleteDest) {
                int delete = erpConnectInfoDao.setTenantId(destDbRoute).deleteByTenantId(tenantId);
                msgBuilder.append("删除目标数量：").append(delete);   // ignoreI18n   实施和开发自用
            }
            ErpConnectInfoEntity query = new ErpConnectInfoEntity();
            query.setTenantId(tenantId);
            List<ErpConnectInfoEntity> entities = erpConnectInfoDao.setTenantId(sourceDbRoute).queryList(query);
            if (CollectionUtils.isNotEmpty(entities)) {
                erpConnectInfoDao.setTenantId(destDbRoute).batchInsert(entities);
            }
            msgBuilder.append("复制数量：").append(entities.size());   // ignoreI18n   实施和开发自用
            return msgBuilder.toString();
        } catch (Exception e) {
            log.warn("copy error e={}", e);
            return i18NStringManager.getByEi(I18NStringEnum.s3648,tenantId) + e.toString();
        }
    }


    private Boolean brushSyncDataMappingsTableData(String sourceTenantId, String destDataBaseTenantId, StringBuilder stringBuilder, ErpTenantConfigurationEntity entity) {
        Map<String, Object> config = JacksonUtil.fromJson(entity.getConfiguration(), Map.class);
        String lastId = null;
        if (config.containsKey("lastId")) {
            lastId = (String) config.get("lastId");
        }
        int limit = 1000, count = 0;
        while (true) {
            if (count / 1000 % 500 == 0) {
                ErpTenantConfigurationEntity configuration = erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).findOneNoCache(sourceTenantId, "0", "0", TenantConfigurationTypeEnum.meedStopBrushTableDataConfig.name());
                if (configuration != null && StringUtils.isNotBlank(configuration.getConfiguration())) {
                    List<String> eis = JacksonUtil.fromJson(entity.getConfiguration(), List.class);
                    if (eis.contains(sourceTenantId)) {
                        config.put("firstFinish", false);
                        break;
                    }
                }
            }
            List<SyncDataMappingsEntity> list = syncDataMappingsDao.setTenantId(sourceTenantId).listOrderById(sourceTenantId, lastId, limit);
            if (CollectionUtils.isEmpty(list)) {
                config.put("firstFinish", true);
                break;
            }
            syncDataMappingsDao.setTenantId(destDataBaseTenantId).batchInsert(sourceTenantId, list);
            lastId = list.get(list.size() - 1).getId();
            count += limit;
            log.info("brushSyncDataMappingsTableData count={}", count);
            if (list.size() < limit) {
                config.put("firstFinish", true);
                break;
            }
        }
        config.put("lastId", lastId);
        entity.setConfiguration(JacksonUtil.toJson(config));
        entity.setUpdateTime(System.currentTimeMillis());
        erpTenantConfigurationDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).updateById(entity);
        Integer sourceSize = syncDataMappingsDao.setTenantId(sourceTenantId).count(sourceTenantId);
        Integer destSize = syncDataMappingsDao.setTenantId(destDataBaseTenantId).count(sourceTenantId);
        stringBuilder.append("first brushSyncDataMappingsTableData=").append("源数量：").append(sourceSize).append(",目标数量：").append(destSize).append(System.lineSeparator());   // ignoreI18n   实施和开发自用
        return sourceSize.equals(destSize);
    }

}
