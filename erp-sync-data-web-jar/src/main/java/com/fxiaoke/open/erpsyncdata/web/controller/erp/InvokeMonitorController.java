package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetInvokeErpErrorCountArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetInvokeMonitorArg;
import com.fxiaoke.open.erpsyncdata.admin.result.GetInvokeErpErrorCountResult;
import com.fxiaoke.open.erpsyncdata.admin.result.GetInvokeMonitorResult;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjCustomFunctionService;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/12/6 16:34:29
 */
@Slf4j
@Api(tags = "数据监控相关接口")
@RestController("invokeMonitorController")
@RequestMapping("cep/invokeMonitor")
public class InvokeMonitorController extends AsyncSupportController {

    @Autowired
    private CustomFunctionService customFunctionService;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;

    @Autowired
    private ErpObjManager erpObjManager;

    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;

    @ApiOperation(value = "异常概况")
    @RequestMapping("getInvokeErpErrorCount")
    public Result<GetInvokeErpErrorCountResult> getInvokeErpErrorCount(@RequestBody GetInvokeErpErrorCountArg arg) {
        return Result.newSuccess();
//        String tenantId = getLoginUserTenantId();
//        String dcId = getDcId();
//
//        List<String> ployDetailIds = getPloyDetailIdsByObjApiNames(tenantId, arg.getErpObjApiName(), dcId);
//
//        int invokeErpErrorCount = objectInvokeElasticSearchDao.getInvokeDistinctCount(tenantId, dcId, ployDetailIds, arg.getStartTime(), InvokeTypeEnum.INTEGRATION_STREAM, ObjectInvokeStatus.error);
//        int invokeErpBlowCount = objectInvokeElasticSearchDao.getInvokeDistinctCount(tenantId, dcId, ployDetailIds, arg.getStartTime(), InvokeTypeEnum.INTEGRATION_STREAM, ObjectInvokeStatus.blow);
//        int funcErrorCount = objectInvokeElasticSearchDao.getInvokeDistinctCount(tenantId, dcId, ployDetailIds, arg.getStartTime(), InvokeTypeEnum.FUNC, ObjectInvokeStatus.error);
//
//        final GetInvokeErpErrorCountResult getInvokeMonitorResult = new GetInvokeErpErrorCountResult();
//        getInvokeMonitorResult.setIntegrationStreamError(invokeErpErrorCount);
//        getInvokeMonitorResult.setBlowTime(invokeErpBlowCount);
//        getInvokeMonitorResult.setApiError(funcErrorCount);
//
//        // 函数异常使用函数上报的数据
//        return Result.newSuccess(getInvokeMonitorResult);
    }

    @ApiOperation(value = "获取折线图")
    @RequestMapping("getInvokeMonitor")
    public Result<GetInvokeMonitorResult> getInvokeMonitor(@RequestBody GetInvokeMonitorArg arg) {
        return Result.newSuccess();
//        final ObjInvokeMonitorType monitorType = monitorTypeMap.get(arg.getMonitor());
//        if (Objects.isNull(monitorType)) {
//            return Result.newErrorByI18N("未知的监控视图", I18NStringEnum.s74.getI18nKey(),null);
//        }
//
//        String tenantId = getLoginUserTenantId();
//
//        List<Integer> actions = monitorType.getActions();
//        if (Objects.nonNull(arg.getAction())) {
//            actions = getActions(arg.getMonitor(),arg.getAction());
//        }
//
//        final Integer invokeType = monitorType.getInvokeType();
//        final InvokeTypeProcessService invokeTypeProcessService = invokeTypeProcessServiceMap.get(invokeType);
//        String dcId = getDcId();
//        final List<String> erpObjApiNames = arg.getErpObjApiName();
//        final List<String> associatedApiName = CollectionUtils.isEmpty(erpObjApiNames) ? null : invokeTypeProcessService.getAssociatedApiName(tenantId, dcId, erpObjApiNames);
//
//        // 没有相关对象直接快速返回
//        if (Objects.nonNull(associatedApiName) && associatedApiName.isEmpty()) {
//            return Result.newSuccess(new GetInvokeMonitorResult(new ArrayList<>(), arg.getMonitor()));
//        }
//
//        final List<TimeMonitor> invokeMonitor = objectInvokeElasticSearchDao.getInvokeMonitor(tenantId, monitorType.isQueryWithDcId() ? dcId : null, null, associatedApiName, arg.getStartTime(), arg.getInterval(), arg.getStatus(), invokeType, actions, monitorType.getStatusList(), monitorType.getSumField());
//
//        final List<GetInvokeMonitorResult.ObjectTimeMonitorVo> objectTimeMonitorVos = GetInvokeMonitorResult.convert2ObjectKeyWithoutName(invokeMonitor);
//        fillName(tenantId, objectTimeMonitorVos, invokeType);
//        return Result.newSuccess(new GetInvokeMonitorResult(objectTimeMonitorVos, arg.getMonitor()));
//    }
//
//    private List<Integer> getActions(final String monitor, final Integer action) {
//        if (Objects.equals(monitor, "timeSpentQueryErp")) {
//            return Objects.equals(action, 1) ? Lists.newArrayList(1, 2, 14) : Lists.newArrayList(3);
//        }
//
//        return ActionEnum.getByCategory(action);
//    }
//
//    private List<String> getPloyDetailIdsByObjApiNames(final String tenantId, final List<String> crmObjApiName, final String dcId) {
//        return getPloyDetailsByObjApiNames(tenantId, crmObjApiName, dcId).stream().map(SyncPloyDetailEntity::getId).distinct().collect(Collectors.toList());
//    }
//
//    @Cached(cacheType = CacheType.LOCAL, expire = 60)
//    private List<SyncPloyDetailEntity> getPloyDetailsByObjApiNames(final String tenantId, final List<String> objApiName, final String dcId) {
//        return CollectionUtils.isEmpty(objApiName) ?
//                new ArrayList<>() :
//                adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
//                        .listStreamByDCIDAndObjApiNames(tenantId, dcId, objApiName, null);
//    }
//
//    private void fillName(final String tenantId, final List<GetInvokeMonitorResult.ObjectTimeMonitorVo> objectTimeMonitorVos, final Integer invokeType) {
//        final InvokeTypeProcessService invokeTypeProcessService = invokeTypeProcessServiceMap.get(invokeType);
//        objectTimeMonitorVos.forEach(objectTimeMonitorVo -> {
//            final String name = invokeTypeProcessService.getName(tenantId, objectTimeMonitorVo.getApiName());
//            objectTimeMonitorVo.setName(name);
//        });
//    }
//
//    private Map<String, ObjInvokeMonitorType> monitorTypeMap;
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        ConfigFactory.getConfig("erp-sync-data-all", "MonitorElastic", config -> {
//            monitorTypeMap = JSON.parseArray(config.get("monitorType", "[{\"type\":\"readDataFromErp\",\"detail\":\"从第三方读取数据量\",\"invokeType\":2,\"actions\":[1,2,3,14],\"sumField\":\"count\"},{\"type\":\"writeCrmData\",\"detail\":\"写入CRM数据量\",\"invokeType\":1,\"actions\":[4,5,6,7,8,9,10],\"sumField\":\"count\"},{\"type\":\"crmPushData\",\"detail\":\"CRM推送数据量\",\"invokeType\":1,\"queryWithDcId\":false,\"actions\":[11],\"sumField\":\"count\"},{\"type\":\"writeErpData\",\"detail\":\"写入ERP数据量\",\"invokeType\":2,\"actions\":[4,5,6,7,8,9,10],\"sumField\":\"count\"},{\"type\":\"numberOfErpQuery\",\"detail\":\"查询ERP次数\",\"invokeType\":2,\"actions\":[1,2,3,14]},{\"type\":\"timeSpentQueryErp\",\"detail\":\"查询ERP总耗时\",\"invokeType\":2,\"actions\":[1,2,3,14],\"sumField\":\"cost\"},{\"type\":\"timeSpentWriteErp\",\"detail\":\"写入ERP总耗时\",\"invokeType\":2,\"actions\":[4,5,6,7,8,9,10],\"sumField\":\"cost\"},{\"type\":\"numberOfErpServiceAnomalies\",\"detail\":\"第三方接口服务异常次数统计\",\"invokeType\":2,\"statusList\":[2,3]},{\"type\":\"numberOfFuncExceptions\",\"detail\":\"函数异常次数统计\",\"invokeType\":3,\"statusList\":[2,3]}]"), ObjInvokeMonitorType.class)
//                    .stream()
//                    .collect(Collectors.toMap(ObjInvokeMonitorType::getType, Function.identity()));
//        });
//    }
//
//    @Data
//    public static class ObjInvokeMonitorType {
//        private String type;
//        private String detail;
//        /**
//         * @see InvokeTypeEnum
//         */
//        private Integer invokeType;
//        /**
//         * @see ActionEnum
//         */
//        private List<Integer> actions;
//        /**
//         * @see ObjectInvokeStatus
//         */
//        private List<Integer> statusList;
//        /**
//         * 总值字段
//         */
//        private String sumField;
//        /**
//         * 查询的时候是否需要dcId
//         * 暂时只有推送的时候没有dcId
//         */
//        private boolean queryWithDcId = true;
//    }
//
//    public interface InvokeTypeProcessService {
//        Integer getType();
//
//        String getName(final String tenantId, final String apiName);
//
//        List<String> getAssociatedApiName(final String tenantId, final String dcId, List<String> erpObjApiNames);
//    }
//
//    private Map<Integer, InvokeTypeProcessService> invokeTypeProcessServiceMap;
//
//    {
//        final InvokeTypeProcessService crmInvokeTypeProcessService = new InvokeTypeProcessService() {
//            @Override
//            public Integer getType() {
//                return InvokeTypeEnum.CRM.getType();
//            }
//
//            @Override
//            @Cached(expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
//            public String getName(final String tenantId, final String apiName) {
//                final HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
//                return objectDescribeService.getDescribe(headerObj, apiName).getData().getDescribe().getDisplayName();
//            }
//
//            @Override
//            public List<String> getAssociatedApiName(final String tenantId, final String dcId, final List<String> erpObjApiNames) {
//                return getPloyDetailsByObjApiNames(tenantId, erpObjApiNames, dcId).stream()
//                        .map(syncPloyDetailEntity ->
//                                Objects.equals(syncPloyDetailEntity.getSourceTenantType(), TenantTypeEnum.CRM.getType()) ?
//                                        syncPloyDetailEntity.getSourceObjectApiName() :
//                                        syncPloyDetailEntity.getDestObjectApiName())
//                        .collect(Collectors.toList());
//            }
//        };
//
//        final InvokeTypeProcessService erpInvokeTypeProcessService = new InvokeTypeProcessService() {
//            @Override
//            public Integer getType() {
//                return InvokeTypeEnum.ERP.getType();
//            }
//
//            @Override
//            @Cached(expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
//            public String getName(final String tenantId, final String apiName) {
//                return erpObjManager.getErpObjName(tenantId, apiName);
//            }
//
//            @Override
//            public List<String> getAssociatedApiName(final String tenantId, final String dcId, final List<String> erpObjApiNames) {
//                return erpObjApiNames.stream()
//                        .distinct()
//                        .map(erpObjApiName -> erpObjManager.getRealObjApiName(tenantId, erpObjApiName))
//                        .collect(Collectors.toList());
//            }
//        };
//
//        final InvokeTypeProcessService funcInvokeTypeProcessService = new InvokeTypeProcessService() {
//            @Override
//            public Integer getType() {
//                return InvokeTypeEnum.FUNC.getType();
//            }
//
//            @Override
//            @Cached(expire = 10, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
//            public String getName(final String tenantId, final String apiName) {
//                return customFunctionService.getFunctionName(tenantId, apiName);
//            }
//
//            @Override
//            public List<String> getAssociatedApiName(final String tenantId, final String dcId, final List<String> erpObjApiNames) {
//                final Stream<String> functionStream = getPloyDetailsByObjApiNames(tenantId, erpObjApiNames, dcId).stream()
//                        .flatMap(syncPloyDetailEntity ->
//                                Stream.of(
//                                        syncPloyDetailEntity.getBeforeFuncApiName(),
//                                        syncPloyDetailEntity.getDuringFuncApiName(),
//                                        syncPloyDetailEntity.getAfterFuncApiName()
//                                ))
//                        .filter(StringUtils::isNotBlank);
//
//                final List<String> objFunctions = erpObjCustomFunctionService.queryAllFunctionApiNameByObjectApiName(tenantId, dcId, erpObjApiNames);
//
//                return Stream.concat(functionStream, objFunctions.stream())
//                        .distinct().collect(Collectors.toList());
//            }
//        };
//
//        invokeTypeProcessServiceMap = Lists.newArrayList(
//                crmInvokeTypeProcessService,
//                erpInvokeTypeProcessService,
//                funcInvokeTypeProcessService
//        ).stream().collect(Collectors.toMap(InvokeTypeProcessService::getType, Function.identity()));
    }
}
