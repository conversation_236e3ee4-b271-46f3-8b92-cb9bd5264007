package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.service.SyncQuotaService
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.LogStorageRuleEnum
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class SyncQuotaServiceImplTest extends BaseSpockTest {
    @Autowired
    private SyncQuotaService syncQuotaService
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager

    @Test
    void getQuota() {
        def quota = syncQuotaService.getQuota("81234", true,true)
        assert quota.isSuccess()
        println(quota.data)
    }

    @Test
    void getLogQuotaFromCache() {
        def quota = syncQuotaService.getLogQuotaFromCache("81234")
        assert quota.isSuccess()
    }

    @Test
    void saveLogStorageRules() {
        def ei = "88466"

        def result = syncQuotaService.updateLogStorageRules(ei, LogStorageRuleEnum.RULE_ONE)
        println(result)
        when:
        result.isSuccess()

        then:
        LogStorageRuleEnum logStorageRule = tenantConfigurationManager.getLogStorageRule(ei)
        println(logStorageRule)

    }
}
