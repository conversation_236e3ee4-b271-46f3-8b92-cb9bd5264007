package com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateExecuteStepEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class K3UltimateGetApiTemplateModel implements Serializable {
    private StepModel add;
    private StepModel update;
    private List<ApiItem> apiList;

    @Data
    public static class StepModel implements Serializable {
        private List<StepItem> stepList;
    }

    @Data
    @AllArgsConstructor
    public static class StepItem implements Serializable {
        private String stepName;
        private K3UltimateExecuteStepEnum executeStepEnum;
        private List<ApiItem> stepList;
        private boolean checked;
    }

    @Data
    @AllArgsConstructor
    public static class ApiItem implements Serializable {
        private String name;
        private String apiName;
        private String url;
        private boolean checked;
    }

    public static List<ApiItem> getApiList(I18NStringManager i18NStringManager,
                                           String lang,
                                           String tenantId,
                                           K3UltimateBaseApiTemplate apiTemplate) {
        List<ApiItem> apiItemList = Lists.newArrayList(
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2201,lang,tenantId),"batchQueryApi",apiTemplate.getBatchQueryApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2202,lang,tenantId),"batchAddApi",apiTemplate.getBatchAddApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2203,lang,tenantId),"batchUpdateApi",apiTemplate.getBatchUpdateApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2204,lang,tenantId),"batchSubmitApi",apiTemplate.getBatchSubmitApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2205,lang,tenantId),"batchUnSubmitApi",apiTemplate.getBatchUnSubmitApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2206,lang,tenantId),"batchAuditApi",apiTemplate.getBatchAuditApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2207,lang,tenantId),"batchUnAuditApi",apiTemplate.getBatchUnAuditApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2208,lang,tenantId),"batchEnableApi",apiTemplate.getBatchEnableApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2209,lang,tenantId),"batchDisableApi",apiTemplate.getBatchDisableApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2210,lang,tenantId),"batchDeleteApi",apiTemplate.getBatchDeleteApi(),false),
                new ApiItem(i18NStringManager.get(I18NStringEnum.s2211,lang,tenantId),"batchValidApi",apiTemplate.getBatchValidApi(),false)
        );
        return apiItemList;
    }
}