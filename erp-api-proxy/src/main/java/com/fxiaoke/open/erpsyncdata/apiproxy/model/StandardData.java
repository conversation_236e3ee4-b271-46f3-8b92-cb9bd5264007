package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准格式
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Data
@ApiModel(value = "标准格式数据",description = "erp数据平台标准格式数据")
public class StandardData implements Serializable, RAMEstimable {
    private static final long serialVersionUID = -1031712456801253532L;

    @ApiModelProperty("主对象apiName")
    private String objAPIName;
    @ApiModelProperty("主对象数据")
    private ObjectData masterFieldVal;
    @ApiModelProperty("从对象数据;key:从对象apiName,value:从对象数据")
    private Map<String, List<ObjectData>> detailFieldVals = new HashMap<>(0);
    @ApiModelProperty("数据注册logId")
    private String syncLogId;
    @ApiModelProperty("数据接收type")
    private Integer dataReceiveType;
    @ApiModelProperty("数据版本")
    private Long dataVersion;
    @ApiModelProperty("需要返回的字段")
    private Map<String,List<String>> needReturnField;

    @Override
    public long ramBytesUsed(int depth) {
        //只计算两个大的
        return RamUsageEstimateUtil.sizeOfObject(masterFieldVal, depth) + RamUsageEstimateUtil.sizeOfObject(detailFieldVals, depth);
    }

    public void removeId(){
        this.masterFieldVal.remove("_id");
        if (detailFieldVals!=null){
            for (List<ObjectData> value : detailFieldVals.values()) {
                for (ObjectData objectData : value) {
                    objectData.remove("_id");
                }
            }
        }
    }

    @Override
    public String toString() {
        ToStringBuilder toStringBuilder = new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
                .append("objAPIName", objAPIName)
                .append("syncLogId", syncLogId)
                .append("dataVersion", dataVersion)
                .append("masterFieldVal", masterFieldVal)
                .append("detailFieldVals", detailFieldVals);
        return toStringBuilder.toString();
    }

    /**
     * 计算所有对象的数量
     */
    public Integer countAllObjData() {
        if (CollUtil.isEmpty(detailFieldVals)) {
            return 1;
        }
        //安全计数
        int sum = detailFieldVals.values().stream().mapToInt(v -> CollUtil.size(v)).sum();
        return sum + 1;
    }


    private static StandardData convert(StandardData standardData) {
        String json = JacksonUtil.toJson(standardData);
        StandardData linkedStandardData = JSONObject.parseObject(json,StandardData.class);
        return linkedStandardData;
    }

    public static StandardData convert(StandardData standardData,
                                             ErpObjectFieldDao erpObjectFieldDao,
                                       SyncDataFixDao adminSyncDataDao,
                                             String tenantId,
                                             String dataCenterId) {
        StandardData linkedStandardData = convert(standardData);

        LinkedHashMap<String, Object> masterObjectData = linkedStandardData.getMasterFieldVal();
        String masterObjectApiName = getErpObjectApiName(tenantId, adminSyncDataDao,masterObjectData.get("sync_data_id").toString());
        updateData(masterObjectData, erpObjectFieldDao, tenantId, dataCenterId, masterObjectApiName);
        masterObjectData.remove("sync_data_id");

        for (String key : standardData.getDetailFieldVals().keySet()) {
            List<ObjectData> detailList = standardData.getDetailFieldVals().get(key);
            List<ObjectData> detailList2 = linkedStandardData.getDetailFieldVals().get(key);
            for (int i = 0; i < detailList.size(); i++) {
                LinkedHashMap<String, Object> linkedHashMap = detailList2.get(i);
                String detailObjectApiName = getErpObjectApiName(tenantId, adminSyncDataDao,linkedHashMap.get("sync_data_id").toString());
                updateData(linkedHashMap, erpObjectFieldDao, tenantId, dataCenterId, detailObjectApiName);
                linkedHashMap.remove("sync_data_id");
            }
        }

        return linkedStandardData;
    }

    public static String getErpObjectApiName(String tenantId,SyncDataFixDao adminSyncDataDao,String sync_data_id) {
        SyncDataEntity entity = adminSyncDataDao.setTenantId(tenantId).getSimple(tenantId,sync_data_id);
        if(entity!=null) {
            return entity.getDestObjectApiName();
        }
        return null;
    }

    /**
     * 字段排序
     */
    public static void updateData(LinkedHashMap<String, Object> linkedHashMap,
                                  ErpObjectFieldDao erpObjectFieldDao,
                                  String tenantId,
                                  String dataCenterId,
                                  String erpObjectApiName) {
        LinkedHashMap<String, Object> linkedHashMap2 = new LinkedHashMap<>();
        for(String key : linkedHashMap.keySet()) {
            linkedHashMap2.put(key,linkedHashMap.get(key));
        }

        List<ErpObjectFieldEntity> fieldEntityList = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findData(tenantId, dataCenterId, erpObjectApiName);
        for (ErpObjectFieldEntity entity : fieldEntityList) {
            if (!linkedHashMap.containsKey(entity.getFieldApiName())){
                continue;
            }
            //当存在该字段，重新放到map后面。
            Object value = linkedHashMap.remove(entity.getFieldApiName());
            linkedHashMap2.remove(entity.getFieldApiName());
            linkedHashMap.put(entity.getFieldApiName(), value);
        }

        linkedHashMap2.forEach((key,value)->{
            //不在映射的字段，统一按原顺序重新放到map后面。
            linkedHashMap.remove(key);
            linkedHashMap.put(key,value);
        });
    }

}
