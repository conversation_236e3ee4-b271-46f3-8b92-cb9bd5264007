package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16 15:17:42
 */
public interface ImportSystemFieldMapping {
    @Data
    class Arg {
        @ApiModelProperty("文件的npath")
        private String npath;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<SheetResult> sheetResults;
        private String message;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class SheetResult {
        private String sheetName;
        private ImportExcelFile.Result result;
    }
}
