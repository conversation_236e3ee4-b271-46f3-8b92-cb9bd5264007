package com.fxiaoke.open.erpsyncdata.common.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum CustomFunctionTypeEnum {
    BEFORE_FUNCTION(1, "同步前", I18NStringEnum.s666.getI18nKey(), "executeBeforeSync"),
    DURING_FUNCTION(2, "同步中", I18NStringEnum.s667.getI18nKey(), "executeDuringSync"),
    AFTER_FUNCTION(3, "同步后", I18NStringEnum.s668.getI18nKey(), "executeAfterSync"),
    CREATE_FUNCTION(4, "创建",I18NStringEnum.s363.getI18nKey(), "createErpObjData"),
    UPDATE_FUNCTION(5, "更新", I18NStringEnum.s364.getI18nKey(), "updateErpObjData"),
    INVALID_FUNCTION(6, "作废", I18NStringEnum.s365.getI18nKey(), "invalidErpObjData"),
    LIST_ERP_FUNCTION(7, "批量查询", I18NStringEnum.s1077.getI18nKey(), "listErpObjDataByTime"),
    GET_ERP_FUNCTION(8, "单条数据", I18NStringEnum.s1078.getI18nKey(), "getErpObjData"),
    ;
    private int type;
    private String name;
    private String i18nKey;
    /**
     * 自定义函数方法名称
     */
    private String functionMethodName;

    public static Set<Integer> listAllTypes() {
        return Lists.newArrayList(values()).stream().map(CustomFunctionTypeEnum::getType).collect(Collectors.toSet());
    }

}
