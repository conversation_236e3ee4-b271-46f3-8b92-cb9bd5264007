package com.fxiaoke.open.erpsyncdata.probe.utils;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.uc.api.event.EnterpriseRunStatusEvent;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Data
@Slf4j
@Service
public class RemoveUnNormalTenantUtil {
    public void removeUnNormalTenant(List<String> tenantIdStrList, EnterpriseEditionService enterpriseEditionService) {
        List<Integer> tenantIdIntList = new ArrayList<>();
        for(String ei : tenantIdStrList) {
            try {
                tenantIdIntList.add(Integer.valueOf(ei));
            } catch (Exception e){
                log.info("removeUnNormalTenant ", ei);
            }
        }

        int startIndex = 0;
        while (startIndex < tenantIdIntList.size()) {
            int endIndex = startIndex+50;
            if(endIndex > (tenantIdIntList.size() - 1)) {
                endIndex = tenantIdIntList.size() -1;
            }
            if(endIndex <= startIndex) {
                break;
            }
            List<Integer> sublist = tenantIdIntList.subList(startIndex, endIndex);
            startIndex=endIndex;

            BatchGetEnterpriseDataArg arg = new BatchGetEnterpriseDataArg();
            arg.setEnterpriseIds(sublist);
            BatchGetEnterpriseDataResult result = enterpriseEditionService.batchGetEnterpriseData(arg);
            if((result == null) || (CollectionUtils.isEmpty(result.getEnterpriseDatas()))) {
                continue;
            }

            //适当的睡眠一下，访问通讯录不要过滤频繁。
            try {
                Thread.sleep(3000);
            }catch (Exception e){}

            //企业状态不正常的ei要剔除，不在调用paas接口
            List<String> removedTenantIdStrList = result.getEnterpriseDatas().stream().filter(v->v.getRunStatus() != EnterpriseRunStatusEvent.RUN_STATUS_NORMAL).map((v)->String.valueOf(v.getEnterpriseId())).collect(Collectors.toList());
            if(!removedTenantIdStrList.isEmpty()) {
                tenantIdStrList.removeAll(removedTenantIdStrList);
            }
        }
    }
}
