package com.fxiaoke.open.oasyncdata.manager;


import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.oasyncdata.db.manager.TenantConfigurationManager;
import com.fxiaoke.open.oasyncdata.db.util.GsonUtil;
import com.fxiaoke.open.oasyncdata.model.ObjectData;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class FunctionApiManager {
    @ReloadableProperty("rest.object.url.prefix")
    private String restUri;

    //SFA新的服务地址
    @ReloadableProperty("rest.sfa.object.url.prefix")
    private String restSfaUri;

    @ReloadableProperty("paas.function.url")
    private String functionUrl;

    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * CRM系统管理员身份
     **/
    private int USER_SYSTEM = -10000;


    /**
     * 创建产品分类接口
     *
     * @param tenantId
     * @param category_name
     * @param category_code
     * @param pid
     * @param order_field
     * @return
     */
    public ObjectData createProductCategory(String tenantId,
                                            String category_name,
                                            String category_code,
                                            String pid,
                                            int order_field) {

        Map<String, String> headerMap = buildHeader(tenantId, USER_SYSTEM);
        ObjectData objectData = new ObjectData();
        objectData.put("name", category_name);
        objectData.put("category_code", category_code);
        objectData.put("pid", pid);
        objectData.put("order_field", order_field);
        String url = Joiner.on(StringUtils.EMPTY).join(restSfaUri, "/product_category/service/add");
        return proxyHttpClient.postUrl(url, objectData, headerMap, new TypeReference<ObjectData>() {
        });
    }


    public ObjectData executeCustomFunction(HeaderObj headerObj, FunctionServiceExecuteArg arg,boolean serializeNull) {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(functionUrl, "/v1/function/currencyFunction");
        if (serializeNull){
            return proxyHttpClient.postUrlSerialNull(url, arg, headerMap, new TypeReference<ObjectData>() {
            });
        }else {
            return proxyHttpClient.postUrl(url, arg, headerMap, new TypeReference<ObjectData>() {
            });
        }
    }

    /**
     * 查找自定义函数，获取自定义函数的详情
     *
     * @param headerObj
     * @param arg
     * @return
     */
    public ObjectData findCustomFunction(HeaderObj headerObj, FunctionServiceFindArg arg) {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(functionUrl, "/API/v1/inner/object/function/service/find");
        String json = GsonUtil.toJson(arg);
        return proxyHttpClient.postUrlByJson(url, json, headerMap, new TypeReference<ObjectData>() {
        });
    }



    private Map<String, String> buildHeader(String tenantId, int operatorId) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userInfo", operatorId + "");
        headerMap.put("client_info", "rest-api");
        headerMap.put(I18NStringManager.X_FS_LOCALE,i18NStringManager.getDefaultLang(tenantId));
        return headerMap;
    }

    private Map<String, String> getHeader(HeaderObj headerObj) {
        Map<String, String> headerMap = new HashMap<>();
        headerObj.forEach((key, value) -> {
            headerMap.put(key, value == null ? "" : value.toString());
        });
        return headerMap;
    }
}
