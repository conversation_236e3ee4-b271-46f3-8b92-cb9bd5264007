package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


public interface ImportObjectFieldData {
    @Data
    class Arg implements Serializable {
        @ApiModelProperty(value = "ERP真实对象apiName")
        private String erpRealObjectApiName;

        @ApiModelProperty(value = "模板类型")
        @NotNull(message = "模板类型不能为空")
        private ExcelTypeEnum excelType;

        @ApiModelProperty(value = "文件npath")
        private String npath;
    }

    @Data
    class Result implements Serializable {
        /**
         * 导入成功条数
         */
        private int successCount;
        /**
         * 导入失败条数
         */
        private int failedCount;
    }
}
