package com.fxiaoke.open.erpsyncdata.web.controller.inner;

import cn.hutool.core.codec.Base32;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.OuterConnectorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.OkHttpUtils;
import com.fxiaoke.open.erpsyncdata.preprocess.model.HttpResponse2;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.HubInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.OuterConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * 外部连接器接口
 * 只支持内部调用
 *
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@RestController
@RequestMapping("inner/outerConnector")
public class OuterConnectorManagerController {
    @Autowired
    private OuterConnectorManager outerConnectorManager;
    @Autowired
    private StoneProxyApi stoneProxyApi;
    @Autowired
    private ConfigCenterManager configCenterManager;

    /**
     * 注册
     *
     * @return
     */
    @PostMapping("register")
    public Result<Void> register(@RequestBody HubInfo hubInfo) {
        return Result.newError("not support now");
    }


    /**
     * 获取列表
     */
    @GetMapping("getHubInfoList")
    public Result<List<HubInfo>> getHubInfoList() {
        return Result.newSuccess(outerConnectorManager.getHubInfoList());
    }

    /**
     * 注册新hub
     *
     * @param hubInfo
     * @return
     */
    @PostMapping("registerHub")
    public Result<String> registerHub(@RequestBody JSONObject hubInfo) {
        checkEnv();
        String name = hubInfo.getString(HubInfo.Fields.name);
        String baseUrl = hubInfo.getString(HubInfo.Fields.baseUrl);
        if (StrUtil.isBlank(name) || StrUtil.isBlank(baseUrl)) {
            return Result.newError("name or baseUrl is empty");
        }

        //检查是否可以访问
        String checkUrl = baseUrl + "/hub/check";
        Request request = new Request.Builder().url(checkUrl).get().build();
        try {
            HttpResponse2 response = OkHttpUtils.execute(request, 3000);
            if (response.getCode() < 200 || response.getCode() > 300) {
                //失败
                return Result.newError(String.format("check hub failed:%s,%s,%s", checkUrl, response.getCode(), response.getBody()));
            }
        } catch (ErpSyncDataException e) {
            return Result.newError(String.format("check hub exception:%s,%s", checkUrl, e.getMessage()));
        }
        //检查name是否只包含数字、字母、下划线
        if (!StrUtil.isAllCharMatch(name, v -> Character.isLetterOrDigit(v) || v == '_')) {
            return Result.newError("hub name must be letter or number or _");
        }
        //检查是否重名

        List<JSONObject> hubs = readHubInfoList();
        Optional<JSONObject> existHub = hubs.stream().filter(v -> v.getString(HubInfo.Fields.name).equals(name)).findFirst();
        if (existHub.isPresent()) {
            return Result.newError(String.format("hub name exist:%s", name));
        }

        hubs.add(hubInfo);
        boolean b = configCenterManager.updateHubInfo(hubs.toString(), "registerHub");
        String token = generateToken(name);
        //返回密钥
        return Result.newSuccess(token);
    }

    private List<JSONObject> readHubInfoList() {
        String hubInfoStr = configCenterManager.readHubInfo();
        //转换为JSONObject
        List<JSONObject> hubs = JSON.parseArray(hubInfoStr, JSONObject.class);
        if (hubs == null) {
            return new ArrayList<>();
        }
        return hubs;
    }

    private String generateToken(String name) {
        return Base32.encode(name);
    }

    /**
     * 删除hub
     */
    @DeleteMapping("deleteHub")
    public Result<Void> deleteHub(@RequestParam String name, @RequestParam String token) {
        checkEnv();
        checkToken(name, token);
        List<JSONObject> hubs = readHubInfoList();
        boolean removed = hubs.removeIf(v -> v.getString(HubInfo.Fields.name).equals(name));
        if (!removed) {
            return Result.newError("hub doesn't exist");
        }
        return Result.newSuccess();
    }


    @PostMapping("upsertConnector")
    public Result<Void> upsertConnector(@RequestParam String token,
                                        @RequestParam String hubName,
                                        @RequestBody OuterConnector connector) {
        checkEnv();
        checkToken(hubName, token);
        String apiName = connector.getApiName();
        String defaultName = connector.getDefaultName();
        if (StrUtil.isBlank(apiName) || StrUtil.isBlank(defaultName)) {
            return Result.newError("apiName or defaultName is empty");
        }


        List<JSONObject> hubs = readHubInfoList();
        Optional<JSONObject> existHub = hubs.stream().filter(v -> v.getString(HubInfo.Fields.name).equals(hubName)).findFirst();
        if (!existHub.isPresent()) {
            return Result.newError(String.format("hub name not found:%s", hubName));
        }

        JSONObject hub = existHub.get();
        JSONArray connectors = hub.getJSONArray(HubInfo.Fields.outerConnectors);
        //查找是否已经存在
        if (connectors == null) {
            connectors = new JSONArray();
            hub.put(HubInfo.Fields.outerConnectors, connectors);
        }
        //remove old one
        connectors.removeIf(v -> Objects.equals(((JSONObject) v).getString(OuterConnector.Fields.apiName), apiName));
        connectors.add(connector);
        configCenterManager.updateHubInfo(hub.toString(), "upsertConnector");
        return Result.newSuccess();
    }

    @DeleteMapping("deleteConnector")
    public Result<Void> deleteConnector(@RequestParam String token,
                                        @RequestParam String hubName,
                                        @RequestParam String connectorApiName) {
        checkEnv();
        checkToken(hubName, token);
        if (StrUtil.isBlank(connectorApiName)) {
            return Result.newError("apiName  is empty");
        }


        List<JSONObject> hubs = readHubInfoList();
        Optional<JSONObject> existHub = hubs.stream().filter(v -> v.getString(HubInfo.Fields.name).equals(hubName)).findFirst();
        if (!existHub.isPresent()) {
            return Result.newError(String.format("hub name not found:%s", hubName));
        }

        JSONObject hub = existHub.get();
        JSONArray connectors = hub.getJSONArray(HubInfo.Fields.outerConnectors);
        //查找是否已经存在
        if (connectors == null) {
            connectors = new JSONArray();
            hub.put(HubInfo.Fields.outerConnectors, connectors);
        }
        //remove old one
        connectors.removeIf(v -> Objects.equals(((JSONObject) v).getString(OuterConnector.Fields.apiName), connectorApiName));

        return Result.newSuccess();
    }

    private void checkToken(String name, String token) {
        String predictToken = generateToken(name);
        if (!Objects.equals(predictToken, token)) {
            throw new ErpSyncDataException("token is incorrect");
        }
    }

    @PostMapping(value = "uploadIcon", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<String> uploadIcon(@RequestParam("file") MultipartFile file) {
        //仅112可用
        checkEnv();

        if (file.isEmpty()) {
            return Result.newError("file can not be null");
        }
        String originalFilename = file.getOriginalFilename();
        String extensionName = StrUtil.subAfter(originalFilename, ".", true);

        // 验证文件类型
        if (!Arrays.asList("png", "jpg", "jpeg").contains(extensionName.toLowerCase())) {
            return Result.newError("only support png,jpg,jpeg");
        }

        // 验证文件大小（例如限制为2MB）
        if (file.getSize() > 2 * 1024 * 1024) {
            return Result.newError("file size too large, maximum support of 2MB");
        }

        String ea = "83952";
        int employeeId = 1000;
        String storageType = "n";
        StoneFileUploadRequest stoneFileUploadRequest = new StoneFileUploadRequest();
        stoneFileUploadRequest.setEa(ea);
        stoneFileUploadRequest.setEmployeeId(employeeId);
        stoneFileUploadRequest.setNeedCdn(true);
        stoneFileUploadRequest.setFileSize(Long.valueOf(file.getSize()).intValue());
        stoneFileUploadRequest.setOriginName(originalFilename);
        stoneFileUploadRequest.setExtensionName(extensionName);
        stoneFileUploadRequest.setBusiness(CommonConstant.ERP_SYNC_DATA_BUSINESS);
        try {
            StoneFileUploadResponse response = stoneProxyApi.uploadByStream(storageType, stoneFileUploadRequest,
                    file.getInputStream());
            String path = response.getPath();
            String iconUrl = ConfigCenter.CDN_DOMAIN + "/image/ea/" + path + "/100*100";
            return Result.newSuccess(iconUrl);
        } catch (Exception e) {
            log.error("uploadIcon exception", e);
            return Result.newError("uploadIcon exception：" + e.getMessage());
        }
    }

    private void checkEnv() {
        boolean isFirstshareENv = Objects.equals(SystemUtils.getRuntimeEnv(), SystemUtils.RuntimeEnv.FIRSTSHARE);
        if (!isFirstshareENv) {
            throw new ErpSyncDataException("only support firstshare env");
        }
    }
}
