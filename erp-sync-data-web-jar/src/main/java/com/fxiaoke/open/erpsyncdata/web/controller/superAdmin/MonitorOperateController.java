package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.dbproxy.monitor.CheckProductErpDataMonitor;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "监控报警")
@RestController()
@RequestMapping("/erp/syncdata/superadmin/monitor")
public class MonitorOperateController {
    @Autowired
    private CheckProductErpDataMonitor checkProductErpDataMonitor;

    @ApiOperation(value = "解除没有产生erp数据报警")
    @RequestMapping(value = "product/erp/notify/remove", method = RequestMethod.GET)
    public Result<String> removeErpProductNotify(@RequestParam String tenantId,
                                                 @RequestParam String objApiName) {
        if (StringUtils.isEmpty(tenantId)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        checkProductErpDataMonitor.cleanNotify(tenantId, objApiName);
        return Result.newSuccess("报警已解除");   // ignoreI18n  实施和开发自用
    }
}
