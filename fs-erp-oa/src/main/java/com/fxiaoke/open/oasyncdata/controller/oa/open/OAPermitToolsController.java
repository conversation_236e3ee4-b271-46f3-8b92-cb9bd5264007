package com.fxiaoke.open.oasyncdata.controller.oa.open;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.oasyncdata.arg.SendTextNoticeArg;
import com.fxiaoke.open.oasyncdata.controller.oa.BaseController;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.oasyncdata.db.util.ParallelUtils;
import com.fxiaoke.open.oasyncdata.model.ImportExcelFile;
import com.fxiaoke.open.oasyncdata.model.OAConnectInfoVO;
import com.fxiaoke.open.oasyncdata.model.OAConnectParam;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import com.fxiaoke.open.oasyncdata.service.*;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * liuyc
 * 2021/3/18
 */
@Api(tags = "OA工具接口，有权限")
@RestController()
@RequestMapping("erp/syncdata/oa/permit/")
@Slf4j
public class OAPermitToolsController extends BaseController {
    @Autowired
    private OAConnParamService oaConnParamService;
    @Autowired
    private OAUserService oaUserService;
    @Autowired
    private OASyncApiService oaSyncApiService;
    @Autowired
    private FileService fileService;
    @Autowired
    private NotificationService notificationService;

    @ApiOperation(value = "新增或更新字段")
    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    public Result<String> addOrUpdateObjectField() {
        log.info("hello ....");
        return Result.newSuccess();
    }


    @ApiOperation(value = "手动点击填充云之家的信息")
    @RequestMapping(value = "/webhook/addInfo",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> addInfo(@RequestParam("appId") String appId, @RequestParam("secret") String secret,@RequestParam(value = "dataCenterId",required = false) String dataCenterId){
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        Result<OAConnectInfoVO> oaConnectInfo = oaConnParamService.getOAConnectInfo(tenantId,dataCenterId);
        if(!oaConnectInfo.isSuccess()|| ObjectUtils.isEmpty(oaConnectInfo.getData())){
            return Result.newError(ResultCodeEnum.OA_UN_CONNECT);
        }
        OAConnectParam oaConnectParam=new OAConnectParam();
        oaConnectParam.setHeaderScript("");
        oaConnectParam.setUrlScript("");
        Map<String,String> commonMap= Maps.newHashMap();
        commonMap.put("appid",appId);
        commonMap.put("secret",secret);
        oaConnectParam.setCommonMap(commonMap);
        OAConnectParam.ResultFormat resultFormat=new OAConnectParam.ResultFormat();
        resultFormat.setCodeName("errorCode");
        resultFormat.setMsgName("error");
        resultFormat.setSuccessCode("200");
        oaConnectParam.setResultFormat(resultFormat);
        Map<String,String> header= Maps.newHashMap();
        header.put("Content-Type","application/json");
        oaConnectParam.setHeader(header);
        String urlScript="import groovy.json.JsonSlurper\n" +
                "import sun.net.www.protocol.https.DelegateHttpsURLConnection\n" +
                "public static String getToken(){\n" +
                "    String token = null;\n" +
                "    String requestUrl = \"http://www.yunzhijia.com/gateway/oauth2/token/getAccessToken\";\n" +
                "    // 修改请求地址\n" +
                "    HttpURLConnection connection = (HttpURLConnection) new URL(requestUrl).openConnection();\n" +
                "    // 设置连接主机服务器的超时时间：15000毫秒\n" +
                "    connection.setConnectTimeout(15000);\n" +
                "    connection.setUseCaches(false);\n" +
                "    connection.setRequestProperty(\"Content-Type\", \"application/json; charset=UTF-8\");\n" +
                "    connection.setRequestMethod(\"POST\");\n" +
                "    String paramJson = \"{ \\\"appId\\\": \\\"${appId}\\\",\\\"secret\\\": \\\"${secret}\\\", \\\"timestamp\\\":\"+ new Date().time+ \",\\\"scope\\\": \\\"app\\\"}\";\n" +
                "    // 设置读取远程返回的数据时间：60000毫秒\n" +
                "    connection.setReadTimeout(60000);\n" +
                "    connection.setDoOutput(true);\n" +
                "    connection.setDoInput(true);\n" +
                "    connection.getOutputStream().write(paramJson.getBytes(\"UTF-8\"));\n" +
                "    connection.connect();\n" +
                "    int code = connection.getResponseCode();\n" +
                "    if (code == 200) {\n" +
                "        InputStream inputStream = connection.getInputStream();\n" +
                "        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));\n" +
                "        String line;\n" +
                "        StringBuffer buffer = new StringBuffer();\n" +
                "        while ((line = bufferedReader.readLine()) != null) {\n" +
                "            buffer.append(line);\n" +
                "        }\n" +
                "        String str = buffer.toString();\n" +
                "        def jsonSlurper = new JsonSlurper();\n" +
                "        Map map = jsonSlurper.parseText(str);\n" +
                "        Map data = (Map)map.get(\"data\");\n" +
                "        token = data.get(\"accessToken\");\n" +
                "    } else {\n" +
                "        throw new RuntimeException(\"握手异常(\" + connection.getResponseCode() + \")！\" + connection.getResponseMessage());\n" +
                "    }\n" +
                "    return token;\n" +
                "}\n" +
                "\n" +
                "url = url + getToken();\n" +
                "\n" +
                "return url;";
        urlScript=urlScript.replace("${appId}",appId);
        urlScript=urlScript.replace("${secret}",secret);
        oaConnectParam.setUrlScript(urlScript);
        oaConnectInfo.getData().setConnectParams(oaConnectParam);
        Result<String> stringResult = oaConnParamService.coverUpdateOAConnectInfo(tenantId, oaConnectInfo.getData());
        return Result.newSuccess();

    }

}
