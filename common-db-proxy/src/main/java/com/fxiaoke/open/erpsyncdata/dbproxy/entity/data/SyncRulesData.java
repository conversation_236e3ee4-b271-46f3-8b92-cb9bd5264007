package com.fxiaoke.open.erpsyncdata.dbproxy.entity.data;

import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import com.fxiaoke.open.erpsyncdata.preprocess.model.TriggerConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncRulesData implements Serializable {
    /**  同步方式 轮询：get,推送：push 调试：debug**/
    @Deprecated
    private String syncType ;
    /**  同步方式 轮询：get,推送：push，事件订阅：subscribe**/
    private List<String> syncTypeList ;
    /**源数据事件类型， 1新增 2修改 3作废**/
    private List<Integer> events;
    /**
     * 按时间间隔轮询
     */
    private PollingIntervalApiDto pollingInterval;

    /**
     * 强制同步依赖对象数据，默认为false
     */
    private Boolean syncDependForce = false;

    /**
     * 编辑时,只有更新配置中的字段才会同步数据
     */
    private TriggerConfig triggerConfig;
}
