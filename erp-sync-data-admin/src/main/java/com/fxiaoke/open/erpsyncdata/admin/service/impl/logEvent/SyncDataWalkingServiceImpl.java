package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.admin.result.CrmTriggerLogResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataListResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SyncLogNode;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogDetailArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.entity.CHSyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.ch.manager.CHSyncDataManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Page;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 11:15 2024/8/8
 * @Desc:
 */
@Service
@Slf4j
@SyncLogNode(SyncLogTypeEnum.SYNC_DATA)
public class SyncDataWalkingServiceImpl extends AbstractLogService {
    @Autowired
    private CHSyncDataManager chSyncDataManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    public ObjectDataService objectDataService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EmployeeService employeeService;

    @Override
    public Result queryListLogByType(String tenantId, StreamLogQueryArg arg, String lang) {
        SyncPloyDetailEntity entity = null;
        if (StringUtils.isNotBlank(arg.getPloyDetailId())) {
            entity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getPloyDetailId());
        }
        if (entity == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (arg.getEndTime() == null) {
            arg.setEndTime(System.currentTimeMillis());
        }
        if (arg.getStartTime() == null) {
            arg.setStartTime(arg.getStartTime() - 1000 * 60 * 60 * 24 * 7);
        }
        List<String> sourceObjectApiNames = Lists.newArrayList(), destObjectApiNames = Lists.newArrayList();
        if (StringUtils.isBlank(arg.getObjApiName())) {
            sourceObjectApiNames.add(entity.getSourceObjectApiName());
            destObjectApiNames.add(entity.getDestObjectApiName());
            if (entity.getDetailObjectMappings() != null) {
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                    sourceObjectApiNames.add(detailObjectMappingData.getSourceObjectApiName());
                    destObjectApiNames.add(detailObjectMappingData.getDestObjectApiName());
                }
            }
        } else {
            if (arg.getObjApiName().equals(entity.getSourceObjectApiName())) {
                sourceObjectApiNames.add(entity.getSourceObjectApiName());
                destObjectApiNames.add(entity.getDestObjectApiName());
            } else {
                if (entity.getDetailObjectMappings() != null) {
                    for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMappingData : entity.getDetailObjectMappings()) {
                        if (arg.getObjApiName().equals(detailObjectMappingData.getSourceObjectApiName())) {
                            sourceObjectApiNames.add(detailObjectMappingData.getSourceObjectApiName());
                            destObjectApiNames.add(detailObjectMappingData.getDestObjectApiName());
                            break;
                        }
                    }
                }
            }
        }
        Long startLogTime = arg.getStartTime(), endLogTime = arg.getEndTime();
        Integer limit = arg.getPageSize(), offset = (arg.getPageNum() - 1) * arg.getPageSize();
        //同步快照日志
        Page<CHSyncDataEntity> syncDataEntities = chSyncDataManager.listByPage(tenantId, sourceObjectApiNames,
                destObjectApiNames, arg.getSyncLogId(), arg.getDataId(), arg.getDataNum(), startLogTime, endLogTime, offset, limit);
        return convert2PageResult(tenantId,lang,syncDataEntities,entity);
    }

    @Override
    public Result<?> queryListByLogIds(final String tenantId, final String realObjApiName, final List<String> logIds, final StreamLogQueryArg streamLogQueryArg, String lang) {
        return Result.newSuccess();
    }

    public Result<Page<SyncDataListResult>> convert2PageResult(String tenantId, String lang,final Page<CHSyncDataEntity> chSyncDataEntityPage,SyncPloyDetailEntity entity) {
        if (chSyncDataEntityPage == null || chSyncDataEntityPage.getData() == null) {
            return Result.newSuccess();
        }
        boolean is2Erp = TenantType.CRM == entity.getSourceTenantType();
        Map<String, String> employeeIdToNameMap = Maps.newHashMap();
        if (is2Erp) {
            List<Integer> operatorIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(chSyncDataEntityPage.getData())) {
                for(CHSyncDataEntity syncData :chSyncDataEntityPage.getData()){
                    if(StringUtils.isNotEmpty(syncData.getOperatorId())){
                        continue;
                    }
                    try{
                        operatorIds.add(Integer.parseInt(syncData.getOperatorId()));
                    }catch (Exception e){

                    }
                }
            }
            if (CollectionUtils.isNotEmpty(operatorIds)) {
                BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
                batchGetEmployeeDtoArg.setEnterpriseId(Integer.parseInt(tenantId));
                batchGetEmployeeDtoArg.setCurrentEmployeeId(1000);
                batchGetEmployeeDtoArg.setEmployeeIds(operatorIds);
                List<EmployeeDto> employeeDtos = employeeService.batchGetEmployeeDto(batchGetEmployeeDtoArg).getEmployees();
                employeeIdToNameMap = employeeDtos.stream().collect(Collectors.toMap(x -> String.valueOf(x.getEmployeeId()), EmployeeDto::getName));
            }
        }
        List<SyncDataListResult> syncDataListResultResults = Lists.newArrayList();
        for (CHSyncDataEntity chSyncDataEntity : chSyncDataEntityPage.getData()) {
            SyncDataListResult syncDataListResultResult = new SyncDataListResult();
            syncDataListResultResult.setSyncLogId(chSyncDataEntity.getLogId());
            syncDataListResultResult.setSourceDataId(chSyncDataEntity.getSourceDataId());
            syncDataListResultResult.setSourceDataName(chSyncDataEntity.getErpTempDataDataNumber());
            if (StringUtils.isBlank(chSyncDataEntity.getErpTempDataDataNumber()) && StringUtils.isNotBlank(chSyncDataEntity.getSourceData())) {
                ObjectData objectData = JacksonUtil.fromJson(chSyncDataEntity.getSourceData(), ObjectData.class);
                syncDataListResultResult.setSourceDataName(objectData.getName());
            }
            syncDataListResultResult.setDestDataId(chSyncDataEntity.getDestDataId());
            syncDataListResultResult.setId(chSyncDataEntity.getId());
            syncDataListResultResult.setSyncDataId(chSyncDataEntity.getId());

            syncDataListResultResult.setOperatorId(chSyncDataEntity.getOperatorId());
            if (employeeIdToNameMap.get(chSyncDataEntity.getOperatorId()) != null) {
                syncDataListResultResult.setOperatorName(employeeIdToNameMap.get(chSyncDataEntity.getOperatorId()));
            } else {
                syncDataListResultResult.setOperatorName(i18NStringManager.get(I18NStringEnum.s988, lang, tenantId));
            }
            syncDataListResultResult.setSourceEventType(chSyncDataEntity.getSourceEventType());
            syncDataListResultResult.setSourceEventTypeName(EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, chSyncDataEntity.getSourceEventType()));
            syncDataListResultResult.setDestEventType(chSyncDataEntity.getDestEventType());
            String dataReceiveType = getDataReceiveType(tenantId, lang, chSyncDataEntity);
            if (chSyncDataEntity.getDestEventType() != null) {
                syncDataListResultResult.setDestEventTypeName(dataReceiveType + EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, chSyncDataEntity.getDestEventType()));
            } else {
                syncDataListResultResult.setDestEventTypeName(dataReceiveType + EventTypeEnum.getNameByType(i18NStringManager, lang, tenantId, chSyncDataEntity.getSourceEventType()));
            }
            syncDataListResultResult.setRemark(chSyncDataEntity.getRemark());
            syncDataListResultResult.setUpdateTime(chSyncDataEntity.getUpdateTime().getTime());
            syncDataListResultResult.setStatus(SyncStatusEnum.getBySyncDataStatus(chSyncDataEntity.getSyncDataStatus()).getStatus());
            syncDataListResultResult.setStatusName(SyncStatusEnum.getBySyncDataStatus(chSyncDataEntity.getSyncDataStatus()).getNameByLang(i18NStringManager, lang, tenantId));
            syncDataListResultResult.setSyncLogId(chSyncDataEntity.getLogId());
            syncDataListResultResults.add(syncDataListResultResult);
        }
        Page<SyncDataListResult> syncDataListResultPage = new Page<>();
        syncDataListResultPage.setData(syncDataListResultResults);
        syncDataListResultPage.setTotalNum(chSyncDataEntityPage.getTotalNum());
        syncDataListResultPage.setHasNext(chSyncDataEntityPage.isHasNext());
        return Result.newSuccess(syncDataListResultPage);
    }

    private String getDataReceiveType(String tenantId, String lang, CHSyncDataEntity syncDataEntity) {
        String dataReceiveType = "";
        if (syncDataEntity.getDataReceiveType() != null) {
            DataReceiveTypeEnum dataReceiveTypeEnum = DataReceiveTypeEnum.getByType(syncDataEntity.getDataReceiveType());
            if (dataReceiveTypeEnum != null&&!dataReceiveTypeEnum.equals(DataReceiveTypeEnum.OTHER)) {
                dataReceiveType = i18NStringManager.get(dataReceiveTypeEnum.getI18nKey(), lang, tenantId, dataReceiveTypeEnum.getName()) + i18NStringManager.get(I18NStringEnum.s5108, lang, tenantId) + "-";
            }
        }
        return dataReceiveType;
    }

    @Override
    public Result<?> queryLogDataById(String tenantId, StreamLogQueryArg.SyncLogDataArg syncLogDataArg, String lang, SyncLog syncLog) {
        if (syncLog == null) {
            //syncLog = chSyncDataManager.getLogById(tenantId, syncLogDataArg.getId(), syncLogDataArg.getNodeEnum());
        }
        if (ObjectUtils.isNotEmpty(syncLog)) {
            StreamLogDetailArg streamLogDetailArg = new StreamLogDetailArg();
            streamLogDetailArg.setSyncLogId(syncLog.getLogId());
            streamLogDetailArg.setTenantId(tenantId);
            ErpTempData erpTempData = Optional.ofNullable(syncLog.getErpTempData()).orElseGet(() -> new ErpTempData());
            CrmTriggerLogResult crmTriggerLogResultResult = new CrmTriggerLogResult();
            crmTriggerLogResultResult.setSyncLogId(syncLog.getLogId());
            crmTriggerLogResultResult.setDataId(erpTempData.getDataId());
            crmTriggerLogResultResult.setCreateTime(syncLog.getCreateTime().getTime());
            crmTriggerLogResultResult.setDataNumber(erpTempData.getDataNumber());
            crmTriggerLogResultResult.setId(String.valueOf(syncLog.getId()));
            crmTriggerLogResultResult.setData(erpTempData.getDataBody());
            return Result.newSuccess(crmTriggerLogResultResult);
        }
        return Result.newSuccess();
    }

}
