package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:01 2022/6/14
 * @Desc:
 */
@Data
@ApiModel
public class CopyBetweenDbArg implements Serializable {
    @ApiModelProperty("是否走一般删除目标数据逻辑")
    private Boolean deleteDest=false;
    @ApiModelProperty("源数据库路由")
    private String sourceDbRoute;
    @ApiModelProperty("目标数据库路由")
    private String destDbRoute;
    @ApiModelProperty("企业id")
    private String tenantId;
    @ApiModelProperty("表名称")
    private List<String> tableNames;
}
