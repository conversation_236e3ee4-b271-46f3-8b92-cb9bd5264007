package com.fxiaoke.open.oasyncdata.mongo;


import com.fxiaoke.open.oasyncdata.constant.EventTypeEnum;
import com.fxiaoke.open.oasyncdata.constant.ObjectApiEnum;
import com.fxiaoke.open.oasyncdata.db.entity.mongo.OASyncLogMappingDoc;
import com.fxiaoke.open.oasyncdata.db.redis.RedisDataSource;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import com.fxiaoke.open.oasyncdata.model.QueryOASyncLogArg;
import com.google.common.collect.Lists;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @Date: 15:32 2021/7/23
 * @Desc:
 */
@Slf4j
@Component
public class OASyncLogMappingsDao {
    @Autowired
    private OASyncLogMongoStore oaSyncLogMongoStore;
    @Autowired
    private RedisDataSource redisDataSource;
    private static final String data_id = "dataId";
    private static final String data_name = "dataName";
    private static final String object_name = "objectName";
    private static final String status = "status";
    private static final String message = "message";
    private static final String object_api_name = "objApiName";
    private static final String business_id = "businessId";
    private static final String event_type = "eventType";
    private static final String title = "title";
    private static final String receiver_id = "receiverId";
    private static final String create_time = "createTime";
    private static final String update_time = "updateTime";
    private static final String last_sync_log_id = "lastSyncLogId";
    private static final String data_json = "dataJson";
    private static final String tenant_id = "tenantId";
    private static final String data_center_id = "dataCenterId";



    public void batchInsert(String tenantId, List<OASyncLogMappingDoc> syncLogs) {
        UpdateOneModel<OASyncLogMappingDoc> updateOneModel;
        List<WriteModel<OASyncLogMappingDoc>> requests = Lists.newArrayList();
        UpdateOptions updateOption = new UpdateOptions().upsert(true);
        for (OASyncLogMappingDoc syncLog : syncLogs) {
            updateOneModel = new UpdateOneModel<>(OAMappingDataHelper.updateByEiObjDataNum(syncLog), OAMappingDataHelper.upsert(syncLog), updateOption);
            requests.add(updateOneModel);
        }
        BulkWriteOptions bulkOption = new BulkWriteOptions().ordered(false);

        MongoCollection<OASyncLogMappingDoc> collection = oaSyncLogMongoStore.getSyncLogMappingCollection(tenantId);
        BulkWriteResult bulkWriteResult;
        try {
            bulkWriteResult = collection.bulkWrite(requests, bulkOption);
        } finally {
        }
        if (!bulkWriteResult.wasAcknowledged()) {
                throw new RuntimeException("upsert failed. result:" + bulkWriteResult);
        }
        return;
    }
    public List<OASyncLogMappingDoc> pageByFilters(QueryOASyncLogArg arg) {
        List<OASyncLogMappingDoc> result = new ArrayList<>();
        MongoCollection<OASyncLogMappingDoc> collection = oaSyncLogMongoStore.getSyncLogMappingCollection(arg.getTenantId());
        Bson filter = buildFilter(arg);
        if (StringUtils.isNotBlank(arg.getStatus())) {
            filter=Filters.and(filter, Filters.eq(status, arg.getStatus()));
        }
        if(ObjectUtils.isNotEmpty(arg.getBusinessType())&&CollectionUtils.isNotEmpty(ObjectApiEnum.convertObjByCrmType(arg.getBusinessType()))){
            filter=Filters.and(filter,Filters.in(object_api_name, ObjectApiEnum.convertObjByCrmType(arg.getBusinessType())));
        }
        int offset=(arg.getPage()-1)*arg.getPageSize();
        collection.find(filter)
                .sort(Sorts.descending("updateTime"))
                .skip(offset)
                .limit(arg.getPageSize())
                .into(result);
        return result;
    }

    public List<OASyncLogMappingDoc> pageByFiltersByFilterInStatus(QueryOASyncLogArg arg,List<String> statusValue) {
        List<OASyncLogMappingDoc> result = new ArrayList<>();
        MongoCollection<OASyncLogMappingDoc> collection = oaSyncLogMongoStore.getSyncLogMappingCollection(arg.getTenantId());
        Bson filter = buildFilter(arg);
        if (CollectionUtils.isNotEmpty(statusValue)) {
            filter=Filters.and(filter, Filters.in(status, statusValue));
        }
        int offset=(arg.getPage()-1)*arg.getPageSize();
        collection.find(filter)
                .sort(Sorts.descending("updateTime"))
                .skip(offset)
                .limit(arg.getPageSize())
                .into(result);
        return result;
    }

    public List<OASyncLogMappingDoc> queryFailData(QueryOASyncLogArg arg, Date gteTime) {
        List<OASyncLogMappingDoc> result = new ArrayList<>();
        Date nowDate=new Date();
        MongoCollection<OASyncLogMappingDoc> collection = oaSyncLogMongoStore.getSyncLogMappingCollection(arg.getTenantId());
        //无需同步，成功的
        Bson filter = Filters.and(buildFilter(arg), Filters.nin(status, "1","4"),Filters.gte("createTime",gteTime),Filters.lt("createTime",nowDate));
        int offset=(arg.getPage()-1)*arg.getPageSize();
        long count = collection.countDocuments(filter);
        collection.find(filter)
                .sort(Sorts.descending("_id"))
                .skip(offset)
                .limit(arg.getPageSize())
                .into(result);
        log.info("query fail count:{}.tenant:{}.size:{}:{}.{}",count,arg.getTenantId(),result.size(),offset,arg.getPageSize());
        return result;
    }

    public long countMappings(QueryOASyncLogArg arg) {
        MongoCollection<OASyncLogMappingDoc> collection = oaSyncLogMongoStore.getSyncLogMappingCollection(arg.getTenantId());
        Bson filter = buildFilter(arg);
        if (StringUtils.isNotBlank(arg.getStatus())) {
            filter=Filters.and(filter, Filters.eq(status, arg.getStatus()));
        }
        if(ObjectUtils.isNotEmpty(arg.getBusinessType())&&CollectionUtils.isNotEmpty(ObjectApiEnum.convertObjByCrmType(arg.getBusinessType()))){
            filter=Filters.and(filter,Filters.in(object_api_name, ObjectApiEnum.convertObjByCrmType(arg.getBusinessType())));
        }
        long size = collection.countDocuments(filter);
        return size;
    }

    public long updateByObjectId(String tenantId, ObjectId objectId, OASyncLogMappingDoc oaSyncLogSnapshotDoc){

        Bson queryFilter=Filters.and(Filters.eq(tenant_id, tenantId),Filters.eq("_id", objectId));

        UpdateResult updateResult = oaSyncLogMongoStore.getSyncLogMappingCollection(tenantId).updateMany(queryFilter, buildUpdate(oaSyncLogSnapshotDoc));
        return updateResult.getModifiedCount();
    }

    public OASyncLogMappingDoc getById(String tenantId,String dataCenterId,ObjectId objectId){
        Bson queryFilter=Filters.and(Filters.eq(tenant_id, tenantId),Filters.eq("_id", objectId));
        if(ConfigCenter.FILTER_LOG_TENANT_ID.contains(tenantId)){
            queryFilter=Filters.and(queryFilter, Filters.eq(data_center_id, dataCenterId));
        }
        List<OASyncLogMappingDoc> result = new ArrayList<>();
        oaSyncLogMongoStore.getSyncLogMappingCollection(tenantId).find(queryFilter).limit(1).into(result);
        if(CollectionUtils.isNotEmpty(result)){
            return result.get(0);
        }
        return null;
    }
    public OASyncLogMappingDoc findOneDataByDataId(String tenantId,String dataCenterId,String objApiName,String dataId){
        Bson queryFilter=Filters.and(Filters.eq(tenant_id, tenantId),Filters.eq(object_api_name, objApiName),Filters.eq(event_type, EventTypeEnum.ADD.getType().toString()),Filters.eq(data_id, dataId));
        if(ConfigCenter.FILTER_LOG_TENANT_ID.contains(tenantId)){
            queryFilter=Filters.and(queryFilter, Filters.eq(data_center_id, dataCenterId));
        }
        List<OASyncLogMappingDoc> result = new ArrayList<>();
        oaSyncLogMongoStore.getSyncLogMappingCollection(tenantId).find(queryFilter).limit(1).into(result);
        if(CollectionUtils.isNotEmpty(result)){
            return result.get(0);
        }
        return null;
    }

    /**
     * 不想用顺序消费，顺序消费可能会阻塞其他企业。现在先初步判断是否已经有创建的数据，是则即可处理。
     * @param tenantId
     * @param objApiName
     * @param sourceDataId
     * @param receiverId
     * @return
     */
    public List<OASyncLogMappingDoc> findOneDataByReceiver(String tenantId,String dataCenterId,String objApiName,String sourceDataId,List<String> receiverId){
        Bson queryFilter=Filters.and(Filters.eq(tenant_id, tenantId),
                Filters.eq(object_api_name, objApiName),Filters.eq(event_type, EventTypeEnum.ADD.getType()),
                Filters.eq(data_id, sourceDataId),
                Filters.in(receiver_id, receiverId));
        if(ConfigCenter.FILTER_LOG_TENANT_ID.contains(tenantId)){
            queryFilter=Filters.and(queryFilter, Filters.eq(data_center_id, dataCenterId));
        }
        List<OASyncLogMappingDoc> result = new ArrayList<>();
        oaSyncLogMongoStore.getSyncLogMappingCollection(tenantId).find(queryFilter).limit(1).into(result);
        if(CollectionUtils.isNotEmpty(result)){
            return result;
        }
        return null;
    }


    public long deleteByObjectId(String tenantId, ObjectId objectId){

        Bson deleteBson=Filters.and(Filters.eq(tenant_id, tenantId),Filters.eq("_id", objectId));
        DeleteResult deleteResult = oaSyncLogMongoStore.getSyncLogMappingCollection(tenantId).deleteMany(deleteBson);
        return deleteResult.getDeletedCount();
    }

    private Bson buildUpdate(OASyncLogMappingDoc oaSyncLogSnapshotDoc){
        List<Bson> updates = new ArrayList<>();

        if(oaSyncLogSnapshotDoc.getStatus()!=null){
            updates.add(set(status,oaSyncLogSnapshotDoc.getStatus()));
        }
        if(oaSyncLogSnapshotDoc.getMessage()!=null){
            updates.add(set(message,oaSyncLogSnapshotDoc.getMessage()));
        }
        if(oaSyncLogSnapshotDoc.getLastSyncLogId()!=null){
            updates.add(set(last_sync_log_id,oaSyncLogSnapshotDoc.getLastSyncLogId()));
        }
        return Updates.combine(updates);
    }



    private Bson buildFilter(QueryOASyncLogArg arg) {
        List<Bson> filters = Lists.newArrayList(Filters.eq(tenant_id, arg.getTenantId()));
        if (arg.getDataId() != null ) {
            filters.add(Filters.eq(data_id, arg.getDataId()));
        }
        if (arg.getDataCenterId() != null ) {
            filters.add(Filters.eq(data_center_id, arg.getDataCenterId()));
        }
        if (arg.getDataName() != null ) {
            filters.add(Filters.eq(data_name, arg.getDataName()));
        }
        if (arg.getReceiverId() != null ) {
            filters.add(Filters.eq(receiver_id, arg.getReceiverId()));
        }
        if (arg.getObjApiName() != null ) {
            filters.add(Filters.eq(object_api_name, arg.getObjApiName()));
        }
        if (arg.getEventType() != null ) {
            filters.add(Filters.eq(event_type, arg.getEventType()));
        }
        if (arg.getStartTime() != null) {
            filters.add(Filters.gte(update_time, new Date(arg.getStartTime())));
        }
        if (arg.getEndTime() != null) {
            filters.add(Filters.lte(update_time, new Date(arg.getEndTime())));
        }

        Bson filter = Filters.and(filters);
        return filter;
    }
}
