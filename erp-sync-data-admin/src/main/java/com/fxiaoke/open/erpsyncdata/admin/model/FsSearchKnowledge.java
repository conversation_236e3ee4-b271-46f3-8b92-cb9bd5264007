package com.fxiaoke.open.erpsyncdata.admin.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18 11:22:33
 */
public interface FsSearchKnowledge {
    @Data
    class Arg {
        @ApiModelProperty("搜索")
        private String keyword;
        @ApiModelProperty("数量")
        private Integer size;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @ApiModelProperty("知识库id")
        private List<RecommendSolution> recommendSolutions;
    }
}
