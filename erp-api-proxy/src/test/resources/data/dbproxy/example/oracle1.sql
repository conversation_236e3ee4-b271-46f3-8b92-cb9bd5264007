-- oracle建表test5，字段为日期，时间，名称，自增主键
-- 创建序列
drop sequence  test5_seq;
CREATE SEQUENCE test5_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- 创建表
CREATE TABLE test5
(
    id         NUMBER PRIMARY KEY,
    date1 DATE,
    time1 TIMESTAMP,
    name       VARCHAR2(100)
);

-- 创建触发器
Drop trigger test5_before_insert;
CREATE OR REPLACE TRIGGER test5_before_insert
    BEFORE INSERT
    ON test5
    FOR EACH ROW
BEGIN
    SELECT test5_seq.NEXTVAL
    INTO :new.id
    FROM dual;
END;

-- 使用PL/SQL在Oracle中连续插入100条记录
BEGIN
    FOR i IN 1..5
        LOOP
            INSERT INTO test5 (date1, time1, name)
            VALUES (TO_DATE('20240511', 'YYYYMMDD'), SYSTIMESTAMP, 'a' || TO_CHAR(i));
        END LOOP;
    COMMIT;
END;


BEGIN
    FOR i IN 1..15
        LOOP
            INSERT INTO test5 (date1, time1, name)
            VALUES (TO_DATE('20240412', 'YYYYMMDD'), SYSTIMESTAMP, 'b' || TO_CHAR(i));
        END LOOP;
    COMMIT;
END;


BEGIN
    FOR i IN 1..25
        LOOP
            INSERT INTO test5 (date1, time1, name)
            VALUES (TO_DATE('20240413', 'YYYYMMDD'), SYSTIMESTAMP, 'c' || TO_CHAR(i));
        END LOOP;
    COMMIT;
END;

SELECT row_.*, rownum rownum_
from (select *
      from test5
      where date1 = TO_DATE('2024-05-11 17:02:56', 'YYYY-MM-DD HH24:MI:SS')
      ) row_
where rownum <= 10;

-- 或者使用DBMS_SESSION包启用跟踪
EXEC DBMS_SESSION.SET_SQL_TRACE(TRUE);