package com.fxiaoke.open.erpsyncdata.monitor.handler

import com.fxiaoke.open.erpsyncdata.monitor.BaseSpockTest
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class AlertRecoverAggregationHandlerTest extends BaseSpockTest {
    @Autowired
    private AlertRecoverAggregationHandler alertRecoverAggregationHandler;

    @Test
    void syncDataFailedAlertRecover() {
        alertRecoverAggregationHandler.syncDataFailedAlertRecover()
    }

    @Test
    void syncDataFailedAlertAggregation() {
        alertRecoverAggregationHandler.syncDataFailedAlertAggregation(100)
    }

    @Test
    void pollingErpAlertAggregation() {
        alertRecoverAggregationHandler.pollingErpAlertAggregation(100)
    }
}
