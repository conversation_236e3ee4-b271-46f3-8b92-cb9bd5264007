package com.fxiaoke.open.oasyncdata.db.util;


import com.fxiaoke.open.oasyncdata.constant.CrmResultCodeEnum;
import com.fxiaoke.open.oasyncdata.model.HttpResponse;
import com.fxiaoke.open.oasyncdata.model.HttpResponseMessage;
import com.fxiaoke.open.oasyncdata.result.CrmObjectDataResult;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * Crm对象通用工具类
 *
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
@Slf4j
public class CrmObjectCommonUtils {

    private static final int SUCCESS_CODE = 0;

    /**
     * 解析返回response
     *
     * @param httpResponseMessage
     * @param methodName
     * @return
     */
    public static CrmObjectDataResult resolveResponse(HttpResponseMessage httpResponseMessage, String methodName) {
        CrmObjectDataResult crmObjectDataResult = new CrmObjectDataResult();
        Integer errCode;
        String errDetail;
        String errMessage;
        if (String.valueOf(HttpStatus.SC_OK).equals(httpResponseMessage.getHttpcode())
                || StringUtils.isNotBlank(httpResponseMessage.getContent())) {
            String content = httpResponseMessage.getContent();
            try {
                errCode = JsonPath.read(content, "$.errCode");
                errMessage = JsonPath.read(content, "$.errMessage");
            } catch (Exception e) {
                errCode = Integer.valueOf(httpResponseMessage.getHttpcode());
                errMessage = httpResponseMessage.getMessage();
            }
            String errorDetailMsg = "{} JsonPath.read errDetail exception, exceptionMessage{}.";
            errDetail = CrmObjectCommonUtils.resolveDetail(content, errCode, errMessage, errorDetailMsg, methodName);
        } else {
            errCode = Integer.valueOf(httpResponseMessage.getHttpcode());
            errDetail = "system error , " + httpResponseMessage.getMessage();
            errMessage = httpResponseMessage.getMessage();
        }
        crmObjectDataResult.setErrorCode(errCode);
        crmObjectDataResult.setErrorMessage(errMessage);
        return crmObjectDataResult;
    }

    /**
     * 解析ErrCode格式的response
     *
     * @param response
     * @return
     */
    public static CrmObjectDataResult resolveErrCodeFormatResponse(HttpResponse response) {
        if (Objects.isNull(response)) {
            return new CrmObjectDataResult(CrmResultCodeEnum.SYSTEM_ERROR);
        }
        Integer errorCode = null;
        String errorMessage = null;
        String errorDetail = null;
        if (StringUtils.isNotEmpty(response.getBody())) {
            DocumentContext documentContext = JsonPath.using(Configuration.defaultConfiguration()
                    .addOptions(Option.DEFAULT_PATH_LEAF_TO_NULL, Option.SUPPRESS_EXCEPTIONS)).parse(response.getBody());
            errorCode = documentContext.read("$.errCode");
            errorMessage = documentContext.read("$.errMessage");
            errorDetail = documentContext.read("$.errDetail");
            if (Objects.isNull(errorDetail)) {
                errorDetail = documentContext.read("$.result.errorDetail");
            }
        }
        if (Objects.isNull(errorCode)) {
            errorCode = response.getCode();
            errorMessage = response.getMessage();
        }
        if (NumberUtils.INTEGER_ZERO.equals(errorCode)) {
            errorDetail = "success";
        }
        return new CrmObjectDataResult(errorCode, errorMessage, errorDetail);
    }

    /**
     * 解析detail
     *
     * @param content
     * @param errCode
     * @param errMessage
     * @param errorDetailMsg
     * @param methodName
     */
    public static String resolveDetail(String content, Integer errCode, String errMessage, String errorDetailMsg,
                                       String methodName) {
        String errDetail = null;
        try {
            errDetail = JsonPath.read(content, "$.errDetail");
        } catch (Exception e) {
            try {
                errDetail = JsonPath.read(content, "$.result.errorDetail");
            } catch (Exception e1) {
                if (Objects.equals(0, errCode)) {
                    errDetail = "success";
                } else {
                    log.debug(errorDetailMsg, methodName, e.getMessage());
                }
                // 特殊处理提示信息
                String tempMessage = null;
                try {
                    tempMessage = JsonPath.read(content, "$.result.message");
                } catch (Exception e2) {
                }
                if (StringUtils.isNotBlank(tempMessage) && !errMessage.equals(tempMessage)) {
                    errDetail = errDetail + ", " + tempMessage;
                }
            }
        }
        return errDetail;
    }

    /**
     * 解析验证规则的结果
     *
     * @param validationRule
     * @return
     */
    public static String parseValidationRule(Map<String, Object> validationRule) {
        String messages = StringUtils.EMPTY;
        if (Objects.nonNull(validationRule)) {
            List<String> blockMessages = (List<String>)validationRule.get("blockMessages");
            if (CollectionUtils.isNotEmpty(blockMessages)) {
                messages = messages + Joiner.on(",").skipNulls().join(blockMessages);
            }
            List<String> nonBlockMessages = (List<String>)validationRule.get("nonBlockMessages");
            if (CollectionUtils.isNotEmpty(nonBlockMessages)) {
                messages = messages + Joiner.on(",").skipNulls().join(nonBlockMessages);
            }
        }
        return messages;
    }

    /**
     * 通用resultMap校验
     *
     * @param crmObjectDataResult
     * @return true:成功，false失败
     */
    public static boolean isResultMapSuccess(CrmObjectDataResult crmObjectDataResult) {
        return Objects.equals(SUCCESS_CODE, crmObjectDataResult.getErrorCode());
    }
    /**
     * 获取从对象的id列表
     *
     * @param newObjectData
     * @return
     */
    public static Map<String, Object> getAddObjectDetailDataIds(Map<String, List<Map>> newObjectData) {
        if (MapUtils.isNotEmpty(newObjectData)) {
            Map<String, Object> detailObjects = Maps.newHashMapWithExpectedSize(newObjectData.size());
            for (Map.Entry<String, List<Map>> entry : newObjectData.entrySet()) {
                List<Map> detailObjectList = entry.getValue();
                if (CollectionUtils.isNotEmpty(detailObjectList)) {
                    List<String> detailObjectIdList = Lists.newArrayListWithCapacity(detailObjectList.size());
                    for (Map detailObject : detailObjectList) {
                        detailObjectIdList.add((String)detailObject.get("_id"));
                    }
                    detailObjects.put(entry.getKey(), detailObjectIdList);
                }
            }
            return detailObjects;
        } else {
            return null;
        }
    }

}
