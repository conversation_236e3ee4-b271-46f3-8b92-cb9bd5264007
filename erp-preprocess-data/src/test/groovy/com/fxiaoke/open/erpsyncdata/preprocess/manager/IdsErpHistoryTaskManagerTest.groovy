package com.fxiaoke.open.erpsyncdata.preprocess.manager

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager
import com.fxiaoke.open.erpsyncdata.preprocess.impl.ErpDataPreprocessServiceImpl
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager
import com.google.common.collect.Maps
import org.assertj.core.util.Lists
import org.assertj.core.util.Sets
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class IdsErpHistoryTaskManagerTest extends Specification {
    ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity()
    IdsErpHistoryTaskManager manager
    Map<String,Object> args= Maps.newHashMap()

    def setup() {
        def syncLogManager = Mock(SyncLogManager) {
            initLogId(*_) >> { return "logId" }
        }
        def send2DispatcherSpeedLimitManager = Mock(Send2DispatcherSpeedLimitManager) {
            getNumPerInterval(*_) >> 100L
            needSendDispatcherNum(*_) >> {return args.get("num")}
            // 其余方法为空实现
        }

        def syncPloyDetailSnapshotDao = Mock(SyncPloyDetailSnapshotDao) {
            setTenantId(*_) >> it
            listNewestBySourceTenantIdAndSrouceObjectApiName(*_) >> {
                SyncPloyDetailSnapshotEntity entity = new SyncPloyDetailSnapshotEntity()
                entity.setDestObjectApiName("destObj1")
                return Lists.newArrayList(entity)
            }
        }
        def erpHistoryDataTaskDao = Mock(ErpHistoryDataTaskDao) {
            setTenantId(*_) >> it
            findById(*_) >> entity
        }
        def pollingDataSpeedRateLimitManager = Mock(PollingDataSpeedRateLimitManager) {
            acquireQueryErpHistoryData(*_) >> { return null }
        }
        def i18NStringManager = Mock(I18NStringManager) {
            getByEi(*_) >> "测试"
            // 其余方法为空实现
        }
        def configCenterConfig = Mock(ConfigCenterConfig) {
            getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET() >> Sets.newHashSet()
        }
        def erpDataPreprocessService = Mock(ErpDataPreprocessServiceImpl) {
            getReSyncObjDataById(*_) >> {
                return Result.newSuccess(Lists.newArrayList())
            }
        }
        def probeErpDataManager = Mock(ProbeErpDataManager) {
            asyncSendErpDataMq(*_) >> { return 1 }
        }
        manager = new IdsErpHistoryTaskManager(
                erpHistoryDataTaskDao: erpHistoryDataTaskDao,
                syncLogManager: syncLogManager,
                configCenterConfig: configCenterConfig,
                "send2DispatcherSpeedLimitManager": send2DispatcherSpeedLimitManager,
                "syncPloyDetailSnapshotDao": syncPloyDetailSnapshotDao,
                "i18NStringManager": i18NStringManager,
                pollingDataSpeedRateLimitManager: pollingDataSpeedRateLimitManager,
                erpDataPreprocessService: erpDataPreprocessService,
                probeErpDataManager: probeErpDataManager
        )
    }

    def "SyncErpHistoryDataByIds"() {
        args.put("num",needNum)
        entity.setNeedStop(stop)
        Result<Void> result = manager.syncErpHistoryDataByIds("1234", entity, dataIds, new StringBuilder())
        expect:
        result.getErrCode() == expectResult
        where:
        expectResult || needNum || stop  || dataIds
        "s106240000" || -1L     || false || []
        "s306240112" || -1L     || false || ["1234"]
        "s306240238" || 10L     || true  || ["123"]
    }

}
