package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * 测试syncdata dao
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/5/10
 */
@Ignore
@Slf4j
public class SyncDataFixDaoImplTest extends BaseTest {
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    private String tenantId;
    private SyncDataEntity mainSyncData;
    private SyncDataEntity detailSyncData;

    @Before
    public void setUp() throws Exception {
        tenantId = "84307";
        //从数据库取样板数据
        mainSyncData = syncDataFixDao.getById(tenantId, "83357339fc044e5db82781e7c0fa9690");
        detailSyncData = syncDataFixDao.getById(tenantId, "104ac4d0ed65441bbf0629c797fde7af");
        mainSyncData.convertAndSetId(new ObjectId());
        mainSyncData.setStatus(SyncDataStatusEnum.BE_PROCESS.getStatus());
        detailSyncData.convertAndSetId(new ObjectId());
        mainSyncData.getSourceDetailSyncDataIds().forEach((k, v) -> v.set(0, detailSyncData.getId()));
    }


    /**
     * 启用缓存测试
     */
    @Test
    public void testAllEnableCache() {
        boolean b = execute();
    }

    /**
     * 停用用缓存测试
     */
    @Test
    public void testAllDisableCache() {
        boolean b = execute();
    }

    private boolean execute() {
        syncDataFixDao.setTenantId("81243");

       //        int updateRemark = syncDataFixDao.updateRemark(String.valueOf(81243), "630f1c191c76fb601945f75d", "备注更新1");
        //插入
        syncDataFixDao.insertCache(mainSyncData);
        syncDataFixDao.insertCache(detailSyncData);

        ObjectData destData = mainSyncData.getDestData();
        ObjectData sourceData = mainSyncData.getSourceData();
        String destDataId = IdUtil.nanoId();
        String mainId = mainSyncData.getId();
        List<String> syncDataIds = Lists.newArrayList(mainId, detailSyncData.getId());
        //缓存查
        List<SyncDataEntity> syncDataEntities = syncDataFixDao.listByIdsCachePri(tenantId, syncDataIds);
        Assert.assertEquals(syncDataEntities.size(), syncDataIds.size());
        Assert.assertNotNull(syncDataFixDao.getById(tenantId, mainId));

        //update
        destData.put("updateTest", true);
        Assert.assertEquals(syncDataFixDao.updateDestDataById(tenantId, mainId, destData), 1);
//        int expected = syncDataFixDao.updateDestEventTypeAndDestDataIdAndStatus(tenantId, mainId, EventTypeEnum.UPDATE.getType(), destDataId, SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.BE_WRITE.getStatus(), System.currentTimeMillis());
//        Assert.assertEquals(expected, 1);
        sourceData.put("updateSrc",true);
        Assert.assertEquals(syncDataFixDao.updateSourceDataById(tenantId, mainId, sourceData,2), 1);
//        int expected2 = syncDataFixDao.updateStatusAndDestDataId(tenantId, mainId, destDataId, SyncDataStatusEnum.BE_WRITE.getStatus(), SyncDataStatusEnum.BE_WRITE.getStatus(),"new remark", System.currentTimeMillis());
//        Assert.assertEquals(expected2, 1);
//        int expected3 = syncDataFixDao.updateStatus(tenantId, mainId, SyncDataStatusEnum.BE_WRITE.getStatus(),"new remark", System.currentTimeMillis());
//        Assert.assertEquals(expected3, 1);
//        int expected4 = syncDataFixDao.updateDestData(tenantId, mainId,destDataId,destData, SyncDataStatusEnum.BE_WRITE.getStatus(), SyncDataStatusEnum.WRITE_SUCCESS.getStatus(), System.currentTimeMillis());
//        Assert.assertEquals(expected4, 1);

        //移除
        syncDataFixDao.removeCacheAndInsertDb(tenantId);
        //休眠待异步线程插入数据
        boolean sleep = ThreadUtil.safeSleep(1000L);
        SyncDataEntity latestSyncData = getLatestSyncData();
        Assert.assertEquals(latestSyncData.getId(), mainId);
//        Assert.assertEquals(latestSyncData.getDestData().getId(), destDataId);
        Assert.assertTrue(latestSyncData.getDestData().getBoolean("updateTest"));
        Assert.assertTrue(latestSyncData.getSourceData().getBoolean("updateSrc"));
        Assert.assertEquals(latestSyncData.getRemark(),"new remark");
        syncDataEntities = syncDataFixDao.listByIds(tenantId, syncDataIds);
        Assert.assertEquals(syncDataEntities.size(), syncDataIds.size());
        syncDataEntities = syncDataFixDao.listSimpleByIds(tenantId, syncDataIds);
        Assert.assertEquals(syncDataEntities.size(), syncDataIds.size());
        return true;
    }

    private SyncDataEntity getLatestSyncData() {
        List<SyncDataEntity> syncDataHistory = syncDataFixDao.listBySourceData(tenantId,tenantId, Lists.newArrayList(mainSyncData.getSourceObjectApiName()), Lists.newArrayList(mainSyncData.getSourceDataId()),tenantId, Lists.newArrayList(mainSyncData.getDestObjectApiName()), System.currentTimeMillis()-1000*60*60*24L,System.currentTimeMillis());
        Assert.assertFalse(syncDataHistory.isEmpty());
        return syncDataHistory.get(0);
    }

    @Test
    public void getByIdFromDb() {
        testGetByIdFromDb();
    }

    private void testGetByIdFromDb() {
        StopWatch sw = new StopWatch();
        sw.start();
        SyncDataEntity byIdFromDb = syncDataFixDao.getByIdFromDb("84307", "627e1a1a80613018ab4b717a");
        sw.stop();
        System.out.println(sw.prettyPrint());
    }
}