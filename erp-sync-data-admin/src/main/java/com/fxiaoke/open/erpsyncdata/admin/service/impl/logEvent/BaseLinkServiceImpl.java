package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.FunctionQueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionResult;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/6/16 19:17
 * @Version 1.0
 */
@Service
@Slf4j
public class BaseLinkServiceImpl {

    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private CustomFunctionService customFunctionService;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    /**
     * 函数的基础方法
     *
     * @param tenantId
     * @param ployDetailId
     * @param functionType
     * @return
     */
    public Result<FunctionQueryResult> queryListLogByType(String tenantId, String ployDetailId, String functionType, String lang) {
        FunctionQueryResult functionQueryResult = new FunctionQueryResult();
        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, ployDetailId,lang);
        if (ObjectUtils.isEmpty(byIdWithCache.getData())) {
            return Result.newSuccess();
        }
        String functionApiName = null;
        if (functionType.equals(SyncLogTypeEnum.PRE_FUNCTION.getType())) {
            functionApiName = byIdWithCache.getData().getBeforeFuncApiName();

        } else if (functionType.equals(SyncLogTypeEnum.MID_FUNCTION.getType())) {
            functionApiName = byIdWithCache.getData().getDuringFuncApiName();
        } else {
            functionApiName = byIdWithCache.getData().getAfterFuncApiName();
        }
        if (StringUtils.isEmpty(functionApiName)) {
            return Result.newSuccess();
        }
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), 10000);
        FunctionServiceFindArg findArg = new FunctionServiceFindArg();
        findArg.setApiName(functionApiName);
        findArg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        Result<FunctionResult<FunctionServiceFindResult>> result = customFunctionService.find(headerObj, findArg);
        if (result.isSuccess()) {
            final FunctionServiceFindData findData = result.getData().getResult().getFunction();
            String functionName = findData.getFunctionName();
            functionQueryResult.setApiName(functionApiName);
            functionQueryResult.setFunctionName(functionName);
            functionQueryResult.setNameSpace(findData.getNameSpace());
            functionQueryResult.setReturnType("Map");
            return Result.newSuccess(functionQueryResult);
        }
        return Result.newError(ResultCodeEnum.FUNCTION_NOT_FOUND);
    }

    public boolean queryFunctionRunStats(String tenantId, String functionApiName, String logId) {
        try {
            ObjectData objectData = sfaApiManager.queryFunctionRunDetail(tenantId, functionApiName, logId);
            log.info("queryFunctionRunStats result:{}", objectData);
            boolean errCode = objectData.getBoolean("success");
            if (errCode) {
                String resultMessage = objectData.getString("results");
                List<ObjectData> results1 = JSONArray.parseArray(resultMessage, ObjectData.class);
                if (CollectionUtils.isNotEmpty(results1)) {
                    Object success = results1.get(0).get("success");
                    return Boolean.valueOf(success.toString());
                }
            }
        } catch (Exception e) {
            log.error("query function status fail :{}",e.getMessage());
        }
        return false;
    }


    /**
     * 链路日志，统一使用真实apiname.很多查询场景需要转换apiname.
     * needSourceApiName 在读取write的时候，因为是直接筛选接口日志的，所以需要转换成destapiname
     *
     * @return
     */
    public String getRealApiName(String tenantId, String streamId, boolean needSourceApiName, String lang) {
        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, streamId, lang);
        if (ObjectUtils.isEmpty(byIdWithCache.getData())) {
            return null;
        }
        String realObjApiName = "";
        SyncPloyDetailResult ployDetailResult = byIdWithCache.getData();
        if (needSourceApiName) {

            //crm源对象是真实对象，ERP的为中间对象，需要转换
            String srcObjApiName = ployDetailResult.getSourceObjectApiName();
            realObjApiName = srcObjApiName;
            if (ployDetailResult.getSourceTenantType().equals(TenantType.ERP)) {
                //换取正式apiName。读取接口日志
                realObjApiName = idFieldConvertManager.getRealObjApiName(tenantId, srcObjApiName);
            }
        }else{
            String destObjApiName = ployDetailResult.getDestObjectApiName();
            realObjApiName = destObjApiName;
            if (ployDetailResult.getDestTenantType().equals(TenantType.ERP)) {
                //换取正式apiName。读取接口日志
                realObjApiName = idFieldConvertManager.getRealObjApiName(tenantId, destObjApiName);
            }
        }
        return realObjApiName;
    }

    /**
     * 链路日志，统一使用真实apiname.很多查询场景需要转换apiname,可以增加使用apiname
     *
     * @return
     */
    public String getRealApiNameFromPreApiName(String tenantId, String streamId, String preObjectApiName, String lang) {
        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, streamId, lang);
        if (ObjectUtils.isEmpty(byIdWithCache.getData())) {
            return null;
        }
        SyncPloyDetailResult ployDetailResult = byIdWithCache.getData();
        //crm源对象是真实对象，ERP的为中间对象，需要转换
        String realObjApiName = ployDetailResult.getSourceObjectApiName();
        if (ObjectUtils.isNotEmpty(preObjectApiName)) {
            realObjApiName = preObjectApiName;
        }
        if (ployDetailResult.getSourceTenantType().equals(TenantType.ERP)) {
            //换取正式apiName。读取接口日志
            realObjApiName = idFieldConvertManager.getRealObjApiName(tenantId, realObjApiName);
        }
        return realObjApiName;
    }


}
