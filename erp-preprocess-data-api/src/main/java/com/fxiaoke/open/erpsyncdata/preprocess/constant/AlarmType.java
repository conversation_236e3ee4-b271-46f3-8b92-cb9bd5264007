package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;

/**
 * 告警类型
 * <AUTHOR>
 * @date 2023-11-23
 */
@AllArgsConstructor
public enum AlarmType {
    /**
     * 接口服务异常，告警级别：紧急
     */
    POLLING_ERP_API_EXCEPTION("接口服务异常",I18NStringEnum.s2254.getI18nKey(),"当对接系统的服务连续异常%s次时", I18NStringEnum.s2250.getI18nKey()),
    /**
     * 集成流熔断，告警级别：紧急
     */
    INTEGRATION_STREAM_BREAK("集成流熔断",I18NStringEnum.s2256.getI18nKey(),"当集成流同步异常增量超过%s条时", I18NStringEnum.s2251.getI18nKey()),
    /**
     * 同步异常，告警级别：重要
     */
    SYNC_EXCEPTION("同步异常",I18NStringEnum.s2255.getI18nKey(),"当集成流同步异常增量超过%s条时", I18NStringEnum.s2251.getI18nKey()),
    /**
     * 接口熔断，告警级别：重要
     */
    GET_BY_ID_API_BREAK("接口熔断",I18NStringEnum.s2257.getI18nKey(),"单条查询接口(getById)异常量超过%s次时", I18NStringEnum.s2252.getI18nKey()),
    /**
     * 超级管理员消息，告警级别：其它
     */
    SUPER_ADMIN("超级管理员消息",I18NStringEnum.s2261.getI18nKey(),null, null),

    /**
     * 轮询ERP服务，不是第一页失败的
     */
    POLLING_ERP_NOT_FIRST_PAGE_ERROR("接口服务异常",I18NStringEnum.s5001.getI18nKey(),"系统监控到此次轮询外部系统第%s页的时候，出现异常,traceId:%s", I18NStringEnum.s5002.getI18nKey()),
    /**
     * 其它，告警级别：其它
     */
    OTHER("其它",I18NStringEnum.s2262.getI18nKey(),null, null),
    ;

    private final String name;
    private final String nameI18nKey;
    private final String condition;
    private final String conditionI18nKey;

    public String getName(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(nameI18nKey,lang,tenantId,name);
    }

    public String getCondition(I18NStringManager i18NStringManager, String lang, String tenantId, String count) {
        return i18NStringManager.get2(conditionI18nKey,lang,tenantId,condition, Lists.newArrayList(count));
    }
}
