package com.fxiaoke.open.erpsyncdata.main.manager;

import com.fxiaoke.open.erpsyncdata.common.constant.SpuSkuConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Component
public class StreamManager {
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;


    public List<SyncPloyDetailSnapshotData2> queryIntegrationStreamSnapshot(SyncDataContextEvent eventData,
                                                                            String ployDetailSnapshotId, List<String> destTenantIds) {
        String sourceTenantId = eventData.getSourceData().getTenantId();
        String sourceObjectApiName = eventData.getSourceData().getApiName();
        List<SyncPloyDetailSnapshotData2> list = Lists.newArrayList();
        ObjectData sourceData = eventData.getSourceData();
        //拿取最新的策略明细快照
        if (StringUtils.isNotBlank(ployDetailSnapshotId)) {
            SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData =
                    syncPloyDetailSnapshotService.getSyncPloyDetailSnapshotBySnapshotId(sourceTenantId, ployDetailSnapshotId).getData();
            if (syncPloyDetailSnapshotData != null && Objects.equals(syncPloyDetailSnapshotData.getStatus(), SyncPloyDetailStatusEnum.ENABLE.getStatus())) {
                list.add(syncPloyDetailSnapshotData);
            }
        } else {
            String apiName = sourceObjectApiName;
            if (sourceObjectApiName.equals(SpuSkuConstant.SPU_SKU_SPEC_VALUE_RELATE_OBJ)) {
                if (eventData.getSourceData().get(SpuSkuConstant.SKU_ID) != null) {
                    apiName = SpuSkuConstant.PRODUCT_OBJ;
                } else if (eventData.getSourceData().get(SpuSkuConstant.SPU_ID) != null) {
                    apiName = SpuSkuConstant.SPU_OBJ;
                }
            } else if (sourceObjectApiName.equals(SpuSkuConstant.MULTI_UNIT_RELATED_OBJ)) {
                if (eventData.getSourceData().get(SpuSkuConstant.PRODUCT_ID) != null) {
                    apiName = SpuSkuConstant.PRODUCT_OBJ;
                } else if (eventData.getSourceData().get(SpuSkuConstant.SPU_ID) != null) {
                    apiName = SpuSkuConstant.SPU_OBJ;
                }
            }
            list = syncPloyDetailSnapshotService.listNewestEnableSyncPloyDetailsSnapshots(sourceTenantId, apiName, eventData.getSourceTenantType(), destTenantIds).getData();
        }
        //判断源数据在策略明细快照中是否应该同步
        String preLogId = LogIdUtil.get();
        log.debug("seync pre manager logId:{}", preLogId);
        return list;
    }

}
