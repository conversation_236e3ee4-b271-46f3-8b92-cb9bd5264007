package com.fxiaoke.open.oasyncdata.db.dao;


import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.db.entity.ErpConnectInfoEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
@Repository
public interface ErpConnectInfoDao extends ErpBaseDao<ErpConnectInfoEntity>, ITenant<ErpConnectInfoDao> {

    /**
     * 获取所有企业tenantId字段
     * @return
     */
    List<String> listTenantId();

    /**
     * 获取企业指定数据中心连接信息
     * 非必要，禁止使用该方法，尤其是可能因数据量大频繁调用的
     *
     * @param tenantId
     * @param id
     * @return
     */
    ErpConnectInfoEntity getByIdAndTenantId(@Param("tenantId") String tenantId,
                                            @Param("id") String id);

    /**
     * 获取企业所有数据中心连接信息
     * 按id排序
     *
     * @param tenantId
     * @return
     */
    List<ErpConnectInfoEntity> listByTenantId(@Param("tenantId") String tenantId);

    /**
     * 计算已存在的连接器数量
     */
    Integer countByTenantIdAndChannel(@Param("tenantId") String tenantId, @Param("channel") ErpChannelEnum channel);

    /**
     * 获取企业所有非CRM数据中心连接信息
     * 按id排序
     *
     * @param tenantId
     * @return
     */
    List<ErpConnectInfoEntity> listErpDcByTenantId(@Param("tenantId") String tenantId);

    /**
     * 获取最新的
     * @param tenantId
     * @return
     */
    ErpConnectInfoEntity getCurrentConnectInfo(@Param("tenantId") String tenantId);

    /**
     * 获取CRM渠道的数据中心ID
     */
    ErpConnectInfoEntity getCRMConnectInfo(@Param("tenantId") String tenantId, @Param("channel") String channel);

    /**
     * 根据dataCenterName查询
     */
    ErpConnectInfoEntity queryInfoByName(@Param("tenantId") String tenantId, @Param("name") String name);


    /**
     * 获取所有企业连接器
     * @return
     */
    List<ErpConnectInfoEntity> listAll();
}