package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.threeSongShu.ThreeSongShuUtils;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.threeSongShu.UploadResult;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 三只松鼠附件上传至阿里云OSS，自定义函数调用
 */
@Slf4j
@Api(tags = "三只松鼠客开接口")
@RestController("threeSongShuController")
// 82732 zsl测试企业026  三只松鼠正式 730688 三只松鼠沙盒730910
@RequestMapping(path = {"inner/erp/syncdata/customfunction/730688","inner/erp/syncdata/customfunction/730910"})
public class ThreeSongShuController {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ThreeSongShuUtils threeSongShuUtils;

    @ApiOperation(value = "通用方法")
    @RequestMapping(value = "/dowork", method = RequestMethod.POST)
    public Result dowork(@RequestHeader(value = "x-fs-ei") Integer tenantId,
                                 @RequestHeader Map<String, String> headerMap,
                                 @RequestBody Map<String, String> bodyMap) {
        log.info("trace ThreeSongShuController dowork tenantId:{}, headerMap:{}, bodymap: {} ", tenantId, headerMap, bodyMap);
        String methodName = headerMap.get("method-name");
        if (StringUtils.isBlank(methodName)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s3661);
        }
        Result result = null;
        try {
            String param = bodyMap.get("param");
            if ("uploadFile".equals(methodName)) {
                result = threeSongShuUtils.uploadFile2Oss(param);
            } else {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s3662);
            }
            log.info("result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("e:{}", e);
            return Result.newError("fail", e.getMessage());
        }
    }

}
