package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.open.erpsyncdata.preprocess.model.MultiLanguageText;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * APL类模板
 *
 * <AUTHOR> (^_−)☆
 */
@Data
@Accessors(chain = true)
@FieldNameConstants
public class AplTemplateDoc implements Serializable {
    /**
     * id
     */
    @Id
    @JSONField(serializeUsing = ObjectIdSer.class,deserializeUsing = ObjectIdSer.class)
    private ObjectId id;

    /**
     * 默认创建的类名，不可更改，作为唯一标识
     */
    private String aplClassName;

    /**
     * 名称
     */
    private MultiLanguageText name = new MultiLanguageText();

    /**
     * 系统名称
     */
    private String systemName;
    /**
     * 版本，固定v1.0.0格式
     */
    private String version;

    /**
     * 多语简介
     */
    private MultiLanguageText description = new MultiLanguageText();

    /**
     * 多语使用说明
     */
    private MultiLanguageText readmeStr = new MultiLanguageText();


    /**
     * 函数体，类名必须为{@link #aplClassName}
     */
    private String aplCode;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序值
     */
    private Integer order = 65536;

    /**
     * 是否启用
     */
    private boolean enable;
    /**
     * 新建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
