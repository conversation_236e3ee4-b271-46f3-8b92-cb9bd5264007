package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel("更新表参数")
public class UpdateTableArg implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty("企业id")
    private String tenantId;
    @ApiModelProperty("表名称")
    private String tableName;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("字段值列表")
    private List<FieldValue> fieldValueList;

    @Data
    public static class FieldValue implements Serializable{
        @ApiModelProperty("字段名称")
        private String fieldKey;
        @ApiModelProperty("字段值")
        private String fieldValue;
    }


}
