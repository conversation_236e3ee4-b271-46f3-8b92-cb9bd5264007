<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjInterfaceCheckedDao">
    <select id="findData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjInterfaceCheckedEntity">
        select * from erp_obj_interface_checked
        where tenant_id=#{tenantId}
        and data_center_id=#{dataCenterId}
        and obj_api_name=#{objApiName}
        and interface_url=#{interfaceUrl}
        limit 1
    </select>
</mapper>