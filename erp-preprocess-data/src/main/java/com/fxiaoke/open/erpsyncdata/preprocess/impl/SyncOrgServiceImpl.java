package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncOrgService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SyncOrgServiceImpl implements SyncOrgService {

    @Override
    public void beforWriteOrg2Crm(SyncDataContextEvent message) {

        String tenantIdStr = message.getDestTenantId();
        Integer tenantId = Integer.valueOf(tenantIdStr);
        if (!message.getDestEventType().equals(EventTypeEnum.UPDATE.getType())
          &&!message.getDestEventType().equals(EventTypeEnum.ADD.getType())) {
            return;
        }
        validatePersonPassword(message);
    }

    /**
     * <p>更新人员不需要更新人员密码</p>
     * @dateTime 2021/1/6 19:13
     * <AUTHOR>
     * @version 1.0
     */
    private void validatePersonPassword(SyncDataContextEvent message){

        if (!message.getDestEventType().equals(EventTypeEnum.UPDATE.getType())) {
            return;
        }
        if (!ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(message.getDestObjectApiName())) {
            return;
        }
        message.getDestData().remove("password");
    }
}
