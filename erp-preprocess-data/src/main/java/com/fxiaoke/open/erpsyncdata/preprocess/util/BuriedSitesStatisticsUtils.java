package com.fxiaoke.open.erpsyncdata.preprocess.util;

import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class BuriedSitesStatisticsUtils {

    private final static String SERVICE_NAME = "fs-erp-sync-data";

    @Autowired
    private I18NStringManager i18NStringManager;

    private static ErpConnectInfoManager erpConnectInfoManager;
    private static DataCenterManager dataCenterManager;

    @Autowired
    public void setDataCenterManager(DataCenterManager dataCenterManager) {
        BuriedSitesStatisticsUtils.dataCenterManager = dataCenterManager;
    }

    @Autowired
    public void setErpConnectInfoManager(ErpConnectInfoManager erpConnectInfoManager) {
        BuriedSitesStatisticsUtils.erpConnectInfoManager = erpConnectInfoManager;
    }

    public void uploadBuriedStitesLogBySnap(String tenantId,String objApiName, Integer director,String snap,Integer receiveType){
        try {
            Map<String, String> resultBySnapshotId = dataCenterManager.getResultBySnapshotId(tenantId, snap);
            ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId,resultBySnapshotId.get("dataCenterId"));
            String outSystemName = "";
            if (connectInfo.getConnectParams() != null && connectInfo.getChannel() == ErpChannelEnum.STANDARD_CHANNEL) {
                try {
                    StandardConnectParam connectParam = ErpChannelEnum.STANDARD_CHANNEL.getConnectParam(connectInfo.getConnectParams());
                    outSystemName = connectParam.getSystemName();
                } catch (Exception e) {
                    log.info("getByChannelAndConnectParam error", e);
                }
            }

            AsyncLog(tenantId, objApiName, director, connectInfo,receiveType,resultBySnapshotId.get("streamId"),outSystemName);
        }catch (Exception e){
            log.error("upload BuriedStitesLogBySnap failed,e:",e);
        }
    }

    private void AsyncLog(String tenantId, String objApiName, Integer director, ErpConnectInfoEntity connectInfo,
                          Integer receiveType,String syncPloyDetailId, String outSystemName){
        Connector connector = AllConnectorUtil.getByChannelAndConnectParam(connectInfo.getChannel(), connectInfo.getConnectParams());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("tenantId", tenantId);
        dataMap.put("objApiName", objApiName);
        dataMap.put("channel", connector.getKey());
        dataMap.put("enterpriseName", connectInfo.getEnterpriseName());
        dataMap.put("director", i18NStringManager.getByEi(director == 1 ? I18NStringEnum.s1288 : I18NStringEnum.s1289, tenantId));
        dataMap.put("trace", "EnterpriseObjectCount");
        dataMap.put("receiveType", receiveType);
        dataMap.put("syncPloyDetailId", syncPloyDetailId);
        dataMap.put("dataCenterId", connectInfo.getId());
        dataMap.put("traceName", i18NStringManager.getByEi(I18NStringEnum.s1287,tenantId));
        dataMap.put("outSystemName", outSystemName);
        DataPersistor.asyncLog(SERVICE_NAME, dataMap);
    }
}
