package com.fxiaoke.open.erpsyncdata.monitor.util

import cn.hutool.core.lang.Pair
import cn.hutool.core.thread.ThreadUtil
import cn.hutool.crypto.asymmetric.RSA
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/1/5
 */
class ProxyTokenUtilTest extends Specification {
    def "GenerateToken"() {
        Pair keyPair = ProxyTokenUtil.generateKey()
        given:
        String privateKey = keyPair.getKey()
        def publicKey = keyPair.getValue()
        boolean valid = true
        for (i in 0..<5) {
            ThreadUtil.sleep(1000)
            String token = ProxyTokenUtil.generateTokenNoCache(privateKey)

            valid = valid && ProxyTokenUtil.checkToken(publicKey, token)
            printf("$token,$valid\n")
        }
        expect:
        valid
    }
    def "GenerateTokenException"() {
        def rsa = new RSA()
        def rsa2 = new RSA()
        given:
        String key = rsa.getPrivateKeyBase64()
        boolean valid = false
        for (i in 0..<5) {
            ThreadUtil.sleep(1000)
            String token = ProxyTokenUtil.generateTokenNoCache(key)
            valid = valid|| ProxyTokenUtil.checkToken(rsa2.getPublicKeyBase64(), token)
            printf("$token,$valid\n")
        }
        expect:
        !valid
    }
}
