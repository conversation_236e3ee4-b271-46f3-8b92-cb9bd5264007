package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpUserOperationLogEntity;

/**
 * <AUTHOR>
 * @Date: 17:34 2021/12/7
 * @Desc:
 */
public interface ErpUserOperationLogService {
    ErpUserOperationLogEntity buildNewErpUserOperationLog(String tenantId,String operationType,String operationUser,String userPhone,
                                                          String objApiName,String arg,String result,Long operationTime);
    void addErpUserOperationLog(ErpUserOperationLogEntity entity);

    void buildAndAddLog(String tenantId,String operationType,String operationUser,String userPhone,
                                String objApiName,String arg,String result,Long operationTime);

}
