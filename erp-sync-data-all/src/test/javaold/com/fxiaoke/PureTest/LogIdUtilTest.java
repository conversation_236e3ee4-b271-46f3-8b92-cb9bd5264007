package com.fxiaoke.PureTest;

import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/15
 */
public class LogIdUtilTest {

    public static void main(String[] args) {
        String s = LogIdUtil.get();
        String logId = LogIdUtil.buildRootLogId("83952", "Material-","test-Id");
        System.out.println(logId);
        String logId1 = LogIdUtil.buildChildLogId(logId, 1);
        System.out.println(logId1);
        String logId2 = LogIdUtil.buildChildLogId(logId1, 2);
        System.out.println(logId2);
        List<String> logIdLine = LogIdUtil.listLogIdLine("83952",logId2);
        System.out.println(logIdLine);
    }
}
