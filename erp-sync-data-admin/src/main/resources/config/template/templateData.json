[{"channel": "ERP_K3CLOUD", "description": "无多单位", "detailStr": "{\"detailMd\":\"## 模板介绍\\n可通过此模板将云星空的物料、物料分组同步到CRM的产品和产品分类(对象)，如果您在CRM中需要使用并开通了商品，系统会在同步数据时自动生成CRM的商品。此模板不包含计量单位，如需请选择模板2。\\n## 使用此模板的前置条件\\n- ✅ CRM不开启多单位\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\n- ✅ CRM开通产品分类对象\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/basic.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FParentId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"上级分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料分组\",\"realObjApiName\":\"SAL_MATERIALGROUP\",\"valid\":true},\"mainRealObjApiName\":\"SAL_MATERIALGROUP\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FForbidStatus\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"禁用状态\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"comName\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"复合名称(编码#名称)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialGroup\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"物料分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"基本单位(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"描述\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductCategoryObj\",\"destObjectName\":\"产品分类\",\"fieldMappings\":[{\"destApiName\":\"category_code\",\"destName\":\"产品分类编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"产品分类名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"pid\",\"destName\":\"上级产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FParentId\",\"sourceName\":\"上级分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_MATERIALGROUP\",\"sourceObjectName\":\"物料分组\"},\"sourceTenantType\":2,\"streamName\":\"物料分组 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"price\",\"destName\":\"标准价格\",\"destType\":\"currency\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"0\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"product_status\",\"destName\":\"上下架\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"A\"},{\"destOption\":\"2\",\"sourceOption\":\"B\"}],\"sourceApiName\":\"FForbidStatus\",\"sourceName\":\"禁用状态\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"编码#名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceName\":\"复合名称(编码#名称)\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"product_category_id\",\"destName\":\"产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup\",\"sourceName\":\"物料分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destName\":\"单位\",\"destTargetApiName\":\"UnitInfoObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"sourceName\":\"基本单位(编码)\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"product_code\",\"destName\":\"产品编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"field_ZQYo6__c\",\"destName\":\"自定义字段\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"描述\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/basic.png", "id": "6433a31bcac99c7d78bd7934", "order": 20, "preconditionIds": ["crmconfig-!multiple_unit", "k3connect", "crmconfig-close_old_category"], "sceneIds": ["jczl"], "tagIds": ["jczl", "Material"], "title": "云星空物料同步到CRM_模板1", "updateTime": 1681289493979, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\r\\n基于企业常用的对接形成的一套通用模板，包含了基础资料、价格管理、报价管理、交易管理、库存和发货管理全业务场景。如果要解决某个具体场景的对接，您可以使用对应场景的模板。此模板不含多单位、序列号和批次号对接。\\r\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ CRM不开启多单位\\r\\n- ✅ 即时库存中间对象主键改为FID\\r\\n- ✅ CRM开通产品分类对象\\r\\n- ✅ CRM开通B类库存、选择的是同步库存明细\\r\\n- ✅ CRM不开启CRM批次与序列号管理\\r\\n## 对接对象和数据流向图\\r\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/full_scene1.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"组织机构\",\"realObjApiName\":\"ORG_Organizations\",\"valid\":true},\"mainRealObjApiName\":\"ORG_Organizations\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FADDRESS1\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"详细地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户-地址信息\",\"realObjApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"valid\":true},{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBANKCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"银行账号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FOPENBANKNAME\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"开户银行\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户-银行信息\",\"realObjApiName\":\"BD_Customer.BD_CUSTBANK\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FCOUNTRY.FNumber\",\"fieldDefineType\":\"country\",\"fieldLabel\":\"国家(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FT_BD_CUSTOMEREXT.FPROVINCE.FNumber\",\"fieldDefineType\":\"province\",\"fieldLabel\":\"省份(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FT_BD_CUSTOMEREXT.FCITY.FNumber\",\"fieldDefineType\":\"city\",\"fieldLabel\":\"城市(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"客户名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTRADINGCURRID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"结算币别(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCreateOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"创建组织(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSELLER.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FADDRESS\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"通讯地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"客户编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICEBANKACCOUNT\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"银行账号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICETEL\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"开票联系电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICEADDRESS\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"开票通讯地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICEBANKNAME\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"开户银行\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTAXREGISTERCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"纳税登记号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FShortName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"简称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICETITLE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"发票抬头\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTEL\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"联系电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FGroup\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"客户分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTaxRate.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"默认税率(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FWEBSITE\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"公司网址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRECCONDITIONID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"收款条件(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"客户编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户\",\"realObjApiName\":\"BD_Customer\",\"valid\":true},\"mainRealObjApiName\":\"BD_Customer\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FCompanyType\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"类型\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"姓名\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEmail\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"邮箱\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FPost\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"职务\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMobile\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"移动电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTel\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"固定电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBizAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"详细地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCompany.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"所属公司(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"联系人\",\"realObjApiName\":\"BD_CommonContact\",\"valid\":true},\"mainRealObjApiName\":\"BD_CommonContact\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FParentId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"上级分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料分组\",\"realObjApiName\":\"SAL_MATERIALGROUP\",\"valid\":true},\"mainRealObjApiName\":\"SAL_MATERIALGROUP\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FForbidStatus\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"禁用状态\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"comName\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"复合名称(编码#名称)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialGroup\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"物料分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"基本单位(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"描述\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSpecification\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"规格型号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FUnitID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"销售单位(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"销售数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTaxPrice\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"含税单价\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntryNote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntryTaxRate\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"税率%\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FIsFree\",\"fieldDefineType\":\"true_or_false\",\"fieldLabel\":\"是否赠品\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDeliveryDate\",\"fieldDefineType\":\"date_time\",\"fieldLabel\":\"要货日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAmount\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"金额\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAllAmount\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"价税合计\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售订单-订单明细\",\"realObjApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FChangeReason\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"变更原因\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBillTypeID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"单据类型(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"销售组织(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSalerId.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"结算币别(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleDeptId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldLabel\":\"销售部门(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FRecConditionId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"收款条件(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FBillAllAmount\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"价税合计\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBillNo\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"单据编号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"ComId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"复合ID(Id#Number)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售订单\",\"realObjApiName\":\"SAL_SaleOrder\",\"valid\":true},\"mainRealObjApiName\":\"SAL_SaleOrder\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"仓库地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"仓库\",\"realObjApiName\":\"BD_STOCK\",\"valid\":true},\"mainRealObjApiName\":\"BD_STOCK\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FBaseQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"库存量(基本单位)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FID\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"stockComId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"库存ID（仓库编号#物料编号）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"即时库存\",\"realObjApiName\":\"STK_Inventory\",\"valid\":true},\"mainRealObjApiName\":\"STK_Inventory\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_OUTSTOCK\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntrynote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRealQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"实发数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSOEntryId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldLabel\":\"销售订单EntryId\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单-明细信息\",\"realObjApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalesManID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustomerID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单\",\"realObjApiName\":\"SAL_OUTSTOCK\",\"valid\":true},\"mainRealObjApiName\":\"SAL_OUTSTOCK\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"AR_RECEIVEBILL\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FREALRECAMOUNTFOR_D\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"表体-实收金额\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCOMMENT\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"收款单-收款单明细\",\"realObjApiName\":\"AR_RECEIVEBILL.RECEIVEBILLENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FRECAMOUNTFOR\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"收款金额\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDATE\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"业务日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSALEERID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FPAYUNIT.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"付款单位(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FREMARK\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"收款单\",\"realObjApiName\":\"AR_RECEIVEBILL\",\"valid\":true},\"mainRealObjApiName\":\"AR_RECEIVEBILL\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"IV_SALESOC\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTAXRATE\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"税率%\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FALLAMOUNTFOR\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"价税合计\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售普通发票-销售普通发票明细\",\"realObjApiName\":\"IV_SALESOC.SALESICENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FINVOICEDATE\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"发票日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCUSTOMERID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSALEERID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTAXREGISTERCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"纳税人识别号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICETITLE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"发票抬头\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FOPENBANKNAME\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"开户银行\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBANKCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"银行账号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售普通发票\",\"realObjApiName\":\"IV_SALESOC\",\"valid\":true},\"mainRealObjApiName\":\"IV_SALESOC\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"IV_SALESIC\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTAXRATE\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"税率%\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FALLAMOUNTFOR\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"价税合计\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售增值税专用发票-销售增值税专用发票明细\",\"realObjApiName\":\"IV_SALESIC.SALESICENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FINVOICEDATE\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"发票日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCUSTOMERID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSALEERID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTAXREGISTERCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"纳税人识别号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICETITLE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"发票抬头\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FOPENBANKNAME\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"开户银行\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBANKCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"银行账号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售增值税专用发票\",\"realObjApiName\":\"IV_SALESIC\",\"valid\":true},\"mainRealObjApiName\":\"IV_SALESIC\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ErpOrganizationObj\",\"destObjectName\":\"ERP组织机构\",\"fieldMappings\":[{\"destApiName\":\"name\",\"destName\":\"组织名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\"}],\"sourceObjectApiName\":\"ORG_Organizations\",\"sourceObjectName\":\"组织机构\"},\"sourceTenantType\":2,\"streamName\":\"ERP组织机构（不轮询）\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"debug\",\"syncTypeList\":[]}},{\"detailMappings\":[{\"destObjectApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"destObjectName\":\"客户-地址信息\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FADDRESS1\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"address\",\"sourceName\":\"详细地址\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"AccountAddrObj\",\"sourceObjectName\":\"客户地址\"},{\"destObjectApiName\":\"BD_Customer.BD_CUSTBANK\",\"destObjectName\":\"客户-银行信息\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FBANKCODE\",\"destName\":\"银行账号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_bank_no\",\"sourceName\":\"开户账户\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FOPENBANKNAME\",\"destName\":\"开户银行\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_bank\",\"sourceName\":\"开户银行\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"AccountFinInfoObj\",\"sourceObjectName\":\"客户财务信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BD_Customer\",\"destObjectName\":\"客户\",\"fieldMappings\":[{\"destApiName\":\"FCOUNTRY.FNumber\",\"destName\":\"国家(编码)\",\"destType\":\"country\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"country\",\"sourceName\":\"国家\",\"sourceType\":\"country\",\"value\":\"\"},{\"destApiName\":\"FT_BD_CUSTOMEREXT.FPROVINCE.FNumber\",\"destName\":\"省份(编码)\",\"destType\":\"province\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"province\",\"sourceName\":\"省\",\"sourceType\":\"province\",\"value\":\"\"},{\"destApiName\":\"FT_BD_CUSTOMEREXT.FCITY.FNumber\",\"destName\":\"城市(编码)\",\"destType\":\"city\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"city\",\"sourceName\":\"市\",\"sourceType\":\"city\",\"value\":\"\"},{\"destApiName\":\"FName\",\"destName\":\"客户名称\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"客户名称\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FTRADINGCURRID.FNumber\",\"destName\":\"结算币别(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_19HAb__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FCreateOrgId.FNumber\",\"destName\":\"创建组织(编码)\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_Wpkj4__c\",\"sourceName\":\"自定义字段\",\"sourceTargetApiName\":\"ErpOrganizationObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FSELLER.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceName\":\"负责人\",\"sourceType\":\"employee\",\"value\":\"owner\"},{\"destApiName\":\"FADDRESS\",\"destName\":\"通讯地址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"address\",\"sourceName\":\"详细地址\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FNumber\",\"destName\":\"客户编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_no\",\"sourceName\":\"客户编号\",\"sourceType\":\"auto_number\",\"value\":\"\"},{\"destApiName\":\"FDescription\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"FINVOICEBANKACCOUNT\",\"destName\":\"银行账号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_lcdyp__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICETEL\",\"destName\":\"开票联系电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_Ia1l0__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICEADDRESS\",\"destName\":\"开票通讯地址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_wklQ1__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICEBANKNAME\",\"destName\":\"开户银行\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_Q7gcp__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FTAXREGISTERCODE\",\"destName\":\"纳税登记号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"uniform_social_credit_code\",\"sourceName\":\"统一社会信用代码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FShortName\",\"destName\":\"简称\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_oHpR7__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICETITLE\",\"destName\":\"发票抬头\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_6xm40__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FTEL\",\"destName\":\"联系电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"tel\",\"sourceName\":\"电话\",\"sourceType\":\"phone_number\",\"value\":\"\"},{\"destApiName\":\"FGroup\",\"destName\":\"客户分组\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_0171d__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FTaxRate.FNumber\",\"destName\":\"默认税率(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_2v15b__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FWEBSITE\",\"destName\":\"公司网址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"url\",\"sourceName\":\"网址\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FRECCONDITIONID.FNumber\",\"destName\":\"收款条件(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_j7uX0__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"sourceObjectApiName\":\"AccountObj\",\"sourceObjectName\":\"客户\"},\"sourceTenantType\":1,\"streamName\":\"客户 CRM往ERP\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0 0 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"once\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BD_CommonContact\",\"destObjectName\":\"联系人\",\"fieldMappings\":[{\"destApiName\":\"FCompanyType\",\"destName\":\"类型\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"BD_Customer\",\"valueType\":1},{\"destApiName\":\"FName\",\"destName\":\"姓名\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"姓名\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FEmail\",\"destName\":\"邮箱\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"email\",\"sourceName\":\"邮件\",\"sourceType\":\"email\",\"value\":\"\"},{\"destApiName\":\"FPost\",\"destName\":\"职务\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"job_title\",\"sourceName\":\"职务\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FMobile\",\"destName\":\"移动电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"mobile1\",\"sourceName\":\"手机1\",\"sourceType\":\"phone_number\",\"value\":\"\"},{\"destApiName\":\"FTel\",\"destName\":\"固定电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"tel1\",\"sourceName\":\"电话1\",\"sourceType\":\"phone_number\",\"value\":\"\"},{\"destApiName\":\"FBizAddress\",\"destName\":\"详细地址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"add\",\"sourceName\":\"地址\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FCompany.FNumber\",\"destName\":\"所属公司(编码)\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"ContactObj\",\"sourceObjectName\":\"联系人\"},\"sourceTenantType\":1,\"streamName\":\"联系人 CRM往ERP\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0 0 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"once\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductCategoryObj\",\"destObjectName\":\"产品分类\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"category_code\",\"destName\":\"产品分类编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"产品分类名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"pid\",\"destName\":\"上级产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FParentId\",\"sourceName\":\"上级分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_MATERIALGROUP\",\"sourceObjectName\":\"物料分组\"},\"sourceTenantType\":2,\"streamName\":\"物料分组 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"price\",\"destName\":\"标准价格\",\"destType\":\"currency\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"0\",\"valueType\":1},{\"destApiName\":\"product_status\",\"destName\":\"上下架\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"A\"},{\"destOption\":\"2\",\"sourceOption\":\"B\"}],\"sourceApiName\":\"FForbidStatus\",\"sourceName\":\"禁用状态\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"编码#名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceName\":\"复合名称(编码#名称)\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"product_category_id\",\"destName\":\"产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup\",\"sourceName\":\"物料分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destName\":\"单位\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"Pcs\"}],\"sourceApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"sourceName\":\"基本单位(编码)\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"product_code\",\"destName\":\"产品编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"field_ZQYo6__c\",\"destName\":\"自定义字段\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"描述\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"product_spec\",\"destName\":\"规格属性\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSpecification\",\"sourceName\":\"规格型号\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectName\":\"销售订单-订单明细\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"SAL_SaleOrder\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"order_id\",\"sourceName\":\"订单\",\"sourceTargetApiName\":\"SalesOrderObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FMaterialId.FNumber\",\"destName\":\"物料编码(编码)\",\"destTargetApiName\":\"BD_MATERIAL\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"product_id\",\"sourceName\":\"产品名称\",\"sourceTargetApiName\":\"ProductObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FUnitID.FNumber\",\"destName\":\"销售单位(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"Pcs\",\"sourceOption\":\"1\"}],\"sourceApiName\":\"unit\",\"sourceName\":\"单位\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceType\":\"quote\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"FQty\",\"destName\":\"销售数量\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"quantity\",\"sourceName\":\"数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"FTaxPrice\",\"destName\":\"含税单价\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"sales_price\",\"sourceName\":\"销售单价（元）\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"FEntryNote\",\"destName\":\"备注\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"FEntryTaxRate\",\"destName\":\"税率%\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_4J905__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"percentile\",\"value\":\"\"},{\"destApiName\":\"FIsFree\",\"destName\":\"是否赠品\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"true\",\"sourceOption\":true},{\"destOption\":\"false\",\"sourceOption\":false}],\"sourceApiName\":\"field_Shspc__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"true_or_false\",\"value\":\"\"},{\"destApiName\":\"FDeliveryDate\",\"destName\":\"要货日期\",\"destType\":\"date_time\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_6s1We__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"SalesOrderProductObj\",\"sourceObjectName\":\"订单产品\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"SAL_SaleOrder\",\"destObjectName\":\"销售订单\",\"fieldMappings\":[{\"destApiName\":\"FChangeReason\",\"destName\":\"变更原因\",\"destType\":\"text\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"编辑了订单\",\"valueType\":1},{\"defaultValue\":\"option1\",\"destApiName\":\"FBillTypeID.FNumber\",\"destName\":\"单据类型(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"XSDD01_SYS\",\"sourceOption\":\"option1\"}],\"sourceApiName\":\"field_44ZxH__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":2},{\"destApiName\":\"FSaleOrgId.FNumber\",\"destName\":\"销售组织(编码)\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_iycxb__c\",\"sourceName\":\"自定义字段\",\"sourceTargetApiName\":\"ErpOrganizationObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FCustId.FNumber\",\"destName\":\"客户(编码)\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FSalerId.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceName\":\"负责人\",\"sourceType\":\"employee\",\"value\":\"owner\"},{\"destApiName\":\"FDate\",\"destName\":\"日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_time\",\"sourceName\":\"下单日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"defaultValue\":\"option1\",\"destApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"destName\":\"结算币别(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"PRE007\",\"sourceOption\":\"rH2XIgS13\"},{\"destOption\":\"PRE001\",\"sourceOption\":\"option1\"}],\"sourceApiName\":\"field_qgia3__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":2},{\"destApiName\":\"FNote\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"defaultValue\":\"option1\",\"destApiName\":\"FSaleOrderFinance.FRecConditionId.FNumber\",\"destName\":\"收款条件(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"SKTJ01_SYS\",\"sourceOption\":\"option1\"}],\"sourceApiName\":\"field_Tyeno__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":2}],\"sourceObjectApiName\":\"SalesOrderObj\",\"sourceObjectName\":\"销售订单\"},\"sourceTenantType\":1,\"streamName\":\"销售订单 CRM往ERP\",\"syncRules\":{\"events\":[1,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"SalesOrderProductObj\",\"destObjectName\":\"订单产品\",\"fieldMappings\":[{\"destApiName\":\"order_id\",\"destName\":\"订单\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"sales_price\",\"destName\":\"销售单价（元）\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTaxPrice\",\"sourceName\":\"含税单价\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"quantity\",\"destName\":\"数量\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FQty\",\"sourceName\":\"销售数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"product_price\",\"destName\":\"价格(元）\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAmount\",\"sourceName\":\"金额\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"subtotal\",\"destName\":\"小计\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAllAmount\",\"sourceName\":\"价税合计\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntryNote\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"field_6s1We__c\",\"destName\":\"自定义字段\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDeliveryDate\",\"sourceName\":\"要货日期\",\"sourceType\":\"date_time\",\"value\":\"\"},{\"destApiName\":\"field_4J905__c\",\"destName\":\"自定义字段\",\"destType\":\"percentile\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntryTaxRate\",\"sourceName\":\"税率%\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"field_Shspc__c\",\"destName\":\"自定义字段\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"false\"},{\"destOption\":true,\"sourceOption\":\"true\"}],\"sourceApiName\":\"FIsFree\",\"sourceName\":\"是否赠品\",\"sourceType\":\"true_or_false\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceObjectName\":\"销售订单-订单明细\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"SalesOrderObj\",\"destObjectName\":\"销售订单\",\"fieldMappings\":[{\"destApiName\":\"order_time\",\"destName\":\"下单日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDate\",\"sourceName\":\"日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustId.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSalerId.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"order_amount\",\"destName\":\"销售订单金额(元)\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSaleOrderFinance.FBillAllAmount\",\"sourceName\":\"价税合计\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"field_H2K4n__c\",\"destName\":\"自定义字段\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBillNo\",\"sourceName\":\"单据编号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"field_qgia3__c\",\"destName\":\"自定义字段\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"option1\",\"sourceOption\":\"PRE001\"},{\"destOption\":\"rH2XIgS13\",\"sourceOption\":\"PRE007\"}],\"sourceApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"sourceName\":\"结算币别(编码)\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"field_44ZxH__c\",\"destName\":\"自定义字段\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"option1\",\"sourceOption\":\"XSDD01_SYS\"}],\"sourceApiName\":\"FBillTypeID.FNumber\",\"sourceName\":\"单据类型(编码)\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"field_iycxb__c\",\"destName\":\"自定义字段\",\"destTargetApiName\":\"ErpOrganizationObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSaleOrgId.FNumber\",\"sourceName\":\"销售组织(编码)\",\"sourceTargetApiName\":\"ORG_Organizations\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_SaleOrder\",\"sourceObjectName\":\"销售订单\"},\"sourceTenantType\":2,\"streamName\":\"销售订单 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"WarehouseObj\",\"destObjectName\":\"仓库\",\"fieldMappings\":[{\"destApiName\":\"is_default\",\"destName\":\"是否默认仓\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"is_enable\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"number\",\"destName\":\"仓库编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceName\":\"编码\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"仓库名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"address\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAddress\",\"sourceName\":\"仓库地址\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_STOCK\",\"sourceObjectName\":\"仓库\"},\"sourceTenantType\":2,\"streamName\":\"仓库 ERP往CRM\",\"syncRules\":{\"events\":[1],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"2022-03-31T15:59:00.000Z\",\"intervalQuantity\":6,\"startDataTime\":\"2022-03-30T16:00:00.000Z\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InventoryDetailsObj\",\"destObjectName\":\"库存明细\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"real_stock\",\"destName\":\"实际库存\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBaseQty\",\"sourceName\":\"库存量(基本单位)\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockId.FNumber\",\"sourceName\":\"仓库编码(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"物料名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"STK_Inventory\",\"sourceObjectName\":\"即时库存\"},\"sourceTenantType\":2,\"streamName\":\"即时库存 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"DeliveryNoteProductObj\",\"destObjectName\":\"发货单产品\",\"fieldMappings\":[{\"destApiName\":\"delivery_note_id\",\"destName\":\"发货单编号\",\"destTargetApiName\":\"DeliveryNoteObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_OUTSTOCK\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntrynote\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"delivery_warehouse_id\",\"destName\":\"发货仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockID.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_num\",\"destName\":\"本次发货数（基准单位）\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRealQty\",\"sourceName\":\"实发数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"sales_order_product_id\",\"destName\":\"订单产品编号\",\"destTargetApiName\":\"SalesOrderProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSOEntryId\",\"sourceName\":\"销售订单EntryId\",\"sourceTargetApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"sourceObjectName\":\"销售出库单-明细信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"DeliveryNoteObj\",\"destObjectName\":\"发货单\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSalesManID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\",\"valueType\":1},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"receive_remark\",\"destName\":\"收货备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_date\",\"destName\":\"发货日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDate\",\"sourceName\":\"日期\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK\",\"sourceObjectName\":\"销售出库单\"},\"sourceTenantType\":2,\"streamName\":\"销售出库单 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"OrderPaymentObj\",\"destObjectName\":\"回款明细\",\"fieldMappings\":[{\"destApiName\":\"payment_id\",\"destName\":\"回款编号\",\"destTargetApiName\":\"PaymentObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"AR_RECEIVEBILL\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"payment_amount\",\"destName\":\"本次回款金额（元）\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FREALRECAMOUNTFOR_D\",\"sourceName\":\"表体-实收金额\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCOMMENT\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"AR_RECEIVEBILL.RECEIVEBILLENTRY\",\"sourceObjectName\":\"收款单-收款单明细\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"PaymentObj\",\"destObjectName\":\"回款\",\"fieldMappings\":[{\"destApiName\":\"amount\",\"destName\":\"回款金额\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRECAMOUNTFOR\",\"sourceName\":\"收款金额\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"payment_time\",\"destName\":\"回款日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDATE\",\"sourceName\":\"业务日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSALEERID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FPAYUNIT.FNumber\",\"sourceName\":\"付款单位(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FREMARK\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"AR_RECEIVEBILL\",\"sourceObjectName\":\"收款单\"},\"sourceTenantType\":2,\"streamName\":\"回款 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"InvoiceApplicationLinesObj\",\"destObjectName\":\"开票申请明细\",\"fieldMappings\":[{\"destApiName\":\"invoice_id\",\"destName\":\"开票申请\",\"destTargetApiName\":\"InvoiceApplicationObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"IV_SALESOC\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"tax_rate\",\"destName\":\"税率\",\"destType\":\"percentile\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXRATE\",\"sourceName\":\"税率%\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"invoiced_amount\",\"destName\":\"开票小计\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FALLAMOUNTFOR\",\"sourceName\":\"价税合计\",\"sourceType\":\"currency\",\"value\":\"\"}],\"sourceObjectApiName\":\"IV_SALESOC.SALESICENTRY\",\"sourceObjectName\":\"销售普通发票-销售普通发票明细\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InvoiceApplicationObj\",\"destObjectName\":\"开票申请\",\"fieldMappings\":[{\"destApiName\":\"invoice_date\",\"destName\":\"开票日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICEDATE\",\"sourceName\":\"发票日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCUSTOMERID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSALEERID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\"},{\"destApiName\":\"tax_id\",\"destName\":\"纳税识别号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXREGISTERCODE\",\"sourceName\":\"纳税人识别号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"invoice_title\",\"destName\":\"开票抬头\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICETITLE\",\"sourceName\":\"发票抬头\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"account_bank\",\"destName\":\"开户行\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FOPENBANKNAME\",\"sourceName\":\"开户银行\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"account_bank_no\",\"destName\":\"开户账号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBANKCODE\",\"sourceName\":\"银行账号\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"IV_SALESOC\",\"sourceObjectName\":\"销售普通发票\"},\"sourceTenantType\":2,\"streamName\":\"销售普通发票 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"InvoiceApplicationLinesObj\",\"destObjectName\":\"开票申请明细\",\"fieldMappings\":[{\"destApiName\":\"invoice_id\",\"destName\":\"开票申请\",\"destTargetApiName\":\"InvoiceApplicationObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"IV_SALESIC\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"tax_rate\",\"destName\":\"税率\",\"destType\":\"percentile\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXRATE\",\"sourceName\":\"税率%\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"invoiced_amount\",\"destName\":\"开票小计\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FALLAMOUNTFOR\",\"sourceName\":\"价税合计\",\"sourceType\":\"currency\",\"value\":\"\"}],\"sourceObjectApiName\":\"IV_SALESIC.SALESICENTRY\",\"sourceObjectName\":\"销售增值税专用发票-销售增值税专用发票明细\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InvoiceApplicationObj\",\"destObjectName\":\"开票申请\",\"fieldMappings\":[{\"destApiName\":\"invoice_date\",\"destName\":\"开票日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICEDATE\",\"sourceName\":\"发票日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCUSTOMERID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSALEERID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\"},{\"destApiName\":\"tax_id\",\"destName\":\"纳税识别号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FTAXREGISTERCODE\",\"sourceName\":\"纳税人识别号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"invoice_title\",\"destName\":\"开票抬头\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FINVOICETITLE\",\"sourceName\":\"发票抬头\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"account_bank\",\"destName\":\"开户行\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FOPENBANKNAME\",\"sourceName\":\"开户银行\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"account_bank_no\",\"destName\":\"开户账号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBANKCODE\",\"sourceName\":\"银行账号\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"IV_SALESIC\",\"sourceObjectName\":\"销售增值税专用发票\"},\"sourceTenantType\":2,\"streamName\":\"销售增值税专用发票 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/full_scene.png", "id": "6433a31bcac99c7d78bd7935", "order": 0, "preconditionIds": ["k3connect", "crmconfig-!multiple_unit", "jskcidcheck", "crmconfig-close_old_category", "stockconfig-king_dee_k3c_sync_type-2", "crmobj-!BatchObj&!SerialNumberObj"], "sceneIds": ["allScenes"], "tagIds": ["allScenes", "commonSimple"], "title": "全业务场景通用集成", "updateTime": 1681470588024, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "description": "多单位", "detailStr": "{\"detailMd\":\"## 模板介绍\\n可通过此模板将云星空的物料、物料分组、计量单位同步到CRM的产品、产品分类(对象)和单位，如果您在CRM中需要使用并开通了商品，系统会在同步数据时自动生成CRM的商品。如果您不需同步计量单位，请选择模板1。\\n## 使用此模板的前置条件\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\n- ✅ CRM开启多单位\\n- ✅ CRM开通产品分类对象\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/basic2.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"计量单位\",\"realObjApiName\":\"BD_UNIT\",\"valid\":true},\"mainRealObjApiName\":\"BD_UNIT\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FParentId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"上级分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料分组\",\"realObjApiName\":\"SAL_MATERIALGROUP\",\"valid\":true},\"mainRealObjApiName\":\"SAL_MATERIALGROUP\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FMaterialGroup\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"物料分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SubHeadEntity.FBaseUnitIdFUNITID\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_UNIT\",\"fieldLabel\":\"基本单位(ID)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"comName\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"复合名称(编码#名称)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDocumentStatus\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"数据状态\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSpecification\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"规格型号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"描述\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"CrmConvertNumber\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"转换比例（基准单位/当前单位）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCurrentUnitIdFUNITID\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_UNIT\",\"fieldLabel\":\"单位(ID)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料单位换算\",\"realObjApiName\":\"BD_MATERIALUNITCONVERT\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIALUNITCONVERT\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"UnitInfoObj\",\"destObjectName\":\"单位\",\"fieldMappings\":[{\"destApiName\":\"name\",\"destName\":\"单位名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_UNIT\",\"sourceObjectName\":\"计量单位\"},\"sourceTenantType\":2,\"streamName\":\"计量单位 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductCategoryObj\",\"destObjectName\":\"产品分类\",\"fieldMappings\":[{\"destApiName\":\"category_code\",\"destName\":\"产品分类编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"产品分类名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"pid\",\"destName\":\"上级产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FParentId\",\"sourceName\":\"上级分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_MATERIALGROUP\",\"sourceObjectName\":\"物料分组\"},\"sourceTenantType\":2,\"streamName\":\"物料分组 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"product_category_id\",\"destName\":\"产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup\",\"sourceName\":\"物料分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destName\":\"单位\",\"destTargetApiName\":\"UnitInfoObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SubHeadEntity.FBaseUnitIdFUNITID\",\"sourceName\":\"基本单位(ID)\",\"sourceTargetApiName\":\"BD_UNIT\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"产品名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceName\":\"复合名称(编码#名称)\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"product_status\",\"destName\":\"上下架\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDocumentStatus\",\"sourceName\":\"数据状态\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"price\",\"destName\":\"标准价格\",\"destType\":\"currency\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"0\",\"valueType\":1},{\"destApiName\":\"product_spec\",\"destName\":\"规格属性\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSpecification\",\"sourceName\":\"规格型号\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"product_code\",\"destName\":\"产品编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"描述\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"MultiUnitRelatedObj\",\"destObjectName\":\"多单位关联表\",\"fieldMappings\":[{\"destApiName\":\"is_enable\",\"destName\":\"是否启用\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"true\",\"valueType\":1},{\"destApiName\":\"is_base\",\"destName\":\"基准单位\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"is_pricing\",\"destName\":\"定价单位\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"conversion_ratio\",\"destName\":\"转换比例\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"CrmConvertNumber\",\"sourceName\":\"转换比例（基准单位/当前单位）\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"unit_id\",\"destName\":\"单位名称\",\"destTargetApiName\":\"UnitInfoObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCurrentUnitIdFUNITID\",\"sourceName\":\"单位(ID)\",\"sourceTargetApiName\":\"BD_UNIT\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIALUNITCONVERT\",\"sourceObjectName\":\"物料单位换算\"},\"sourceTenantType\":2,\"streamName\":\"物料单位换算 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/basic.png", "id": "6433a31bcac99c7d78bd7936", "order": 30, "preconditionIds": ["k3connect", "crmconfig-multiple_unit", "crmconfig-close_old_category"], "sceneIds": ["jczl"], "tagIds": ["jczl", "Material", "jldw"], "title": "云星空物料同步到CRM_模板2", "updateTime": 1681289542068, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n推荐您在CRM中管理客户(含客户地址、客户财务信息)、联系人信息。然后可通过此模板将数据同步到云星空。\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/basic3.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"组织机构\",\"realObjApiName\":\"ORG_Organizations\",\"valid\":true},\"mainRealObjApiName\":\"ORG_Organizations\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FADDRESS1\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"详细地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户-地址信息\",\"realObjApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"valid\":true},{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBANKCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"银行账号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FOPENBANKNAME\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"开户银行\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户-银行信息\",\"realObjApiName\":\"BD_Customer.BD_CUSTBANK\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FCOUNTRY.FNumber\",\"fieldDefineType\":\"country\",\"fieldLabel\":\"国家(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FT_BD_CUSTOMEREXT.FPROVINCE.FNumber\",\"fieldDefineType\":\"province\",\"fieldLabel\":\"省份(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FT_BD_CUSTOMEREXT.FCITY.FNumber\",\"fieldDefineType\":\"city\",\"fieldLabel\":\"城市(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"客户名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTRADINGCURRID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"结算币别(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCreateOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"创建组织(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSELLER.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FADDRESS\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"通讯地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"客户编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICEBANKACCOUNT\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"银行账号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICETEL\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"开票联系电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICEADDRESS\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"开票通讯地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICEBANKNAME\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"开户银行\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTAXREGISTERCODE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"纳税登记号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FShortName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"简称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FINVOICETITLE\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"发票抬头\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTEL\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"联系电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FGroup\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"客户分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTaxRate.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"默认税率(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FWEBSITE\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"公司网址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRECCONDITIONID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"收款条件(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"客户编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户\",\"realObjApiName\":\"BD_Customer\",\"valid\":true},\"mainRealObjApiName\":\"BD_Customer\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FCompanyType\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"类型\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"姓名\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEmail\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"邮箱\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FPost\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"职务\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMobile\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"移动电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTel\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"固定电话\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBizAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"详细地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCompany.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"所属公司(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"联系人\",\"realObjApiName\":\"BD_CommonContact\",\"valid\":true},\"mainRealObjApiName\":\"BD_CommonContact\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ErpOrganizationObj\",\"destObjectName\":\"ERP组织机构\",\"fieldMappings\":[{\"destApiName\":\"name\",\"destName\":\"组织名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\"}],\"sourceObjectApiName\":\"ORG_Organizations\",\"sourceObjectName\":\"组织机构\"},\"sourceTenantType\":2,\"streamName\":\"ERP组织机构（不轮询）\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"debug\",\"syncTypeList\":[]}},{\"detailMappings\":[{\"destObjectApiName\":\"BD_Customer.BD_CUSTCONTACT\",\"destObjectName\":\"客户-地址信息\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FADDRESS1\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"address\",\"sourceName\":\"详细地址\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"AccountAddrObj\",\"sourceObjectName\":\"客户地址\"},{\"destObjectApiName\":\"BD_Customer.BD_CUSTBANK\",\"destObjectName\":\"客户-银行信息\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"虚拟主从字段\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FBANKCODE\",\"destName\":\"银行账号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_bank_no\",\"sourceName\":\"开户账户\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FOPENBANKNAME\",\"destName\":\"开户银行\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_bank\",\"sourceName\":\"开户银行\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"AccountFinInfoObj\",\"sourceObjectName\":\"客户财务信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BD_Customer\",\"destObjectName\":\"客户\",\"fieldMappings\":[{\"destApiName\":\"FCOUNTRY.FNumber\",\"destName\":\"国家(编码)\",\"destType\":\"country\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"country\",\"sourceName\":\"国家\",\"sourceType\":\"country\",\"value\":\"\"},{\"destApiName\":\"FT_BD_CUSTOMEREXT.FPROVINCE.FNumber\",\"destName\":\"省份(编码)\",\"destType\":\"province\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"province\",\"sourceName\":\"省\",\"sourceType\":\"province\",\"value\":\"\"},{\"destApiName\":\"FT_BD_CUSTOMEREXT.FCITY.FNumber\",\"destName\":\"城市(编码)\",\"destType\":\"city\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"city\",\"sourceName\":\"市\",\"sourceType\":\"city\",\"value\":\"\"},{\"destApiName\":\"FName\",\"destName\":\"客户名称\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"客户名称\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FTRADINGCURRID.FNumber\",\"destName\":\"结算币别(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_19HAb__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FCreateOrgId.FNumber\",\"destName\":\"创建组织(编码)\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_Wpkj4__c\",\"sourceName\":\"自定义字段\",\"sourceTargetApiName\":\"ErpOrganizationObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FSELLER.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceName\":\"负责人\",\"sourceType\":\"employee\",\"value\":\"owner\"},{\"destApiName\":\"FADDRESS\",\"destName\":\"通讯地址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"address\",\"sourceName\":\"详细地址\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FNumber\",\"destName\":\"客户编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_no\",\"sourceName\":\"客户编号\",\"sourceType\":\"auto_number\",\"value\":\"\"},{\"destApiName\":\"FDescription\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"FINVOICEBANKACCOUNT\",\"destName\":\"银行账号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_lcdyp__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICETEL\",\"destName\":\"开票联系电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_Ia1l0__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICEADDRESS\",\"destName\":\"开票通讯地址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_wklQ1__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICEBANKNAME\",\"destName\":\"开户银行\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_Q7gcp__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FTAXREGISTERCODE\",\"destName\":\"纳税登记号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"uniform_social_credit_code\",\"sourceName\":\"统一社会信用代码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FShortName\",\"destName\":\"简称\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_oHpR7__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FINVOICETITLE\",\"destName\":\"发票抬头\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_6xm40__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FTEL\",\"destName\":\"联系电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"tel\",\"sourceName\":\"电话\",\"sourceType\":\"phone_number\",\"value\":\"\"},{\"destApiName\":\"FGroup\",\"destName\":\"客户分组\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_0171d__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FTaxRate.FNumber\",\"destName\":\"默认税率(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_2v15b__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FWEBSITE\",\"destName\":\"公司网址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"url\",\"sourceName\":\"网址\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FRECCONDITIONID.FNumber\",\"destName\":\"收款条件(编码)\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_j7uX0__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"sourceObjectApiName\":\"AccountObj\",\"sourceObjectName\":\"客户\"},\"sourceTenantType\":1,\"streamName\":\"客户 CRM往ERP\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0 0 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"once\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BD_CommonContact\",\"destObjectName\":\"联系人\",\"fieldMappings\":[{\"destApiName\":\"FCompanyType\",\"destName\":\"类型\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"BD_Customer\",\"valueType\":1},{\"destApiName\":\"FName\",\"destName\":\"姓名\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"姓名\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FEmail\",\"destName\":\"邮箱\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"email\",\"sourceName\":\"邮件\",\"sourceType\":\"email\",\"value\":\"\"},{\"destApiName\":\"FPost\",\"destName\":\"职务\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"job_title\",\"sourceName\":\"职务\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FMobile\",\"destName\":\"移动电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"mobile1\",\"sourceName\":\"手机1\",\"sourceType\":\"phone_number\",\"value\":\"\"},{\"destApiName\":\"FTel\",\"destName\":\"固定电话\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"tel1\",\"sourceName\":\"电话1\",\"sourceType\":\"phone_number\",\"value\":\"\"},{\"destApiName\":\"FBizAddress\",\"destName\":\"详细地址\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"add\",\"sourceName\":\"地址\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FCompany.FNumber\",\"destName\":\"所属公司(编码)\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"ContactObj\",\"sourceObjectName\":\"联系人\"},\"sourceTenantType\":1,\"streamName\":\"联系人 CRM往ERP\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0 0 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"once\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/basic.png", "id": "6433a31bcac99c7d78bd7937", "order": 10, "preconditionIds": ["k3connect"], "sceneIds": ["jczl"], "tagIds": ["jczl", "cust", "contact"], "title": "CRM客户同步到云星空", "updateTime": *************, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果您已经完成了基础资料(客户和物料)的对接，可以使用此模板将云星空的销售价目表(含价格明细、适用客户)、可销控制同步到CRM的价目表、可售范围。如不需对接可销控制，请选择模板2。\\n## 使用此模板的前置条件\\r\\n- ✅ 开启可售范围(与价目表解耦)\\r\\n- ✅ CRM开启价目表\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ 已经对接了客户、产品\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/price_management1.png)\",\"erpObjInfos\":[{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_SAL_PriceList\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FPrice\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"价格\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售价目表-价格明细\",\"realObjApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\"valid\":true},{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_SAL_PriceList\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售价目表-适用客户\",\"realObjApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEffectiveDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"生效日\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FExpiryDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"失效日\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售价目表\",\"realObjApiName\":\"BD_SAL_PriceList\",\"valid\":true},\"mainRealObjApiName\":\"BD_SAL_PriceList\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_SC_CustMat\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"AvailableProductObj\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"产品\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"主键\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"产品\",\"realObjApiName\":\"SAL_SC_CustMat.AvailableProductObj\",\"valid\":true},{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_SC_CustMat\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"AvailableAccountObj\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"主键\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"客户\",\"realObjApiName\":\"SAL_SC_CustMat.AvailableAccountObj\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FCustomerId.FName\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"客户编码(名称)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"可销控制-客户物料\",\"realObjApiName\":\"SAL_SC_CustMat\",\"valid\":true},\"mainRealObjApiName\":\"SAL_SC_CustMat\"}],\"streamInfos\":[{\"detailMappings\":[{\"destObjectApiName\":\"PriceBookProductObj\",\"destObjectName\":\"价目表明细\",\"fieldMappings\":[{\"destApiName\":\"pricebook_id\",\"destName\":\"价目表\",\"destTargetApiName\":\"PriceBookObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"BD_SAL_PriceList\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"product_id\",\"destName\":\"产品\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"stand_price\",\"destName\":\"标准价格\",\"destQuoteFieldType\":\"currency\",\"destType\":\"quote\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FPrice\",\"sourceName\":\"价格\",\"sourceType\":\"currency\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\"sourceObjectName\":\"销售价目表-价格明细\"},{\"destObjectApiName\":\"PriceBookAccountObj\",\"destObjectName\":\"价目表适用客户\",\"fieldMappings\":[{\"destApiName\":\"price_book_id\",\"destName\":\"价目表\",\"destTargetApiName\":\"PriceBookObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"BD_SAL_PriceList\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1000\",\"valueType\":1},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustID.FNumber\",\"sourceName\":\"客户编码(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"sourceObjectName\":\"销售价目表-适用客户\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"PriceBookObj\",\"destObjectName\":\"价目表\",\"fieldMappings\":[{\"destApiName\":\"active_status\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"价目表名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"apply_account_range\",\"destName\":\"适用客户\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"ALL\\\",\\\"value\\\":\\\"ALL\\\"}\",\"valueType\":1},{\"destApiName\":\"start_date\",\"destName\":\"有效开始时间\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEffectiveDate\",\"sourceName\":\"生效日\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"end_date\",\"destName\":\"有效结束时间\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FExpiryDate\",\"sourceName\":\"失效日\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList\",\"sourceObjectName\":\"销售价目表\"},\"sourceTenantType\":2,\"streamName\":\"价目表 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"AvailableAccountObj\",\"destObjectName\":\"可售客户\",\"fieldMappings\":[{\"destApiName\":\"available_range_id\",\"destName\":\"可售范围\",\"destTargetApiName\":\"AvailableRangeObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_SC_CustMat\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"AvailableAccountObj\",\"sourceName\":\"客户\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_SC_CustMat.AvailableAccountObj\",\"sourceObjectName\":\"客户\"},{\"destObjectApiName\":\"AvailableProductObj\",\"destObjectName\":\"可售产品\",\"fieldMappings\":[{\"destApiName\":\"available_range_id\",\"destName\":\"可售范围\",\"destTargetApiName\":\"AvailableRangeObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_SC_CustMat\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"AvailableProductObj\",\"sourceName\":\"产品\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_SC_CustMat.AvailableProductObj\",\"sourceObjectName\":\"产品\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"AvailableRangeObj\",\"destObjectName\":\"可售范围\",\"fieldMappings\":[{\"destApiName\":\"product_range\",\"destName\":\"可售产品范围\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"valueType\":1},{\"destApiName\":\"status\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"account_range\",\"destName\":\"可售客户范围\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"org_range\",\"destName\":\"适用组织\",\"destTargetApiName\":\"PersonnelObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"ALL\\\",\\\"value\\\":\\\"ALL\\\"}\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"可售范围名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerId.FName\",\"sourceName\":\"客户编码(名称)\",\"sourceType\":\"text\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_SC_CustMat\",\"sourceObjectName\":\"可销控制-客户物料\"},\"sourceTenantType\":2,\"streamName\":\"可销控制 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 9-23 * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"09:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/price_list.png", "id": "6433a31bcac99c7d78bd7938", "order": 40, "preconditionIds": ["crmobj-AvailableRangeObj&AvailableAccountObj&!AvailablePriceBookObj", "crmobj-PriceBookObj&PriceBookProductObj", "k3connect", "stream-AccountObj&ProductObj"], "sceneIds": ["jggl"], "tagIds": ["jggl", "xsjmb", "kxkz"], "title": "云星空销售价目表同步到CRM_模板1", "updateTime": *************, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果您已经完成了基础资料(客户和物料)的对接，可以使用此模板将云星空的销售价目表(含价格明细、适用客户)同步到CRM的价目表、可售范围。此模板不含可销控制，如需请选择模板1。\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开启价目表\\r\\n- ✅ 开启可售范围(与价目表解耦)\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/price_management2.png)\",\"erpObjInfos\":[{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_SAL_PriceList\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FPrice\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"价格\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售价目表-价格明细\",\"realObjApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\"valid\":true},{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"BD_SAL_PriceList\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售价目表-适用客户\",\"realObjApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FName\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEffectiveDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"生效日\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FExpiryDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"失效日\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售价目表\",\"realObjApiName\":\"BD_SAL_PriceList\",\"valid\":true},\"mainRealObjApiName\":\"BD_SAL_PriceList\"}],\"streamInfos\":[{\"detailMappings\":[{\"destObjectApiName\":\"AvailableProductObj\",\"destObjectName\":\"可售产品\",\"fieldMappings\":[{\"destApiName\":\"available_range_id\",\"destName\":\"可售范围\",\"destTargetApiName\":\"AvailableRangeObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"BD_SAL_PriceList\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\"sourceObjectName\":\"销售价目表-价格明细\"},{\"destObjectApiName\":\"AvailableAccountObj\",\"destObjectName\":\"可售客户\",\"fieldMappings\":[{\"destApiName\":\"available_range_id\",\"destName\":\"可售范围\",\"destTargetApiName\":\"AvailableRangeObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"BD_SAL_PriceList\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustID.FNumber\",\"sourceName\":\"客户编码(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"sourceObjectName\":\"销售价目表-适用客户\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"AvailableRangeObj\",\"destObjectName\":\"可售范围\",\"fieldMappings\":[{\"destApiName\":\"product_range\",\"destName\":\"可售产品范围\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"valueType\":1},{\"destApiName\":\"status\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"account_range\",\"destName\":\"可售客户范围\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"FIXED\\\",\\\"value\\\":\\\"FIXED\\\"}\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"org_range\",\"destName\":\"适用组织\",\"destTargetApiName\":\"PersonnelObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"ALL\\\",\\\"value\\\":\\\"ALL\\\"}\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"可售范围名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList\",\"sourceObjectName\":\"销售价目表\"},\"sourceTenantType\":2,\"streamName\":\"价目表到可售范围 ERP往CRM\",\"syncRules\":{\"events\":[1],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"PriceBookProductObj\",\"destObjectName\":\"价目表明细\",\"fieldMappings\":[{\"destApiName\":\"pricebook_id\",\"destName\":\"价目表\",\"destTargetApiName\":\"PriceBookObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"BD_SAL_PriceList\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"product_id\",\"destName\":\"产品\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"stand_price\",\"destName\":\"标准价格\",\"destQuoteFieldType\":\"currency\",\"destType\":\"quote\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FPrice\",\"sourceName\":\"价格\",\"sourceType\":\"currency\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_PRICELISTENTRY\",\"sourceObjectName\":\"销售价目表-价格明细\"},{\"destObjectApiName\":\"PriceBookAccountObj\",\"destObjectName\":\"价目表适用客户\",\"fieldMappings\":[{\"destApiName\":\"price_book_id\",\"destName\":\"价目表\",\"destTargetApiName\":\"PriceBookObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"BD_SAL_PriceList\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustID.FNumber\",\"sourceName\":\"客户编码(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList.SAL_APPLYCUSTOMER\",\"sourceObjectName\":\"销售价目表-适用客户\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"PriceBookObj\",\"destObjectName\":\"价目表\",\"fieldMappings\":[{\"destApiName\":\"active_status\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"价目表名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FName\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"apply_account_range\",\"destName\":\"适用客户\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"use_range\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"{\\\"type\\\":\\\"ALL\\\",\\\"value\\\":\\\"ALL\\\"}\",\"valueType\":1},{\"destApiName\":\"start_date\",\"destName\":\"有效开始时间\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEffectiveDate\",\"sourceName\":\"生效日\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"end_date\",\"destName\":\"有效结束时间\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FExpiryDate\",\"sourceName\":\"失效日\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SAL_PriceList\",\"sourceObjectName\":\"销售价目表\"},\"sourceTenantType\":2,\"streamName\":\"价目表 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/price_list.png", "id": "6433a31bcac99c7d78bd7939", "order": 50, "preconditionIds": ["crmobj-PriceBookObj&PriceBookProductObj", "crmobj-AvailableRangeObj&AvailableAccountObj&!AvailablePriceBookObj"], "sceneIds": ["jggl"], "tagIds": ["jggl", "xsjmb"], "title": "云星空销售价目表同步到CRM_模板2", "updateTime": *************, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果您已经完成了基础资料(客户和物料)的对接，可以使用此模板将云星空的物料清单同步到CRM，这样就可以在CRM下销售订单啦，然后可将订单同步到云星空。\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开启CPQ\\r\\n- ✅ CRM不开启生成产品选配实例\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ 已经对接了客户、产品\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/quote2.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"SubHeadEntity.FSuite\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"套件\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"订单（虚拟主从字段）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDiscountRate\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"折扣率%\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FUnitID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"销售单位\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"销售数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTaxPrice\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"含税单价\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"root_prod_pkg_key\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟root key\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"parent_prod_pkg_key\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟parent key\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"prod_pkg_key\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟key\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRowType\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"产品类型\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FParentRowId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"父行标识\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRowId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"行标识\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBOMEntryId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ENG_BOM\",\"fieldLabel\":\"物料清单id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntryNote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"订单明细Id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"订单明细\",\"realObjApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalerId.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FChangeReason\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"变更原因\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FExchangeRate\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"汇率\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBillTypeID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"单据类型（编码）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"结算币别\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"销售组织\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"订单id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"ComId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"复合ID(Id#Number)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBillNo\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"单据编号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售订单\",\"realObjApiName\":\"SAL_SaleOrder\",\"valid\":true},\"mainRealObjApiName\":\"SAL_SaleOrder\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细Id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料清单-子项明细\",\"realObjApiName\":\"ENG_BOM.TreeEntity\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"isRequired\",\"fieldDefineType\":\"true_or_false\",\"fieldLabel\":\"是否必选\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"productId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"产品编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"root_id\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ENG_BOM\",\"fieldLabel\":\"根BOMid\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"quantity\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"parent_bom_id\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ENG_BOM\",\"fieldLabel\":\"父BOMid\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料清单\",\"realObjApiName\":\"ENG_BOM\",\"valid\":true},\"mainRealObjApiName\":\"ENG_BOM\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"is_package\",\"destName\":\"产品组合\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"0\"},{\"destOption\":true,\"sourceOption\":\"1\"}],\"sourceApiName\":\"SubHeadEntity.FSuite\",\"sourceName\":\"套件\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectName\":\"订单明细\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"订单（虚拟主从字段）\",\"destTargetApiName\":\"SAL_SaleOrder\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"order_id\",\"sourceName\":\"订单\",\"sourceTargetApiName\":\"SalesOrderObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FDiscountRate\",\"destName\":\"折扣率%\",\"destType\":\"text\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"100\",\"valueType\":1},{\"destApiName\":\"FMaterialId.FNumber\",\"destName\":\"物料编码\",\"destTargetApiName\":\"BD_MATERIAL\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"product_id\",\"sourceName\":\"产品名称\",\"sourceTargetApiName\":\"ProductObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FUnitID.FNumber\",\"destName\":\"销售单位\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"unit\",\"sourceName\":\"单位\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceType\":\"quote\",\"value\":\"\"},{\"destApiName\":\"FQty\",\"destName\":\"销售数量\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"quantity\",\"sourceName\":\"数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"FTaxPrice\",\"destName\":\"含税单价\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"sales_price\",\"sourceName\":\"销售单价（元）\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"root_prod_pkg_key\",\"destName\":\"产品组合虚拟root key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"root_prod_pkg_key\",\"sourceName\":\"产品组合虚拟root key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"parent_prod_pkg_key\",\"destName\":\"产品组合虚拟parent key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parent_prod_pkg_key\",\"sourceName\":\"产品组合虚拟parent key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"prod_pkg_key\",\"destName\":\"产品组合虚拟key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"prod_pkg_key\",\"sourceName\":\"产品组合虚拟key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FRowType\",\"destName\":\"产品类型\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"Standard\",\"sourceOption\":\"Standard\"},{\"destOption\":\"Parent\",\"sourceOption\":\"Parent\"},{\"destOption\":\"Son\",\"sourceOption\":\"Son\"},{\"destOption\":\"Service\",\"sourceOption\":\"Service\"}],\"sourceApiName\":\"field_nm52a__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"FParentRowId\",\"destName\":\"父行标识\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parent_prod_pkg_key\",\"sourceName\":\"产品组合虚拟parent key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FRowId\",\"destName\":\"行标识\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"prod_pkg_key\",\"sourceName\":\"产品组合虚拟key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FBOMEntryId\",\"destName\":\"物料清单id\",\"destTargetApiName\":\"ENG_BOM\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"bom_id\",\"sourceName\":\"产品选配明细\",\"sourceTargetApiName\":\"BOMObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FEntryNote\",\"destName\":\"备注\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"SalesOrderProductObj\",\"sourceObjectName\":\"订单产品\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"SAL_SaleOrder\",\"destObjectName\":\"销售订单\",\"fieldMappings\":[{\"destApiName\":\"FSalerId.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceName\":\"负责人\",\"sourceType\":\"employee\",\"value\":\"owner\"},{\"destApiName\":\"FChangeReason\",\"destName\":\"变更原因\",\"destType\":\"text\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"编辑了订单\",\"valueType\":1},{\"destApiName\":\"FSaleOrderFinance.FExchangeRate\",\"destName\":\"汇率\",\"destType\":\"number\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"FBillTypeID.FNumber\",\"destName\":\"单据类型（编码）\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"XSDD01_SYS\",\"valueType\":1},{\"destApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"destName\":\"结算币别\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"PRE001\",\"sourceOption\":\"option1\"},{\"destOption\":\"PRE007\",\"sourceOption\":\"d6zNr0p14\"}],\"sourceApiName\":\"field_xTd41__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"FCustId.FNumber\",\"destName\":\"客户\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"defaultValue\":\"000\",\"destApiName\":\"FSaleOrgId.FNumber\",\"destName\":\"销售组织\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_f4801__c\",\"sourceName\":\"自定义字段\",\"sourceTargetApiName\":\"ErpOrganizationObj\",\"sourceType\":\"object_reference\",\"value\":\"\",\"valueType\":2},{\"destApiName\":\"FDate\",\"destName\":\"日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_time\",\"sourceName\":\"下单日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"FNote\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"SalesOrderObj\",\"sourceObjectName\":\"销售订单\"},\"sourceTenantType\":1,\"streamName\":\"订单 CRM往ERP\",\"syncRules\":{\"events\":[1,2,3,5,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BOMObj\",\"destObjectName\":\"产品选配明细\",\"fieldMappings\":[{\"destApiName\":\"record_type\",\"destName\":\"业务类型\",\"destType\":\"record_type\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"default__c\",\"valueType\":1},{\"destApiName\":\"amount_editable\",\"destName\":\"数量可编辑\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"true\",\"valueType\":1},{\"destApiName\":\"enabled_status\",\"destName\":\"启用状态\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"true\",\"valueType\":1},{\"destApiName\":\"price_editable\",\"destName\":\"价格可编辑\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"true\",\"valueType\":1},{\"destApiName\":\"selected_by_default\",\"destName\":\"默认选中\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"true\",\"valueType\":1},{\"destApiName\":\"is_required\",\"destName\":\"必选\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"false\"},{\"destOption\":true,\"sourceOption\":\"true\"}],\"sourceApiName\":\"isRequired\",\"sourceName\":\"是否必选\",\"sourceType\":\"true_or_false\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"productId\",\"sourceName\":\"产品编码\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"root_id\",\"destName\":\"根BOMid\",\"destTargetApiName\":\"BOMObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"root_id\",\"sourceName\":\"根BOMid\",\"sourceTargetApiName\":\"ENG_BOM\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"amount\",\"destName\":\"数量\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"quantity\",\"sourceName\":\"数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"parent_bom_id\",\"destName\":\"父BOMid\",\"destTargetApiName\":\"BOMObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parent_bom_id\",\"sourceName\":\"父BOMid\",\"sourceTargetApiName\":\"ENG_BOM\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"ENG_BOM\",\"sourceObjectName\":\"物料清单\"},\"sourceTenantType\":2,\"streamName\":\"产品选配明细 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/bom_img.png", "id": "6433a31bcac99c7d78bd793a", "order": 60, "preconditionIds": ["crmconfig-cpq", "crmconfig-!bom_instance", "k3connect", "stream-AccountObj&ProductObj"], "sceneIds": ["bjgl"], "tagIds": ["bjgl", "BOM", "salesOrder"], "title": "云星空物料清单同步到CRM", "updateTime": *************, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果您已经完成了基础资料(客户和物料)的对接，并且在CRM使用CPQ管理报价和下销售订单，那么您可以使用此模板将相关数据同步到云星空，包括产品选配实例和销售订单。\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ CRM开启生成产品选配实例\\r\\n- ✅ CRM开启CPQ\\r\\n- ✅ 已经对接了客户、产品\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/quote1.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"SubHeadEntity.FSuite\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"套件\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"订单（虚拟主从字段）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDiscountRate\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"折扣率%\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FUnitID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"销售单位\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"销售数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FTaxPrice\",\"fieldDefineType\":\"currency\",\"fieldLabel\":\"含税单价\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"root_prod_pkg_key\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟root key\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"parent_prod_pkg_key\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟parent key\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"prod_pkg_key\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟key\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRowType\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"产品类型\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"bom_instance_tree_id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟bom_instance_tree_id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"bom_id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"产品组合虚拟bom_id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntryNote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"订单明细Id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"订单明细\",\"realObjApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalerId.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FChangeReason\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"变更原因\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FExchangeRate\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"汇率\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBillTypeID.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"单据类型（编码）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"结算币别\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSaleOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"销售组织\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"订单id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"ComId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"复合ID(Id#Number)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"单据编号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售订单\",\"realObjApiName\":\"SAL_SaleOrder\",\"valid\":true},\"mainRealObjApiName\":\"SAL_SaleOrder\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细Id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料清单-子项明细\",\"realObjApiName\":\"ENG_BOM.TreeEntity\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FBILLTYPE.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"单据类型(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBOMCATEGORY\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"BOM分类\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FBOMUSE\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"BOM用途\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCreateOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"创建组织(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FUseOrgId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"ORG_Organizations\",\"fieldLabel\":\"使用组织(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMATERIALID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"父项物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料清单\",\"realObjApiName\":\"ENG_BOM\",\"valid\":true},\"mainRealObjApiName\":\"ENG_BOM\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"is_package\",\"destName\":\"产品组合\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"0\"},{\"destOption\":true,\"sourceOption\":\"1\"}],\"sourceApiName\":\"SubHeadEntity.FSuite\",\"sourceName\":\"套件\",\"sourceType\":\"select_one\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectName\":\"订单明细\",\"fieldMappings\":[{\"destApiName\":\"fake_master_detail\",\"destName\":\"订单（虚拟主从字段）\",\"destTargetApiName\":\"SAL_SaleOrder\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"order_id\",\"sourceName\":\"订单\",\"sourceTargetApiName\":\"SalesOrderObj\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"FDiscountRate\",\"destName\":\"折扣率%\",\"destType\":\"text\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"100\",\"valueType\":1},{\"destApiName\":\"FMaterialId.FNumber\",\"destName\":\"物料编码\",\"destTargetApiName\":\"BD_MATERIAL\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"product_id\",\"sourceName\":\"产品名称\",\"sourceTargetApiName\":\"ProductObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FUnitID.FNumber\",\"destName\":\"销售单位\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"002\",\"sourceOption\":\"1\"},{\"destOption\":\"100011\",\"sourceOption\":\"7\"},{\"destOption\":\"WJ001\",\"sourceOption\":\"3\"}],\"sourceApiName\":\"unit\",\"sourceName\":\"单位\",\"sourceQuoteFieldTargetObjectApiName\":\"ProductObj\",\"sourceQuoteFieldTargetObjectField\":\"unit\",\"sourceQuoteFieldType\":\"select_one\",\"sourceQuoteRealField\":\"product_id\",\"sourceType\":\"quote\",\"value\":\"\"},{\"destApiName\":\"FQty\",\"destName\":\"销售数量\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"quantity\",\"sourceName\":\"数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"FTaxPrice\",\"destName\":\"含税单价\",\"destType\":\"currency\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"sales_price\",\"sourceName\":\"销售单价（元）\",\"sourceType\":\"currency\",\"value\":\"\"},{\"destApiName\":\"root_prod_pkg_key\",\"destName\":\"产品组合虚拟root key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"root_prod_pkg_key\",\"sourceName\":\"产品组合虚拟root key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"parent_prod_pkg_key\",\"destName\":\"产品组合虚拟parent key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parent_prod_pkg_key\",\"sourceName\":\"产品组合虚拟parent key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"prod_pkg_key\",\"destName\":\"产品组合虚拟key\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"prod_pkg_key\",\"sourceName\":\"产品组合虚拟key\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"FRowType\",\"destName\":\"产品类型\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"Standard\",\"sourceOption\":\"Standard\"},{\"destOption\":\"Parent\",\"sourceOption\":\"Parent\"},{\"destOption\":\"Son\",\"sourceOption\":\"Son\"},{\"destOption\":\"Service\",\"sourceOption\":\"Service\"}],\"sourceApiName\":\"field_nm52a__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"bom_instance_tree_id\",\"destName\":\"产品组合虚拟bom_instance_tree_id\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"bom_instance_tree_id\",\"sourceName\":\"实例树ID\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"bom_id\",\"destName\":\"产品组合虚拟bom_id\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"bom_id\",\"sourceName\":\"产品选配明细\",\"sourceTargetApiName\":\"BOMObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"FEntryNote\",\"destName\":\"备注\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"SalesOrderProductObj\",\"sourceObjectName\":\"订单产品\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"SAL_SaleOrder\",\"destObjectName\":\"销售订单\",\"fieldMappings\":[{\"destApiName\":\"FSalerId.FNumber\",\"destName\":\"销售员(编码)\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"owner\",\"sourceName\":\"负责人\",\"sourceType\":\"employee\",\"value\":\"owner\"},{\"destApiName\":\"FChangeReason\",\"destName\":\"变更原因\",\"destType\":\"text\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"编辑了订单\",\"valueType\":1},{\"destApiName\":\"FSaleOrderFinance.FExchangeRate\",\"destName\":\"汇率\",\"destType\":\"number\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"FBillTypeID.FNumber\",\"destName\":\"单据类型（编码）\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"XSDD01_SYS\",\"valueType\":1},{\"destApiName\":\"FSaleOrderFinance.FSettleCurrId.FNumber\",\"destName\":\"结算币别\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"PRE001\",\"sourceOption\":\"option1\"},{\"destOption\":\"PRE007\",\"sourceOption\":\"d6zNr0p14\"}],\"sourceApiName\":\"field_xTd41__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"FCustId.FNumber\",\"destName\":\"客户\",\"destTargetApiName\":\"BD_Customer\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"account_id\",\"sourceName\":\"客户名称\",\"sourceTargetApiName\":\"AccountObj\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"defaultValue\":\"000\",\"destApiName\":\"FSaleOrgId.FNumber\",\"destName\":\"销售组织\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"field_f4801__c\",\"sourceName\":\"自定义字段\",\"sourceTargetApiName\":\"ErpOrganizationObj\",\"sourceType\":\"object_reference\",\"value\":\"\",\"valueType\":2},{\"destApiName\":\"FDate\",\"destName\":\"日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"order_time\",\"sourceName\":\"下单日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"FNote\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"remark\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"F_PAEZ_Attachment\",\"destName\":\"附件\",\"destType\":\"file_attachment\",\"mappingType\":2002,\"optionMappings\":[],\"sourceApiName\":\"field_j3d4z__c\",\"sourceName\":\"自定义字段\",\"sourceType\":\"file_attachment\",\"value\":\"\"}],\"sourceObjectApiName\":\"SalesOrderObj\",\"sourceObjectName\":\"销售订单\"},\"sourceTenantType\":1,\"streamName\":\"订单 CRM往ERP\",\"syncRules\":{\"events\":[1,2,3,5,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ENG_BOM\",\"destObjectName\":\"物料清单\",\"fieldMappings\":[{\"destApiName\":\"FBILLTYPE.FNumber\",\"destName\":\"单据类型(编码)\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"WLQD01_SYS\",\"valueType\":1},{\"destApiName\":\"FBOMCATEGORY\",\"destName\":\"BOM分类\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"FBOMUSE\",\"destName\":\"BOM用途\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"99\",\"valueType\":1},{\"destApiName\":\"FCreateOrgId.FNumber\",\"destName\":\"创建组织(编码)\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"000\",\"valueType\":1},{\"destApiName\":\"FUseOrgId.FNumber\",\"destName\":\"使用组织(编码)\",\"destTargetApiName\":\"ORG_Organizations\",\"destType\":\"object_reference\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"000\",\"valueType\":1},{\"destApiName\":\"FMATERIALID.FNumber\",\"destName\":\"父项物料编码(编码)\",\"destTargetApiName\":\"BD_MATERIAL\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"product_id\",\"sourceName\":\"产品名称\",\"sourceTargetApiName\":\"ProductObj\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BomInstanceObj\",\"sourceObjectName\":\"产品选配实例\"},\"sourceTenantType\":1,\"streamName\":\"产品选配实例 CRM往ERP\",\"syncRules\":{\"events\":[1,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"\",\"intervalQuantity\":6,\"startDataTime\":\"\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/bom_img.png", "id": "6433a31bcac99c7d78bd793b", "order": 70, "preconditionIds": ["k3connect", "crmconfig-bom_instance", "crmconfig-cpq", "stream-AccountObj&ProductObj"], "sceneIds": ["bjgl"], "tagIds": ["bjgl", "BOM", "salesOrder"], "title": "CRM产品选配实例同步到云星空", "updateTime": *************, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果企业在云星空管理发货和库存，则可以通过此模板将相关数据同步到CRM，这需要CRM先开通B类库存。此模板包含序列号和批次号主档的同步，不包含仓位。\\n## 使用此模板的前置条件\\r\\n- ✅ 即时库存中间对象主键改为FID\\r\\n- ✅ CRM开启CRM批次与序列号管理\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ CRM不开启多单位\\r\\n- ✅ CRM开通B类库存、选择的是同步库存明细\\r\\n- ✅ 集成平台配置发货单产品按序列号拆单\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/shipping_and_inventory1.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FForbidStatus\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"禁用状态\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"comName\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"复合名称(编码#名称)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialGroup\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"物料分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"VirtualHasBatchAndSerial\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"是否开启批次序列号管理（虚拟）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"基本单位(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"描述\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSpecification\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"规格型号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"仓库地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"仓库\",\"realObjApiName\":\"BD_STOCK\",\"valid\":true},\"mainRealObjApiName\":\"BD_STOCK\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"批号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FHProduceDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"生产日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FHExpiryDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"有效期至\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"批号主档\",\"realObjApiName\":\"BD_BatchMainFile\",\"valid\":true},\"mainRealObjApiName\":\"BD_BatchMainFile\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"序列号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSubHeadEntity.FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"序列号主档\",\"realObjApiName\":\"BD_SerialMainFile\",\"valid\":true},\"mainRealObjApiName\":\"BD_SerialMainFile\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FBaseQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"库存量(基本单位)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FLot\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_BatchMainFile\",\"fieldLabel\":\"批号ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"stockComId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"库存ID（仓库编号#物料编号）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FID\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"即时库存\",\"realObjApiName\":\"STK_Inventory\",\"valid\":true},\"mainRealObjApiName\":\"STK_Inventory\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_OUTSTOCK\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"serialId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_SerialMainFile\",\"fieldLabel\":\"序列号id（拆单）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntrynote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRealQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"实发数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSOEntryId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldLabel\":\"销售订单EntryId\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FLotFLOTID\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_BatchMainFile\",\"fieldLabel\":\"批号(ID)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单-明细信息\",\"realObjApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalesManID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustomerID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单\",\"realObjApiName\":\"SAL_OUTSTOCK\",\"valid\":true},\"mainRealObjApiName\":\"SAL_OUTSTOCK\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"price\",\"destName\":\"标准价格\",\"destType\":\"currency\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"0\",\"valueType\":1},{\"destApiName\":\"product_status\",\"destName\":\"上下架\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"A\"},{\"destOption\":\"2\",\"sourceOption\":\"B\"}],\"sourceApiName\":\"FForbidStatus\",\"sourceName\":\"禁用状态\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"编码#名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceName\":\"复合名称(编码#名称)\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"product_category_id\",\"destName\":\"产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup\",\"sourceName\":\"物料分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"batch_sn\",\"destName\":\"批次与序列号管理\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"1\"},{\"destOption\":\"3\",\"sourceOption\":\"3\"},{\"destOption\":\"2\",\"sourceOption\":\"2\"}],\"sourceApiName\":\"VirtualHasBatchAndSerial\",\"sourceName\":\"是否开启批次序列号管理（虚拟）\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destName\":\"单位\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"Pcs\"}],\"sourceApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"sourceName\":\"基本单位(编码)\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"product_code\",\"destName\":\"产品编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"field_ZQYo6__c\",\"destName\":\"自定义字段\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"描述\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"product_spec\",\"destName\":\"规格属性\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSpecification\",\"sourceName\":\"规格型号\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"WarehouseObj\",\"destObjectName\":\"仓库\",\"fieldMappings\":[{\"destApiName\":\"is_default\",\"destName\":\"是否默认仓\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"is_enable\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"number\",\"destName\":\"仓库编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceName\":\"编码\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"仓库名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"address\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAddress\",\"sourceName\":\"仓库地址\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_STOCK\",\"sourceObjectName\":\"仓库\"},\"sourceTenantType\":2,\"streamName\":\"仓库 ERP往CRM\",\"syncRules\":{\"events\":[1],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"2022-03-31T15:59:00.000Z\",\"intervalQuantity\":6,\"startDataTime\":\"2022-03-30T16:00:00.000Z\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BatchObj\",\"destObjectName\":\"批次\",\"fieldMappings\":[{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"批次编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"批号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"manufacture_date\",\"destName\":\"生产日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FHProduceDate\",\"sourceName\":\"生产日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"expiry_date\",\"destName\":\"有效日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FHExpiryDate\",\"sourceName\":\"有效期至\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_BatchMainFile\",\"sourceObjectName\":\"批号主档\"},\"sourceTenantType\":2,\"streamName\":\"批号主档 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"SerialNumberObj\",\"destObjectName\":\"序列号\",\"fieldMappings\":[{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"序列号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"序列号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"所属仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSubHeadEntity.FStockId.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SerialMainFile\",\"sourceObjectName\":\"序列号主档\"},\"sourceTenantType\":2,\"streamName\":\"序列号 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InventoryDetailsObj\",\"destObjectName\":\"库存明细\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"real_stock\",\"destName\":\"实际库存\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBaseQty\",\"sourceName\":\"库存量(基本单位)\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockId.FNumber\",\"sourceName\":\"仓库编码(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"物料名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"batch_id\",\"destName\":\"批次\",\"destTargetApiName\":\"BatchObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FLot\",\"sourceName\":\"批号ID\",\"sourceTargetApiName\":\"BD_BatchMainFile\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"STK_Inventory\",\"sourceObjectName\":\"即时库存\"},\"sourceTenantType\":2,\"streamName\":\"即时库存 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"DeliveryNoteProductObj\",\"destObjectName\":\"发货单产品\",\"fieldMappings\":[{\"destApiName\":\"delivery_note_id\",\"destName\":\"发货单编号\",\"destTargetApiName\":\"DeliveryNoteObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_OUTSTOCK\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"serial_number_id\",\"destName\":\"序列号\",\"destTargetApiName\":\"SerialNumberObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"serialId\",\"sourceName\":\"序列号id（拆单）\",\"sourceTargetApiName\":\"BD_SerialMainFile\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntrynote\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"delivery_warehouse_id\",\"destName\":\"发货仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockID.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_num\",\"destName\":\"本次发货数（基准单位）\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRealQty\",\"sourceName\":\"实发数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"sales_order_product_id\",\"destName\":\"订单产品编号\",\"destTargetApiName\":\"SalesOrderProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSOEntryId\",\"sourceName\":\"销售订单EntryId\",\"sourceTargetApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"batch_id\",\"destName\":\"批次编号\",\"destTargetApiName\":\"BatchObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FLotFLOTID\",\"sourceName\":\"批号(ID)\",\"sourceTargetApiName\":\"BD_BatchMainFile\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"sourceObjectName\":\"销售出库单-明细信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"DeliveryNoteObj\",\"destObjectName\":\"发货单\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSalesManID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\",\"valueType\":1},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"receive_remark\",\"destName\":\"收货备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_date\",\"destName\":\"发货日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDate\",\"sourceName\":\"日期\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK\",\"sourceObjectName\":\"销售出库单\"},\"sourceTenantType\":2,\"streamName\":\"销售出库单 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/shipments.png", "id": "6433a31bcac99c7d78bd793c", "order": 80, "preconditionIds": ["jskcidcheck", "crmobj-BatchObj&SerialNumberObj", "k3connect", "crmconfig-!multiple_unit", "stockconfig-king_dee_k3c_sync_type-2", "tenantconfig-outStockSerialNumber-1"], "sceneIds": ["fhhkc"], "tagIds": ["fhhkcgl", "xlhhpc"], "title": "云星空库存同步到CRM_模板1", "updateTime": 1681468157272, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果企业在云星空管理发货和库存，则可以通过此模板将相关数据同步到CRM，这需要CRM先开通B类库存。此模板包含序列号和批次号主档、仓位的同步。\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开启CRM批次与序列号管理\\r\\n- ✅ 即时库存中间对象主键改为FID\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ CRM不开启多单位\\r\\n- ✅ CRM开通B类库存、选择的是同步库存明细\\r\\n- ✅ CRM开启仓位\\r\\n- ✅ 集成平台配置发货单产品按序列号拆单\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/shipping_and_inventory1.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FForbidStatus\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"禁用状态\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"comName\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"复合名称(编码#名称)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialGroup\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_MATERIALGROUP\",\"fieldLabel\":\"物料分组\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"VirtualHasBatchAndSerial\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"是否开启批次序列号管理（虚拟）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"fieldDefineType\":\"select_one\",\"fieldLabel\":\"基本单位(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDescription\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"描述\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSpecification\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"规格型号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"物料\",\"realObjApiName\":\"BD_MATERIAL\",\"valid\":true},\"mainRealObjApiName\":\"BD_MATERIAL\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"parentWarehouseNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"母仓库编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"isLoc\",\"fieldDefineType\":\"true_or_false\",\"fieldLabel\":\"是否仓位\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"仓库地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"仓库\",\"realObjApiName\":\"BD_STOCK\",\"valid\":true},\"mainRealObjApiName\":\"BD_STOCK\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"批号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FHProduceDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"生产日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FHExpiryDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"有效期至\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"批号主档\",\"realObjApiName\":\"BD_BatchMainFile\",\"valid\":true},\"mainRealObjApiName\":\"BD_BatchMainFile\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNumber\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"序列号\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSubHeadEntity.FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"序列号主档\",\"realObjApiName\":\"BD_SerialMainFile\",\"valid\":true},\"mainRealObjApiName\":\"BD_SerialMainFile\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FBaseQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"库存量(基本单位)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FLot\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_BatchMainFile\",\"fieldLabel\":\"批号ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"stockComId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"库存ID（仓库编号#物料编号）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FID\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"即时库存\",\"realObjApiName\":\"STK_Inventory\",\"valid\":true},\"mainRealObjApiName\":\"STK_Inventory\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_OUTSTOCK\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"serialId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_SerialMainFile\",\"fieldLabel\":\"序列号id（拆单）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntrynote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRealQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"实发数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSOEntryId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldLabel\":\"销售订单EntryId\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FLotFLOTID\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_BatchMainFile\",\"fieldLabel\":\"批号(ID)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单-明细信息\",\"realObjApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalesManID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustomerID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单\",\"realObjApiName\":\"SAL_OUTSTOCK\",\"valid\":true},\"mainRealObjApiName\":\"SAL_OUTSTOCK\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"ProductObj\",\"destObjectName\":\"产品\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"price\",\"destName\":\"标准价格\",\"destType\":\"currency\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"0\",\"valueType\":1},{\"destApiName\":\"product_status\",\"destName\":\"上下架\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"A\"},{\"destOption\":\"2\",\"sourceOption\":\"B\"}],\"sourceApiName\":\"FForbidStatus\",\"sourceName\":\"禁用状态\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"编码#名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"comName\",\"sourceName\":\"复合名称(编码#名称)\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"product_category_id\",\"destName\":\"产品分类\",\"destTargetApiName\":\"ProductCategoryObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialGroup\",\"sourceName\":\"物料分组\",\"sourceTargetApiName\":\"SAL_MATERIALGROUP\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"batch_sn\",\"destName\":\"批次与序列号管理\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"1\"},{\"destOption\":\"3\",\"sourceOption\":\"3\"},{\"destOption\":\"2\",\"sourceOption\":\"2\"}],\"sourceApiName\":\"VirtualHasBatchAndSerial\",\"sourceName\":\"是否开启批次序列号管理（虚拟）\",\"sourceType\":\"select_one\",\"value\":\"\"},{\"destApiName\":\"unit\",\"destName\":\"单位\",\"destType\":\"select_one\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":\"1\",\"sourceOption\":\"Pcs\"}],\"sourceApiName\":\"SubHeadEntity.FBaseUnitId.FNumber\",\"sourceName\":\"基本单位(编码)\",\"sourceType\":\"select_one\",\"value\":\"\",\"valueType\":1},{\"destApiName\":\"product_code\",\"destName\":\"产品编码\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"编码\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"field_ZQYo6__c\",\"destName\":\"自定义字段\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDescription\",\"sourceName\":\"描述\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"product_spec\",\"destName\":\"规格属性\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSpecification\",\"sourceName\":\"规格型号\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_MATERIAL\",\"sourceObjectName\":\"物料\"},\"sourceTenantType\":2,\"streamName\":\"物料 ERP往CRM\",\"syncRules\":{\"events\":[1,2],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"WarehouseObj\",\"destObjectName\":\"仓库\",\"fieldMappings\":[{\"destApiName\":\"is_default\",\"destName\":\"是否默认仓\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"is_enable\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"number\",\"destName\":\"仓库编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceName\":\"编码\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"仓库名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"parent_warehouse\",\"destName\":\"母仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parentWarehouseNumber\",\"sourceName\":\"母仓库编码\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"whether_position\",\"destName\":\"是否为仓位\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"false\"},{\"destOption\":true,\"sourceOption\":\"true\"}],\"sourceApiName\":\"isLoc\",\"sourceName\":\"是否仓位\",\"sourceType\":\"true_or_false\",\"value\":\"\"},{\"destApiName\":\"address\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAddress\",\"sourceName\":\"仓库地址\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_STOCK\",\"sourceObjectName\":\"仓库\"},\"sourceTenantType\":2,\"streamName\":\"仓库 ERP往CRM\",\"syncRules\":{\"events\":[1],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"2022-03-31T15:59:00.000Z\",\"intervalQuantity\":6,\"startDataTime\":\"2022-03-30T16:00:00.000Z\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"BatchObj\",\"destObjectName\":\"批次\",\"fieldMappings\":[{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"批次编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"批号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"manufacture_date\",\"destName\":\"生产日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FHProduceDate\",\"sourceName\":\"生产日期\",\"sourceType\":\"date\",\"value\":\"\"},{\"destApiName\":\"expiry_date\",\"destName\":\"有效日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FHExpiryDate\",\"sourceName\":\"有效期至\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_BatchMainFile\",\"sourceObjectName\":\"批号主档\"},\"sourceTenantType\":2,\"streamName\":\"批号主档 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"SerialNumberObj\",\"destObjectName\":\"序列号\",\"fieldMappings\":[{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"name\",\"destName\":\"序列号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceName\":\"序列号\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"所属仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSubHeadEntity.FStockId.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_SerialMainFile\",\"sourceObjectName\":\"序列号主档\"},\"sourceTenantType\":2,\"streamName\":\"序列号 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InventoryDetailsObj\",\"destObjectName\":\"库存明细\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"real_stock\",\"destName\":\"实际库存\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBaseQty\",\"sourceName\":\"库存量(基本单位)\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockId.FNumber\",\"sourceName\":\"仓库编码(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"物料名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"batch_id\",\"destName\":\"批次\",\"destTargetApiName\":\"BatchObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FLot\",\"sourceName\":\"批号ID\",\"sourceTargetApiName\":\"BD_BatchMainFile\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"STK_Inventory\",\"sourceObjectName\":\"即时库存\"},\"sourceTenantType\":2,\"streamName\":\"即时库存 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"DeliveryNoteProductObj\",\"destObjectName\":\"发货单产品\",\"fieldMappings\":[{\"destApiName\":\"delivery_note_id\",\"destName\":\"发货单编号\",\"destTargetApiName\":\"DeliveryNoteObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_OUTSTOCK\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"serial_number_id\",\"destName\":\"序列号\",\"destTargetApiName\":\"SerialNumberObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"serialId\",\"sourceName\":\"序列号id（拆单）\",\"sourceTargetApiName\":\"BD_SerialMainFile\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntrynote\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"delivery_warehouse_id\",\"destName\":\"发货仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockID.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_num\",\"destName\":\"本次发货数（基准单位）\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRealQty\",\"sourceName\":\"实发数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"sales_order_product_id\",\"destName\":\"订单产品编号\",\"destTargetApiName\":\"SalesOrderProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSOEntryId\",\"sourceName\":\"销售订单EntryId\",\"sourceTargetApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"batch_id\",\"destName\":\"批次编号\",\"destTargetApiName\":\"BatchObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FLotFLOTID\",\"sourceName\":\"批号(ID)\",\"sourceTargetApiName\":\"BD_BatchMainFile\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"sourceObjectName\":\"销售出库单-明细信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"DeliveryNoteObj\",\"destObjectName\":\"发货单\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSalesManID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\",\"valueType\":1},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"receive_remark\",\"destName\":\"收货备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_date\",\"destName\":\"发货日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDate\",\"sourceName\":\"日期\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK\",\"sourceObjectName\":\"销售出库单\"},\"sourceTenantType\":2,\"streamName\":\"销售出库单 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/shipments.png", "id": "6433a31bcac99c7d78bd793d", "order": 90, "preconditionIds": ["crmobj-BatchObj&SerialNumberObj", "jskcidcheck", "k3connect", "crmconfig-!multiple_unit", "stockconfig-king_dee_k3c_sync_type-2", "stockconfig-warehouse_module_open_ware_position-1", "tenantconfig-outStockSerialNumber-1"], "sceneIds": ["fhhkc"], "tagIds": ["fhhkcgl", "stockLoc", "xlhhpc"], "title": "云星空库存同步到CRM_模板2", "updateTime": 1681473813173, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果企业在云星空管理发货和库存，则可以通过此模板将相关数据同步到CRM，这需要CRM先开通B类库存。此模板包含仓位的同步，不包含序列号和批次号主档。\\n## 使用此模板的前置条件\\r\\n- ✅ 即时库存中间对象主键改为FID\\r\\n- ✅ CRM开通B类库存、选择的是同步库存明细（弃用）\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ CRM不开启CRM批次与序列号管理\\r\\n- ✅ CRM不开启多单位\\r\\n- ✅ CRM开通B类库存、选择的是同步库存明细\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/shipping_and_inventory2.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"parentWarehouseNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"母仓库编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"isLoc\",\"fieldDefineType\":\"true_or_false\",\"fieldLabel\":\"是否仓位\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"仓库地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"仓库\",\"realObjApiName\":\"BD_STOCK\",\"valid\":true},\"mainRealObjApiName\":\"BD_STOCK\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FBaseQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"库存量(基本单位)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FID\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"stockComId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"库存ID（仓库编号#物料编号）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"即时库存\",\"realObjApiName\":\"STK_Inventory\",\"valid\":true},\"mainRealObjApiName\":\"STK_Inventory\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_OUTSTOCK\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntrynote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRealQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"实发数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSOEntryId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldLabel\":\"销售订单EntryId\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单-明细信息\",\"realObjApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalesManID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustomerID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单\",\"realObjApiName\":\"SAL_OUTSTOCK\",\"valid\":true},\"mainRealObjApiName\":\"SAL_OUTSTOCK\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"WarehouseObj\",\"destObjectName\":\"仓库\",\"fieldMappings\":[{\"destApiName\":\"is_default\",\"destName\":\"是否默认仓\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"is_enable\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"number\",\"destName\":\"仓库编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceName\":\"编码\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"仓库名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"parent_warehouse\",\"destName\":\"母仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"parentWarehouseNumber\",\"sourceName\":\"母仓库编码\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"whether_position\",\"destName\":\"是否为仓位\",\"destType\":\"true_or_false\",\"mappingType\":1,\"optionMappings\":[{\"destOption\":false,\"sourceOption\":\"false\"},{\"destOption\":true,\"sourceOption\":\"true\"}],\"sourceApiName\":\"isLoc\",\"sourceName\":\"是否仓位\",\"sourceType\":\"true_or_false\",\"value\":\"\"},{\"destApiName\":\"address\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAddress\",\"sourceName\":\"仓库地址\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_STOCK\",\"sourceObjectName\":\"仓库\"},\"sourceTenantType\":2,\"streamName\":\"仓库 ERP往CRM\",\"syncRules\":{\"events\":[1],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"2022-03-31T15:59:00.000Z\",\"intervalQuantity\":6,\"startDataTime\":\"2022-03-30T16:00:00.000Z\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InventoryDetailsObj\",\"destObjectName\":\"库存明细\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"real_stock\",\"destName\":\"实际库存\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBaseQty\",\"sourceName\":\"库存量(基本单位)\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockId.FNumber\",\"sourceName\":\"仓库编码(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"物料名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"STK_Inventory\",\"sourceObjectName\":\"即时库存\"},\"sourceTenantType\":2,\"streamName\":\"即时库存 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"DeliveryNoteProductObj\",\"destObjectName\":\"发货单产品\",\"fieldMappings\":[{\"destApiName\":\"delivery_note_id\",\"destName\":\"发货单编号\",\"destTargetApiName\":\"DeliveryNoteObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_OUTSTOCK\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntrynote\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"delivery_warehouse_id\",\"destName\":\"发货仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockID.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_num\",\"destName\":\"本次发货数（基准单位）\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRealQty\",\"sourceName\":\"实发数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"sales_order_product_id\",\"destName\":\"订单产品编号\",\"destTargetApiName\":\"SalesOrderProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSOEntryId\",\"sourceName\":\"销售订单EntryId\",\"sourceTargetApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"sourceObjectName\":\"销售出库单-明细信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"DeliveryNoteObj\",\"destObjectName\":\"发货单\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSalesManID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\",\"valueType\":1},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"receive_remark\",\"destName\":\"收货备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_date\",\"destName\":\"发货日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDate\",\"sourceName\":\"日期\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK\",\"sourceObjectName\":\"销售出库单\"},\"sourceTenantType\":2,\"streamName\":\"销售出库单 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/shipments.png", "id": "6433a31bcac99c7d78bd793e", "order": 100, "preconditionIds": ["jskcidcheck", "crmobj-InventoryDetailsObj", "k3connect", "crmobj-!BatchObj&!SerialNumberObj", "crmconfig-!multiple_unit", "stockconfig-king_dee_k3c_sync_type-2", "stockconfig-warehouse_module_open_ware_position-1"], "sceneIds": ["fhhkc"], "tagIds": ["fhhkcgl", "stockLoc"], "title": "云星空库存同步到CRM_模板3", "updateTime": 1681291488103, "version": "2.0.********"}, {"channel": "ERP_K3CLOUD", "detailStr": "{\"detailMd\":\"## 模板介绍\\n如果企业在云星空管理发货和库存，则可以通过此模板将相关数据同步到CRM，这需要CRM先开通B类库存。此模板不包含序列号和批次号主档、仓位的同步。\\n## 使用此模板的前置条件\\r\\n- ✅ CRM开通金蝶云星空连接器并建立连接\\r\\n- ✅ 即时库存中间对象主键改为FID\\r\\n- ✅ CRM开通B类库存、选择的是同步库存明细（弃用）\\r\\n- ✅ CRM不开启CRM批次与序列号管理\\r\\n- ✅ CRM不开启多单位\\r\\n## 对接对象和数据流向图\\n![](https://a9.fspage.com/FSR/weex/erpdss/dataIntergration/shipping_and_inventory1.png)\",\"erpObjInfos\":[{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"Number\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"编码\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"name\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"名称\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FAddress\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"仓库地址\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"仓库\",\"realObjApiName\":\"BD_STOCK\",\"valid\":true},\"mainRealObjApiName\":\"BD_STOCK\"},{\"details\":[],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FBaseQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"库存量(基本单位)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialId.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FID\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"stockComId\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"库存ID（仓库编号#物料编号）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"即时库存\",\"realObjApiName\":\"STK_Inventory\",\"valid\":true},\"mainRealObjApiName\":\"STK_Inventory\"},{\"details\":[{\"exist\":true,\"fields\":[{\"fieldApiName\":\"fake_master_detail\",\"fieldDefineType\":\"master_detail\",\"fieldExtendValue\":\"SAL_OUTSTOCK\",\"fieldLabel\":\"虚拟主从字段\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FMaterialID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_MATERIAL\",\"fieldLabel\":\"物料编码(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FEntrynote\",\"fieldDefineType\":\"text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FStockID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_STOCK\",\"fieldLabel\":\"仓库(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FRealQty\",\"fieldDefineType\":\"number\",\"fieldLabel\":\"实发数量\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FSOEntryId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder.SaleOrderEntry\",\"fieldLabel\":\"销售订单EntryId\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"DetailId\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"明细id\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单-明细信息\",\"realObjApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"valid\":true}],\"main\":{\"exist\":true,\"fields\":[{\"fieldApiName\":\"FSalesManID.FNumber\",\"fieldDefineType\":\"employee\",\"fieldLabel\":\"销售员(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"SalesOrderComId\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"SAL_SaleOrder\",\"fieldLabel\":\"销售订单（复合id）\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FNote\",\"fieldDefineType\":\"long_text\",\"fieldLabel\":\"备注\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FCustomerID.FNumber\",\"fieldDefineType\":\"object_reference\",\"fieldExtendValue\":\"BD_Customer\",\"fieldLabel\":\"客户(编码)\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"FDate\",\"fieldDefineType\":\"date\",\"fieldLabel\":\"日期\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false},{\"fieldApiName\":\"id\",\"fieldDefineType\":\"id\",\"fieldLabel\":\"ID\",\"needBeIdType\":false,\"needCheckType\":false,\"required\":false}],\"fieldsComplete\":true,\"objName\":\"销售出库单\",\"realObjApiName\":\"SAL_OUTSTOCK\",\"valid\":true},\"mainRealObjApiName\":\"SAL_OUTSTOCK\"}],\"streamInfos\":[{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"WarehouseObj\",\"destObjectName\":\"仓库\",\"fieldMappings\":[{\"destApiName\":\"is_default\",\"destName\":\"是否默认仓\",\"destType\":\"true_or_false\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"false\",\"valueType\":1},{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"is_enable\",\"destName\":\"启用状态\",\"destType\":\"select_one\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"1\",\"valueType\":1},{\"destApiName\":\"number\",\"destName\":\"仓库编号\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"Number\",\"sourceName\":\"编码\",\"sourceType\":\"id\",\"value\":\"\"},{\"destApiName\":\"name\",\"destName\":\"仓库名称\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"name\",\"sourceName\":\"名称\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"address\",\"destName\":\"详细地址\",\"destType\":\"text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FAddress\",\"sourceName\":\"仓库地址\",\"sourceType\":\"long_text\",\"value\":\"\"}],\"sourceObjectApiName\":\"BD_STOCK\",\"sourceObjectName\":\"仓库\"},\"sourceTenantType\":2,\"streamName\":\"仓库 ERP往CRM\",\"syncRules\":{\"events\":[1],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"2022-03-31T15:59:00.000Z\",\"intervalQuantity\":6,\"startDataTime\":\"2022-03-30T16:00:00.000Z\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"InventoryDetailsObj\",\"destObjectName\":\"库存明细\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":3,\"optionMappings\":[],\"value\":\"-10000\",\"valueType\":1},{\"destApiName\":\"real_stock\",\"destName\":\"实际库存\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FBaseQty\",\"sourceName\":\"库存量(基本单位)\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"warehouse_id\",\"destName\":\"仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockId.FNumber\",\"sourceName\":\"仓库编码(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"物料名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialId.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"STK_Inventory\",\"sourceObjectName\":\"即时库存\"},\"sourceTenantType\":2,\"streamName\":\"即时库存 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}},{\"detailMappings\":[{\"destObjectApiName\":\"DeliveryNoteProductObj\",\"destObjectName\":\"发货单产品\",\"fieldMappings\":[{\"destApiName\":\"delivery_note_id\",\"destName\":\"发货单编号\",\"destTargetApiName\":\"DeliveryNoteObj\",\"destType\":\"master_detail\",\"mappingType\":1,\"sourceApiName\":\"fake_master_detail\",\"sourceName\":\"虚拟主从字段\",\"sourceTargetApiName\":\"SAL_OUTSTOCK\",\"sourceType\":\"master_detail\"},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"product_id\",\"destName\":\"产品名称\",\"destTargetApiName\":\"ProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceName\":\"物料编码(编码)\",\"sourceTargetApiName\":\"BD_MATERIAL\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"remark\",\"destName\":\"备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FEntrynote\",\"sourceName\":\"备注\",\"sourceType\":\"text\",\"value\":\"\"},{\"destApiName\":\"delivery_warehouse_id\",\"destName\":\"发货仓库\",\"destTargetApiName\":\"WarehouseObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FStockID.FNumber\",\"sourceName\":\"仓库(编码)\",\"sourceTargetApiName\":\"BD_STOCK\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_num\",\"destName\":\"本次发货数（基准单位）\",\"destType\":\"number\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FRealQty\",\"sourceName\":\"实发数量\",\"sourceType\":\"number\",\"value\":\"\"},{\"destApiName\":\"sales_order_product_id\",\"destName\":\"订单产品编号\",\"destTargetApiName\":\"SalesOrderProductObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FSOEntryId\",\"sourceName\":\"销售订单EntryId\",\"sourceTargetApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"sourceType\":\"object_reference\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK.SAL_OUTSTOCKENTRY\",\"sourceObjectName\":\"销售出库单-明细信息\"}],\"exists\":false,\"mainMapping\":{\"destObjectApiName\":\"DeliveryNoteObj\",\"destObjectName\":\"发货单\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"destName\":\"负责人\",\"destType\":\"employee\",\"mappingType\":2,\"optionMappings\":[],\"sourceApiName\":\"FSalesManID.FNumber\",\"sourceName\":\"销售员(编码)\",\"sourceType\":\"employee\",\"value\":\"FNumber\",\"valueType\":1},{\"destApiName\":\"sales_order_id\",\"destName\":\"销售订单编号\",\"destTargetApiName\":\"SalesOrderObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"SalesOrderComId\",\"sourceName\":\"销售订单（复合id）\",\"sourceTargetApiName\":\"SAL_SaleOrder\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"receive_remark\",\"destName\":\"收货备注\",\"destType\":\"long_text\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNote\",\"sourceName\":\"备注\",\"sourceType\":\"long_text\",\"value\":\"\"},{\"destApiName\":\"account_id\",\"destName\":\"客户名称\",\"destTargetApiName\":\"AccountObj\",\"destType\":\"object_reference\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FCustomerID.FNumber\",\"sourceName\":\"客户(编码)\",\"sourceTargetApiName\":\"BD_Customer\",\"sourceType\":\"object_reference\",\"value\":\"\"},{\"destApiName\":\"delivery_date\",\"destName\":\"发货日期\",\"destType\":\"date\",\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FDate\",\"sourceName\":\"日期\",\"sourceType\":\"date\",\"value\":\"\"}],\"sourceObjectApiName\":\"SAL_OUTSTOCK\",\"sourceObjectName\":\"销售出库单\"},\"sourceTenantType\":2,\"streamName\":\"销售出库单 ERP往CRM\",\"syncRules\":{\"events\":[1,2,7],\"pollingInterval\":{\"cronExpression\":\"0/6 * * * *\",\"dayLimitType\":\"EVERY_DAY\",\"endDataTime\":\"23:59\",\"intervalQuantity\":6,\"limitValues\":[],\"startDataTime\":\"00:00\",\"timeUnit\":\"minutes\"},\"syncFollowMain\":false,\"syncType\":\"get\",\"syncTypeList\":[\"get\"]}}]}", "enable": true, "headerImg": "https://a9.fspage.com/FSR/weex/erpdss/template_cover/shipments.png", "id": "6433a31bcac99c7d78bd793f", "order": 110, "preconditionIds": ["k3connect", "jskcidcheck", "crmobj-!BatchObj&!SerialNumberObj", "crmconfig-!multiple_unit", "stockconfig-king_dee_k3c_sync_type-2"], "sceneIds": ["fhhkc"], "tagIds": ["fhhkcgl"], "title": "云星空库存同步到CRM_模板4", "updateTime": 1681291506668, "version": "2.0.********"}]