package com.facishare.open.erp.connertor.rest.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:41:09
 */
public class RestConfigManager {
    private List<GrayServiceConfig> grayServiceConfigList = new ArrayList<>();
    private ServiceConfig defaultConfig;

    public RestConfigManager(final JSONObject jsonObject) {
        defaultConfig = jsonObject.toJavaObject(ServiceConfig.class);

        if (jsonObject.containsKey("gray")) {
            final JSONArray jsonArray = jsonObject.getJSONArray("gray");
            if (CollectionUtils.isNotEmpty(jsonArray)) {
                grayServiceConfigList = IntStream.range(0, jsonArray.size())
                        .mapToObj(jsonArray::getJSONObject)
                        .map(GrayServiceConfig::new)
                        .collect(Collectors.toList());
            }
        }
    }

    public ServiceConfig getServiceConfig(String... keys) {
        return grayServiceConfigList.stream()
                .filter(conifg -> conifg.isAllow(keys))
                .findFirst()
                .map(GrayServiceConfig::getServiceConfig)
                .orElse(defaultConfig);
    }
}
