package com.fxiaoke.open.oasyncdata.util;

import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Properties;

@Slf4j
public class PropertiesUtil {

    public static final String MODIFY_DATE = "propsFileModifyDate";
    private static Properties properties = new SequencedProperties();

    public static synchronized Properties readOrderedPropertiesFile(String path) {

        try {
            File file = new File(path);
            if(!file.exists()){
                if(!file.getParentFile().exists()){
                    file.getParentFile().mkdirs();
                    file.createNewFile();
                }else{
                    file.createNewFile();
                }
            }
            @Cleanup InputStream inputStream = new BufferedInputStream(new FileInputStream(path));
            //prop.load(in);//直接这么写，如果properties文件中有汉子，则汉字会乱码。因为未设置编码格式。
            @Cleanup InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "utf-8");
            properties.load(inputStreamReader);
        } catch (Exception e) {
           log.error("读取配置文件失败！", e);
        }
        return properties;
    }

    /**
     * 写Properties文件（有序）
     */
    public static synchronized void writeOrderedPropertiesFile(Properties properties, String path, String comments) {

        try {
//            properties.put(PropertiesUtil.MODIFY_DATE, DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            @Cleanup OutputStreamWriter outputStreamWriter = null;
            //保存属性到b.properties文件
            FileOutputStream fileOutputStream = new FileOutputStream(path, false);//true表示追加打开,false每次都是清空再重写
            //prop.store(oFile, "此参数是保存生成properties文件中第一行的注释说明文字");//这个会两个地方乱码
            //prop.store(new OutputStreamWriter(oFile, "utf-8"), "汉字乱码");//这个就是生成的properties文件中第一行的注释文字乱码
            outputStreamWriter = new OutputStreamWriter(fileOutputStream, "utf-8");
            properties.store(outputStreamWriter, comments);

        } catch (Exception e) {
            log.error("写入配置文件失败！", e);
        }

    }

}
