package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryTempTimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpTempResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpProcessedDataService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 11:26 2021/4/21
 * @Desc:
 */
@Ignore
public class ErpProcessedDataServiceImplTest extends BaseTest {

    @Autowired
    private ErpProcessedDataService erpProcessedDataService;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Test
    public void saveErpProcessedData() {
        //Result<Long> longResult = erpTempDataDao.deleteAllErpTempData("");
        QueryTempTimeFilterArg arg = new QueryTempTimeFilterArg();
        arg.setTenantId("83952");
        arg.setObjAPIName("materials_1h5rka39u");
        arg.setSnapshotId("b6b99cef138843e2889687fb42b1b415");
        arg.setStartTime(1700110171000L);
        arg.setEndTime(1700110171679L);
        arg.setOperationType(2);
        arg.setLimit(100);
        arg.setOffset(0);
        listTemp(arg);
    }

    private void listTemp(QueryTempTimeFilterArg arg) {
        Result<ListErpTempResult> listErpObjDataResultResult = erpDataPreprocessService.listErpObjDataFromMongo(arg);
        //Result<ListErpObjDataResult> listErpObjDataResultResult = erpDataPreprocessService.listErpObjDataByTime(arg);
        System.out.println(listErpObjDataResultResult);
    }

    @Test
    public void getErpObjDataFromMongoIfExist() {
        //Result<Long> longResult = erpTempDataDao.deleteAllErpTempData("");
        ErpIdArg arg = new ErpIdArg();
        arg.setTenantId("84801");
        arg.setObjAPIName("BD_SAL_PriceList.BillHead");
        arg.setDataId("793015");

        Result<SyncDataContextEvent> erpObjDataFromMongoIfExist = erpDataPreprocessService.getErpObjDataFromMongoIfExist(arg);

        System.out.println("");
    }
    @Test
    public void getErpInvalidDetailList() {
        ObjectData objectData = new ObjectData();
        objectData.putTenantId("84801");
        objectData.putApiName("BD_Customer.BillHead");
        objectData.putId("CUST02205");
        SyncDataContextEvent erpObjData = new SyncDataContextEvent();
        erpObjData.setSourceData(objectData);
        Map<String, List<ObjectData>> detailData = Maps.newHashMap();
        erpObjData.setDetailData(detailData);
        detailData.put("BD_Customer.BD_CUSTCONTACT",Lists.newArrayList());
        Result<List<SyncDataContextEvent>> list= erpProcessedDataService.getErpInvalidDetailList("84801","", Lists.newArrayList(erpObjData));
        System.out.printf("");
    }

}