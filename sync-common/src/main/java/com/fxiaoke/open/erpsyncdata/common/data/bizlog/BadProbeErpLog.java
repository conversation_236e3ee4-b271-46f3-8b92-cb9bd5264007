package com.fxiaoke.open.erpsyncdata.common.data.bizlog;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BadProbeErpLog {
    @Tag(1)
    @Builder.Default
    private String logType = "erpdss-bad-probe";
    @Tag(2)
    private long stamp;
    @Tag(3)
    private String appName;
    @Tag(7)
    private String tenantId;
    @Tag(10)
    private String objectApiName;

    //string1
    @Tag(51)
    private String stopProbe="n";
    @Tag(201)
    private long offset;
}
