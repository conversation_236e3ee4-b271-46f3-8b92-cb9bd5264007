package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.admin.arg.EnableStreamByTenant;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.constant.UserOperatorModuleEnum;
import com.fxiaoke.open.erpsyncdata.admin.model.ObjectFieldAnalysisResult;
import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantEnvOpInfo;
import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.AdminTenantInfo;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.admin.remote.EnterpriseRelationManager;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpHistoryDataTaskService;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectAndFieldsService;
import com.fxiaoke.open.erpsyncdata.admin.service.ObjectFieldAnalysisService;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminService;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminConnectorInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.StreamSimpleInfo;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UserOperatorLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UserOperatorLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CopyTenantConfigurationArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.InitErpObjectFieldsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ObjectFieldAnalysisArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.UpdateTableArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CopyService;
import com.fxiaoke.open.erpsyncdata.web.interceptor.SuperAdminTokenUtil;
import com.fxiaoke.open.erpsyncdata.web.service.IntegrationTaskService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/18
 */
@RestController
@RequestMapping("erp/syncdata/superadmin")
@Slf4j
public class SuperAdminController extends SuperAdminBaseController {
    @Autowired
    private SuperAdminService superAdminService;
    @Autowired
    private ErpObjectAndFieldsService erpObjectAndFieldsService;
    @Autowired
    private CopyService copyService;
    @Autowired
    private ObjectFieldAnalysisService objectFieldAnalysisService;
    @Autowired
    private UserOperatorLogDao userOperatorLogDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private EnterpriseRelationManager enterpriseRelationManager;
    @Autowired
    private IntegrationTaskService integrationTaskService;
    @Autowired
    private ErpHistoryDataTaskService erpHistoryDataTaskService;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    /**
     * 生成一个可以在所有环境使用的token，10分钟内有效。
     * @return
     */
    @GetMapping("generateToken")
    public Result<String> generateToken(){
        UserVo superAdmin = getUserVo();
        if (superAdmin==null){
            return Result.newError("未获取到身份");   // ignoreI18n   实施和开发自用
        }
        String token = SuperAdminTokenUtil.generateToken(superAdmin);
        return Result.newSuccess(token);
    }

    @GetMapping({"opstool/index", "opstool"})
    public ModelAndView opstool() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/erp/syncdata/superadmin/opstool/index.jsp");
        modelAndView.addObject("cdnBaseUrl", ConfigCenter.CDN_BASE_URL);
        modelAndView.addObject("sdkUrl", ConfigCenter.AMIS_SDK_URL);
        UserVo userVo = getUserVo();
        String ea = userVo.getEnterpriseAccount();
        Integer employeeId = userVo.getEmployeeId();
        String name = getName();
        modelAndView.addObject("userName", String.format("%s(%s.%s)", name, ea, employeeId));
        return modelAndView;
    }

    @GetMapping({"opstool/editor"})
    public ModelAndView editor() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/erp/syncdata/superadmin/opstool/editor.jsp");
        UserVo userVo = getUserVo();
        String ea = userVo.getEnterpriseAccount();
        Integer employeeId = userVo.getEmployeeId();
        String name = getName();
        modelAndView.addObject("userName", String.format("%s(%s.%s)", name, ea, employeeId));
        return modelAndView;
    }

    /**
     * 使用说明
     *
     * @return
     */
    @GetMapping("readme")
    public Result<String> readme() {
        UserVo superAdmin = getUserVo();
        if (superAdmin == null) {
            return Result.newError("未获取到身份");   // ignoreI18n   实施和开发自用
        }
        Map<String, String> variableMap = new HashMap<>();
        //允许替换一些变量
        String token = SuperAdminTokenUtil.generateToken(superAdmin);
        variableMap.put("superAdminToken", token);
        ErpTenantConfigurationEntity global = tenantConfigurationManager.findGlobal(TenantConfigurationTypeEnum.OPSTOOL_README.name());
        String readme = "还没有内容呢！！！";   // ignoreI18n   实施和开发自用
        if (global != null) {
            readme = global.getConfiguration();
        }
        readme = StrUtil.format(readme, variableMap);
        return Result.newSuccess(readme);
    }


    @PostMapping("/superUpdateById")
    public Result<String> superUpdateById(@RequestBody UpdateTableArg updateTableArg) {
        if (updateTableArg == null || StringUtils.isBlank(updateTableArg.getId()) || StringUtils.isBlank(updateTableArg.getTableName())
                || CollectionUtils.isEmpty(updateTableArg.getFieldValueList())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        if (updateTableArg.getTableName().contains("sync_data") || updateTableArg.getTableName().contains("erp_processed_data")) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(updateTableArg.getTableName()).append(" SET ");
        for (int i = 0; i < updateTableArg.getFieldValueList().size(); i++) {
            UpdateTableArg.FieldValue fieldValue = updateTableArg.getFieldValueList().get(i);
            if (StringUtils.isBlank(fieldValue.getFieldKey())) {
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
            }
            if (StringUtils.isBlank(fieldValue.getFieldValue())) {
                if (i == updateTableArg.getFieldValueList().size() - 1) {
                    sql.append(fieldValue.getFieldKey()).append("=").append("null");
                } else {
                    sql.append(fieldValue.getFieldKey()).append("=").append("null").append(",");
                }
            } else {
                if (i == updateTableArg.getFieldValueList().size() - 1) {
                    sql.append(fieldValue.getFieldKey()).append("='").append(fieldValue.getFieldValue()).append("'");
                } else {
                    sql.append(fieldValue.getFieldKey()).append("='").append(fieldValue.getFieldValue()).append("',");
                }
            }

        }
        sql.append(" where id='").append(updateTableArg.getId()).append("';");
        Result<Void> voidResult = superAdminService.superUpdate(updateTableArg.getTenantId(), sql.toString());
        Result<String> result = Result.copy(voidResult);
        result.setData(sql.toString());
        return result;
    }


    @PostMapping("/redoIndex")
    public Result<List<Map<String, Object>>> redoIndex( @RequestHeader(value = "tenantId") String tenantId) {
        Result<List<Map<String, Object>>> voidResult = superAdminService.redoIndex( tenantId);
        return voidResult;
    }

    @RequestMapping(value = "/initErpObjectFields", method = RequestMethod.POST)
    public Result<Void> initErpObjectFields(@RequestBody InitErpObjectFieldsArg arg) {
        return erpObjectAndFieldsService.initObjAndFields(arg);
    }

    /**
     * 复制策略和策略明细（企业之间的复制）复制集成流
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "/copyTenantPloyAndPloyDetail", method = RequestMethod.POST)
    public Result<List<String>> copyTenantPloyAndPloyDetail(@RequestBody CopyTenantConfigurationArg.CopyPloyAndDetailArg arg,
                                                            @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return copyService.copyTenantPloyAndPloyDetail(arg,lang);
    }

    /**
     * 复制erp对象（企业之间的复制）
     * 不考虑冲突
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "/copyTenantErpObj", method = RequestMethod.POST)
    public Result<List<String>> copyTenantErpObj(@RequestBody CopyTenantConfigurationArg.CopyErpObjArg arg,
                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return copyService.copyTenantErpObj(arg,lang);
    }

    /**
     * 复制EAI配置*
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "/copyTenantEaiConfig", method = RequestMethod.POST)
    public Result<List<String>> copyTenantEaiConfig(@RequestBody CopyTenantConfigurationArg.CopyEaiConfigArg arg,
                                                    @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return copyService.copyTenantEaiConfig(arg,lang);
    }

    /**
     * 复制企业连接信息（企业之间的复制）
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "/copyTenantConnectInfo", method = RequestMethod.POST)
    public Result<List<String>> copyTenantConnectInfo(@RequestBody CopyTenantConfigurationArg.CopyConnectArg arg,
                                                      @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return copyService.copyTenantConnectInfo(arg.getSourceTenantId(), arg.getSourceDataCenterId(), arg.getTargetTenantId(),lang);
    }



    @RequestMapping(value = "/objectFieldAnalysis", method = RequestMethod.POST)
    public Result<ObjectFieldAnalysisResult> objectFieldAnalysis(@RequestBody ObjectFieldAnalysisArg arg) {
        return objectFieldAnalysisService.analysisObjectFields(arg.getTenantId(), arg.getSourceObjectApiName(), arg.getSourceTenantType());
    }

    /**
     *
     * @param requestParam
     * @return
     */
    @GetMapping(value = "/tenantInfoQuery")
    public Result<Amis.Crud<AdminTenantInfo>> tenantInfoQuery(@RequestParam(required = false) Map<String,String> requestParam) {
        Result<Amis.Crud<AdminTenantInfo>> voidResult = superAdminService.tenantInfoQuery(requestParam);
        return voidResult;
    }

    @GetMapping(value = "/connectorInfoQuery")
    public Result<Amis.Crud<AdminConnectorInfo>> connectorInfoQuery(@RequestParam(required = false) Map<String,String> requestParam) {
        Result<Amis.Crud<AdminConnectorInfo>> voidResult = superAdminService.connectorInfoQuery(requestParam);
        return voidResult;
    }


    @RequestMapping(value = "/streamInfoQuery", method = RequestMethod.GET)
    public Result<List<Dict>> streamInfoQuery(@RequestParam(required = false) String tenantId,
                                              @RequestParam(required = false) String keywords) {
        Result<List<Dict>> voidResult = superAdminService.streamInfoQuery(tenantId, keywords);
        return voidResult;
    }

    @RequestMapping(value = "/allStreamInfoQuery", method = RequestMethod.GET)
    public Result<Amis.Crud<StreamSimpleInfo>> streamInfoQuery(@RequestParam(required = false) Map<String,String> requestParam) {
        Result<Amis.Crud<StreamSimpleInfo>> voidResult = superAdminService.streamInfoQuery(requestParam);
        return voidResult;
    }

    @RequestMapping(value = "/allStreamInfoExport", method = RequestMethod.GET)
    public Result<Void> streamInfoExport() {
        String tenantId = getLoginUserTenantId();
        Integer userId = getLoginUserId();
        String ea = getEa();
        Result<Void> voidResult = superAdminService.exportToExcel(tenantId, userId, ea);
        return voidResult;
    }

    @RequestMapping(value = "/dataNodeQuery", method = RequestMethod.GET)
    public Result<List<Dict>> dataNodeQuery(@RequestParam(required = false) Boolean isTop60,
                                            @RequestParam(required = false) String tenantId,
                                            @RequestParam(required = false) String objApiName,
                                            @RequestParam(required = false) String dataId) {
        Result<List<Dict>> voidResult = superAdminService.dataNodeQuery(isTop60,tenantId, objApiName,dataId);
        return voidResult;
    }

    @RequestMapping(value = "/operationQuery", method = RequestMethod.GET)
    public Result<List<AdminTenantEnvOpInfo>> operationQuery(@RequestParam(required = false) String keywords,
                                                             @RequestParam(required = false) String ids) {
        //查最近2000条
        List<UserOperatorLog> list = userOperatorLogDao.queryUserOperatorLogs("1", "1", UserOperatorModuleEnum.TENANT_CONFIG_OPERATION.name(), null, 0, 2000);
        List<AdminTenantEnvOpInfo> resultList = Lists.newArrayList();
        for (UserOperatorLog userOperatorLog : list) {
            AdminTenantEnvOpInfo adminTenantEnvOpInfo = JSONObject.parseObject(userOperatorLog.getSnapshotData(), AdminTenantEnvOpInfo.class);
            adminTenantEnvOpInfo.setId(userOperatorLog.getId());
            resultList.add(adminTenantEnvOpInfo);
        }
        if (StrUtil.isNotBlank(ids)) {
            resultList.removeIf(v -> !CollUtil.containsAny(v.getTenantIds(), StrUtil.split(ids, ",")));
        }
        if (StrUtil.isNotBlank(keywords)) {
            //用||划分多个关键词
            String[] split = keywords.split("\\|\\|");
            resultList.removeIf(v ->
                    !StrUtil.containsAnyIgnoreCase(v.getUserName(), split) &&
                            !StrUtil.containsAnyIgnoreCase(v.getDirection(), split)
            );
        }
        Set<String> tenantList = Sets.newHashSet();
        for (AdminTenantEnvOpInfo opInfo : resultList) {
            tenantList.addAll(opInfo.getTenantIds());
        }
        Set<Integer> tenantIds=tenantList.stream().map(id->Integer.valueOf(id)).collect(Collectors.toSet());
        Map<Integer,String> ei2ea=eieaConverter.enterpriseIdToAccount(tenantIds);
        List<String> sandBoxTenantIds = Lists.newArrayList();
        for(Integer ei:ei2ea.keySet()){
            if(ei2ea.get(ei)!=null&&ei2ea.get(ei).contains("sandbox")){
                sandBoxTenantIds.add(String.valueOf(ei));
            }
        }
        Map<String, List<String>> tenantId2Level = crmRemoteManager.queryEnterpriseLevelLabel(Lists.newArrayList(tenantList));
        for (AdminTenantEnvOpInfo opInfo : resultList) {
            Map<String, List<String>> map = Maps.newHashMap();
            for (String ei : opInfo.getTenantIds()) {
                if (sandBoxTenantIds.contains(ei)) {
                    if(map.containsKey("sandbox")){
                        map.get("sandbox").add(ei);
                    }else{
                        map.put("sandbox",Lists.newArrayList(ei));
                    }
                } else {
                    List<String> levels = tenantId2Level.get(ei);
                    if (levels != null && levels.stream().anyMatch(v -> v.contains("VIP"))) {
                        if(map.containsKey("VIP+")){
                            map.get("VIP+").add(ei);
                        }else{
                            map.put("VIP+",Lists.newArrayList(ei));
                        }
                    }else{
                        if(map.containsKey("other")){
                            map.get("other").add(ei);
                        }else{
                            map.put("other",Lists.newArrayList(ei));
                        }
                    }
                }
            }
            StringBuilder sb=new StringBuilder();
            for(String key:map.keySet()){
                sb.append(key).append(map.get(key).toString().replaceAll(" ","").replaceAll(",","||")).append(System.lineSeparator());
            }
            opInfo.setLevel2TenantIds(sb.toString());
        }
        return Result.newSuccess(resultList);
    }

    @RequestMapping(value = "/deleteOperationLog", method = RequestMethod.POST)
    public Result<Long> deleteOperationLog(@RequestBody IdArg id) {
        if (id==null||StringUtils.isBlank(id.getId())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        Long delete = userOperatorLogDao.delete(id.getId());
        return Result.newSuccess(delete);
    }


    @PostMapping("/tenantList/streamEnable")
    public Result<String> enableStreamByTenant(@RequestBody EnableStreamByTenant.Arg arg) {
        List<String> tenantIds = ListUtils.emptyIfNull(arg.getTenantIds()).stream().map(String::trim).filter(StringUtils::isNotBlank).collect(Collectors.toList());

//        如果没有传tenantIds,则根据upStreamId获取所有下游租户
        if (CollectionUtils.isEmpty(tenantIds) && StringUtils.isNotBlank(arg.getUpStreamId())) {
            final Result2<List<String>> allDownstreamTenantIds = getDownStreamTenantIds(arg.getUpStreamId().trim());
            if (!allDownstreamTenantIds.isSuccess()) {
                return Result.newError(allDownstreamTenantIds.getErrCode(), allDownstreamTenantIds.getErrMsg());
            }
            tenantIds = allDownstreamTenantIds.getData();
        }

        if (CollectionUtils.isEmpty(tenantIds)) {
            return Result.newSuccess();
        }

        final String taskId = UUID.randomUUID().toString();
        final List<String> tenantIds1 = tenantIds;

        asyncExecute(() -> integrationTaskService.enableStreamByTenant(arg, tenantIds1, taskId));

        return Result.newSuccess(taskId);
    }

    private Result2<List<String>> getDownStreamTenantIds(String upStreamId) {
        return enterpriseRelationManager.getAllDownstreamTenantIds(upStreamId);
    }

    @RequestMapping(value = "/brushTask", method = RequestMethod.GET)
    public Result<String> brushTask(@RequestParam(required = false) String environment,
                                            @RequestParam(required = false) String tenantId) {
        if(StringUtils.isNotEmpty(tenantId)){
            erpHistoryDataTaskService.brushHistoryTask(tenantId);
            return Result.newSuccess();
        }
        if(StringUtils.isNotEmpty(environment)&&environment.equals("GRAY")){
            for (String grayTenant : ConfigCenter.GRAY_TENANTS) {
                erpHistoryDataTaskService.brushHistoryTask(grayTenant);
            }
            log.info("brushTask success");
            return Result.newSuccess();

        }
        if(StringUtils.isNotEmpty(environment)&&environment.equals("VIP")){
            for (String vipTenant : ConfigCenter.VIP_ENVIROMENT_TENANT) {
                erpHistoryDataTaskService.brushHistoryTask(vipTenant);
            }
            log.info("brushTask success");
            return Result.newSuccess();

        }
        //剩下的就是全网
        List<String> strings = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
        Set<String> allSet = Sets.newHashSet();
        allSet.addAll(strings);
        allSet.removeAll(ConfigCenter.GRAY_TENANTS);
        allSet.removeAll(ConfigCenter.VIP_ENVIROMENT_TENANT);
        for (String tenantIdSet : allSet) {
            erpHistoryDataTaskService.brushHistoryTask(tenantIdSet);
        }
        log.info("brushTask success");
        return Result.newSuccess();
    }

    /**
     * 展示重试的数据
     */
    @RequestMapping(value = "/queryRetryData", method = RequestMethod.GET)
    public Result<String> queryRetryData(@RequestParam(required = false) String tenantId, @RequestParam(required = false) String eventType ,@RequestParam(required = false) Integer limit,@RequestParam(required = false) Integer offset) {

        return Result.newSuccess();
    }


}
