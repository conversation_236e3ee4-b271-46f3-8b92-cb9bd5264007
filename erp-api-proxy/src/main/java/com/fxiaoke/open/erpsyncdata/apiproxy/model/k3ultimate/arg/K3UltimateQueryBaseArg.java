package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate.arg;

import lombok.Getter;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 旗舰版查询参数基类
 */

public class K3UltimateQueryBaseArg extends LinkedHashMap<String,Object> implements Serializable {
    /**
     * 过滤创建日期起始日期 示例：2023-09-01 00:00:00
     */
    @Getter
    private String start_createtime;
    public void setStart_createtime(String value) {
        start_createtime = value;
        put("start_createtime",start_createtime);
    }
    /**
     * 过滤创建日期截止日期 示例：2023-09-01 00:00:00
     */
    @Getter
    private String end_createtime;
    public void setEnd_createtime(String value) {
        end_createtime = value;
        put("end_createtime",end_createtime);
    }

    /**
     * 过滤修改时间起始日期 示例：2023-09-01 00:00:00
     */
    @Getter
    private String start_modifytime;
    public void setStart_modifytime(String value) {
        start_modifytime = value;
        put("start_modifytime",start_modifytime);
    }
    /**
     * 过滤修改时间截止日期 示例：2023-09-01 00:00:00
     */
    @Getter
    private String end_modifytime;
    public void setEnd_modifytime(String value) {
        end_modifytime = value;
        put("end_modifytime",end_modifytime);
    }

    /**
     * 过滤审核时间起始日期 示例：2023-09-01 00:00:00
     */
    @Getter
    private String start_approvedate;
    public void setStart_approvedate(String value) {
        start_approvedate = value;
        put("start_approvedate",start_approvedate);
    }
    /**
     * 过滤审核时间截止日期 示例：2023-09-01 00:00:00
     */
    @Getter
    private String end_approvedate;
    public void setEnd_approvedate(String value) {
        end_approvedate = value;
        put("end_approvedate",end_approvedate);
    }

    /**
     * 数据状态 [A:暂存, B:已提交, C:已审核]
     */
    @Getter
    private List<String> status;
    public void setStatus(List<String> value) {
        status = value;
        put("status",status);
    }

    /**
     * id
     */
    @Getter
    private List<String> id;
    public void setId(List<String> value) {
        id = value;
        put("id",id);
    }

    /**
     * 编码
     */
    @Getter
    private List<String> number;
    public void setNumber(List<String> value) {
        number = value;
        put("number",number);
    }

    /**
     * 编码
     */
    @Getter
    private List<String> billno;
    public void setBillno(List<String> value) {
        billno = value;
        put("billno",billno);
    }

    /**
     * 创建组织.编码
     */
    @Getter
    private List<String> createorg_number;
    public void setCreateorg_number(List<String> value) {
        createorg_number = value;
        put("createorg_number",createorg_number);
    }

    /**
     * 使用状态 [0:禁用, 1:可用]
     */
    @Getter
    private String enable;
    public void setEnable(String value) {
        enable = value;
        put("enable",enable);
    }
}
