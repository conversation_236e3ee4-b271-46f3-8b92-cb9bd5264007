package com.fxiaoke.open.erpsyncdata.web.controller.setUp;

import com.fxiaoke.open.erpsyncdata.admin.arg.CepArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.service.TemplateService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fxiaoke.open.erpsyncdata.admin.model.Template.*;

/**
 * 模板中心
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/3/21
 */
@Slf4j
@RestController()
@RequestMapping("cep/setUp/template")
public class TemplateController extends BaseController {
    @Autowired
    private TemplateService templateService;


    /**
     * 获取渠道和场景
     * 不再过滤
     *
     * @param arg 空
     * @return
     */
    @PostMapping("filters")
    public Result<Filters> getFilters(@RequestBody CepArg arg) {
        return templateService.getFilters();
    }

    /**
     * 获取模板基础内容列表,会对企业做范围过滤
     *
     * @param arg 筛选条件，传空查所有
     * @return
     */
    @PostMapping("listBaseInfo")
    public Result<List<TemplateInfo>> listBaseInfo(@RequestBody QueryBaseInfoArg arg) {
        return templateService.listBaseInfo(arg);
    }

    /**
     * 获取模板完整内容,任意纷享身份访问。
     *
     * @param arg
     * @return
     */
    @PostMapping("getAllInfo")
    public Result<TemplateInfo> getAllInfo(@RequestBody IdArg arg) {
        return templateService.getAllInfo(arg.getId());
    }

    /**
     * 前置条件校验
     *
     * @param arg 传模板id,条件列表不用穿
     * @return
     */
    @PostMapping("preCheckCondition")
    public Result<List<Precondition>> preCheckCondition(@RequestBody PreCheckArg arg) {
        return templateService.preCheckCondition(getLoginUserTenantId(), getDcId(), arg);
    }


    /**
     * 第三方对象配置校验
     *
     * @param arg 传模板id
     * @return
     */
    @PostMapping("preCheckErpObj")
    public Result<List<TemplateErpObjInfo>> preCheckErpObj(@RequestBody PreCheckArg arg,
                                                           @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return templateService.preCheckErpObj(getLoginUserTenantId(), getDcId(), arg, lang);
    }

    /**
     * 创建模板集成流
     *
     * @param arg 模板id+上一步选择的对象列表
     * @return 模板集成流列表
     */
    @PostMapping("createTemplateIntegrationStreams")
    public Result<List<StreamInfo>> createTemplateIntegrationStreams(@RequestBody ListStreamsArg arg,
                                                                     @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return templateService.createTemplateIntegrationStreams(getLoginUserTenantId(),getDcId(),arg,lang);
    }
}
