<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans" xmlns:p="http://www.springframework.org/schema/p" xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

  <!--MQ Consumer-->
  <bean id="crmActionMqConsumer"
        class="com.fxiaoke.open.erpsyncdata.main.crmevent.CrmActionMqConsumer"
        init-method="init"
        p:rocketMQConsumerConfigName="erp-sync-data-crm-notify-consumer"/>

  <bean  id="sfaStatusChangeListener" class="com.fxiaoke.open.erpsyncdata.main.crmevent.SFAStatusChangeListener" init-method="init">
    <constructor-arg index="0" value="erp-sync-listen-enterprise"></constructor-arg>
    <constructor-arg index="1" value="spu.name.server"></constructor-arg>
    <constructor-arg index="2" value="spu.consumer.group"></constructor-arg>
    <constructor-arg index="3" value="spu.consume.topic"></constructor-arg>
  </bean>

  <bean  id="enterpriseStatusChangeConsumer" class="com.fxiaoke.open.erpsyncdata.main.crmevent.EnterpriseStatusChangeConsumer" init-method="init">
    <constructor-arg index="0" value="erp-sync-listen-enterprise"></constructor-arg>
    <constructor-arg index="1" value="name.server"></constructor-arg>
    <constructor-arg index="2" value="consumer.group"></constructor-arg>
    <constructor-arg index="3" value="consume.topic"></constructor-arg>
  </bean>
</beans>
