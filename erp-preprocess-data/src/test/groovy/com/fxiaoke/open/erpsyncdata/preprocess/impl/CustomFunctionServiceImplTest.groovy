package com.fxiaoke.open.erpsyncdata.preprocess.impl

import com.facishare.function.biz.api.service.FunctionService
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2022/12/2 10:42:42
 */
class CustomFunctionServiceImplTest extends Specification {

    private FunctionService functionService;

    def setup() {
        functionService = Mock(FunctionService) {
            executeFuncMethod(*_) >> {
                def res = new CustomFunctionServiceImpl.SyncResult()
                return res
            }
        }
    }

    def "测试函数执行"() {
        when:
        def functionServiceImpl = new CustomFunctionServiceImpl(
                bizFunctionService: functionService
        )

        then:
        functionServiceImpl.executeCustomClassFunction("82857", "TestErp__c", ["{\"destTenantId\":\"82857\",\"sourceDataId\":\"815028\",\"sourceObjectApiName\":\"BD_SAL_DiscountList.BillHead\",\"destObjectApiName\":\"object_Q7S4L__c\",\"objectData\":{\"FEffectiveDate\":1667404800000,\"tenant_id\":\"82857\",\"modifiedTime\":1669719639216,\"FModifierId.Id\":\"605439\",\"FExpiryDate\":4102416000000,\"FForbiderId.Id\":\"0\",\"FCurrencyIdFCURRENCYID\":7,\"FCurrencyId.FNumber\":\"PRE007\",\"FName\":\"阿达发的\",\"mongo_id\":\"6385e637d3245cdbd10d5548\",\"FLimitSalesMan\":\"1\",\"FAuditStatus\":\"U\",\"object_describe_api_name\":\"BD_SAL_DiscountList.BillHead\",\"id\":815028,\"FUseOrgIdFOrgID\":1,\"erp_num\":\"XSZKB0006\",\"FNumber\":\"XSZKB0006\",\"FCreateDate\":*************,\"FCreatorId.Id\":\"605439\",\"FDiscountFor\":\"2\",\"FCreateOrgIdFOrgID\":1,\"erp_id\":\"815028\",\"FDescription\":\"阿达是的\",\"FModifyDate\":*************,\"FUseOrgId.FNumber\":\"000\",\"last_modified_by\":[\"-10000\"],\"FDocumentStatus\":\"A\",\"FApproverId.Id\":\"0\",\"FCurrencyId.Name\":\"美元\",\"FCreateOrgId.Name\":\"纷享销客\",\"FCreateOrgId.FNumber\":\"000\",\"FLimitCustomer\":\"2\",\"FForbidStatus\":\"A\",\"FUseOrgId.Name\":\"纷享销客\",\"name\":\"XSZKB0006\",\"FDiscountObject\":\"A\",\"_id\":\"815028\"},\"sourceTenantId\":\"82857\",\"details\":{},\"sourceEventType\":2}"], ["name":"test123","object_describe_api_name":"AccountObj"], CustomFunctionTypeEnum.BEFORE_FUNCTION)

        functionServiceImpl.executeCustomClassFunction("82857", "TestErp__c", ["{\"destTenantId\":\"82857\",\"sourceObjectApiName\":\"BD_SAL_DiscountList.BillHead\",\"destObjectApiName\":\"object_Q7S4L__c\",\"objectData\":{\"object_describe_api_name\":\"object_Q7S4L__c\",\"tenant_id\":\"82857\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"field_Wwr19__c\":\"阿达发的\",\"name\":\"XSZKB0006\",\"field_4Sc8u__c\":\"阿达是的\",\"_id\":\"6385e66123aee20001aced0e\"},\"syncDataId\":\"6385e6619ee6ce5b48a901cb\",\"sourceTenantId\":\"82857\",\"destEventType\":1,\"details\":{}}"], ["name":"test123","object_describe_api_name":"AccountObj"], CustomFunctionTypeEnum.DURING_FUNCTION)

        functionServiceImpl.executeCustomClassFunction("82857", "TestErp__c", ["{\"destTenantId\":\"82857\",\"sourceObjectApiName\":\"BD_SAL_DiscountList.BillHead\",\"completeDataWriteResult\":{\"destEventType\":1,\"destTenantType\":1,\"detailWriteResults\":[],\"errCode\":0,\"errMsg\":\"success\",\"success\":true,\"syncPloyDetailSnapshotId\":\"d10eca477dfb4a869c5406ebd9fb2172\",\"tenantId\":\"82857\",\"writeResult\":{\"destDataId\":\"6385e66123aee20001aced0e\",\"destDetailSyncDataIdAndDestDataMap\":{},\"errCode\":0,\"errMsg\":\"success\",\"simpleSyncData\":{\"destDataId\":\"6385e66123aee20001aced0e\",\"destDataName\":\"XSZKB0006\",\"destObjectApiName\":\"object_Q7S4L__c\",\"sourceDataId\":\"815028\",\"sourceObjectApiName\":\"BD_SAL_DiscountList.BillHead\",\"syncDataId\":\"6385e6619ee6ce5b48a901cb\"},\"success\":true,\"syncDataId\":\"6385e6619ee6ce5b48a901cb\"}},\"destObjectApiName\":\"object_Q7S4L__c\",\"objectData\":{\"object_describe_api_name\":\"object_Q7S4L__c\",\"tenant_id\":\"82857\",\"owner\":[\"1000\"],\"record_type\":\"default__c\",\"field_Wwr19__c\":\"阿达发的\",\"name\":\"XSZKB0006\",\"field_4Sc8u__c\":\"阿达是的\",\"_id\":\"6385e66123aee20001aced0e\"},\"syncDataId\":\"6385e6619ee6ce5b48a901cb\",\"sourceTenantId\":\"82857\",\"destEventType\":1,\"details\":{},\"sourceEventType\":2}"], ["name":"test123","object_describe_api_name":"AccountObj"], CustomFunctionTypeEnum.AFTER_FUNCTION)

    }
}
