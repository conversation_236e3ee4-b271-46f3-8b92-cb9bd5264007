package com.fxiaoke.open.oasyncdata.impl;

import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.open.oasyncdata.manager.UserManager;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.model.OASyncApiVO;
import com.fxiaoke.open.oasyncdata.service.ObjectServiceFiledConvert;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/18 17:33 审批流程处理字段
 * @Version 1.0
 */
@Service("approvalServiceImpl")
@Slf4j
public class ApprovalServiceImpl implements ObjectServiceFiledConvert {

    @Autowired
    private UserManager userManager;
    @ReloadableProperty("oa.author.app.url")
    private String oaAuthorAppUrl;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;

    /**
     * 处理特殊字段
     * @param controllerGetDescribeResult
     * @param tenantId
     */
    @Override
    public void dealSpecialField(ControllerGetDescribeResult controllerGetDescribeResult, String tenantId,String dataCenterId) {
        //人员的描述，全部替换成对应的erp人员
        HashMap<String, FieldDescribe> fields = controllerGetDescribeResult.getDescribe().getFields();
        fields.keySet().forEach(item ->{
            switch (fields.get(item).getType()){
                case "employee":
                    Object employeeValue = controllerGetDescribeResult.getData().get(item);
                    if(ObjectUtils.isEmpty(employeeValue)){
                        break;
                    }
                    List<String> empIds = (List<String>) employeeValue;
                    //不转化。再后面在转
                    if(!item.equals("candidate_ids")){
                        List<String> erpSubmitterCodeList = userManager.getUserCodeList(empIds, tenantId,dataCenterId);
                        if(!CollectionUtils.isEmpty(erpSubmitterCodeList)){
                            //处理人节点取list
                            controllerGetDescribeResult.getData().put(item,erpSubmitterCodeList.get(0));
                        }
                    }
                    break;
                case "date_time":
                    Object dataTime = controllerGetDescribeResult.getData().get(item);
                    if(ObjectUtils.isEmpty(dataTime)){
                        break;
                    }
                    if(item.equals("create_time")){
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = new Date(((Double) dataTime).longValue());
                        String createTime = simpleDateFormat.format(date);
                        controllerGetDescribeResult.getData().put(item, createTime);
                    }
                    break;
                default:break;
            }
        });


    }

//    /**
//     * 统一处理接收人，不同的biztype标识的字段不一致
//     * @param objectData
//     * @param tenantId
//     */
//    @Override
//    public List<String> getCRMReceivers(ObjectData objectData, String tenantId) {
//        List<String> userCodeList = (List) objectData.get("candidate_ids");
//        return userCodeList;
//    }

    @Override
    public Map<String,String> builderTitle(ObjectData objectData,String bizType, String lang,String dataCenterId) {
        //审批流的title

        HashMap<String, String> objectNameMap = Maps.newHashMap();
        objectNameMap.put("object_name",String.valueOf(objectData.get("object_api_name__r")));
        objectNameMap.put("data_name",String.valueOf(objectData.get("object_data_id__r")));
        return objectNameMap;
    }


    /** 占位符特殊处理
     *
     * @param oaObjectFieldVO
     * @param data
     * @param tenantId
     * @param resultJson
     * @return
     */
    @Override
    public String specialHand(OAObjectFieldVO oaObjectFieldVO, ObjectData data, String tenantId, String resultJson,String dataCenterId) {
        //需要注意（取回审批）删除的状态，可能会有__r,__l的数据没有补充
        String apiName = data.get("object_api_name").toString();
        String dataId = data.get("object_data_id").toString();
        //webUrl和appUrl两个占位符暂时无法通用，复星OA专用免登录
        if ("#webUrl#".equals(oaObjectFieldVO.getReplaceName())) {
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), URLEncoder.encode(oaAuthorWebUrl + apiName + "/" + dataId +
                    "/" + tenantId));
        }

        if ("#appUrl#".equals(oaObjectFieldVO.getReplaceName())) {
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), URLEncoder.encode(oaAuthorAppUrl + apiName + "/" + dataId +
                    "/" + tenantId));
        }
        if ("#currentTime#".equals(oaObjectFieldVO.getReplaceName())) {
            long time = System.currentTimeMillis();
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), String.valueOf(time));
        }
        // 特殊的时间格式，用来做鉴权
        if ("#currentTime2#".equals(oaObjectFieldVO.getReplaceName())) {
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), df.format(new Date()));
        }
        // 取nickname，特殊加的字段
        if ("#F054".equals(oaObjectFieldVO.getReplaceName())) {
            Map<String, String> submitterMap =  Optional.ofNullable((Map) data.get("submitter__r")).orElseGet(() -> Maps.newHashMap());;

            //避免为空导致的异常
            String submitterName = Optional.ofNullable(submitterMap.get("nickname")).orElseGet(() -> StringUtils.EMPTY);
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), submitterName);
        }
        if ("#F031".equals(oaObjectFieldVO.getReplaceName())) {
            Long time = new Double((Double) data.get("last_modify_time")).longValue();
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), String.valueOf(time));
        }
        if ("#F014".equals(oaObjectFieldVO.getReplaceName())) {
            //1685161068142
            //1684910163068
            //1.684910163068E12
            Long time = new Double((Double) data.get("last_modified_time")).longValue();
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), String.valueOf(time));
        }
        if (data.get(oaObjectFieldVO.getFieldApiName()) == null || "#F001".equals(oaObjectFieldVO.getReplaceName())) {
            return resultJson;
        }
        return null;
    }

    public static void main(String[] args) {
        ObjectData objectData=new ObjectData();
//        objectData.put("last_modified_time","1685161068142");
//        Long time = new Double((Double) objectData.get("last_modified_time")).longValue();
//        System.out.printf(""+time);
        objectData.put("last_modified_time","1684910163068");
       long time = new Double((Double) objectData.get("last_modified_time")).longValue();
        System.out.printf(""+time);

    }
}

