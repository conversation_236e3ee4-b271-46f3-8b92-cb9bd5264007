package com.fxiaoke.open.erpsyncdata.probe.handler;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Date: 10:10 2022/6/9
 * @Desc:
 */
@Slf4j
@Component
@JobHander(value = "restoreSync2CrmSpeedLimitHandler")
public class RestoreSync2CrmSpeedLimitHandler  extends IJobHandler {

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        executeJob(triggerParam);
        return ReturnT.SUCCESS;
    }


    public void executeJob(TriggerParam triggerParam) {
    }

};
