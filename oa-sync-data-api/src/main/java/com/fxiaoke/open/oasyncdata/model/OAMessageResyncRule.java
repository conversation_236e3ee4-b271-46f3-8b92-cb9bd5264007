package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.constant.CrmTypeMessageEnum;
import com.fxiaoke.open.oasyncdata.constant.OAEventEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/10/23 15:24
 * @desc
 */
@Data
public class OAMessageResyncRule  implements Serializable {
    /**
     * 重试条件 0 异常 2 失败
     * @see com.fxiaoke.open.oasyncdata.constant.OASyncLogEnum
     */
    private List<String> messageStatus;
    /**
     * 重试业务类型  crmToDo 待办  crmNotify 提醒
     * @see CrmTypeMessageEnum
     */
    private List<String> businessTypes;

    /**
     * 重试业务事件类型 取eventStatus
     * @see OAEventEnum
     */
    private List<String> resyncEventTypes;

    /**
     * 重试频率
     */
    private Integer intervalMinutes;
    /**
     * 重试次数
     */
    private Integer resyncCounts;

    /**
     * 重试规则状态
     * true/false
     */
    private Boolean status;

}
