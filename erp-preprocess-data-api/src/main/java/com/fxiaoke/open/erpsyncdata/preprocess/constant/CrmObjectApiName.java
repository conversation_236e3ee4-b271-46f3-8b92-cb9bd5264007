package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.google.common.collect.Sets;

import java.util.Set;

public interface CrmObjectApiName {
    String PRODUCT_CATEGORY_OBJ = "ProductCategoryObj";
    String PRODUCT_OBJ = "ProductObj";
    String ACCOUNT_API_NAME = "AccountObj";

    String OPPORTUNITY_OBJ = "OpportunityObj";

    String CONTACT_API_NAME = "ContactObj";

    String ACCOUNT_ADDR_API_NAME = "AccountAddrObj";

    String Employee_API_NAME =  "PersonnelObj";

    /**
     * 支持填充外部负责人的对象
     */
    Set<String> OutDataPrivilegeFields = Sets.newHashSet(ACCOUNT_API_NAME, OPPORTUNITY_OBJ);
}
