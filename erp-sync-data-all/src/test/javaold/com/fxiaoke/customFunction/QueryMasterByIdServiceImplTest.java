package com.fxiaoke.customFunction;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryMasterByIdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.QueryMasterByIdModel;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class QueryMasterByIdServiceImplTest extends BaseTest {
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private ProbeErpDataService probeErpDataService;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private SyncPloyDetailManager syncPloyDetailManager;
    @Autowired
    private AllModelDubboService allModelDubboService;

    @Test
    public void executeLogic2() {
        List<SyncPloyDetailSnapshotEntity> syncPloyDetailSnapshotEntities = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("88521")).listEnableSnapshotOffsetOne("88521", "881a7f9c500347ec8844507b32aacda0", 1);

        QueryMasterByIdArg queryMasterByIdArg = new QueryMasterByIdArg();
        queryMasterByIdArg.setTenantId("88466");
        queryMasterByIdArg.setSourceObjApiName("AccountObj");
        queryMasterByIdArg.setDataId("6572bb427d5c310007e63d41");
        queryMasterByIdArg.setSourceEventType(EventTypeEnum.UPDATE.getType());
        queryMasterByIdArg.setIntegrationStreamId("030a755b41d04d63b57901e6a384dbd9");
        queryMasterByIdArg.setTriggerSync(true);

        queryMasterByIdArg.setIncludeDetail(true);

        String jsonParams = JSONObject.toJSONString(queryMasterByIdArg);

        CustomFunctionCommonArg arg = new CustomFunctionCommonArg();
        arg.setTenantId("88466");
        arg.setType("queryMasterById");
        arg.setParams(jsonParams);
        Result<String> result = executeLogic(arg);
        assert result.isSuccess();
    }

    public Result<String> executeLogic(CustomFunctionCommonArg arg) {
        log.info("QueryMasterByIdServiceImpl.executeLogic,arg={}", arg);

        if (arg == null || StringUtils.isEmpty(arg.getTenantId()) || StringUtils.isEmpty(arg.getParams())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        QueryMasterByIdArg queryMasterByIdArg = JSONObject.parseObject(arg.getParams(), QueryMasterByIdArg.class);
        if (queryMasterByIdArg == null
                || StringUtils.isEmpty(queryMasterByIdArg.getTenantId())
                || StringUtils.isEmpty(queryMasterByIdArg.getDataId())
                || StringUtils.isEmpty(queryMasterByIdArg.getSourceObjApiName())
                || StringUtils.isEmpty(queryMasterByIdArg.getIntegrationStreamId())
                || queryMasterByIdArg.getSourceEventType() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        SyncPloyDetailEntity ployDetailEntity = syncPloyDetailManager.getEntryById(queryMasterByIdArg.getTenantId(),
                queryMasterByIdArg.getIntegrationStreamId());
        if(ployDetailEntity==null) {
            return Result.newError(ResultCodeEnum.INTEGRATION_STREAM_NOT_EXIST);
        }

        List<SyncPloyDetailSnapshotEntity> list = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(queryMasterByIdArg.getTenantId()))
                .listEnableSnapshotsBySyncPloyDetailsId(queryMasterByIdArg.getTenantId(), queryMasterByIdArg.getIntegrationStreamId(),
                        SyncPloyDetailStatusEnum.ENABLE.getStatus());

        if (CollectionUtils.isEmpty(list)) {
            return Result.newError(ResultCodeEnum.NO_ENABLED_INTEGRATION_STREAM);
        }
        SyncPloyDetailSnapshotEntity ployDetailSnapshotEntity = list.get(0);
        String sourceObjApiName = ployDetailSnapshotEntity.getSourceObjectApiName();
        String destObjApiName = ployDetailSnapshotEntity.getDestObjectApiName();

        String realApiName = ployDetailSnapshotEntity.getSourceObjectApiName();
        if (ployDetailEntity.getSourceTenantType().equals(TenantTypeEnum.ERP.getType())) {
            realApiName = idFieldConvertManager.getRealObjApiName(queryMasterByIdArg.getTenantId(), realApiName);
        }

        String logId = syncLogManager.initLogId(arg.getTenantId(), realApiName);
        log.info("QueryMasterByIdServiceImpl.executeLogic,traceId:{} transfer logId:{} ", com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil.get(), logId);
        com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil.initTrace(logId);


        List<QueryMasterByIdModel> dataList = new ArrayList<>();
        if (TenantType.ERP.equals(ployDetailEntity.getSourceTenantType())) {
            ErpIdArg erpIdArg = new ErpIdArg();
            erpIdArg.setTenantId(queryMasterByIdArg.getTenantId());
            erpIdArg.setObjAPIName(queryMasterByIdArg.getSourceObjApiName());
            erpIdArg.setDataId(queryMasterByIdArg.getDataId());
            erpIdArg.setSourceEventType(queryMasterByIdArg.getSourceEventType());
            erpIdArg.setSyncPloyDetailSnapshotId(list.get(0).getId());

            log.info("QueryMasterByIdServiceImpl.executeLogic,erpIdArg={}", erpIdArg);
            Result<List<SyncDataContextEvent>> result = erpDataPreprocessService.getReSyncObjDataById(erpIdArg);
            if (result.isSuccess()) {
                if (queryMasterByIdArg.isTriggerSync()) {
                    boolean needSendDetailEvent = true;
                    if (configCenterConfig.getNO_SEND_DETAIL_EVENT_CRM_OBJ_SET().contains(destObjApiName)) {
                        //不需要发送明细数据
                        needSendDetailEvent = false;
                    }
                    probeErpDataService.batchSendErpDataMqByContext(result.getData(), needSendDetailEvent);
                }

                if (CollectionUtils.isNotEmpty(result.getData())) {
                    QueryMasterByIdModel model = new QueryMasterByIdModel();
                    dataList = result.getData().stream().map((item) -> {
                        BeanUtils.copyProperties(item, model);
                        return model;
                    }).collect(Collectors.toList());
                }
                return Result.newSuccess(JSONObject.toJSONString(dataList));
            }
            return Result.copy(result);
        } else {
            HeaderObj headerObj = I18NHeaderObj.getHeader(queryMasterByIdArg.getTenantId(), i18NStringManager);
            String masterId = BomUtils.getBomInstanceId(queryMasterByIdArg.getDataId(), sourceObjApiName);
            com.fxiaoke.crmrestapi.common.result.Result<ObjectDataGetByIdResult> objectDataResult = objectDataService.getById(headerObj, sourceObjApiName, masterId, false);
            if (objectDataResult != null
                    && objectDataResult.isSuccess()
                    && objectDataResult.getData() != null
                    && objectDataResult.getData().getObjectData() != null) {
                com.fxiaoke.crmrestapi.common.data.ObjectData objectData = objectDataResult.getData().getObjectData();
                ObjectData sourceData = new ObjectData();
                sourceData.putAll(objectData);

                if (queryMasterByIdArg.isTriggerSync()) {
                    SyncDataContextEvent eventData = new SyncDataContextEvent();
                    eventData.setSourceData(sourceData);
                    eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());//默认发送更新
                    eventData.setSourceTenantType(TenantType.CRM);
                    eventData.setPloyDetailSnapshotId(ployDetailSnapshotEntity.getId());
                    eventData.setSyncLogId(LogIdUtil.get());
                    eventData.setDataReceiveType(DataReceiveTypeEnum.PAAS_META_EVENT.getType());
                    //调用dubbo的数据保留旧接口
                    BatchSendEventDataArg.EventData batchSendEventArg = SyncDataContextUtils.convertSendArgResultByContext(eventData);
                    BatchSendEventDataArg batchSendEventDataArg = new BatchSendEventDataArg(Lists.newArrayList(batchSendEventArg));
                    allModelDubboService.batchSendEventData2DispatcherMq(batchSendEventDataArg);
                }
                QueryMasterByIdModel dataModel = new QueryMasterByIdModel();
                dataModel.setSourceData(sourceData);
                dataList.add(dataModel);

                return Result.newSuccess(JSONObject.toJSONString(dataList));
            }
            return Result.newError(ResultCodeEnum.CALL_CRM_INTERFACE_FAILED, objectDataResult != null ? "" : objectDataResult.getMessage());
        }
    }
}
