package com.facishare.open.erp.connector.proxy.linkedin.service;

import com.facishare.open.erp.connector.proxy.linkedin.model.SimpleResponse;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import lombok.Getter;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/5/9 19:22:30
 */
@Component
public class OkHttpSupportHelp {
    @Autowired
    private OkHttpSupport okHttpSupport;

    @Getter
    private static OkHttpSupportHelp okHttpSupportHelp;

    @Autowired
    public void setOkHttpSupportHelp(final OkHttpSupportHelp okHttpSupportHelp) {
        OkHttpSupportHelp.okHttpSupportHelp = okHttpSupportHelp;
    }

    public SimpleResponse getResponse(final Request request) {
        return (SimpleResponse) okHttpSupport.syncExecute(request, new SyncCallback() {
            @Override
            public SimpleResponse response(final Response response) throws IOException {
                return new SimpleResponse(response);
            }
        });
    }
}
