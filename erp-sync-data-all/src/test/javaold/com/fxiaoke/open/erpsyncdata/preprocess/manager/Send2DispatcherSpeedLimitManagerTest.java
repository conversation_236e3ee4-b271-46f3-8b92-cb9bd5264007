package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date: 14:53 2023/3/2
 * @Desc:
 */
@Ignore
public class Send2DispatcherSpeedLimitManagerTest extends BaseTest {
    @Autowired
    private Send2DispatcherSpeedLimitManager send2DispatcherSpeedLimitManager;

    @Test
    public void tempDataNeedSend2Dispatcher() {
//        Boolean aBoolean = send2DispatcherSpeedLimitManager.ifMoreWaitingDispatcherData("84801");
//        System.out.println("");
    }
}