package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import lombok.Data;

import java.io.Serializable;

@Data
public class SyncDataMappingData extends BaseData implements Serializable {
    private String id;
    private String sourceTenantId;
    private String sourceObjectApiName;
    private String sourceDataId;
    private String sourceDataName;
    private String destObjectApiName;
    private String destTenantId;
    private String destDataId;
    private String destDataName;
    private Boolean isCreated;
    private String lastSyncDataId;
    private Integer lastSyncStatus;
    private Long lastSourceDataVserion;
    private String remark;
}
