package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.arg.CepArg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Getter
@Setter
@ToString
@ApiModel("分页参数")
public class PageArg extends CepArg implements Serializable {
    private static final long serialVersionUID = -1311659449786413692L;

    @ApiModelProperty("当前页码，默认1")
    private Integer pageNum=1;
    @ApiModelProperty("每页数量，默认100")
    private Integer pageSize=100;
    @ApiModelProperty("查询内容")
    private String queryStr;
}
