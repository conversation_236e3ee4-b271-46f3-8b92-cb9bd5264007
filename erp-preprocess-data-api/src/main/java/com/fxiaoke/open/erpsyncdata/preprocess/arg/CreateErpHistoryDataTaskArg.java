package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date: 14:15 2021/9/2
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("创建同步任务参数")
public class CreateErpHistoryDataTaskArg {
    @ApiModelProperty("是否已校验")
    private Boolean isCheck;
    @ApiModelProperty("同步任务")
    private ErpHistoryDataTaskResult task;

    @ApiModelProperty("绕开限制的秘钥")
    private String skipLimitCheckSecret;
}
