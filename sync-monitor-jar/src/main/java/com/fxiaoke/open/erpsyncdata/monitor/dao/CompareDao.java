package com.fxiaoke.open.erpsyncdata.monitor.dao;

import cn.hutool.core.lang.Dict;
import com.fxiaoke.open.erpsyncdata.monitor.constant.DataSourceType;
import com.fxiaoke.open.erpsyncdata.monitor.model.CompareObjectMapping;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
public interface CompareDao {

    Dict getDataSourceInfo(String tenantId, String objApiName, DataSourceType dataSourceType);
    List<CompareObjectMapping> listObjMappingAndRefreshCache(String tenantId);
    CompareObjectMapping getObjMapping(String tenantId, String compareName);
}
