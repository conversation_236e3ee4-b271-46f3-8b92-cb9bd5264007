package com.fxiaoke.open.oasyncdata.model;

import lombok.Data;

import java.io.Serializable;

/**
 * oa字段apiName
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Data
public class OAObjectFieldVO implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    private String id;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 对象名称
     */
    private String objApiName;


    /**
     * 标签
     */
    private String label;

    /**
     * 字段ApiName
     */
    private String fieldApiName;

    /**
     * 占位符
     */
    private String replaceName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;
}