package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/3/16 10:36
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HeaderFunctionArg implements Serializable {
    private static final long serialVersionUID = 8281928851787534145L;

    private String tenantId;
    private String dataCenterId;
    private String  functionName;
    private String functionNameSpace;//函数命名空间
    private Object params;
    private ErpObjInterfaceUrlEnum interfaceUrl;
    private String syncPloyDetailSnapshotId;

}
