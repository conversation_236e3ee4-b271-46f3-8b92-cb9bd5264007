package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/19
 */
public class IdGeneratorTest {
    public static void main(String[] args) {
        IdGenerator idGenerator = new IdGenerator();
        String s = idGenerator.get();
        System.out.println(s);
        TimeInterval timer = DateUtil.timer();
        for (int i = 0; i < 100000; i++) {
            idGenerator.get();
        }
        long cost = timer.intervalMs();
        System.out.println(cost);
        System.out.println(1000 * 100000 / cost);
    }
}