package com.fxiaoke.open.erpsyncdata.main.service

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class SyncPloyDetailSnapshotServiceImplTest extends Specification {

    SyncPloyDetailSnapshotServiceImpl impl
    def tenantId = "123456"

    def setup() {
        def factory = Mock(OuterServiceFactory) {
            get(_) >> Mock(OuterService) {
                getMasterObjectApiName(*_) >> { args ->
                    if (args[1] != null) {
                        return Result2.newSuccess()
                    }
                    return Result2.newSuccess("test")
                }
            }
        }
        def dao = Mock(SyncPloyDetailSnapshotDao) {
            setTenantId(*_) >> it
            listNewestByDestTenantIdAndDestObjectApiName(*_) >> {
                def mappings = new DetailObjectMappingsData()
                mappings.add(new DetailObjectMappingsData.DetailObjectMappingData(destObjectApiName:  "api_name"))
                def entity = new SyncPloyDetailSnapshotEntity(syncPloyDetailData: new SyncPloyDetailData(detailObjectMappings: mappings))
                return [entity]
            }
            listNewestBySourceTenantIdAndSrouceObjectApiName(*_) >> {
                def mappings = new DetailObjectMappingsData()
                mappings.add(new DetailObjectMappingsData.DetailObjectMappingData(sourceObjectApiName: "api_name"))
                SyncPloyDetailSnapshotEntity entity = new SyncPloyDetailSnapshotEntity(destTenantId: tenantId, syncPloyDetailData: new SyncPloyDetailData(detailObjectMappings: mappings))
                return [entity]
            }
        }
        def manager = Mock(SyncPloyDetailSnapshotManager) {
            getEntryBySnapshotId(*_) >> new SyncPloyDetailSnapshotEntity(syncPloyDetailData: new SyncPloyDetailData())
        }
        impl = new SyncPloyDetailSnapshotServiceImpl(outerServiceFactory: factory, syncPloyDetailSnapshotDao: dao, syncPloyDetailSnapshotManager: manager)
    }

    def "test isMatchDestCondition"() {
        expect:
        impl.isMatchDestCondition(tenantId, ObjectApiNameEnum.FS_PRICEBOOKPRODUCT_OBJ.getObjApiName(), 0).isSuccess()
    }

    def "test listNewestEnableSyncPloyDetailsSnapshots - #name"() {
        expect:
        impl.listNewestEnableSyncPloyDetailsSnapshots(tenantId, "api_name", type, ["123456"]).isSuccess()
        where:
        name        | type
        "获取到"     | 0
        "未获取"     | null
    }

    def "test listEnableSyncPloyDetailByDestApiName - #name"() {
        expect:
        impl.listEnableSyncPloyDetailByDestApiName(tenantId, "api_name", type, ["123456"]).isSuccess()
        where:
        name        | type
        "获取到"     | 0
        "未获取"     | null
    }

    def "test getSyncPloyDetailSnapshotBySnapshotId"() {
        expect:
        impl.getSyncPloyDetailSnapshotBySnapshotId(tenantId, "test").isSuccess()
    }
}
