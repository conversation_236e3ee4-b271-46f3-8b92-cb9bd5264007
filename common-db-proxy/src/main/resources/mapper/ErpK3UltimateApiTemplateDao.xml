<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpK3UltimateApiTemplateDao">

  <select id="findData" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateApiTemplateEntity">
    select * from erp_k3_ultimate_api_template
    where tenant_id=#{tenantId}
    and data_center_id=#{dataCenterId}
    and erp_obj_api_name=#{erpObjApiName}
  </select>
</mapper>