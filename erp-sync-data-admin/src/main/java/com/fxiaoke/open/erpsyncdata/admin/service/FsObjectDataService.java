package com.fxiaoke.open.erpsyncdata.admin.service;

import com.facishare.organization.api.model.employee.EmployeeDto;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 13:59 2020/9/3
 * @Desc:
 */
public interface FsObjectDataService {
    Result<List<EmployeeDto>> getEmployeeFs(Integer ei);

    Result<List<EmployeeDto>> queryEmployeeByFsUserId(Integer ei, Collection<Integer> userIds);

}
