package com.fxiaoke.open.erpsyncdata.dbproxy.remote.service;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.enterprise.arg.BatchGetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/4 15:55:11
 */
@Component
public class UserCenterService {
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserCenterService userCenterService;

    @Cached(timeUnit = TimeUnit.MINUTES, expire = 10, cacheType = CacheType.LOCAL)
    public String getEnterpriseName(String tenantId) {
        if (StringUtils.isBlank(tenantId) || !StringUtils.isNumeric(tenantId)) {
            return null;
        }

        final GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(new GetSimpleEnterpriseDataArg(Integer.parseInt(tenantId), null));

        return Objects.isNull(simpleEnterpriseData.getEnterpriseData()) ? null : simpleEnterpriseData.getEnterpriseData().getEnterpriseName();
    }

    public Map<String, String> batchGetEnterpriseName(List<String> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return new HashMap<>();
        }

        final List<Integer> eis = tenantIds.stream()
                .distinct()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        final BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(new BatchGetSimpleEnterpriseDataArg(eis, null));

        return batchGetSimpleEnterpriseDataResult.getSimpleEnterpriseList().stream()
                .collect(Collectors.toMap(data -> String.valueOf(data.getEnterpriseId()), SimpleEnterpriseData::getEnterpriseName, (s1, s2) -> s1));
    }

    public Map<String, SimpleEnterpriseData> batchGetSimpleEnterprise(Collection<String> tenantIds) {
        final List<Integer> eis = tenantIds.stream().distinct().map(Integer::valueOf).collect(Collectors.toList());
        BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg(eis, null);
        BatchGetSimpleEnterpriseDataResult result = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
        return result.getSimpleEnterpriseList().stream().collect(Collectors.toMap(data -> String.valueOf(data.getEnterpriseId()), Function.identity(), (s1, s2) -> s1));
    }

    public SimpleEnterpriseData getSimpleEnterpriseData(String tenantId) {
        final GetSimpleEnterpriseDataResult simpleEnterpriseData = enterpriseEditionService.getSimpleEnterpriseData(new GetSimpleEnterpriseDataArg(Integer.parseInt(tenantId), null));
        return simpleEnterpriseData.getEnterpriseData();
    }

    public Map<String, EnterpriseData> batchGetEnterprise(Collection<String> tenantIds) {
        final List<Integer> eis = tenantIds.stream().distinct().map(Integer::valueOf).collect(Collectors.toList());
        final List<EnterpriseData> enterpriseDatas = getEnterpriseData(eis);
        return enterpriseDatas.stream().collect(Collectors.toMap(data -> String.valueOf(data.getEnterpriseId()), Function.identity(), (s1, s2) -> s1));
    }

    public List<EnterpriseData> getEnterpriseData(List<Integer> eis) {
        BatchGetEnterpriseDataArg arg = new BatchGetEnterpriseDataArg(eis, null);
        BatchGetEnterpriseDataResult result = enterpriseEditionService.batchGetEnterpriseData(arg);
        return result.getEnterpriseDatas();
    }

    public String getTenantId(String ea) {
        return String.valueOf(eieaConverter.enterpriseAccountToId((ea)));
    }

    public List<String> getTenantIds(List<String> downstreamAccounts) {
        final Collection<Integer> eis = eieaConverter.enterpriseAccountToId(downstreamAccounts).values();
        return eis.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
    }

    public String getDomain(String tenantId) {
        return userCenterService.getDomain(Integer.parseInt(tenantId));
    }

    @Cached(cacheType = CacheType.LOCAL, expire = 600)
    public String getDomain(int ei) {
        final GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseId(ei);
        final GetEnterpriseDataResult enterpriseData = enterpriseEditionService.getEnterpriseData(arg);
        return Optional.ofNullable(enterpriseData)
                .map(GetEnterpriseDataResult::getEnterpriseData)
                .map(EnterpriseData::getDomain)
                .map(domain -> domain.startsWith("https") ? domain : "https://" + domain)
                .orElse(ConfigCenter.ERP_DOMAIN_URL);
    }

    /**
     * 文件预览路径，%s为带ext的npath
     */
    public String getPreviewFilePathFormat(String enterpriseAccount) {
        final int ei = eieaConverter.enterpriseAccountToId(enterpriseAccount);
        return userCenterService.getDomain(ei) + "/FSC/EM/File/GetByPath?path=%s";
    }

    /**
     * 文件预览路径，%s为带ext的npath
     */
    public String getDownloadFilePath(String tenantId) {
        return getDomain(tenantId) + "/FSC/EM/File/DownloadByPath?Path=%s&name=%s";
    }

    /**
     * 文件预览路径
     */
    public String getTnViewUrlFormat(String tenantId) {
        return getDomain(tenantId) + "/dps/preview/bypath?path=%s.%s";
    }
}
