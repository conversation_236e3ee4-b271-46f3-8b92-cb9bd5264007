package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.factory;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.specialBusiness.K3UltimateSpecialBusiness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class K3UltimateSpecialBusinessFactory {
    @Autowired
    private Map<String, K3UltimateSpecialBusiness> specialBusinessMap;

    public K3UltimateSpecialBusiness getHandlerByName(String name) {
        K3UltimateSpecialBusiness specialBusiness = specialBusinessMap.get(name);
        if(specialBusiness==null)
            specialBusiness = specialBusinessMap.get("base");//基类组件  K3UltimateBaseSpecialBusiness
        return specialBusiness;
    }
}
