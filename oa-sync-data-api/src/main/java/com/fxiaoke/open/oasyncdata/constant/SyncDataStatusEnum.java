package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 这里如果新增，记得在SyncStatusEnum同步新增
 */
@Getter
@AllArgsConstructor
public enum SyncDataStatusEnum {
    TRIGGER_FAILED(1, "触发失败", I18NStringEnum.s687.getI18nKey()),
    BE_PROCESS(2, "待处理", I18NStringEnum.s688.getI18nKey()),
    PROCESS_FAILED(3, "处理失败", I18NStringEnum.s689.getI18nKey()),
    /**
     * @Deprocated 去除这个状态
     */
    @Deprecated
    BE_WRITE(4, "待传输", I18NStringEnum.s690.getI18nKey()),
    WRITE_FAILED(5, "传输失败", I18NStringEnum.s691.getI18nKey()),
    WRITE_SUCCESS(6, "成功", I18NStringEnum.s6.getI18nKey()),
    WAITTING(7, "等待处理", I18NStringEnum.s692.getI18nKey()),
    @Deprecated
    IGNORE(100, "忽略", I18NStringEnum.s693.getI18nKey()),
    ;
    private int status;
    private String name;
    private String i18nKey;

    public static String getNameByStatus(I18NStringManager i18NStringManager, String lang, String tenantId, int status) {
        for (SyncDataStatusEnum syncDataStatus : SyncDataStatusEnum.values()) {
            if (syncDataStatus.getStatus() == status) {
                return i18NStringManager.get(syncDataStatus.getI18nKey(),lang,tenantId,syncDataStatus.getName());
            }
        }
        return null;
    }

    public static boolean isFailed(int status) {
        if (TRIGGER_FAILED.status == status || PROCESS_FAILED.status == status || WRITE_FAILED.status == status) {
            return true;
        }
        return false;
    }

    public static int isSyncDataStatusReturnInt(int status) {
        if (TRIGGER_FAILED.status == status || PROCESS_FAILED.status == status || WRITE_FAILED.status == status) {
            return SyncStatusEnum.FAILED.getStatus();
        }else if (WRITE_SUCCESS.status == status) {
            return SyncStatusEnum.SUCCESS.getStatus();
        }
        return 0;
    }

    public static boolean isSuccess(int status) {
        if (WRITE_SUCCESS.status == status) {
            return true;
        }
        return false;
    }

    public static int isSuccessReturnInt(int status) {
        if (WRITE_SUCCESS.status == status) {
            return SyncStatusEnum.SUCCESS.getStatus();
        }
        return 0;
    }

    public static boolean isDoing(int status) {
        if (BE_PROCESS.status == status || BE_WRITE.status == status) {
            return true;
        }
        return false;
    }

    public static boolean isDoingOrWaitting(int status) {
        if (isDoing(status) || WAITTING.status == status) {
            return true;
        }
        return false;
    }
}