package com.fxiaoke.open.oasyncdata.service;


import com.fxiaoke.open.oasyncdata.model.QueryOAEmployeeMappingListArg;
import com.fxiaoke.open.oasyncdata.result.EmployeeMappingResult;
import com.fxiaoke.open.oasyncdata.result.QueryResult;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/3/18
 */
public interface OAUserService {
    Result<QueryResult<List<EmployeeMappingResult>>> getOAUserList(String tenantId, QueryOAEmployeeMappingListArg queryArg);


    Result<String> updateOAUserInfo(String tenantId, EmployeeMappingResult employeeMappingResult);


    Result<String> deleteOAUserInfo(String tenantId, EmployeeMappingResult employeeMappingResult);

    /**
     * 删除员工或用户的绑定关系
     *
     * @param tenantId
     * @return
     */
    Result<String> batchDeleteOAUserMapping(String tenantId,
                                            String dataCenterId,
                                            List<String> idList,
                                            String lang);

    /**
     * 导出系统映射数据，如果idList为空，导出所有，如果不为空，只导出idList指定的数据
     *
     * @param tenantId
     * @param userId
     * @param dataCenterId
     * @param idList
     * @return
     */
    Result<Void> exportOAUserMapping(String tenantId,
                                     Integer userId,
                                     String dataCenterId,
                                     List<String> idList,
                                     String lang);
}
