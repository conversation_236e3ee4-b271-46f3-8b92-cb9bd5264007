<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 配置基础组件为ERROR级别，避免打印过多影响服务自己日志 -->
    <logger name="org.apache" level="ERROR"/>
    <logger name="com.github" level="WARN"/>
    <logger name="Access" level="INFO"/>
    <logger name="com.facishare.uc.api.cache" level="ERROR"/>
    <logger name="com.facishare.paas.license.factory.LicenseFactoryBean" level="ERROR"/>

    <logger level="INFO" name="com.fxiaoke.open.erpsyncdata" additivity="true"/>

    <logger name="com.fxiaoke.dispatcher.mq" level="INFO"/>
    <logger name="com.fxiaoke.dispatcher.processor" level="INFO"/>
    <logger name="com.fxiaoke.dispatcher.store" level="INFO"/>
    <logger name="com.fxiaoke.dispatcher.processor.LockService" level="INFO"/>

    <logger name="com.github.mybatis.interceptor" level="INFO">
        <appender-ref ref="console"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="console"/>
    </root>

</configuration>