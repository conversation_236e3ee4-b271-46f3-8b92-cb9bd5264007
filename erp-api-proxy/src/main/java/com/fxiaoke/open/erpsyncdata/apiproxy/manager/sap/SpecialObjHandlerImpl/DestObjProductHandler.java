package com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandlerImpl;

import com.fxiaoke.open.erpsyncdata.apiproxy.manager.sap.SpecialObjHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpIdResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:48 2020/12/22
 * @Desc:
 */
@Component
public class DestObjProductHandler implements SpecialObjHandler {
    @Autowired
    private SyncCpqService syncCpqService;
    @Override
    public void afterQueryListData(TimeFilterArg timeFilterArg, List<StandardData> standardDataList,String dataCenterId) {
        syncCpqService.listErpProductByTime(standardDataList,timeFilterArg,dataCenterId);
    }

    @Override
    public void afterReSyncDataById(ErpIdArg erpIdArg, List<StandardData> standardDataList,String dataCenterId) {
        syncCpqService.getErpProductById(standardDataList,erpIdArg,dataCenterId);
    }

    @Override
    public String getObjApiName() {
        return ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName();
    }

    @Override
    public void beforeCreateErpObjData(String tenantId,StandardData standardData,String dataCenterId) {

    }

    @Override
    public void afterCreateErpObjData(String tenantId,StandardData standardData, Result<ErpIdResult> result,String dataCenterId) {

    }

    @Override
    public void beforeUpdateErpObjData(String tenantId,StandardData standardData,String dataCenterId) {

    }

    @Override
    public void afterUpdateErpObjData(String tenantId,StandardData standardData, Result<ErpIdResult> result,String dataCenterId) {

    }
}
