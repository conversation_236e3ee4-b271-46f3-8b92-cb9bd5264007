package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.FieldMappingsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * 集成平台实施效率工具服务
 * <AUTHOR>
 * @date 2021.12.08
 */
public interface ProductiveService {
    /**
     * 帮助客户默认预置常用的对象和策略以及策略明细
     * @return
     */
    Result<Integer> isObjectPreset(String sourceObjectApiName,String destObjectApiName);
    Result<FieldMappingsResult> getFieldMappingData(String sourceObjectApiName, String destObjectApiName);
    Result<ObjectMappingResult> getFieldTemplateByName(String sourceObjectApiName,
                                                       String destObjectApiName,
                                                       SyncPloyDetailResult syncPloyDetailResult,
                                                       String lang);
}
