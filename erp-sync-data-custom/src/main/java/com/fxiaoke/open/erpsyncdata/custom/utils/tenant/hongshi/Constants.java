package com.fxiaoke.open.erpsyncdata.custom.utils.tenant.hongshi;
import org.springframework.util.ResourceUtils;
import java.io.FileNotFoundException;

public class Constants {
    public final static int CONNECT_TIMEOUT = 600; // 设置连接超时时间，单位秒
    public final static int READ_TIMEOUT = 1200; // 设置读取超时时间，单位秒
    public final static int WRITE_TIMEOUT = 600; // 设置写的超时时间，单位秒
    public static final String DEVELOP_MODE = "0";

    public static final String MER_PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCfljSis++nBMCdm+kbv4lAaMp2A+bhsf+phrzd5RfB396DpLaunNMGr14ILD7Sw//5tjqQaSW3jPQHj+XSjFDcfZ2/0HdWOo37x0Jjlbt1RXvKjHpsV9HM0oplBxLagiqTK1kGrXas+MkboNQLwuE0RSaoCsvPid7uVjSa7rAV0gAXJxYqpFbx/TU6Ikvm9MUaNJ0vAYI3BsqsAdowwdG/6jApNmway9cxCfneN5QPtVSeIIg8z9gwzxHW9KpTkaFONALDMrDNM2OVS1sjxDpbv4Fs2BQuuPmsYJ/jTGWX+8uPMHp6rQgqdv5Zs0nPEslyUuMHrDoenTY0OTCnVTqhAgMBAAECggEBAIikwU7rCPGWZSLOo6uJtgi7cVfZgXCQy5spbkAE7/GmMU3A1FbwmtQnhjkzQVuQTXZQLRcHoS7n9u8erCc60+XnfRIHepcPrhD+jX7db1Q7eYJ2BEEOdOqO/8b/ZopIWBIJVMId9vamUvRgIhAr/k6WQ0IxdE+w+nxSvdpHM+iJudg4YtNt248uBseJ6yKPrg3qHOQs/5X49CQYbeMTmSjkZhElH3YXeerzF0CIvLiUoBz1P8lGcmsjqOUE/xNtHaYnR1WaG7aBPOwAAGsZ8oedKoUhkIXDHuEZHF4EILUbbRG4Loa2iVD25rhCGnWXrr0XaI2k58nGCoyzdRXsgEECgYEA41ZH79oEXZA8aZdULbDVIRW5gqssE/IXWPk59pin6/OssIVBn0eH1DXdr1DGL0diNiol/k13I+jvkQ/4PagtU4pueOoiIXyofZQ3sZFFtbrs1MhS9j7k5SovJ39LepDhYdtYrub5H8+RkRPEZ2TZM8JzMoASIH3Cmz+BRZ1O0gkCgYEAs7Uo6dT9bZF3C53D9IoUfPtXoT/h4IthVLTMmN/Z3Q3+xmeXF7Xi49DWSU3hOz5WyvXKWdbdjFnWjVe48lX/kGNIzhHKuO1LkOSxKA84IbmJebu3ubdMCVue/ny8oBObb6/w9VtWlgRHqcLD/kSIVV9C8quSWzfyEQXx2kpD6dkCgYBwx1zooD+KKwPNXkVkZVVWFHDzYfd77Z2nhfw7WX/0je4do/3qBpJbtF1Fm8y1p7QRKteuUL/KEMTIMiP0cnoJ96LWWVCVTEJKCj2W2Xq7rP/Qj5CYmJr1FPhPn9bR9w3YffiAzBTKsy6DykqlAGLASrbbVq2ncLwaSGbZIjD4AQKBgQCxJ5+jk+IZB1OR0f8ArLYx1xy1y2ICl6Rleq5O7i25aziDok8igp2e85qrPOkSHAw3nEHUeqqA13dYcufRjdGYJyf4bTWpQ9RAjBHQaeBt7peFt/YQUI/WkcmVGqZgL7QFUC570YTz8lgrE+NeX3T+pCA9VcG7xtUMzw1yoc99UQKBgGLpRPkjWq2NqBioMMxv/qbCjyxkUqecBTRwgm3GI5/CnPvSDmkKpE9RTbOjH5g1aKtFzp+I2lajsGnyuOl2upv/Ygj20ffy20eKeqTysTaQMvAEdlB24Ts0Gi9ThlGXpZICw6FnOiKyRcQRcMxya0Hz3uY2WAYYjrdm0sGUmRoa";//商户私钥
    public static final String PLOAT_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr0vK5nXvpAoWPOkT1MUtwnFiNMObwUhC9874a9wVWFcGDg9kp965+3Y9JAdGjCstFEZ5PYiWnJ270TVthPWbm6JyoeMgL2e9nAh4d4oxKyAQVeZ3fU8PFpteR2P8BvQtMMhqYEH2uu8W4/iSrNFQP/4706wzoMra7NrGESYwWn/S4drM/HA4iajLncccQSWgFEwSzuwoVWkxpJWfoNvw/yAk9CIWWxqcKjkNJBOpBvx/JEbVNj8zBIU+CS8vkRMJ2NV9a2hR1rq5CdcIZvDiVzRWQnIo54kCx3akXAiH8w2dlX0VklOzVMrVX+T6preKwOfqBpKOm/EBHFeOl5FBFQIDAQAB";//平台公钥
    public static final String TEST_SERVER_URL = "http://************:13004";
    public static final String TEST_KEY = "0123456789ABCDEFFEDCBA9876543210";
    public static final String TEST_ACCOUNT_UUID = "46af1bb1-2d11-46d9-b1ae-27f59e8178c7";

    public static final String ONLINE_SERVER_URL = "https://api.shufafin.com";
    public static final String ONLINE_BACK_URL = "http://10.255.0.113:7485";
    public static final String ONLINE_KEY = "617B3A0CE8F071009231F236FF9AA95C";
    public static final String ONLINE_ACCOUNT_UUID = "a5762297-4049-4aeb-99bd-9b37ec37686f";
    public static String path="";

    static {
        try {
            path= ResourceUtils.getURL("classpath:").getPath();
            //System.out.println("path" +path);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }
    /**
     * 授信证书路径
     */
    private static final String testtrustCertPath = path+"/tenant/hongshi/CFCA_ACS_TEST_OCA31.cer";

    private static final String onllinetrustCertPath = path+"/tenant/hongshi/cfca_cmc_server.cer";

    public static String getTrustCertPath() {

        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return onllinetrustCertPath;
        }
        // 开发模式
        else {
            return testtrustCertPath;
        }
    }

    /**
     * 验证吊销证书路径
     */
    private static final String testcrlCertPath = path+"/tenant/hongshi/crl33.crl";

    private static final String onlinecrlCertPath = path+"/tenant/hongshi/cfca_cmc_server.crl";

    public static String getCrlCertPath() {
        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return onlinecrlCertPath;
        }
        // 开发模式
        else {
            return testcrlCertPath;
        }
    }

    /**
     * 签名证书路径
     */
    private static final String testsignCertPath =  path+"/tenant/hongshi/cfca_chanpay.pfx";

    private static final String onlinesignCertPath =  path+"/tenant/hongshi/C10502.pfx";

    public static String getSignCertPath() {
        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return onlinesignCertPath;
        }
        // 开发模式
        else {
            return testsignCertPath;
        }
    }

    /**
     * 签名证书密码
     */
    private static final String testsignCertPwd = "12345678";
    private static final String onlinesignCertPwd = "12345678";

    public static String getSignCertPwd() {
        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return onlinesignCertPwd;
        }
        // 开发模式
        else {
            return testsignCertPwd;
        }
    }

    public static String getServerUrl() {
        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return ONLINE_SERVER_URL;
        }
        // 开发模式
        else {
            return TEST_SERVER_URL;
        }
    }

    public static String getKey() {
        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return ONLINE_KEY;
        }
        // 开发模式
        else {
            return TEST_KEY;
        }
    }

    public static String getAccountUuid() {
        // 生产模式
        if ("0".equals(DEVELOP_MODE)) {
            return ONLINE_ACCOUNT_UUID;
        }
        // 开发模式
        else {
            return TEST_ACCOUNT_UUID;
        }
    }
}
