package com.fxiaoke.open.erpsyncdata.admin.model;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.mongo.CollStat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/20
 */
public class CleanLogMongoDb {
    /**
     * 记录
     */
    @Data
    @Accessors(chain = true)
    public static class Record {
        private String tenantId;
        /**
         * 期望删除的数量
         */
        private long exceptDeleteCount;
        /**
         * 真实删除的数量
         */
        private long deleteCount;
        /**
         * 总计数
         */
        private long allCount;
        /**
         * 统计信息
         */
        private Map<String, CollStat> statMap;
    }

}
