package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.admin.service.OAConnectorService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result3;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result4;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class OAConnectorServiceImpl implements OAConnectorService {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private EIEAConverter eieaConverter;

    @Override
    public Result3<Void> checkAndInitFeiShuConnector(String tenantId,String dataCenterId) {
        if(StringUtils.isEmpty(ConfigCenter.feishuConnectorInternalUrl)) {
            log.info("OAConnectorServiceImpl.checkAndInitFeiShuConnector,feishuConnectorInternalUrl={}",ConfigCenter.feishuConnectorInternalUrl);
            return new Result3<>();
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        String url = ConfigCenter.feishuConnectorInternalUrl + "/checkAndInitConnector?fsEa="+fsEa + "&dataCenterId=" + dataCenterId;
        log.info("OAConnectorServiceImpl.checkAndInitFeiShuConnector,url={}",url);
        Result3<Void> response = proxyHttpClient.getUrl(url, new HashMap<>(), new TypeReference<Result3<Void>>() {

        });
        log.info("OAConnectorServiceImpl.checkAndInitFeiShuConnector,response={}",response);
        return response;
    }

    @Override
    public Result4<Void> checkAndInitQywxConnector(String tenantId,String dataCenterId) {
        if(StringUtils.isEmpty(ConfigCenter.qywxConnectorInternalUrl)) {
            log.info("OAConnectorServiceImpl.checkAndInitQywxConnector,qywxConnectorInternalUrl={}",ConfigCenter.qywxConnectorInternalUrl);
            Result4<Void> result4 = new Result4<>();
            result4.setErrorCode("s120050000");
            return result4;
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId));
        String url = ConfigCenter.qywxConnectorInternalUrl + "/checkAndInitConnector?fsEa=" + fsEa + "&dataCenterId=" + dataCenterId;
        log.info("OAConnectorServiceImpl.checkAndInitQywxConnector,url={}",url);
        Result4<Void> response = proxyHttpClient.getUrl(url, new HashMap<>(), new TypeReference<Result4<Void>>() {

        });
        log.info("OAConnectorServiceImpl.checkAndInitQywxConnector,response={}",response);
        return response;
    }

    @Override
    public Result<List<DataCenterInfoResult>> checkAndInitOAConnector(String tenantId) {
        if(StringUtils.isEmpty(ConfigCenter.OUTOA_BASE_INNER_URL)) {
            log.info("OAConnectorServiceImpl.checkAndInitFeiShuConnector,feishuConnectorInternalUrl={}",ConfigCenter.feishuConnectorInternalUrl);
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        String url = ConfigCenter.OUTOA_BASE_INNER_URL + "/getOrInitConnect?tenantId="+tenantId;
        Result<List<JSONObject>> outerOAConnectResult = proxyHttpClient.postUrl(url, null, new HashMap<>(), new TypeReference<Result<List<JSONObject>>>() {
        });
        List<DataCenterInfoResult> dataCenterInfoResults= Lists.newArrayList();
        if(outerOAConnectResult.isSuccess()&&outerOAConnectResult.getData()!=null) {
            for(JSONObject jsonObject:outerOAConnectResult.getData()) {
                DataCenterInfoResult dataCenterInfoResult = JSONObject.toJavaObject(jsonObject, DataCenterInfoResult.class);
                dataCenterInfoResult.setChannel(ErpChannelEnum.valueOf(jsonObject.getString("channel")));
                dataCenterInfoResults.add(dataCenterInfoResult);
            }
        }
        return Result.newSuccess(dataCenterInfoResults);
    }

    @Override
    public Result<DataCenterInfoResult> enableAddConnect(String tenantId, String channelEnum) {
        if(StringUtils.isEmpty(ConfigCenter.OUTOA_BASE_INNER_URL)) {
            log.info("OAConnectorServiceImpl.checkAndInitFeiShuConnector,feishuConnectorInternalUrl={}",ConfigCenter.feishuConnectorInternalUrl);
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        String url = ConfigCenter.OUTOA_BASE_INNER_URL + "/enableAddConnect?tenantId="+tenantId+"&channelEnum="+channelEnum;
        Result<JSONObject> outerOAConnectResult = proxyHttpClient.postUrl(url, null, new HashMap<>(), new TypeReference<Result<JSONObject>>() {
        });
        log.info("data result:{}",JSONObject.toJSONString(outerOAConnectResult));
        DataCenterInfoResult dataCenterInfoResult=new DataCenterInfoResult();
        if(outerOAConnectResult.isSuccess()&&outerOAConnectResult.getData()!=null) {
            dataCenterInfoResult=JSONObject.parseObject(outerOAConnectResult.getData().toJSONString(),DataCenterInfoResult.class);
            return Result.newSuccess(dataCenterInfoResult);

        }else{
            return Result.newError(outerOAConnectResult.getErrCode(),outerOAConnectResult.getErrMsg());
        }

    }
}