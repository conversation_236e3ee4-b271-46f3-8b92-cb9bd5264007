package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ListErpObjDataResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
@Slf4j
public class ErpDataServiceImplTest extends BaseTest {

  @Autowired
  private ErpDataService erpDataService;

  @Test
  public void getErpDataTest() {
    ErpIdArg erpIdArg = new ErpIdArg();
    erpIdArg.setTenantId("81138");
    erpIdArg.setObjAPIName("SAL_SaleOrder");
    erpIdArg.setDataId("100731");
    Result<SyncDataContextEvent> erpObjDataById = erpDataService.getErpObjDataById(erpIdArg,"",null);
    log.info("erpObjDataByIdresult:{}", JacksonUtil.toJson(erpObjDataById));
  }
}
