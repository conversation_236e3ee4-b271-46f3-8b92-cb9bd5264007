package com.fxiaoke.open.erpsyncdata.admin.result;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/21
 */
@Getter
@Setter
@ToString
public class PollingTaskResult {
    /**
     * 任务Id
     */
    private String taskId;
    /**
     * 是否已完成
     */
    private boolean complete = true;

    /**
     * 构建未完成的任务。
     * @param taskId
     * @return
     */
    public static PollingTaskResult of(String taskId){
        PollingTaskResult pollingTaskResult = new PollingTaskResult();
        pollingTaskResult.setTaskId(taskId);
        return pollingTaskResult;
    }
}
