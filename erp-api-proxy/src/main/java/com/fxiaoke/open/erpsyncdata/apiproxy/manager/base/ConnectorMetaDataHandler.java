package com.fxiaoke.open.erpsyncdata.apiproxy.manager.base;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.ObjectDescArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjTreeNode;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
public interface ConnectorMetaDataHandler {
    /**
     * 获取对象列表
     */
     Result<List<ErpObjTreeNode>> listObjects(ErpConnectInfoEntity connectInfo);

    /**
     * 获取单个对象树，不带字段。参数只有主对象apiName，如果可以也可以直接使用getObjectDesc
     */
     Result<ErpObjTreeNode> getObjectTree(ErpConnectInfoEntity connectInfo, ObjectDescArg arg);

    /**
     * 获取单个对象描述，带字段。参数有主对象apiName，和明细apiName
     */
     Result<ErpObjTreeNode> getObjectDesc(ErpConnectInfoEntity connectInfo, ObjectDescArg.ParseObjField arg);
}
