package com.fxiaoke.open.oasyncdata.db.entity;

import com.fxiaoke.open.oasyncdata.db.annotation.TenantID;
import com.fxiaoke.open.oasyncdata.db.constant.TenantConfigurationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date: 10:25 2021/2/25
 * @Desc: erp企业配置信息
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "erp_tenant_configuration")
public class ErpTenantConfigurationEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 配置类型
     * 也存在动态type
     *
     * @see TenantConfigurationTypeEnum
     */
    private String type;

    /**
     * 配置信息
     */
    private String configuration;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}