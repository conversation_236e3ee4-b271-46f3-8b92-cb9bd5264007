package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.dao
//package com.fxiaoke.open.erpsyncdata.dbproxy.elastic.dao
//
//import com.alibaba.fastjson.JSON
//import com.fxiaoke.elasticsearch.service.ElasticSearchService
//import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig
//import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum
//import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum
//import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ObjectInvokeEntity
//import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ObjectInvokeStatus
//import org.elasticsearch.ElasticsearchStatusException
//import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
//import org.elasticsearch.client.RequestOptions
//import org.elasticsearch.client.indices.CreateIndexRequest
//import org.elasticsearch.client.indices.GetMappingsRequest
//import org.elasticsearch.common.settings.Settings
//import org.elasticsearch.xcontent.XContentType
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.test.context.ContextConfiguration
//import spock.lang.Specification
//
//import java.util.stream.IntStream
//
///**
// * <AUTHOR>
// * @date 2022/12/8 09:56:44
// */
//@ContextConfiguration("classpath*:test/test-es.xml")
//class com.fxiaoke.open.erpsyncdata.dbproxy.elastic.dao.ObjectInvokeElasticSearchDaoTest extends Specification {
//
//    @Autowired
//    private ElasticSearchService searchService
//
//    private ObjectInvokeElasticSearchDao objectInvokeElasticSearchDao = new ObjectInvokeElasticSearchDao()
//
//    static {
//        System.setProperty("process.profile", "fstest");
//        System.setProperty("process.name", "fs-erp-sync-data");
//    }
//
//    void setup() {
//        objectInvokeElasticSearchDao.searchService = searchService
//        objectInvokeElasticSearchDao.config = Mock(ConfigCenterConfig)
//        objectInvokeElasticSearchDao.afterPropertiesSet()
//        objectInvokeElasticSearchDao.init()
//    }
//
//    def tenantId = "-9996"
//    def dcId = "-qrjuhg"
//
//    def "测试Es聚合"() {
//        when:
//        def startTime = System.currentTimeMillis() - 150L * 1000 * 3600 * 24
//        tenantId = "84801"
//        dcId = "780777150699143168"
//        startTime = System.currentTimeMillis() - 1L * 1000 * 3600 * 24
//
//        then:
//        def monitor = objectInvokeElasticSearchDao.getInvokeMonitor(tenantId, dcId, null, null, startTime, "10d", null, null, null, null, "count")
//
//        println JSON.toJSONString(monitor)
//
////        def result = GetInvokeMonitorResult.convert2ObjectKeyWithoutName(monitor)
////
////        println JSON.toJSONString(result)
//
//    }
//
//    def "添加测试数据"() {
//        expect:
//        def entity = new ObjectInvokeEntity(
//                tenantId: tenantId,
//                dcId: dcId,
//                invokeType: InvokeTypeEnum.ERP.getType(),
//        )
//        def random = new Random()
//        def statusLength = ObjectInvokeStatus.values().length
//        def actionLength = ActionEnum.values().length
////        for (j in 0..<5) {
//            IntStream.range(0, 1000).parallel()
//                    .forEach({ i ->
//                        entity.setObjectApiName("testObj" + (i % 3))
//                        entity.setAction(i % actionLength + 1)
//                        entity.setStatus(i % statusLength + 1)
//                        entity.setCost(random.nextInt(10000))
//                        entity.setCount(random.nextInt(10))
//                        entity.setCreateTime(System.currentTimeMillis() - i * 100L * 3600 * 24)
//                        objectInvokeElasticSearchDao.addOne(entity)
//                    })
////        }
//
//        objectInvokeElasticSearchDao.destroy()
//    }
//
//    def "测试错误统计数"() {
//        when:
//        tenantId = "84801"
//        dcId = "780777150699143168"
//
//        then:
////        println objectInvokeElasticSearchDao.getInvokeDistinctCount(tenantId, dcId, null, 1671658201054L, InvokeTypeEnum.INTEGRATION_STREAM, ObjectInvokeStatus.error);
////        println  objectInvokeElasticSearchDao.getInvokeDistinctCount(tenantId, dcId, null, 1671658201054L, InvokeTypeEnum.INTEGRATION_STREAM, ObjectInvokeStatus.blow);
//        println  objectInvokeElasticSearchDao.getInvokeDistinctCount(tenantId, dcId, null, 1671658201054L, InvokeTypeEnum.FUNC, ObjectInvokeStatus.error);
//    }
//
//    def "删除索引"() {
//        expect:
////        def index = "erpdss_invoke_monitor_" + DateFormatUtils.format(System.currentTimeMillis(), "yyyy_MM_dd")
////        def index = "erpdss_invoke_monitor_*"
//        def index = "erp_object_invoke_monitor"
//        searchService.getEsClient().indices().delete(new DeleteIndexRequest(index), RequestOptions.DEFAULT).isAcknowledged();
//    }
//
//    def "查询索引结构"() {
//        expect:
//        def request = new GetMappingsRequest().indices("erp_object_invoke_monitor")
//        def mappings = searchService.getEsClient().indices().getMapping(request, RequestOptions.DEFAULT).mappings()
//        println JSON.toJSONString(mappings)
//    }
//
//    def "测试创建过期文档-5.0后已不支持_ttl"() {
//        when:
//        def index = "test_expire_doc"
//        CreateIndexRequest req = new CreateIndexRequest(index);
//        req.mapping('{"_ttl":{ "enabled": true}}', XContentType.JSON);
//        Settings settings = Settings.builder()
//                .put("index.number_of_shards", 3)
//                .put("index.number_of_replicas", 2)
//                .build();
//        req.settings(settings);
//        searchService.getEsClient().indices().create(req, RequestOptions.DEFAULT).isAcknowledged();
//
////        def id = IdGenerator.get()
////        IndexRequest add = new IndexRequest(index).id(id).routing(tenantId).source(JSON.toJSONString(["createTime":System.currentTimeMillis()]), XContentType.JSON)
////                .timeout(new TimeValue(1000, TimeUnit.MILLISECONDS));
////        searchService.index(add).getId();
////
////        def get = searchService.get(index, id)
////        Thread.sleep(1000)
////        def get2 = searchService.get(index, id)
//
//        then:
//        thrown(ElasticsearchStatusException)
//    }
//}
