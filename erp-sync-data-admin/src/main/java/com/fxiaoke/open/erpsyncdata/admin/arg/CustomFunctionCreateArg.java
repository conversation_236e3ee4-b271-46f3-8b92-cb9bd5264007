package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel
@Data
public class CustomFunctionCreateArg implements Serializable {
    private static final long serialVersionUID = -3705276808928288128L;
    @ApiModelProperty("策略id")
    private String id;
    @ApiModelProperty("自定义函数APIName")
    private String customFuncApiName;
    /**{@link CustomFunctionTypeEnum}*/
    @ApiModelProperty("自定义函数阶段类型1.同步前 2.同步中 3同步后")
    private Integer customFuncType;
    @ApiModelProperty("绑定自定义函数的对象apiName")
    private String bindingObjectApiName;
}
