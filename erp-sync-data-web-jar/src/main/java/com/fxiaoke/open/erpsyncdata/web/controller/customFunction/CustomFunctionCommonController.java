package com.fxiaoke.open.erpsyncdata.web.controller.customFunction;

import com.fxiaoke.open.erpsyncdata.admin.annotation.CustomFunctionRateLimit;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerMethodProxy;
import com.fxiaoke.open.erpsyncdata.web.annontation.ControllerTenantIDArg;
import com.fxiaoke.open.erpsyncdata.web.factory.CustomFunctionFactory;
import com.fxiaoke.open.erpsyncdata.web.service.customFunction.CustomFunctionCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date: 15:20 2021/3/18
 * @Desc:
 */
@Slf4j
@Api(tags = "自定义函数公共接口")
@RestController("CustomFunctionCommonController")
@RequestMapping("inner/erp/syncdata/customfunction/common")
public class CustomFunctionCommonController {
    @Autowired
    private CustomFunctionFactory customFunctionFactory;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "自定义函数公共接口")
    @RequestMapping(value = "/execute",method = RequestMethod.POST)
    @ControllerMethodProxy
    @CustomFunctionRateLimit
    public Result<?> customFunctionExecuteLogic(@ControllerTenantIDArg @RequestHeader(value="x-fs-ei") Integer tenantId, @RequestHeader(value="x-fs-locale", required = false) String lang, @RequestBody CustomFunctionCommonArg arg) {
        if (arg == null||tenantId==null||arg.getType()==null||arg.getParams()==null) {
            log.info("tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        log.info("CustomFunctionCommonController.customFunctionExecuteLogic.tenantId={},lang={}",tenantId, lang);
        TraceUtil.setLocale(StringUtils.isNotEmpty(lang) ? lang : i18NStringManager.getDefaultLang(String.valueOf(tenantId)));
        arg.setTenantId(String.valueOf(tenantId));
        CustomFunctionCommonService customFunctionCommonService=customFunctionFactory.getHandlerByType(arg.getType());
        return customFunctionCommonService.executeLogic(arg);
    }
}
