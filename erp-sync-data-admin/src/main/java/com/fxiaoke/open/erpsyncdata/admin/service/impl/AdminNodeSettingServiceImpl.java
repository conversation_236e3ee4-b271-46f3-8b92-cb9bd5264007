package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCreateArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailUpdateFieldMappingsArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectFieldResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminNodeSettingService;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.FsCrmObjectService;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/18 16:46 节点的处理
 * @Version 1.0
 */
@Slf4j
@Service
public class AdminNodeSettingServiceImpl implements AdminNodeSettingService {

    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @ReloadableProperty("eip.type.mapping.white.list")
    private String typeMappingWhiteListJson;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private FsCrmObjectService fsCrmObjectService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<Void> updateFunction(String tenantId, CustomFunctionCreateArg arg) {
        if (!CustomFunctionTypeEnum.listAllTypes().contains(arg.getCustomFuncType())) {
            return Result.newError(ResultCodeEnum.CUSTOM_FUNC_APINAME_TYPE_NOT_EXIST);
        }
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateFuncApiName(arg.getId(), arg.getCustomFuncType(), arg.getCustomFuncApiName(), System.currentTimeMillis());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateFieldMapping(String tenantId, SyncPloyDetailUpdateFieldMappingsArg arg) {
        if (arg.getMasterObjectMapping() == null) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        if (CollectionUtils.isEmpty(arg.getMasterObjectMapping().getFieldMappings())) {
            return Result.newError(ResultCodeEnum.NOT_SET_FIELD);
        }
        if (ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(arg.getMasterObjectMapping().getDestObjectApiName()) ||
                ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(arg.getMasterObjectMapping().getDestObjectApiName())) {//针对部门人员对象的特殊逻辑
            //TODO 更换mappingType，如果前端做了这个逻辑就可以去掉了
            for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMappingArg : arg.getMasterObjectMapping().getFieldMappings()) {
                if (ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(fieldMappingArg.getDestTargetApiName()) ||
                        ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(fieldMappingArg.getDestTargetApiName())) {//针对查找关联部门人员的字段的mappingType做特殊处理
                    if (3 == fieldMappingArg.getMappingType()) {
                        continue;
                    }
                    fieldMappingArg.setMappingType(FieldMappingTypeEnum.DEPARTMENT_EMPLOYEE_FROM_MAPPING_TABLE.getType());
                }
            }
        }
        //处理主对象附件
        for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping : arg.getMasterObjectMapping().getFieldMappings()) {
            if (ErpFieldTypeEnum.isAttachmentType(fieldMapping.getDestType()) && ErpFieldTypeEnum.isAttachmentType(fieldMapping.getSourceType())) {
                fieldMapping.setMappingType(FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType());
            }
        }
        //处理从对象附件
        for (int i = 0; i < arg.getDetailObjectMappings().size(); i++) {
            for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping2 : arg.getDetailObjectMappings().get(i).getFieldMappings()) {
                if (ErpFieldTypeEnum.isAttachmentType(fieldMapping2.getDestType()) && ErpFieldTypeEnum.isAttachmentType(fieldMapping2.getSourceType())) {
                    fieldMapping2.setMappingType(FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType());
                }
            }
        }

        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, arg.getId());

        if (!syncPloyDetailEntity.getSourceObjectApiName().equals(arg.getMasterObjectMapping().getSourceObjectApiName()) || !syncPloyDetailEntity.getDestObjectApiName()
                .equals(arg.getMasterObjectMapping().getDestObjectApiName()) || syncPloyDetailEntity.getDetailObjectMappings().size() != arg.getDetailObjectMappings().size()) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }

        //校验该企业是否有权限在该策略下添加明细
        if (!syncPloyDetailEntity.getTenantId().equals(tenantId)) {
            return Result.newError(ResultCodeEnum.NOT_HAVE_DATA_AUTH);
        }
        if (!syncPloyDetailEntity.getDetailObjectMappings().isEmpty()) {
            for (SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg objectMappingArg : arg.getDetailObjectMappings()) {
                if (CollectionUtils.isEmpty(objectMappingArg.getFieldMappings())) {
                    return Result.newError(ResultCodeEnum.NOT_SET_FIELD);
                }
            }
        }

        Map<String, List<String>> typeMappingWhiteList = JSON.parseObject(typeMappingWhiteListJson, Map.class);
        //TODO 是否需要独立处理
        for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping : arg.getMasterObjectMapping().getFieldMappings()) {
            //如果主对象主从字段映射查找关联，改类型为查找关联
            if (ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getSourceType()) && ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getDestType())) {
                fieldMapping.setSourceType(ErpFieldTypeEnum.object_reference.name());
            }
            if (ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getSourceType()) && ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getDestType())) {
                fieldMapping.setDestType(ErpFieldTypeEnum.object_reference.name());
            }
            if (fieldMapping.getMappingType() != FieldMappingTypeEnum.SOUCRE_QUOTE_VAULE.getType()) {
                continue;
            }
            if (FieldType.EMPLOYEE.equals(fieldMapping.getSourceType()) && "mobile".equals(fieldMapping.getValue())) {
                if (!typeMappingWhiteList.containsKey(FieldType.PHONE_NUMBER) || !typeMappingWhiteList.get(FieldType.PHONE_NUMBER).contains(fieldMapping.getDestType())) {
                    log.info("unsupported type mapping, sourceFieldApiName : {}, destFieldApiName : {}, sourceType : {}, destType : {}", fieldMapping.getSourceApiName(), fieldMapping.getDestApiName(),
                            fieldMapping.getSourceType(), fieldMapping.getDestType());
                    return Result.newError(ResultCodeEnum.UNSUPPORTED_TYPE_MAPPING.getErrCode(),
                            "employee.mobile->" + fieldMapping.getDestApiName());
                }
            } else {
                if (!typeMappingWhiteList.containsKey(fieldMapping.getSourceType()) || !typeMappingWhiteList.get(fieldMapping.getSourceType()).contains(fieldMapping.getDestType())) {
                    log.info("unsupported type mapping, sourceFieldApiName : {}, destFieldApiName : {}, sourceType : {}, destType : {}", fieldMapping.getSourceApiName(), fieldMapping.getDestApiName(),
                            fieldMapping.getSourceType(), fieldMapping.getDestType());
                    return Result.newError(ResultCodeEnum.UNSUPPORTED_TYPE_MAPPING.getErrCode(),
                            ResultCodeEnum.UNSUPPORTED_TYPE_MAPPING.getErrMsg() + fieldMapping.getSourceApiName() + "->" + fieldMapping.getDestApiName());
                }
            }
        }

        FieldMappingsData fieldMappingsData = new FieldMappingsData();
        fieldMappingsData.addAll(BeanUtil2.deepCopyList(arg.getMasterObjectMapping().getFieldMappings(), FieldMappingData.class));
        DetailObjectMappingsData detailObjectMappingsData = new DetailObjectMappingsData();
        if (CollectionUtils.isNotEmpty(arg.getDetailObjectMappings())) {
            detailObjectMappingsData.addAll(BeanUtil2.deepCopyList(arg.getDetailObjectMappings(), DetailObjectMappingsData.DetailObjectMappingData.class));
        }

        SyncPloyDetailEntity entity = new SyncPloyDetailEntity();
        entity.setId(arg.getId());
        entity.setFieldMappings(fieldMappingsData);
        entity.setDetailObjectMappings(detailObjectMappingsData);
        entity.setUpdateTime(System.currentTimeMillis());
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
        return Result.newSuccess();
    }

    @Override
    public Result<String> updateIntegrationStreamNodes(String tenantId, UpdateIntegrationStreamArg arg) {
        SyncPloyDetailEntity entity = new SyncPloyDetailEntity();
        entity.setId(arg.getId());
        IntegrationStreamNodesData integrationStreamNodesData = new IntegrationStreamNodesData();
        entity.setIntegrationStreamNodes(integrationStreamNodesData);
        //回写组件
        integrationStreamNodesData.setReverseWriteNode(updateReverseStreamNode(arg));
        //通知组件
        integrationStreamNodesData.setNotifyComplementNode(updateNotifyStreamNode(arg));
        //重试组件
        integrationStreamNodesData.setReSyncErrorDataNode(updateReSyncErrorDataNode(arg));
        //操作中间表节点
        integrationStreamNodesData.setCheckSyncDataMappingNode(updateCheckSyncDataMappingNode(arg));
        //通过源数据查询crm节点
        integrationStreamNodesData.setQueryCrmObject2DestNodeBySource(updateQueryCrmObject2DestNodeBySourceNode(arg));
        //通过转换后数据查询crm节点
        integrationStreamNodesData.setQueryCrmObject2DestNodeByDest(updateQueryCrmObject2DestNodeByDestNode(arg));
        //数据范围-查询crm节点
        integrationStreamNodesData.setSyncConditionsQueryDataNode(updateSyncConditionsQueryDataNode(arg));
        //不更新字段apiName,主数据的这个信息，在字段映射存不了，所以就在这里存储了
        Map<String,List<String>> objApiName2NotUpdateFieldApiName= Maps.newHashMap();
        ObjectMappingResult fieldMappings=arg.getFieldMappingNode().getFieldMappings();
        if(fieldMappings!=null&&fieldMappings.getNotUpdateFieldApiNameList()!=null){
            objApiName2NotUpdateFieldApiName.put(fieldMappings.getDestObjectApiName(),fieldMappings.getNotUpdateFieldApiNameList());
        }
        List<ObjectMappingResult> detailObjectMappings =arg.getFieldMappingNode().getDetailObjectMappings();
        if(detailObjectMappings!=null){
            for(ObjectMappingResult objectMappingResult:detailObjectMappings){
                if(objectMappingResult.getNotUpdateFieldApiNameList()!=null){
                    objApiName2NotUpdateFieldApiName.put(objectMappingResult.getDestObjectApiName(),objectMappingResult.getNotUpdateFieldApiNameList());
                }
            }
        }
        integrationStreamNodesData.setObjApiName2NotUpdateFieldApiName(objApiName2NotUpdateFieldApiName);
        //需要返回字段ApiName
        if(arg.getSourceSystemNode()!=null&&arg.getSourceSystemNode().getObjApiName2NeedReturnFieldApiName()!=null){
            integrationStreamNodesData.setObjApiName2NeedReturnFieldApiName(arg.getSourceSystemNode().getObjApiName2NeedReturnFieldApiName());
        }

        entity.setIntegrationStreamNodes(integrationStreamNodesData);
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
        return Result.newSuccess();
    }

    @Override
    public Result<ImportExcelFile.Result> importFieldMapping(String tenantId,
                                                             String lang,
                                                             String dataCenterId,
                                                             String crmDataCenterId,
                                                             String integrationStreamId,
                                                             List<ErpIntegrationStreamExcelVo> integrationStreamExcelVos,
                                                             String direction,
                                                             Map<String, String> erpObjectMap,
                                                             Map<String, Map<String, String>> erpObjectFiledMap,
                                                             Map<String, String> thirdPartyObjectMapping) {
        ImportExcelFile.Result result = new ImportExcelFile.Result();
        List<ImportExcelFile.ErrorRow> invokeExceptionRows = new LinkedList<>();
        //crm：{对象：{字段：字段详情}}
        Map<String, Map<String, ObjectFieldResult>> crmObjectFiledDetail = new HashMap<>();
        Map<String, Set<String>> thirdPartyObjectFiledMapping = new HashMap<>();
        SyncPloyDetailUpdateFieldMappingsArg syncPloyDetailUpdateFieldMappingsArg = new SyncPloyDetailUpdateFieldMappingsArg();
        syncPloyDetailUpdateFieldMappingsArg.setId(integrationStreamId);
        List<SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg> objectMappings = new LinkedList<>();
        SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg objectMapping = new SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg();
        List<SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg> fieldMappings = new LinkedList<>();
        String masterObjectApiName = integrationStreamExcelVos.get(0).getThirdPartyObjectApiName();
        log.info("ErpObjectFieldServiceImpl.importObjectFiledApiNameData,tenantId={},masterObjectApiName={}", tenantId, masterObjectApiName);
        Set<String> objectApiNameSet = new HashSet<>();
        objectApiNameSet.add(masterObjectApiName);
        for (int i = 0; i < integrationStreamExcelVos.size(); i++) {
            log.info("ErpObjectFieldServiceImpl.importObjectFiledApiNameData,tenantId={},integrationStreamExcelVo={}", tenantId, integrationStreamExcelVos.get(i));
            //组装数据的同时，校验是否符合
            //对象或者字段不在库里，提示错误位置
            try {
                result.incrInvoke(1);
                if (!thirdPartyObjectMapping.containsKey(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName())) {
                    //对象不存在
                    ImportExcelFile.ErrorRow errorRow = new ImportExcelFile.ErrorRow();
                    errorRow.setRowNo(i + 4);
                    errorRow.setErrMsg(String.format(i18NStringManager.get(I18NStringEnum.s4031,
                            lang,
                            tenantId),
                            integrationStreamExcelVos.get(i).getThirdPartyObjectApiName(),
                            erpObjectMap.get(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName())));
                    invokeExceptionRows.add(errorRow);
                    if (i == integrationStreamExcelVos.size() - 1) {
                        dealSyncPloyDetailUpdateFieldMappings(direction,
                                objectMapping,
                                integrationStreamExcelVos.get(i),
                                thirdPartyObjectMapping,
                                masterObjectApiName,
                                fieldMappings,
                                syncPloyDetailUpdateFieldMappingsArg,
                                objectMappings,
                                invokeExceptionRows,
                                result);
                    }
                    continue;
                }

                if (!objectApiNameSet.contains(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName())) {
                    objectApiNameSet.add(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName());
                    if (direction.equals(ErpChannelEnum.CRM.name())) {
                        //crm是源对象
                        objectMapping.setSourceObjectApiName(integrationStreamExcelVos.get(i - 1).getCrmObjectApiName());
                        //erp对象的是 中间对象
                        objectMapping.setDestObjectApiName(thirdPartyObjectMapping.get(integrationStreamExcelVos.get(i - 1).getThirdPartyObjectApiName()));
                    } else {
                        //erp对象的是 中间对象
                        objectMapping.setSourceObjectApiName(thirdPartyObjectMapping.get(integrationStreamExcelVos.get(i - 1).getThirdPartyObjectApiName()));
                        //crm是源对象
                        objectMapping.setDestObjectApiName(integrationStreamExcelVos.get(i - 1).getCrmObjectApiName());
                    }

                    //初始化
                    if (masterObjectApiName.equals(integrationStreamExcelVos.get(i - 1).getThirdPartyObjectApiName())) {
                        //主对象
                        objectMapping.setFieldMappings(fieldMappings);
                        syncPloyDetailUpdateFieldMappingsArg.setMasterObjectMapping(objectMapping);
                    } else {
                        //从对象
                        objectMapping.setFieldMappings(fieldMappings);
                        objectMappings.add(objectMapping);
                    }
                    //清空上一次的对象
                    objectMapping = new SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg();
                    fieldMappings = new LinkedList<>();
                }

                if (!thirdPartyObjectFiledMapping.containsKey(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName())) {
                    //批量查询字段
                    ErpObjectFieldEntity query = new ErpObjectFieldEntity();
                    query.setTenantId(tenantId);
                    query.setDataCenterId(dataCenterId);
                    query.setErpObjectApiName(thirdPartyObjectMapping.get(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName()));
                    List<ErpObjectFieldEntity> entryByFieldApiNames = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
                    thirdPartyObjectFiledMapping.put(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName(), entryByFieldApiNames.stream().map(ErpObjectFieldEntity::getFieldApiName).collect(Collectors.toSet()));
                }

                if (!thirdPartyObjectFiledMapping.get(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName()).contains(integrationStreamExcelVos.get(i).getThirdPartyFieldApiName())) {
                    //字段不存在
                    ImportExcelFile.ErrorRow errorRow = new ImportExcelFile.ErrorRow();
                    errorRow.setRowNo(i + 4);
                    errorRow.setErrMsg(String.format(i18NStringManager.get(I18NStringEnum.s4032,
                            lang,
                            tenantId),
                            integrationStreamExcelVos.get(i).getThirdPartyObjectApiName(),
                            integrationStreamExcelVos.get(i).getThirdPartyFieldApiName(),
                            erpObjectFiledMap.get(integrationStreamExcelVos.get(i).getThirdPartyObjectApiName()).get(integrationStreamExcelVos.get(i).getThirdPartyFieldApiName())));
                    invokeExceptionRows.add(errorRow);
                    if (i == integrationStreamExcelVos.size() - 1) {
                        dealSyncPloyDetailUpdateFieldMappings(direction,
                                objectMapping,
                                integrationStreamExcelVos.get(i),
                                thirdPartyObjectMapping,
                                masterObjectApiName,
                                fieldMappings,
                                syncPloyDetailUpdateFieldMappingsArg,
                                objectMappings,
                                invokeExceptionRows,
                                result);
                    }
                    continue;
                }

                if (!crmObjectFiledDetail.containsKey(integrationStreamExcelVos.get(i).getCrmObjectApiName())) {
                    //crm对象的字段还缺少字段类型，可以查出来
                    Result<ListObjectFieldsResult> listResult = fsCrmObjectService.listObjectFieldsWithFilterBlackList(tenantId, integrationStreamExcelVos.get(i).getCrmObjectApiName(), lang);
                    if (!listResult.isSuccess() || ObjectUtils.isEmpty(listResult.getData())) {
                        log.info("ErpObjectFieldServiceImpl.importObjectFiledApiNameData,tenantId={},listResult={}", tenantId, listResult);
                        //对象不存在
                        crmObjectFiledDetail.put(integrationStreamExcelVos.get(i).getCrmObjectApiName(), new HashMap<>());
                    } else {
                        crmObjectFiledDetail.put(integrationStreamExcelVos.get(i).getCrmObjectApiName(), listResult.getData().getFields().stream()
                                .collect(Collectors.toMap(
                                        ObjectFieldResult::getApiName,
                                        item -> item,
                                        (existing, replacement) -> existing)));
                    }
                }

                if (!crmObjectFiledDetail.get(integrationStreamExcelVos.get(i).getCrmObjectApiName()).containsKey(integrationStreamExcelVos.get(i).getCrmFileApiName())) {
                    //crm字段不存在
                    ImportExcelFile.ErrorRow errorRow = new ImportExcelFile.ErrorRow();
                    errorRow.setRowNo(i + 4);
                    errorRow.setErrMsg(String.format(i18NStringManager.get(I18NStringEnum.s4032,
                            lang,
                            tenantId),
                            integrationStreamExcelVos.get(i).getCrmObjectApiName(),
                            integrationStreamExcelVos.get(i).getCrmFileApiName(),
                            i18NStringManager.get(I18NStringEnum.s1,
                                    lang,
                                    tenantId)));
                    invokeExceptionRows.add(errorRow);
                    if (i == integrationStreamExcelVos.size() - 1) {
                        dealSyncPloyDetailUpdateFieldMappings(direction,
                                objectMapping,
                                integrationStreamExcelVos.get(i),
                                thirdPartyObjectMapping,
                                masterObjectApiName,
                                fieldMappings,
                                syncPloyDetailUpdateFieldMappingsArg,
                                objectMappings,
                                invokeExceptionRows,
                                result);
                    }
                    continue;
                }

                //组装数据
                //对象处理
                SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMappingArg = new SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg();
                //字段就没有中间字段说法
                if (direction.equals(ErpChannelEnum.CRM.name())) {
                    fieldMappingArg.setSourceApiName(integrationStreamExcelVos.get(i).getCrmFileApiName());
                    fieldMappingArg.setSourceType(crmObjectFiledDetail.get(integrationStreamExcelVos.get(i).getCrmObjectApiName()).get(integrationStreamExcelVos.get(i).getCrmFileApiName()).getType());
                    fieldMappingArg.setDestApiName(integrationStreamExcelVos.get(i).getThirdPartyFieldApiName());
                    fieldMappingArg.setDestType(integrationStreamExcelVos.get(i).getThirdPartyFieldType());
                } else {
                    fieldMappingArg.setSourceApiName(integrationStreamExcelVos.get(i).getThirdPartyFieldApiName());
                    fieldMappingArg.setSourceType(integrationStreamExcelVos.get(i).getThirdPartyFieldType());
                    fieldMappingArg.setDestApiName(integrationStreamExcelVos.get(i).getCrmFileApiName());
                    fieldMappingArg.setDestType(crmObjectFiledDetail.get(integrationStreamExcelVos.get(i).getCrmObjectApiName()).get(integrationStreamExcelVos.get(i).getCrmFileApiName()).getType());
                }
                fieldMappingArg.setMappingType(FieldMappingTypeEnum.SOURCE_VALUE.getType());
                fieldMappings.add(fieldMappingArg);

                if (i == integrationStreamExcelVos.size() - 1) {
                    dealSyncPloyDetailUpdateFieldMappings(direction,
                            objectMapping,
                            integrationStreamExcelVos.get(i),
                            thirdPartyObjectMapping,
                            masterObjectApiName,
                            fieldMappings,
                            syncPloyDetailUpdateFieldMappingsArg,
                            objectMappings,
                            invokeExceptionRows,
                            result);
                }
                result.incrInsert(1);
            } catch (Exception e) {
                log.error("ErpObjectFieldServiceImpl.importObjectFiledApiNameData,tenantId={}", tenantId, e);
                ImportExcelFile.ErrorRow errorRow = new ImportExcelFile.ErrorRow();
                errorRow.setRowNo(i + 4);
                errorRow.setErrMsg(i18NStringManager.get(I18NStringEnum.s36,
                        lang,
                        tenantId));
                invokeExceptionRows.add(errorRow);
            }
        }

        //转换逻辑，看代码是没有失败
        if (CollectionUtils.isEmpty(syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getFieldMappings())) {
            return Result.newSuccess(result);
        }

        importObjectFieldMapping(tenantId, syncPloyDetailUpdateFieldMappingsArg);

        return Result.newSuccess(result);
    }

    private void dealSyncPloyDetailUpdateFieldMappings(String direction,
                                                       SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg objectMapping,
                                                       ErpIntegrationStreamExcelVo erpIntegrationStreamExcelVo,
                                                       Map<String, String> thirdPartyObjectMapping,
                                                       String masterObjectApiName,
                                                       List<SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg> fieldMappings,
                                                       SyncPloyDetailUpdateFieldMappingsArg syncPloyDetailUpdateFieldMappingsArg,
                                                       List<SyncPloyDetailUpdateFieldMappingsArg.ObjectMappingArg> objectMappings,
                                                       List<ImportExcelFile.ErrorRow> invokeExceptionRows,
                                                       ImportExcelFile.Result result) {
        //最后一次处理
        if (direction.equals(ErpChannelEnum.CRM.name())) {
            //crm是源对象
            objectMapping.setSourceObjectApiName(erpIntegrationStreamExcelVo.getCrmObjectApiName());
            //erp对象的是 中间对象
            objectMapping.setDestObjectApiName(thirdPartyObjectMapping.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
        } else {
            //erp对象的是 中间对象
            objectMapping.setSourceObjectApiName(thirdPartyObjectMapping.get(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
            //crm是源对象
            objectMapping.setDestObjectApiName(erpIntegrationStreamExcelVo.getCrmObjectApiName());
        }

        if (masterObjectApiName.equals(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName())) {
            //主对象
            objectMapping.setFieldMappings(fieldMappings);
            syncPloyDetailUpdateFieldMappingsArg.setMasterObjectMapping(objectMapping);
        } else {
            //从对象
            objectMapping.setFieldMappings(fieldMappings);
            objectMappings.add(objectMapping);
            syncPloyDetailUpdateFieldMappingsArg.setDetailObjectMappings(objectMappings);
        }
        result.setInvokeExceptionRows(invokeExceptionRows);
    }

    private void importObjectFieldMapping(String tenantId, SyncPloyDetailUpdateFieldMappingsArg syncPloyDetailUpdateFieldMappingsArg) {
        if (ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getDestObjectApiName()) ||
                ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getDestObjectApiName())) {//针对部门人员对象的特殊逻辑
            //TODO 更换mappingType，如果前端做了这个逻辑就可以去掉了
            for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMappingArg : syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getFieldMappings()) {
                if (ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName().equals(fieldMappingArg.getDestTargetApiName()) ||
                        ObjectApiNameEnum.FS_PERSONNEL_OBJ.getObjApiName().equals(fieldMappingArg.getDestTargetApiName())) {//针对查找关联部门人员的字段的mappingType做特殊处理
                    if (3 == fieldMappingArg.getMappingType()) {
                        continue;
                    }
                    fieldMappingArg.setMappingType(FieldMappingTypeEnum.DEPARTMENT_EMPLOYEE_FROM_MAPPING_TABLE.getType());
                }
            }
        }
        //处理主对象附件
        for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping : syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getFieldMappings()) {
            if (ErpFieldTypeEnum.isAttachmentType(fieldMapping.getDestType()) && ErpFieldTypeEnum.isAttachmentType(fieldMapping.getSourceType())) {
                fieldMapping.setMappingType(FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType());
            }
        }
        //处理从对象附件
        if (CollectionUtils.isNotEmpty(syncPloyDetailUpdateFieldMappingsArg.getDetailObjectMappings())) {
            for (int i = 0; i < syncPloyDetailUpdateFieldMappingsArg.getDetailObjectMappings().size(); i++) {
                if (CollectionUtils.isNotEmpty(syncPloyDetailUpdateFieldMappingsArg.getDetailObjectMappings().get(i).getFieldMappings())) {
                    for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping2 : syncPloyDetailUpdateFieldMappingsArg.getDetailObjectMappings().get(i).getFieldMappings()) {
                        if (ErpFieldTypeEnum.isAttachmentType(fieldMapping2.getDestType()) && ErpFieldTypeEnum.isAttachmentType(fieldMapping2.getSourceType())) {
                            fieldMapping2.setMappingType(FieldMappingTypeEnum.ATTACHMENT_MAPPING_TABLE.getType());
                        }
                    }
                }
            }
        }

        for (SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg fieldMapping : syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getFieldMappings()) {
            //如果主对象主从字段映射查找关联，改类型为查找关联
            if (ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getSourceType()) && ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getDestType())) {
                fieldMapping.setSourceType(ErpFieldTypeEnum.object_reference.name());
            }
            if (ErpFieldTypeEnum.object_reference.name().equals(fieldMapping.getSourceType()) && ErpFieldTypeEnum.master_detail.name().equals(fieldMapping.getDestType())) {
                fieldMapping.setDestType(ErpFieldTypeEnum.object_reference.name());
            }
        }

        FieldMappingsData fieldMappingsData = new FieldMappingsData();
        fieldMappingsData.addAll(BeanUtil2.deepCopyList(syncPloyDetailUpdateFieldMappingsArg.getMasterObjectMapping().getFieldMappings(), FieldMappingData.class));
        DetailObjectMappingsData detailObjectMappingsData = new DetailObjectMappingsData();
        if (CollectionUtils.isNotEmpty(syncPloyDetailUpdateFieldMappingsArg.getDetailObjectMappings())) {
            detailObjectMappingsData.addAll(BeanUtil2.deepCopyList(syncPloyDetailUpdateFieldMappingsArg.getDetailObjectMappings(), DetailObjectMappingsData.DetailObjectMappingData.class));
        }

        SyncPloyDetailEntity entity = new SyncPloyDetailEntity();
        entity.setId(syncPloyDetailUpdateFieldMappingsArg.getId());
        entity.setFieldMappings(fieldMappingsData);
        entity.setDetailObjectMappings(detailObjectMappingsData);
        entity.setUpdateTime(System.currentTimeMillis());
        adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateByIdSelective(entity);
    }

    private IntegrationStreamNodesData.ReSyncErrorDataNode updateReSyncErrorDataNode(UpdateIntegrationStreamArg arg) {
        if (arg.getReSyncErrorDataNode() == null || (arg.getReSyncErrorDataNode().getReSyncTimeInterval() == null && arg.getReSyncErrorDataNode().getReSyncRightNow() == null)
                || arg.getReSyncErrorDataNode().getReSyncTopLimit() == null) {
            return null;
        }
        IntegrationStreamNodesData.ReSyncErrorDataNode node=
                JacksonUtil.fromJson(JacksonUtil.toJson(arg.getReSyncErrorDataNode()), IntegrationStreamNodesData.ReSyncErrorDataNode.class);
        return node;
    }

    private IntegrationStreamNodesData.SyncConditionsQueryDataNode updateSyncConditionsQueryDataNode(UpdateIntegrationStreamArg arg) {
        if (arg.getSyncConditionsNode() == null||arg.getSyncConditionsNode().getSyncConditions()==null
                ||arg.getSyncConditionsNode().getSyncConditions().getSyncConditionsQueryDataNode()==null) {
            return null;
        }
        //数据范围-查询crm节点
        IntegrationStreamNodesData.SyncConditionsQueryDataNode node =
                JacksonUtil.fromJson(JacksonUtil.toJson(arg.getSyncConditionsNode().getSyncConditions().getSyncConditionsQueryDataNode()), IntegrationStreamNodesData.SyncConditionsQueryDataNode.class);
        return node;
    }

    private IntegrationStreamNodesData.QueryCrmObject2DestNode updateQueryCrmObject2DestNodeBySourceNode(UpdateIntegrationStreamArg arg) {
        if (arg.getQueryCrmObject2DestNodeBySource() == null) {
            return null;
        }
        //查询crm节点
        IntegrationStreamNodesData.QueryCrmObject2DestNode node =
                JacksonUtil.fromJson(JacksonUtil.toJson(arg.getQueryCrmObject2DestNodeBySource()), IntegrationStreamNodesData.QueryCrmObject2DestNode.class);
        return node;
    }

    private IntegrationStreamNodesData.QueryCrmObject2DestNode updateQueryCrmObject2DestNodeByDestNode(UpdateIntegrationStreamArg arg) {
        if (arg.getQueryCrmObject2DestNodeByDest() == null) {
            return null;
        }
        //查询crm节点
        IntegrationStreamNodesData.QueryCrmObject2DestNode node =
                JacksonUtil.fromJson(JacksonUtil.toJson(arg.getQueryCrmObject2DestNodeByDest()), IntegrationStreamNodesData.QueryCrmObject2DestNode.class);
        return node;
    }

    public IntegrationStreamNodesData.CheckSyncDataMappingNode updateCheckSyncDataMappingNode(UpdateIntegrationStreamArg arg) {
        if (arg.getCheckSyncDataMappingNode() == null) {
            return null;
        }
        //操作中间表节点
        IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode =
                JacksonUtil.fromJson(JacksonUtil.toJson(arg.getCheckSyncDataMappingNode()), IntegrationStreamNodesData.CheckSyncDataMappingNode.class);

        return checkSyncDataMappingNode;
    }

    private IntegrationStreamNodesData.ReverseWriteNode updateReverseStreamNode(UpdateIntegrationStreamArg arg) {
        if (arg.getReverseWriteNode() == null) {
            return null;
        }
        //反写crm节点
        IntegrationStreamNodesData.ReverseWriteNode reverseWriteNode = new IntegrationStreamNodesData.ReverseWriteNode();
        if (arg.getReverseWriteNode().getFieldMappings() != null && arg.getReverseWriteNode().getFieldMappings().getFieldMappings() != null) {
            FieldMappingsData fieldMappingsData = new FieldMappingsData();
            arg.getReverseWriteNode().getFieldMappings().getFieldMappings().stream().forEach(fieldMappingResult -> fieldMappingsData.add(BeanUtil.copy(fieldMappingResult, FieldMappingData.class)));
            reverseWriteNode.setFieldMappings(fieldMappingsData);
            reverseWriteNode.setSourceObjectApiName(arg.getReverseWriteNode().getFieldMappings().getSourceObjectApiName());
            reverseWriteNode.setDestObjectApiName(arg.getReverseWriteNode().getFieldMappings().getDestObjectApiName());
        }
        if (arg.getReverseWriteNode().getDetailObjectMappings() != null) {
            DetailObjectMappingsData detailObjectMappings = new DetailObjectMappingsData();
            arg.getReverseWriteNode().getDetailObjectMappings().stream().forEach(objectMappingResult -> detailObjectMappings.add(BeanUtil.copy(objectMappingResult, DetailObjectMappingsData.DetailObjectMappingData.class)));
            reverseWriteNode.setDetailObjectMappings(detailObjectMappings);
        }
        return reverseWriteNode;
    }


    private IntegrationStreamNodesData.NotifyComplementNode updateNotifyStreamNode(UpdateIntegrationStreamArg arg) {
        if (ObjectUtils.isEmpty(arg.getNotifyComplementNode())) {
            return null;
        }
        IntegrationStreamNodesData.NotifyComplementNode notifyComplementNode = BeanUtil.copy(arg.getNotifyComplementNode(), IntegrationStreamNodesData.NotifyComplementNode.class);

        if (CollectionUtils.isNotEmpty(arg.getNotifyComplementNode().getNotifyConditionFilters())) {
            notifyComplementNode.setNotifyConditionAviator(arg.getNotifyComplementNode().generateExpression());
        }
        return notifyComplementNode;
    }

}
