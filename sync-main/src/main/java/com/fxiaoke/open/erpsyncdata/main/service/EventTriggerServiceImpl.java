package com.fxiaoke.open.erpsyncdata.main.service;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.retry.AsyncReTryIfFailedManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BomUtils;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg.EventData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.EventTriggerService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EventTriggerServiceImpl implements EventTriggerService {
    @Autowired
    private SyncMainService syncMainService;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private AsyncReTryIfFailedManager asyncReTryIfFailedManager;


    @Override
    public Result2<Void> batchSendEventData2DispatcherMq(BatchSendEventDataArg arg) {
        for (EventData eventData : arg.getEventDatas()) {
            if(StringUtils.isEmpty(eventData.getSyncLogId())){
                String initLogId = syncLogManager.getInitLogId(eventData.getSourceData().getTenantId(), eventData.getSourceData().getApiName());
                String finalLogId = LogIdUtil.buildChildLogId(initLogId, 0);
                //log.info("batchSendEventData2DispatcherMq logId:{},ei:{}.dataid:{}",finalLogId,eventData.getSourceData().getTenantId(),eventData.getSourceData().getId());
                eventData.setSyncLogId(finalLogId);
            }
            if(StringUtils.equalsIgnoreCase(eventData.getSourceData().getApiName(), ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName())) {
                ObjectData objectData = outerServiceFactory.get(eventData.getSourceTenantType())
                        .getObjectData(eventData.getSourceData().getTenantId(), eventData.getSourceTenantType(),
                                eventData.getSourceData().getApiName(), eventData.getSourceData().getId()).getData();
                log.info("EventTriggerServiceImpl.batchSendEventData2DispatcherMq,objectData={}", JSONObject.toJSONString(objectData));
                String root_id = objectData.getString("root_id");
                String bom_id = objectData.getString("bom_id");
                if(!StringUtils.equalsIgnoreCase(root_id,bom_id)) {
                    eventData.setDataVersion(null);//设置为空，后续通过这个判断不发送节点
                    //丢掉非CPQ父产品对应的数据
                    log.info("EventTriggerServiceImpl.batchSendEventData2DispatcherMq,root_id!=bom_id={}", root_id);
                    continue;
                }
                objectData.putId(BomUtils.getSrcDataId(objectData));
                eventData.setSourceData(objectData);
                log.info("EventTriggerServiceImpl.batchSendEventData2DispatcherMq,eventData={}", JSONObject.toJSONString(eventData));
            }
            SyncDataContextEvent syncDataContextEvent = SyncDataContextUtils.convertEventByBatchSendEventDataArg(eventData);
            Result2<String> result = syncMainService.sendEventData2DispatcherMq(syncDataContextEvent);
            log.info("trace sendEventData2DispatcherMq ei:{}.dataid:{}, mq msgid:{}",eventData.getSourceData().getTenantId(),eventData.getSourceData().getId(), result.getData());
        }
        return Result2.newSuccess();
    }

    @LogLevel(LogLevelEnum.TRACE)
    @Override
    public Result2<Void> batchSendEventData2DispatcherMqByContext(List<SyncDataContextEvent> syncDataContextEvents) {
        Boolean isSendFailed=false;
        for (SyncDataContextEvent eventData : syncDataContextEvents) {
            if(StringUtils.isEmpty(eventData.getSyncLogId())){
                String initLogId = syncLogManager.getInitLogId(eventData.getSourceData().getTenantId(), eventData.getSourceData().getApiName());
                String finalLogId = LogIdUtil.buildChildLogId(initLogId, 0);
                //log.info("batchSendEventData2DispatcherMq logId:{},ei:{}.dataid:{}",finalLogId,eventData.getSourceData().getTenantId(),eventData.getSourceData().getId());
                eventData.setSyncLogId(finalLogId);
            }
            if(ObjectUtils.isEmpty(eventData.getDataReceiveType())){
                eventData.setDataReceiveType(DataReceiveTypeEnum.OTHER.getType());
            }
            if(StringUtils.equalsIgnoreCase(eventData.getSourceData().getApiName(), ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName())) {
                ObjectData objectData = outerServiceFactory.get(eventData.getSourceTenantType())
                        .getObjectData(eventData.getSourceData().getTenantId(), eventData.getSourceTenantType(),
                                eventData.getSourceData().getApiName(), eventData.getSourceData().getId()).getData();
                log.info("EventTriggerServiceImpl.batchSendEventData2DispatcherMq,objectData={}", JSONObject.toJSONString(objectData));
                String root_id = objectData.getString("root_id");
                String bom_id = objectData.getString("bom_id");
                if(!StringUtils.equalsIgnoreCase(root_id,bom_id)) {
                    eventData.setDataVersion(null);//设置为空，后续通过这个判断不发送节点
                    //丢掉非CPQ父产品对应的数据
                    log.info("EventTriggerServiceImpl.batchSendEventData2DispatcherMq,root_id!=bom_id={}", root_id);
                    continue;
                }
                objectData.putId(BomUtils.getSrcDataId(objectData));
                eventData.setSourceData(objectData);
                log.info("EventTriggerServiceImpl.batchSendEventData2DispatcherMq,eventData={}", JSONObject.toJSONString(eventData));
            }
            Result2<String> result = syncMainService.sendEventData2DispatcherMq(eventData);
            log.info("trace sendEventData2DispatcherMq ei:{}.dataid:{}, mq msgid:{}",eventData.getSourceData().getTenantId(),eventData.getSourceData().getId(), result.getData());
            if(!result.isSuccess()){
                isSendFailed=true;
                //上报bizlog 记录发送失败
                asyncReTryIfFailedManager.reTryBatchSendEventData2DispatcherMqByContext(Lists.newArrayList(eventData), result.getErrMsg());

            }
        }
        if(isSendFailed){//有失败的都返回错误结果，待重试
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        return Result2.newSuccess();
    }
}
