package com.fxiaoke.open.erpsyncdata.probe.handler;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023-08-10
 */
@Ignore
public class PgBackupHandlerTest extends BaseTest {
    @Autowired
    private PgBackupHandler pgBackupHandler;

    @Test
    public void testBackup() throws Exception {
        ReturnT<String> execute = pgBackupHandler.execute(new TriggerParam());

    }
}