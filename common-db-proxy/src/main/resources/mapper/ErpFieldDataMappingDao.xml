<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao">
    <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity">
        <!--@mbg.generated-->
        <!--@Table erp_field_data_mapping-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="data_center_id" jdbcType="VARCHAR" property="dataCenterId"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
        <result column="data_type" jdbcType="VARCHAR" property="dataType"/>
        <result column="fs_data_id" jdbcType="VARCHAR" property="fsDataId"/>
        <result column="fs_data_name" jdbcType="VARCHAR" property="fsDataName"/>
        <result column="erp_data_id" jdbcType="VARCHAR" property="erpDataId"/>
        <result column="erp_data_name" jdbcType="VARCHAR" property="erpDataName"/>
        <result column="field_data_extend_value" jdbcType="LONGVARCHAR" property="fieldDataExtendValue"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_id,data_center_id, channel, data_type, fs_data_id, fs_data_name, erp_data_id,
        erp_data_name,field_data_extend_value,
        create_time, update_time
    </sql>

    <!--auto generated by MybatisCodeHelper on 2020-08-31-->
    <delete id="deleteByType">
        delete
        from erp_field_data_mapping
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and channel = #{channel,jdbcType=VARCHAR}
          and data_type = #{dataType,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByDataType">
        delete
        from erp_field_data_mapping
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and data_center_id = #{dataCenterId}
          and data_type = #{dataType,jdbcType=VARCHAR}
    </delete>


    <select id="findByDataId" resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity">
        select *
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
            and data_center_id = #{dataCenterId}
            and data_type = #{dataType,jdbcType=VARCHAR}
            and (fs_data_id = #{fsDataId,jdbcType=VARCHAR} or erp_data_id = #{erpDataId,jdbcType=VARCHAR})
        </where>
    </select>

    <select id="countByTenantIdAndDataType" resultType="java.lang.Integer">
        select count(id)
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and data_type = #{dataType}
        <if test="queryStr != null and queryStr!=''">
            and ( erp_data_id like #{queryStr} or erp_data_name like #{queryStr} or fs_data_id like #{queryStr})
        </if>
    </select>

    <select id="listByTenantIdAndDataType"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity">
        select *
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and data_type = #{dataType}
        <if test="queryStr != null and queryStr!=''">
            and ( erp_data_id like #{queryStr} or erp_data_name like #{queryStr} or fs_data_id like #{queryStr})
        </if>
        order by update_time desc
        offset #{offset} limit #{limit}
    </select>

    <select id="countByTenantIdAndDataTypeAndDcId" resultType="java.lang.Integer">
        select count(id)
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and data_center_id = #{dataCenterId}
                  and data_type = #{dataType}
        <if test="isBind != null">
            <choose>
                <when test="isBind">
                    and fs_data_id IS NOT NULL
                    and fs_data_id != ''
                </when>
                <otherwise>
                    and (fs_data_id IS NULL or fs_data_id = '')
                </otherwise>
            </choose>
        </if>
        <if test="queryStr != null and queryStr != ''">
            and (
            fs_data_name like CONCAT('%', #{queryStr}, '%')
            or erp_data_name like CONCAT('%', #{queryStr}, '%')
            or erp_data_id like CONCAT('%', #{queryStr}, '%')
            or fs_data_id = #{queryStr}
            )
        </if>
    </select>

    <select id="listByTenantIdAndDataTypeAndDcId"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity">
        select *
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and data_center_id = #{dataCenterId}
                  and data_type = #{dataType}
        <if test="isBind != null">
            <choose>
                <when test="isBind">
                    and fs_data_id IS NOT NULL
                    and fs_data_id != ''
                </when>
                <otherwise>
                    and (fs_data_id IS NULL or fs_data_id = '')
                </otherwise>
            </choose>
        </if>
        <if test="queryStr != null and queryStr != ''">
            and (
            fs_data_name like CONCAT('%', #{queryStr}, '%')
            or erp_data_name like CONCAT('%', #{queryStr}, '%')
            or erp_data_id like CONCAT('%', #{queryStr}, '%')
            or fs_data_id = #{queryStr}
            )
        </if>
        order by update_time desc
        offset #{offset} limit #{limit}
    </select>

    <select id="countByTenantIdAndDataTypeAndQueryStr" resultType="java.lang.Integer">
        select count(id)
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and data_center_id = #{dataCenterId}
                  and data_type = #{dataType}
        <if test="queryStr != null and queryStr != ''">
            and (fs_data_id like CONCAT('%', #{queryStr}, '%') or fs_data_name like CONCAT('%', #{queryStr}, '%') or
                 erp_data_id like CONCAT('%', #{queryStr}, '%') or erp_data_name like CONCAT('%', #{queryStr}, '%'))
        </if>
    </select>

    <select id="listByTenantIdAndDataTypeAndQueryStr"
            resultType="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity">
        select *
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and data_center_id = #{dataCenterId}
                  and data_type = #{dataType}
        <if test="queryStr != null and queryStr != ''">
            and (fs_data_id like CONCAT('%', #{queryStr}, '%') or fs_data_name like CONCAT('%', #{queryStr}, '%') or
                 erp_data_id like CONCAT('%', #{queryStr}, '%') or erp_data_name like CONCAT('%', #{queryStr}, '%'))
        </if>
        order by update_time desc
        offset #{offset} limit #{limit}
    </select>

    <select id="queryOAUserCodeList" resultType="String">
        select erp_data_id
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
                    and data_type = 'employee_oa'
                    and
                    fs_data_id in
            <foreach item="item" collection="receiverIds" separator="," open="(" close=")" index="">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </select>

    <select id="queryOAUserCode" resultType="String">
        select erp_data_id
        from erp_field_data_mapping
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and data_type = 'employee_oa'
          and fs_data_id = #{receiverId}
    </select>

    <select id="queryFxUserCode" resultType="String">
        select fs_data_id
        from erp_field_data_mapping
        where tenant_id = #{tenantId,jdbcType=VARCHAR}
          and data_type = 'employee_oa'
          and erp_data_id = #{receiverId}
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-04-08-->
    <select id="listNoSearch" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
                    and data_type = #{dataType,jdbcType=VARCHAR}
            <if test="fsDataId != null">
                and fs_data_id = #{fsDataId,jdbcType=VARCHAR}
            </if>
            <if test="erpDataId != null">
                and erp_data_id = #{erpDataId,jdbcType=VARCHAR}
            </if>
            <if test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listNoSearch2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
            and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            and data_type = #{dataType,jdbcType=VARCHAR}
            <if test="fsDataId != null">
                and fs_data_id = #{fsDataId,jdbcType=VARCHAR}
            </if>
            <if test="erpDataId != null">
                and erp_data_id = #{erpDataId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listByFsIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
            and data_type = #{dataType,jdbcType=VARCHAR}
            <if test="fsDataIds != null">
                and fs_data_id in
                <foreach item="item" index="index" collection="fsDataIds"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listByIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
            <if test="idList != null">
                and id in
                <foreach item="item" index="index" collection="idList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listByErpIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
            and data_type = #{dataType,jdbcType=VARCHAR}
            <if test="erpDataIds != null">
                and erp_data_id in
                <foreach item="item" index="index" collection="erpDataIds"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="dataCenterId != null">
                and data_center_id = #{dataCenterId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2021-04-12-->
    <delete id="batchDeleteByIds">
        delete
        from erp_field_data_mapping
                where tenant_id = #{tenantId,jdbcType=VARCHAR}
                  and id in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteByTenantId">
        delete
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
        </where>
    </delete>
    <select id="getTenantIdListByChannel" resultType="string">
        select
        distinct tenant_id
        from erp_field_data_mapping
        <where>
            channel = #{channel,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="getDataListByChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from erp_field_data_mapping
        <where>
            tenant_id = #{tenantId,jdbcType=VARCHAR}
            and channel = #{channel,jdbcType=VARCHAR}
            and data_type = #{dataType,jdbcType=VARCHAR}
        </where>
    </select>
</mapper>