package com.fxiaoke.open.oasyncdata.db.model;

import lombok.Data;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/29
 */
@Data
public class K3SourceBillInfo {
    /**
     * 单据id
     */
    private String formId;

    /**
     * 源单单据id
     */
    private String srcBillFormId;

    /**
     * 需要填充订单的明细名称,平台的entryName，
     */
    private String entryName;

    /**
     * 源单类型字段（查询字段），如果源单id不为空该字段失效
     */
    private String srcBillTypeField;

    /**
     * 源单编号字段（查询字段）
     */
    private String srcBillNoField;

    /**
     * 源单id字段（查询字段）
     */
    private String srcBillIdField;

    /**
     * 源单明细id字段（查询字段）
     */
    private String srcEntryIdField;

    /**
     * id字段（查询字段）
     */
    private String fidField = "FID";

    /**
     * 单据编号字段（查询字段）
     */
    private String fbillNoField = "FBillNo";

    /**
     * 直接关联订单编码字段（查询字段）
     */
    private String directlyRelatedOrderField;
}
