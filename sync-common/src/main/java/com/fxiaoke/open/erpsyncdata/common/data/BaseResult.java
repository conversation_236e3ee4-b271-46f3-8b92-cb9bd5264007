package com.fxiaoke.open.erpsyncdata.common.data;

import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;

import java.io.Serializable;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.Data;

@Data
public abstract class BaseResult implements Serializable {
    protected int errCode = ResultCodeEnum.SUCCESS.getErrCode();
    protected String errMsg = I18NStringManager.getByTraceLang(I18NStringEnum.s6);

    public boolean isSuccess() {
        return this.errCode == ResultCodeEnum.SUCCESS.getErrCode();
    }
}
