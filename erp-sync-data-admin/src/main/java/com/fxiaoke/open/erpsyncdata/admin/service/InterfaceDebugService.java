package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.GetInterfaceLogIdBySyncDataIdArg;
import com.fxiaoke.open.erpsyncdata.admin.model.InterfaceDebug;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.InterfaceMonitorData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpInterfaceMonitorResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.bson.types.ObjectId;

import javax.validation.Valid;
import java.util.List;

/**
 * ERP接口调试相关
 * <AUTHOR> (^_−)☆
 * @date 2021/9/23
 */
public interface InterfaceDebugService {
    /**
     * 根据同步记录id获取接口调用记录Id
     * 先放这防止冲突
     *
     * @param arg
     * @return
     */
    Result<String> getInterfaceLogIdBySyncDataId(GetInterfaceLogIdBySyncDataIdArg arg);

    Result<InterfaceDebug.InfoData> getInterfaceInfo(InterfaceDebug.GetInfoArg arg,String lang);

    Result<InterfaceDebug.InfoData> getInterfaceInfoV2(InterfaceDebug.GetInfoArg arg,String lang);
    @Deprecated
    Result<ErpInterfaceMonitorResult> debugGetData(InterfaceDebug.GetDataArg arg,String lang);

    Result<ErpInterfaceMonitorResult> debugGetDataV2(InterfaceDebug.GetDataArg arg,String lang);
    @Deprecated
    Result<ErpInterfaceMonitorResult> debugListData(@Valid InterfaceDebug.ListDataArg arg,String lang);

    Result<ErpInterfaceMonitorResult> debugListDataV2(@Valid InterfaceDebug.ListDataArg arg,String lang);
}
