package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.facishare.function.biz.api.dto.FunctionFeature;
import com.facishare.function.biz.api.service.FunctionService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.ConnectParamUtil;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.manager.InterfaceMonitorManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FunctionServiceExecuteReturnData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionResult;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.github.trace.TraceContext;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomFunctionServiceImpl implements CustomFunctionService {
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private InterfaceMonitorManager interfaceMonitorManager;
    @Autowired
    private TenantConfigurationManager configurationManager;
    @Autowired
    private FunctionService bizFunctionService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result2<FunctionResult<FunctionServiceFindResult>> find(HeaderObj headerObj, FunctionServiceFindArg arg) {
        ObjectData objectData = sfaApiManager.findCustomFunction(headerObj, arg);
        log.debug("CustomFunctionServiceImpl.find,objectData={}", objectData);
        if (objectData == null) {
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        FunctionServiceFindResult findResult = new FunctionServiceFindResult();
        int errCode = objectData.getInt("errCode");
        String errMessage = objectData.getString("errMessage");

        FunctionResult<FunctionServiceFindResult> functionResult = new FunctionResult<>();
        functionResult.setErrCode(errCode);
        functionResult.setErrMessage(errMessage);

        if (errCode == 0) {
            Map<String, Object> resultMap = objectData.getMap("result");
            if (resultMap == null)
                return Result2.newError(ResultCodeEnum.SYSTEM_ERROR);

            Map<String, Object> functionMap = (Map<String, Object>) resultMap.get("function");

            String json = GsonUtil.toJson(functionMap);

            FunctionServiceFindData findData = GsonUtil.fromJson(json, FunctionServiceFindData.class);
            findResult.setFunction(findData);

            functionResult.setResult(findResult);
        }

        if (errCode == 0) {
            return Result2.newSuccess(functionResult);
        }
        return Result2.newError(errCode, errMessage);
    }

    private Result2<FunctionServiceExecuteReturnData> currencyFunction2(String tenantId,
                                                                        String erpDataCenterId,
                                                                        HeaderObj headerObj,
                                                                        FunctionServiceExecuteArg arg,
                                                                        ErpObjInterfaceUrlEnum interfaceUrl,
                                                                        CustomFunctionTypeEnum functionType) {
        // 集成流函数且为自定义类时,执行类的方法
        if (Objects.nonNull(functionType)) {
            final Result<String> classFunction = getFunctionType(tenantId, arg.getApiName());
            log.debug("CustomFunctionServiceImpl getFunctionType, tenantId:{} apiName:{} result:{}", tenantId, arg.getApiName(), classFunction);
            if (!classFunction.isSuccess()) {
                return Result2.newError4CustomFunc(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode(), classFunction.getErrMsg());
            }
            String type = classFunction.getData();
            if (Objects.equals(type, FunctionServiceFindData.TYPE_CLASS)) {
                return currencyAplClass(tenantId, arg, functionType);
            }
        }

        Boolean serializeNull = configurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.SERIALIZE_NULL_TENANTS);
        Map<String, Object> functionResultMap;
        Result2<FunctionServiceExecuteReturnData> result;
        ObjectData objectData = sfaApiManager.executeCustomFunction(headerObj, arg, serializeNull);
        log.debug("CustomFunctionServiceImpl.currencyFunction,objectData={}", objectData);
        if (objectData == null) {
            return Result2.newError(ResultCodeEnum.NOT_GET_RESULT);
        }

        int errCode = objectData.getInt("errCode");
        String errMessage = objectData.getString("errMessage");
        Map<String, Object> resultMap = objectData.getMap("result");

        result = Result2.newError4CustomFunc(errCode, errMessage);

        if (resultMap == null)
            return Result2.newError(ResultCodeEnum.SYSTEM_ERROR);

        Boolean success = MapUtils.getBoolean(resultMap, "success");
        String errorInfo = MapUtils.getString(resultMap, "errorInfo");

        if (success == false) {
            List<String> items = Splitter.on(":::").splitToList(errorInfo);
            if (CollectionUtils.isNotEmpty(items) && items.size() == 2) {
                int code = 0;
                try {
                    code = Integer.valueOf(items.get(0));
                } catch (Exception e) {
                    code = errCode;
                }
                return Result2.newError(code, items.get(1));
            }
        }

        if (success != null && success == false) {
            result.setIntErrCode(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
            result.setErrMsg(errorInfo);
            return result;
        }

        try {
            functionResultMap = (Map<String, Object>) resultMap.get("functionResult");
        } catch (Exception e) {
            throw new ErpSyncDataException(I18NStringEnum.s260,tenantId);
        }

        if (functionResultMap == null) {
            //            result.setIntErrCode(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode());
            //            result.setErrMsg("自定义函数没有返回值");
            return Result2.newSuccess(new FunctionServiceExecuteReturnData());
        }

        ErpConnectInfoEntity entity = erpConnectInfoManager.getByIdAndTenantId(tenantId, erpDataCenterId);
        ConnectInfoResult.ConnectParams connectParams = null;
        try {
            connectParams = ConnectParamUtil.json2ConnectParam(entity);
        } catch (Exception e) {

        }
        String codeKey = "code";
        String messageKey = "message";
        String dataKey = "data";
        String successCode = "0";
        if (connectParams != null) {
            if (connectParams.getStandard() != null) {
                codeKey = connectParams.getStandard().getResultFormat().getCodeName();
                messageKey = connectParams.getStandard().getResultFormat().getMsgName();
                dataKey = connectParams.getStandard().getResultFormat().getDataName();
                successCode = connectParams.getStandard().getResultFormat().getSuccessCode();
            }
            if (connectParams.getSap() != null) {
                codeKey = connectParams.getSap().getResultFormat().getCodeName();
                messageKey = connectParams.getSap().getResultFormat().getMsgName();
                dataKey = connectParams.getSap().getResultFormat().getDataName();
                successCode = connectParams.getSap().getResultFormat().getSuccessCode();
            }
        }
        Map<String, Object> dataMap = null;
        //ExecuteCustomFunctionArg 自定义函数参数中的对象数据,函数通过return syncArg返回
        if (functionResultMap.containsKey("objectData")) {
            log.info("自定义函数返回值兼容返回syncArg, tenantId:{} apiName:{}", tenantId, arg.getApiName());
            try {
                log.info("自定义函数返回值兼容返回syncArg, tenantId:{} apiName:{}", tenantId, arg.getApiName());
                dataMap = (Map<String, Object>) functionResultMap.get("objectData");
            } catch (Exception e) {
                throw new ErpSyncDataException(I18NStringEnum.s260,tenantId);
            }
        }

        //自定义函数直接返回结果，没有按集成平台标准的返回格式进行包装
        if (functionResultMap.containsKey(codeKey) == false) {
            log.info("自定义函数返回值兼容直接返回data, tenantId:{} apiName:{}", tenantId, arg.getApiName());
            dataMap = new HashMap<>();
            dataMap.putAll(functionResultMap);
        } else {
            //函数通过集成平台定义的标准格式返回数据
            String code = MapUtils.getString(functionResultMap, codeKey);
            if (StringUtils.isNotEmpty(code)) {
                String message = MapUtils.getString(functionResultMap, messageKey);

                result.setIntErrCode(StringUtils.equalsIgnoreCase(code, successCode) ? 0 : -1);
                result.setErrMsg(message);

                if (StringUtils.equalsIgnoreCase(code, successCode)||(functionResultMap.containsKey(dataKey)&&functionResultMap.get(dataKey)!=null)) {//包含dataKey都设置
                    try {
                        dataMap = (Map<String, Object>) functionResultMap.get(dataKey);
                    } catch (Exception e) {
                        log.warn("CustomFunctionServiceImpl.currencyFunction,自定义函数返回值无法转换成Map,请检查返回值,自定义函数返回值为={}", functionResultMap);
                        //throw new ErpSyncDataException(i18NStringManager.getByEi(I18NStringEnum.s3645,tenantId));
                        dataMap = new HashMap<>();
                    }
                }
            }
        }

        FunctionServiceExecuteReturnData returnData = new FunctionServiceExecuteReturnData();
        if (dataMap != null) {
            returnData.putAll(dataMap);
        } else {
            returnData.putAll(functionResultMap);
        }
        result.setData(returnData);
        if (result.getIntErrCode() == 0) {
            result.setErrCode(com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SUCCESS.getErrCode());
        }
        return result;
    }

    private Result2<FunctionServiceExecuteReturnData> currencyAplClass(final String tenantId, final FunctionServiceExecuteArg arg, final CustomFunctionTypeEnum functionType) {
        Boolean serializeNull = configurationManager.inWhiteList(tenantId, TenantConfigurationTypeEnum.SERIALIZE_NULL_TENANTS);
        final List<String> requestBody = arg.getParameters().stream()
                .map(FunctionServiceParameterData::getValue)
                .map(params -> BooleanUtils.isTrue(serializeNull) ? JacksonUtil.toJson(params) : JSON.toJSONString(params))
                .collect(Collectors.toList());
        final Result<SyncResult> functionResult = executeCustomClassFunction(tenantId, arg.getApiName(), requestBody, arg.getObjectData(), functionType);
        log.debug("CustomFunctionServiceImpl.executeCustomClassFunction,functionResult={}", functionResult);

        if (!functionResult.isSuccess()) {
            return Result2.newError4CustomFunc(ResultCodeEnum.CUSTOM_FUNC_EXECUTE_FAIL.getErrCode(), functionResult.getErrMsg());
        }

        final SyncResult data = functionResult.getData();
        // 函数代码直接return null
        if (data == null) {
            return Result2.newSuccess(new FunctionServiceExecuteReturnData());
        }

        if (!data.isSuccess()) {
            return Result2.newError(-1, data.getMessage());
        }

        FunctionServiceExecuteReturnData returnData = new FunctionServiceExecuteReturnData();
        // 同步后函数没有返回值
        if (MapUtils.isNotEmpty(data.getData())) {
            returnData.putAll(data.getData());
        }

        return Result2.newSuccess(returnData);
    }

    protected Result<SyncResult> executeCustomClassFunction(final String tenantId, final String funcApiName, final List<String> requestBody, final Map<String, Object> objectData, final CustomFunctionTypeEnum functionType) {
        try {
            final String s = bizFunctionService.executeFuncMethod(tenantId, "-10000", funcApiName, functionType.getFunctionMethodName(), requestBody, objectData, 20, FunctionFeature.builder().resultWriteMapNullValue(true).build());
            log.debug("CustomFunctionServiceImpl.executeCustomClassFunction,objectData={}", s);
            return Result.newSuccess(JSON.parseObject(s, SyncResult.class));
        } catch (Exception e) {
            return Result.newError(e.getMessage());
        }
    }

    @Data
    public static class SyncResult {
        public static final String SUCCESS_CODE = "0";
        private String code = SUCCESS_CODE;
        private String message;
        private JSONObject data;

        public boolean isSuccess() {
            return StringUtils.equals(code, SyncResult.SUCCESS_CODE);
        }
    }

    @Cached(cacheType = CacheType.LOCAL, localLimit = 5000, postCondition = "result.isSuccess()")
    public Result<String> getFunctionType(final String tenantId, final String apiName) {
        final Result<FunctionServiceFindData> result = getFunction(tenantId, apiName);
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        final FunctionServiceFindData data = result.getData();
        if (Objects.isNull(data)) {
            return Result.newError(i18NStringManager.getByEi2(I18NStringEnum.s1028.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s1028.getI18nValue(), apiName),
                    Lists.newArrayList(apiName)));
        }
        return Result.newSuccess(data.getType());
    }

    @Cached(cacheType = CacheType.LOCAL, localLimit = 5000, postCondition = "result.isSuccess()")
    @Override
    public Result<String> getFunctionNameSpace(final String tenantId, final String apiName) {
        final Result<FunctionServiceFindData> result = getFunction(tenantId, apiName);
        if (!result.isSuccess()) {
            return Result.copy(result);
        }
        final FunctionServiceFindData data = result.getData();
        if (Objects.isNull(data)) {
            return Result.newError(i18NStringManager.getByEi2(I18NStringEnum.s1028.getI18nKey(),
                    tenantId,
                    String.format(I18NStringEnum.s1028.getI18nValue(), apiName),
                    Lists.newArrayList(apiName)));
        }
        return Result.newSuccess(data.getNameSpace());
    }

    @Cached(cacheType = CacheType.LOCAL, localLimit = 10000, postCondition = "result.isSuccess()")
    public Result<FunctionServiceFindData> getFunction(final String tenantId, final String apiName) {
        final HeaderObj headerObj = I18NHeaderObj.getHeader2(tenantId, i18NStringManager);
        final FunctionServiceFindArg arg = new FunctionServiceFindArg();
        arg.setApiName(apiName);
        arg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
        final Result2<FunctionResult<FunctionServiceFindResult>> result2 = find(headerObj, arg);
        if (!result2.isSuccess()) {
            return Result.copy((BaseResult) result2);
        }
        return Result.newSuccess(result2.getData().getResult().getFunction());
    }

    @Cached(cacheType = CacheType.LOCAL, localLimit = 5000, postCondition = "result.isSuccess()")
    @Override
    public String getFunctionName(final String tenantId, final String apiName) {
        final FunctionServiceFindData data = getFunction(tenantId, apiName).getData();
        if (Objects.isNull(data)) {
            return i18NStringManager.getByEi(I18NStringEnum.s1029, tenantId);
        }
        return data.getFunctionName();
    }

    @Override
    public List<String> getLackFunctions(String tenantId, Collection<String> functions) {
        if (CollectionUtils.isEmpty(functions)) {
            return Lists.newArrayList();
        }
        return functions.stream()
                .filter(function -> {
                    final Result<FunctionServiceFindData> dataResult = getFunction(tenantId, function);
                    return !dataResult.isSuccess() || Objects.isNull(dataResult.getData());
                }).collect(Collectors.toList());
    }

    @Override
    public Result2<FunctionServiceExecuteReturnData> currencyFunction(String tenantId,
                                                                      String erpDataCenterId,
                                                                      HeaderObj headerObj,
                                                                      FunctionServiceExecuteArg arg,
                                                                      ErpObjInterfaceUrlEnum interfaceUrl,
                                                                      CustomFunctionTypeEnum functionType,
                                                                      final String ployDetailSnapshotId) {
        long callTime = System.currentTimeMillis();
        Result2<FunctionServiceExecuteReturnData> result2 = null;
        try {
            result2 = currencyFunction2(tenantId, erpDataCenterId, headerObj, arg, interfaceUrl, functionType);
        } finally {
            if (interfaceUrl != null) {
                long returnTime = System.currentTimeMillis();
                long costTime = returnTime - callTime;
                Map<String, Object> objectMap = (Map<String, Object>) arg.getParameters().get(0).getValue();
                Object objApiName = null;
                TimeFilterArg timeFilterArg = null;
                if (objectMap.get("objectData") != null) {
                    Map<String, Object> map = (Map<String, Object>) objectMap.get("objectData");
                    objApiName = map.get("objAPIName");
                    log.debug("CustomFunctionServiceImpl.currencyFunction,objApiName={}", objApiName);
                    timeFilterArg = GsonUtil.fromJson(GsonUtil.toJson(map), TimeFilterArg.class);
                    if (timeFilterArg != null && timeFilterArg.getStartTime() == null) {//如果序列化后没有开始时间，置空
                        timeFilterArg = null;
                    }
                }
                interfaceMonitorManager.saveErpInterfaceMonitor(tenantId,
                        erpDataCenterId,
                        objApiName == null ? "emptyObjApiName" : objApiName.toString(),
                        interfaceUrl.name(),
                        arg,
                        GsonUtil.toJson(result2 == null ? "" : result2),
                        result2 != null && result2.isSuccess() ? 1 : 2,
                        callTime,
                        returnTime,
                        i18NStringManager.getByEi(I18NStringEnum.s1030, tenantId),
                        TraceContext.get().getTraceId(),
                        costTime,
                        timeFilterArg);
            }
        }
        return result2;
    }
}
