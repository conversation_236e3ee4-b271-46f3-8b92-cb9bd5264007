package com.fxiaoke.open.erpsyncdata.dbproxy.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 14:54 2021/2/25
 * @Desc:
 */
@AllArgsConstructor
@Getter
public enum ErpTempDataStatusEnum {
    STATUS_OTHER(10000, "其他状态", I18NStringEnum.s881.getI18nKey()),
    STATUS_NOT_READY(10010, "未准备好", I18NStringEnum.s1167.getI18nKey()),
    STATUS_IS_READY(10020, "已准备好", I18NStringEnum.s1168.getI18nKey()),
    STATUS_PROCESS_SUC(10030, "预处理成功", I18NStringEnum.s1169.getI18nKey()),
    STATUS_SEND_MQ_SUC(10040, "发送成功", I18NStringEnum.s1170.getI18nKey()),
    STATUS_NOT_TRIGGER(10050, "数据触发不成功", I18NStringEnum.s1171.getI18nKey()),
    STATUS_IS_TRIGGER(10060, "数据触发成功", I18NStringEnum.s896.getI18nKey()),
    STATUS_FUNCTION_FILTER(10070, "同步前函数过滤", I18NStringEnum.s1172.getI18nKey()),

    ;

    private Integer status;
    private String desc;
    private String i18nKey;

    public String getDesc(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,desc);
    }

    /**
     * 状态如果不是ErpTempDataStatusEnum中的，统一是其他状态STATUS_OTHER
     * @param status
     * @return
     */
    public static ErpTempDataStatusEnum getErpTempDataStatus(Integer status) {
        for (ErpTempDataStatusEnum theStatus : ErpTempDataStatusEnum.values()) {
            if (theStatus.getStatus().equals(status)) {
                return theStatus;
            }
        }
        return STATUS_OTHER;
    }
    public static List<Integer> getNormalErpTempDataStatus() {
        List<Integer> normalStatus= Lists.newArrayList();
        for (ErpTempDataStatusEnum theStatus : ErpTempDataStatusEnum.values()) {
            if (theStatus.getStatus().equals(STATUS_NOT_READY.status)) {
               continue;
            }
            normalStatus.add(theStatus.status);
        }
        return normalStatus;
    }
}
