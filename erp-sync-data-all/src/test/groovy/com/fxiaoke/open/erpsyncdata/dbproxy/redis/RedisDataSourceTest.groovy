package com.fxiaoke.open.erpsyncdata.dbproxy.redis

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/4
 */
@Ignore
class RedisDataSourceTest extends BaseSpockTest {
    @Autowired
    private RedisDataSource redisDataSource

    @Test
    void redisTest() {
        long val = redisDataSource.get().incr("mytestkey");
        System.out.println("init val: " + val);
        val = redisDataSource.get().incr("mytestkey");
        System.out.println("init val more : " + val);

       // def result = redisDataSource.get().putAt("test","test111")
        //println(result)
    }
}
