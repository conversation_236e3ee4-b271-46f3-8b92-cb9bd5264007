package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;

import javax.validation.constraints.NotNull;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 */
public class SystemParams extends LinkedHashMap<String, String> {

    public String getBaseUrl() {
        return get("baseUrl");
    }

    public SystemParams setBaseUrl(String baseUrl) {
        if (baseUrl != null) {
            put("baseUrl", baseUrl);
        }
        return this;
    }

    public ConnectorAuthType getAuthType() {
        String authType = get("authType");
        if (authType == null) {
            return null;
        }
        return ConnectorAuthType.valueOf(authType);
    }

    public SystemParams setAuthType(ConnectorAuthType type) {
        if (type != null) {
            put("authType", type.name());
        }
        return this;
    }

    public static SystemParams of(@NotNull Map<String, String> src) {
        SystemParams systemParams = new SystemParams();
        systemParams.putAll(src);
        return systemParams;
    }
}
