package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAObjFieldEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/16
 * @Desc:
 */
@Repository
public interface OAObjFieldDao extends ErpBaseDao<OAObjFieldEntity> , ITenant<OAObjFieldDao> {

    /**
     * 根据企业id和对象ApiName查找
     *
     * @param tenantId
     * @return
     */
    List<OAObjFieldEntity> getByTenantIdAndObj(@Param("tenantId") String tenantId, @Param("objApiName") String objApiName);

}