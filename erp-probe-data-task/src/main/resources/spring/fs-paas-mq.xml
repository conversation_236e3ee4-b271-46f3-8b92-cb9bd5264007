<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p" xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--MQ Consumer-->
    <bean id="newPaasObjectDataMqConsumer" class="com.fxiaoke.open.erpsyncdata.crmmq.consumer.PaasObjectDataMqConsumer"
          init-method="init" p:rocketMQConsumerConfigName="erp-sync-data-paas-object-consumer"/>
</beans>
