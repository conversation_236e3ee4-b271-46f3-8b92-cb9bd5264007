package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.google.common.collect.ImmutableSet;

import java.util.Set;

/**
 * Created by fengyh on 2020/8/22.
 * <p>
 * <p>
 * 对象拆分类型说明：
 * <p>
 * erp对象拆分，只局限一种场景：一个erp对象拆分到0个或者多个crm对象，erp一个对象返回数据时，可以包含主从(erp中叫做明细)数据。
 * <p>
 * 以下场景不做对象拆分：
 * 1.erp中主和从分别有自己的APIname, 通过不同的erp API接口返回。
 * 这种场景的同步，需要分别绑定两个CRM对象。对象不用拆分，但是不支持数据分流。
 * <p>
 * 2.合并erp多个对象到一个crm对象。不在对接模块做拆分逻辑处理，
 * 而通过实施方案，在crm上创建两个一模一样的对象，然后通过crm自定义函数合并数据.
 * <p>
 * 数据分流特殊性说明：
 * 数据分流是在其他对象拆分类型上的一种独特的业务需求。
 * 比如说 先按照字段拆分后，再做数据分流。或者明细拆分到主从后，再做数据分流。
 * <p>
 * DATA_DISTRIBUTE比较特殊。db中的拆分类型，不会明确存储做了数据分流。
 * 而是在db中多存储一个字段，叫做拆分批次，同一个erp对象如果拆分了多次，那么就要做数据分流。
 * 每次拆分的对象，作为同一个拆分批次存入db.
 */
public enum ErpObjSplitTypeEnum {
    NOT_SPLIT, //erp一个对象对应crm一个对象。
    FIELD_SPLIT, //erp主对象字段拆分到crm的主从对象，比如erp客户 对应crm 客户+客户财务
    DETAIL2DETAIL_SPLIT, //erp对象的明细拆分到主从， 比如crp订单 对 crm订单+订单明细
    DETAIL2LOOKUP_SPLIT, //erp对象的明细拆分到查找关联的独立对象， erp明细数据拆分到独立对象，查找关联到拆分的主对象，比如u8的开票
    /**
     * @deprecated 未发现代码中有使用。启用时删除注释
     */
    @Deprecated
    DATA_DISTRIBUTE,
    /**
     * 明细子结构，即明细的明细,拆分为查找关联明细的单独对象
     */
    SUB_DETAIL_LOOKUP_DETAIL,


    ;//数据分流到多个独立对象， 比如k3c 即时库存，对应crm 库存+批次库存. 把他转为上面几种拆分类型，然后依靠拆分对象上的数据范围配置 来实现数据分流

    public final static Set<ErpObjSplitTypeEnum> DETAIL_TYPES = ImmutableSet.of(DETAIL2DETAIL_SPLIT, DETAIL2LOOKUP_SPLIT,SUB_DETAIL_LOOKUP_DETAIL);

    /**
     * 允许单独集成流,可能没有单独轮询
     */
    public final static Set<ErpObjSplitTypeEnum> TYPES_ALLOW_INDEPENDENT_STREAM = ImmutableSet.of(NOT_SPLIT, FIELD_SPLIT, DETAIL2LOOKUP_SPLIT, SUB_DETAIL_LOOKUP_DETAIL);
    /**
     * 允许单独轮询的对象
     */
    public final static Set<ErpObjSplitTypeEnum> MAIN_OBJ_TYPES = ImmutableSet.of(NOT_SPLIT, FIELD_SPLIT, DETAIL2LOOKUP_SPLIT);
}
