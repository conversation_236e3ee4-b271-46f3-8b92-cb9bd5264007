package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.BaseData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 改个名字区分下
 */
@Data
public class SyncPloyDetailData2 extends BaseData implements Serializable {
    private String id;
    private String syncPloyId;
    private String tenantId;
    /**
     * {@link  SyncPloyDetailStatusEnum}
     */
    private Integer status;
    private Integer sourceTenantType;
    private Integer destTenantType;
    private String sourceObjectApiName;
    private String destObjectApiName;
    private List<FieldMappingData> fieldMappings;
    private List<DetailObjectMappingsData.DetailObjectMappingData> detailObjectMappings;
    private SyncRulesData2 syncRules;
    private SyncConditionsData syncConditions;
    private List<SyncConditionsData> detailObjectSyncConditions;
    private IntegrationStreamNodesData integrationStreamNodes;
    private String beforeFuncApiName;
    private String duringFuncApiName;
    private String afterFuncApiName;
    /** 源数据中心 */
    private String sourceDataCenterId;
    /** 目标数据中心 */
    private String destDataCenterId;

    @Override
    public String toString() {
        return "SyncPloyDetailData2";
    }
}
