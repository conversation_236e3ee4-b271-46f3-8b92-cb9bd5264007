package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.custom.arg.GetAccountStatementData2TableArg;
import com.fxiaoke.open.erpsyncdata.custom.arg.GetErpData2TableArg;
import com.fxiaoke.open.erpsyncdata.custom.service.GetErpData2TableService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Date: 15:20 2020/9/22
 * @Desc:
 */
@Slf4j
@Api(tags = "科顺相关接口")
@RestController("KeShunController")
@RequestMapping("inner/erp/syncdata/customfunction/keshun")
public class KeShunController {
    @Autowired
    private GetErpData2TableService getErpData2TableService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private I18NStringManager i18NStringManager;

    private static ExecutorService  executorService;
    static {
        ThreadFactory workerFactory = new ThreadFactoryBuilder()
                .setNameFormat("KeShunController-%d").build();
        executorService = new ThreadPoolExecutor(20, 30, 0, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(50), workerFactory);
    }

    @ApiOperation(value = "科顺企业根据指定条件获取erp数据推送到数据库表")
    @RequestMapping(value = "/getKeShunErpData2Table",method = RequestMethod.POST)
    public Result<String> getKeShunErpData2Table(@RequestHeader(value="x-fs-ei") Integer tenantId,@RequestHeader(value = "x-user-id") String userId,@RequestBody GetAccountStatementData2TableArg arg) {
        if (arg == null||tenantId==null) {
            log.info("param is null tenantId={},arg={}",tenantId,arg);
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        GetErpData2TableArg getErpData2TableArg=new GetErpData2TableArg();
        GetErpData2TableArg.ErpDataFilter erpDataFilter=new GetErpData2TableArg.ErpDataFilter();
        getErpData2TableArg.setErpDataFilter(erpDataFilter);
        erpDataFilter.setErpFakeObjApiName(arg.getFakeErpApiName());
        erpDataFilter.setStartTime(arg.getStartTime());
        erpDataFilter.setEndTime(arg.getEndTime());
        erpDataFilter.setErpField2Values(Lists.newArrayList());
        for(String customerId:arg.getSapCustomers()){
            Map<String,String> erpField2Value= Maps.newHashMap();
            erpField2Value.put(arg.getErpFieldApiName(),customerId);
            erpDataFilter.getErpField2Values().add(erpField2Value);
        }
        final String ei = String.valueOf(tenantId);
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                StringBuilder msg=new StringBuilder();
                try {
                    log.info("pushErpData2Table task start,ei={},arg={}", tenantId,arg);
                    Result<String> result = getErpData2TableService.pushErpData2Table(ei, userId, getErpData2TableArg);
                    if(result.isSuccess()){
                        msg.append(i18NStringManager.getByEi(I18NStringEnum.s3740, ei));
                    }else {
                        msg.append(i18NStringManager.getByEi(I18NStringEnum.s3777, ei)).append(System.lineSeparator()).append(i18NStringManager.getByEi(I18NStringEnum.s857, ei)).append(result.getErrMsg());
                    }
                    log.info("pushErpData2Table task end,ei={},result={}", tenantId,result);
                } catch (Throwable e) {
                    msg.append(i18NStringManager.getByEi(I18NStringEnum.s3777, ei)).append(System.lineSeparator()).append(i18NStringManager.getByEi(I18NStringEnum.s857, ei)).append(e.getMessage());
                    log.error("pushErpData2Table task error.", e);
                }
                if(StringUtils.isNotBlank(userId)){
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setTenantId(ei);
                    arg.setMsgTitle(i18NStringManager.getByEi(I18NStringEnum.s3741, ei));
                    arg.setReceivers(Lists.newArrayList(Integer.valueOf(userId)));
                    arg.setMsg(msg.toString());
                    Result<Void> voidResult = notificationService.sendErpSyncDataAppNotice(arg,
                            AlarmRuleType.OTHER,
                            AlarmRuleType.OTHER.getName(i18NStringManager,null,tenantId+""),
                            AlarmType.OTHER,
                            AlarmLevel.GENERAL);
                }
            }
        });
        Result<String> result=Result.newSuccess();
        result.setErrMsg(i18NStringManager.getByEi(I18NStringEnum.s3742, ei)+ TraceUtil.get());
        return result;
    }

}
