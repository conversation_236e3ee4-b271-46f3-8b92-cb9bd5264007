{"type": "page", "title": "重试指定失败的数据", "remark": null, "name": "retryData", "toolbar": [], "body": [{"type": "crud", "name": "retryData", "api": {"method": "post", "url": "../retryData/listData"}, "defaultParams": {"perPage": 100}, "filter": {"title": "筛选", "body": [{"size": "sm", "label": "tenantId", "type": "input-text", "name": "tenantId", "clearable": true}, {"label": "类型", "type": "select", "name": "retryDataEnum", "searchable": true, "creatable": true, "clearable": true, "size": "lg", "source": "post:../retryData/returnType"}, {"type": "input-datetime-range", "name": "startTime", "label": "日期范围"}, {"name": "selectStatus", "type": "checkboxes", "label": "状态多选", "value": "0,2", "joinValues": false, "options": [{"label": "待重试", "value": "0"}, {"label": "重试成功", "value": "1"}, {"label": "重试失败", "value": "2"}]}], "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:f7cb74dd9045"}, {"type": "service", "body": [{"type": "button", "label": "筛选重试", "level": "primary", "actionType": "ajax", "api": "post:../retryData/retryConditionData", "messages": {"success": "正在异步重试。。。", "failed": "重试失败"}, "id": "u:036395095f61"}], "id": "u:6e2deddbc2bd"}]}, "initApi": {"method": "post", "url": "../retryData/listData", "data": {"tenantId": "${tenantId}"}}, "headerToolbar": ["export-csv", "reload", "bulkActions"], "bulkActions": [{"type": "button", "label": "批量重试", "actionType": "ajax", "confirmText": "确定要批量重试选中的这些数据？", "api": "../retryData/retryConditionData", "data": {"ids": "${ids}"}, "messages": {"success": "正在异步重试。。。", "failed": "重试失败"}, "id": "u:048f2212be46"}], "columns": [{"name": "id", "label": "id", "searchable": true}, {"name": "tenantId", "label": "tenantId", "sortable": true, "searchable": true}, {"name": "retryDataEnum", "label": "数据类型", "sortable": true, "searchable": true}, {"name": "status", "label": "重试状态 0 待重试 1 重试成功 2 重试失败", "sortable": true, "searchable": true}, {"name": "resultMsg", "label": "失败的错误信息", "sortable": true, "searchable": true}, {"name": "mqMsg", "label": "数据详情", "sortable": true, "searchable": true}, {"name": "createTime", "label": "createTime", "sortable": true, "searchable": true}, {"name": "updateTime", "label": "updateTime", "sortable": true, "searchable": true}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "combineNum": 0, "bodyClassName": "panel-default"}]}