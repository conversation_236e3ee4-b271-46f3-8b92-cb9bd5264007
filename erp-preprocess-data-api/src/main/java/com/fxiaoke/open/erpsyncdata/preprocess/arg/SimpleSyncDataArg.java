package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/26
 */
@Data
@ApiModel("syncData简单版")
public class SimpleSyncDataArg implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty("企业ei，标准接口不传")
    private String tenantId;
    @ApiModelProperty("源对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("目标对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("源对象Id")
    private String sourceDataId;
    @ApiModelProperty("源对象主属性")
    private String sourceDataName;
    @ApiModelProperty("目标对象Id")
    private String destDataId;
    @ApiModelProperty("同步状态")
    private Integer status;
    @ApiModelProperty("是否回写crm失败")
    private Boolean reverseWrite2CrmFailed;
    @ApiModelProperty("是否同步后函数执行失败")
    private Boolean afterFuncFailed;
    @ApiModelProperty("同步原因")
    private String remark;
    @ApiModelProperty("消息接收人")
    private Integer receivers;
}
