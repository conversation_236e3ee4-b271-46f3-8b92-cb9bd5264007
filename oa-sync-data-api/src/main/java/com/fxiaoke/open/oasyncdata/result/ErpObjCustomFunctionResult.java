package com.fxiaoke.open.oasyncdata.result;

import com.fxiaoke.open.oasyncdata.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 19:51 2020/11/23
 * @Desc:
 */
@Data
@ApiModel
public class ErpObjCustomFunctionResult implements Serializable {

    @ApiModelProperty("数据id")
    public String id;
    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("对象apiName")
    public String objApiName;
    @ApiModelProperty("路径")
    public ErpObjInterfaceUrlEnum url;
    @ApiModelProperty("自定义函数apiName")
    public String funcApiName;
    @ApiModelProperty("标准api格式")
    public String interfaceFormat;
}
