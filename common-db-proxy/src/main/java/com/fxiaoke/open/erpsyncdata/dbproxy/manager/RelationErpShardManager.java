package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.JetCacheName;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationErpShardDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RelationErpShardManager {
    @Autowired
    private RelationErpShardDao relationErpShardDao;

    /**
     * 获取主要信息
     */
    @Cached(name = JetCacheName.RELATION_ERP_SHARD_SIMPLE, syncLocal = true, expire = 600, localExpire = 10, cacheType = CacheType.BOTH)
    public RelationErpShardDto getSimple(String downStreamId, String dcId) {
        return relationErpShardDao.getSimple(downStreamId, dcId);
    }

    @CacheInvalidate(name = JetCacheName.RELATION_ERP_SHARD_SIMPLE)
    public void invalidSimpleCache(String downStreamId, String dcId) {
        log.info("invalid relation erp shard simple,{},{}", downStreamId, dcId);
    }

    public int deleteDownstreamEnterprise(String groupId, String downstreamId) {
        return relationErpShardDao.deleteDownstreamEnterprise(groupId, downstreamId);
    }
}
