package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * 标准明细数据
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/21
 */
@Data
public class StandardDetailData implements Serializable, RAMEstimable {
    private static final long serialVersionUID = -8496485549825825510L;

    /**
     * "主对象apiName"
     */
    private String objAPIName;
    /**
     * 主对象数据Id
     */
    private String masterDataId;
    /**
     * 主对象数据主属性
     */
    private String masterDataName;
    /**
     * 从对象数据;key:从对象apiName,value:从对象数据
     */
    private LinkedHashMap<String, ObjectData> detailFieldVal;

    private static StandardDetailData convert(StandardDetailData standardDetailData) {
        String json = JSONObject.toJSONString(standardDetailData);
        StandardDetailData linkedStandardDetailData = JSONObject.parseObject(json,StandardDetailData.class);
        return linkedStandardDetailData;
    }

    public static StandardDetailData convert(StandardDetailData standardDetailData,
                                                   ErpObjectFieldDao erpObjectFieldDao,
                                             SyncDataFixDao adminSyncDataDao,
                                                   String tenantId,
                                                   String dataCenterId) {
        StandardDetailData linkedStandardDetailData = convert(standardDetailData);

        for (String key : linkedStandardDetailData.getDetailFieldVal().keySet()) {
            LinkedHashMap<String, Object> detailData = linkedStandardDetailData.getDetailFieldVal().get(key);

            String detailObjectApiName = StandardData.getErpObjectApiName(tenantId, adminSyncDataDao,detailData.get("sync_data_id").toString());
            StandardData.updateData(detailData, erpObjectFieldDao, tenantId, dataCenterId, detailObjectApiName);
            detailData.remove("sync_data_id");
        }

        return linkedStandardDetailData;
    }

//    public static void main(String[] args) {
//        String json = "{\"detailFieldVal\":{\"SalesOrderObj.detail\":{\"tenant_id\":\"82370\",\"owner\":[],\"fake_master_detail\":\"60e3fa23bd3f630001daeaf0\",\"price\":\"10.00\",\"object_describe_api_name\":\"SalesOrderObj.detail\",\"count\":\"5.00\",\"detailId\":\"000000000001\",\"_id\":\"60e3fa23bd3f630001daeaf1\",\"product_name\":\"撒大声地\",\"created_by\":[]}},\"masterFieldVal\":{\"tenant_id\":\"82370\",\"order_date\":\"2021-07-06\",\"object_describe_api_name\":\"SalesOrderObj\",\"name\":\"20210706-000001\",\"remark\":\"111\",\"_id\":\"60e3fa23bd3f630001daeaf0\"},\"objAPIName\":\"SalesOrderObj\"}";
//        StandardDetailData standardDetailData = JSONObject.parseObject(json,StandardDetailData.class);
//
//        StandardDetailData linkedStandardDetailData = convert(standardDetailData);
//
//        System.out.println(linkedStandardDetailData);
//
//    }

    @Override
    public long ramBytesUsed(int depth) {
        //只计算大的
        return RamUsageEstimateUtil.sizeOfObject(detailFieldVal, depth);
    }
}
