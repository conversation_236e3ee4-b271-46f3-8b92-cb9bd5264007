package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FileUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryInventoryResult;
import com.fxiaoke.open.erpsyncdata.common.util.IdUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/26
 */
@Ignore
public class SyncSkuSpuServiceImplTest extends BaseTest {
    @Autowired
    private SyncSkuSpuServiceImpl syncSkuSpuService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    public void testSYnc() throws IOException {
        String str = FileUtils.readString("D:\\project\\fs-erp-sync-data\\erp-sync-data-all\\src\\test\\resources\\testfile\\DoWriteMsg.json");
        SyncDataContextEvent doWriteResultData = JacksonUtil.fromJson(str, new TypeReference<SyncDataContextEvent>() {
        });
        syncSkuSpuService.handleSkuSpu2Crm(doWriteResultData);
    }

    @Test
    public  void testJson(){
        String message="{\"rowcount\":1,\"success\":true,\"message\":\"success\",\"data\":[{\"FID\":\"001dd8c7-1cd7-80da-11ea-0136af836c91\",\"FSTOCKORGID\":1,\"FSTOCKORGNUMBER\":\"000\",\"FSTOCKORGNAME\":\"纷享销客\",\"FKEEPERTYPEID\":\"BD_KeeperOrg\",\"FKEEPERTYPENAME\":null,\"FKEEPERID\":1,\"FKEEPERNUMBER\":\"000\",\"FKEEPERNAME\":\"纷享销客\",\"FOWNERTYPEID\":\"BD_OwnerOrg\",\"FOWNERTYPENAME\":null,\"FOWNERID\":1,\"FOWNERNUMBER\":\"000\",\"FOWNERNAME\":\"纷享销客\",\"FSTOCKID\":658621,\"FSTOCKNUMBER\":\"CK0001\",\"FSTOCKNAME\":\"测试仓库1\",\"FSTOCKLOCID\":0,\"FSTOCKLOC\":null,\"FAUXPROPID\":0,\"FAUXPROP\":null,\"FSTOCKSTATUSID\":10000,\"FSTOCKSTATUSNUMBER\":\"KCZT01_SYS\",\"FSTOCKSTATUSNAME\":\"可用\",\"FLOT\":681727,\"FLOTNUMBER\":\"PC001\",\"FBOMID\":0,\"FBOMNUMBER\":\"\",\"FMTONO\":\"\",\"FPROJECTNO\":\"\",\"FPRODUCEDATE\":\"0001-01-01 00:00:00\",\"FEXPIRYDATE\":\"0001-01-01 00:00:00\",\"FBASEUNITID\":10101,\"FBASEUNITNUMBER\":\"Pcs\",\"FBASEUNITNAME\":\"Pcs\",\"FBASEQTY\":1050.0000000000,\"FBASELOCKQTY\":50.0000000000,\"FSECQTY\":0.0,\"FSECLOCKQTY\":0.0,\"FSTOCKUNITID\":10101,\"FSTOCKUNITNUMBER\":\"Pcs\",\"FSTOCKUNITNAME\":\"Pcs\",\"FMATERIALID\":681726,\"FMASTERID\":681726,\"FMATERIALNUMBER\":\"CH4416\",\"FMATERIALNAME\":\"批次测试001\",\"FQTY\":1050.0000000000,\"FLOCKQTY\":50.0000000000,\"FSECUNITID\":0,\"FSECUNITNUMBER\":null,\"FSECUNITNAME\":null,\"FOBJECTTYPEID\":\"STK_Inventory\",\"FBASEAVBQTY\":1000.0000000000,\"FAVBQTY\":1000.0000000000,\"FSECAVBQTY\":0.0,\"FUPDATETIME\":\"2021-06-09 18:06:18\"}]}";
        QueryInventoryResult.CombineInventoryResult combineInventoryResult = JacksonUtil.fromJson(message, QueryInventoryResult.CombineInventoryResult.class);
        QueryInventoryResult.CombineInventoryResult result = JSONObject.parseObject(message, new com.alibaba.fastjson.TypeReference<QueryInventoryResult.CombineInventoryResult>() {
        });
        String resss = JacksonUtil.toJson(result);
        QueryInventoryResult.CombineInventoryResult combineInventoryResult1 = JacksonUtil.fromJson(JacksonUtil.toJson(result), QueryInventoryResult.CombineInventoryResult.class);
        System.out.println(combineInventoryResult);
    }



    @Test
    public void test(){
        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);
//        Result<ObjectDataQueryListByIdsResult> queryResult = objectDataService
//                .queryListByIds(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), Collections.singletonList("6041d63a0584b5000160dd46"));
//        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
//        searchTemplateQuery.addFilter("product_id", Lists.newArrayList("6041d63a0584b5000160dd14"), "EQ");
//        Result<QueryBySearchTemplateResult> result = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), searchTemplateQuery);
//        ControllerListArg controllerListArg=new ControllerListArg();
//        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
//        SearchQuery searchQuery=new SearchQuery();
//        searchQuery.setOffset(0);
//        searchQuery.setLimit(100);
//        Filter filter=new Filter();
//        filter.setFieldName("product_id");
//        filter.setOperator("EQ");
//        filter.setFieldValues(Lists.newArrayList("6041d63a0584b5000160dd14"));
//        searchQuery.setFilters(Lists.newArrayList(filter));
//        controllerListArg.setSearchQuery(searchQuery);
//        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(),controllerListArg );
//        ObjectData fsObjectData=new ObjectData();
//        fsObjectData.setTenantId(81243);
//        fsObjectData.put("name","2222");
//        fsObjectData.put("object_describe_api_name",ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
//        fsObjectData.put("_id","6041d63a0584b5000160dd14");
//        fsObjectData.put("multi_unit_data",list.getData().getDataList());
//        ActionEditArg actionEditArg = new ActionEditArg();
//        actionEditArg.setObjectData(fsObjectData);
//        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), actionEditArg);
//        ObjectData addObjectData=new ObjectData();
//        addObjectData.put("_id", IdUtil.generateId());
//        addObjectData.setTenantId(81243);
//        addObjectData.put("object_describe_api_name", ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
//        addObjectData.put("product_id","6041d63a0584b5000160dd14");
//        addObjectData.put("is_base",false);
//        addObjectData.put("conversion_ratio","11");
//        addObjectData.put("unit_id","5");
//        ActionAddArg addArg = new ActionAddArg();
//        addArg.setObjectData(addObjectData);
//        Result<ActionAddResult> add = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), addArg);
//        ObjectData addProductData=new ObjectData();
//        addProductData.put("_id", IdUtil.generateId());
//        addProductData.setTenantId(81243);
//        addProductData.put("object_describe_api_name", ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
//        addProductData.put("owner", Lists.newArrayList("1000"));
//        addProductData.put("created_by",Lists.newArrayList("1000"));
//        addProductData.put("batch_sn","1");
//        addProductData.put("product_status","1");
//        addProductData.put("category","742629");
//        addProductData.put("product_code","34123412");
//        addProductData.put("is_multiple_unit",true);
//        addProductData.put("record_type","default__c");
//        addProductData.put("unit","60472a4ed215ff000163abc6");
//        addProductData.put("name","7890678");
//
//        Map<String,Object> baseUnit= Maps.newHashMap();
//        baseUnit.put("is_base",true);
//        baseUnit.put("is_enable",true);
//        baseUnit.put("is_pricing",true);
//        baseUnit.put("price","1");
//        baseUnit.put("conversion_ratio","1");
//        baseUnit.put("unit_id","60472a4ed215ff000163abc6");
//        addProductData.put("multi_unit_data", Lists.newArrayList(baseUnit));
//        ActionAddArg addArg = new ActionAddArg();
//        addArg.setObjectData(addProductData);
//        Result<ActionAddResult> add = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(), addArg);
        ObjectData fsObjectData=new ObjectData();
        fsObjectData.setTenantId(81961);
        fsObjectData.put("object_describe_api_name", ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        fsObjectData.put("_id","604b39761c08f0000176184f");
        fsObjectData.put("product_id","");
        fsObjectData.put("spu_id","604b19ca024ac9000151cf63");
        ActionEditArg actionEditArg = new ActionEditArg();
        actionEditArg.setObjectData(fsObjectData);
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(),false,false, null, null, actionEditArg);
        System.out.println("");
    }

    @Test
    public void testSPU(){
        com.fxiaoke.open.erpsyncdata.common.data.ObjectData spuObjectDataData=new com.fxiaoke.open.erpsyncdata.common.data.ObjectData();
        spuObjectDataData.putOwner(Lists.newArrayList("1001"));
        spuObjectDataData.putTenantId("81772");
        spuObjectDataData.putApiName(ObjectApiNameEnum.FS_SPU.getObjApiName());
//        spuObjectDataData.put("batch_sn","1");
//        spuObjectDataData.put("category","31");
//        spuObjectDataData.put("name","测试名称产品1");
//        spuObjectDataData.put("is_spec",false);
        spuObjectDataData.putId("60913665446242d381b522a2d6fbb5fb");
//        spuObjectDataData.put("record_type","default__c");
//        spuObjectDataData.put("standard_price","1");
//        spuObjectDataData.put("unit","3");

        Result<String> result = syncSkuSpuService.updateSpuObj(81772, spuObjectDataData);
        System.out.println("");
    }
    @Test
    public void testSpuMultiUnit(){
        List<ObjectData> multiUnitList = getMultiUnitList("81961", "604b28ec1c08f0000175fa8f", "605055cf1c08f000017928fb", "");
        List<ObjectData> multiUnitList1 = getMultiUnitList("81961", "604b28ec1c08f0000175fa8f", "", "605055cf1366bc0001402d19");

        HeaderObj headerObj = new HeaderObj(81961, CrmConstants.SYSTEM_USER);

        ObjectData addObjectData=new ObjectData();
        addObjectData.put("_id", IdUtil.generateId());
        addObjectData.setTenantId(81961);
        addObjectData.put("object_describe_api_name", ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        addObjectData.put("product_id","604ec945bd93170001c130bc");
        addObjectData.put("spu_id","604ec945bd93170001c130bb");
        addObjectData.put("is_base",false);
        addObjectData.put("conversion_ratio","11");
        addObjectData.put("unit_id","6");
        ActionAddArg addArg = new ActionAddArg();
        addArg.setObjectData(addObjectData);
        Result<ActionAddResult> add = metadataActionService.add(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(),false,false, null, null, addArg);

        ControllerListArg controllerListArg1=new ControllerListArg();
        controllerListArg1.setObjectDescribeApiName(ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
        SearchQuery searchQuery1=new SearchQuery();
        searchQuery1.setOffset(0);
        searchQuery1.setLimit(100);
        Filter filter1=new Filter();
        filter1.setFieldName("spu_id");
        filter1.setOperator("EQ");
        filter1.setFieldValues(Lists.newArrayList("604ec945bd93170001c130bb"));
        searchQuery1.setFilters(Lists.newArrayList(filter1));
        controllerListArg1.setSearchQuery(searchQuery1);
        Result<Page<ObjectData>> list1 = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName(),controllerListArg1 );

        ControllerListArg controllerListArg=new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery=new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        Filter filter=new Filter();
        filter.setFieldName("spu_id");
        filter.setOperator("EQ");
        filter.setFieldValues(Lists.newArrayList("604ec945bd93170001c130bb"));
        searchQuery.setFilters(Lists.newArrayList(filter));
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(),controllerListArg );

        ControllerListArg controllerListArg2=new ControllerListArg();
        controllerListArg2.setObjectDescribeApiName(ObjectApiNameEnum.FS_SPU.getObjApiName());
        SearchQuery searchQuery2=new SearchQuery();
        searchQuery2.setOffset(0);
        searchQuery2.setLimit(100);
        Filter filter2=new Filter();
        filter2.setFieldName("_id");
        filter2.setOperator("EQ");
        filter2.setFieldValues(Lists.newArrayList("604ec945bd93170001c130bb"));
        searchQuery2.setFilters(Lists.newArrayList(filter2));
        controllerListArg2.setSearchQuery(searchQuery2);
        Result<Page<ObjectData>> list2 = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(),controllerListArg );
        ObjectData fsObjectData=list2.getData().getDataList().get(0);
        fsObjectData.put("sku",list1.getData().getDataList());
        fsObjectData.put("multi_unit_data", list.getData().getDataList());
        ActionEditArg actionEditArg = new ActionEditArg();
        actionEditArg.setObjectData(fsObjectData);
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_SPU.getObjApiName(),false,false, null, null, actionEditArg);

        System.out.println("");
    }

    private List<ObjectData> getMultiUnitList(String tenantId, String unitId, String productId, String spuId) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setObjectDescribeApiName(ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        searchQuery.setFilters(Lists.newArrayList());
        if (StringUtils.isNotBlank(productId)) {
            Filter productFilter = new Filter();
            productFilter.setFieldName("product_id");
            productFilter.setOperator("EQ");
            productFilter.setFieldValues(Lists.newArrayList(productId));
            searchQuery.getFilters().add(productFilter);
        }
        if (StringUtils.isNotBlank(spuId)) {
            Filter spuFilter = new Filter();
            spuFilter.setFieldName("spu_id");
            spuFilter.setOperator("EQ");
            spuFilter.setFieldValues(Lists.newArrayList(spuId));
            searchQuery.getFilters().add(spuFilter);
        }
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> list = metadataControllerService.list(headerObj, ObjectApiNameEnum.FS_MULTIUNITRELATED_OBJ.getObjApiName(), controllerListArg);
        if (list != null && list.getData() != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(list.getData().getDataList())) {
            for(ObjectData objectData:list.getData().getDataList()){
                if(unitId.equals(objectData.get("unit_id"))){
                    return Lists.newArrayList(objectData);
                }
            }
            return list.getData().getDataList();
        }
        return Lists.newArrayList();
    }

    @Test
    public void queryBySearchTemplate() {
        String json = "{\"object_data\":{\"tenant_id\":\"81961\",\"lock_rule\":null,\"hasSpecValueCreatePrivilege\":true,\"batch_sn\":\"1\",\"remark\":\"\",\"lock_user\":null,\"is_multiple_unit\":true,\"extend_obj_data_id\":null,\"is_deleted\":false,\"created_by__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"life_status_before_invalid\":null,\"object_describe_api_name\":\"SPUObj\",\"owner_department_id\":\"999998\",\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"81961\",\"name\":\"zsl\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"zsl\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1613787963408,\"modifyTime\":1613788077051,\"dept\":\"999998\",\"post\":\"\"}],\"owner_department\":\"待分配\",\"out_owner\":null,\"owner__r\":{\"picAddr\":\"\",\"description\":\"\",\"dept\":\"999998\",\"supervisorId\":null,\"title\":null,\"modifyTime\":1613788077051,\"post\":\"\",\"createTime\":1613787963408,\"phone\":\"\",\"name\":\"zsl\",\"nickname\":\"zsl\",\"tenantId\":\"81961\",\"id\":\"1000\",\"position\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"standard_price\":\"1.00\",\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"待分配\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"999998\",\"parentId\":\"999999\",\"status\":0},\"create_time\":1635580589128,\"life_status\":\"normal\",\"order_field\":null,\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"is_spec\":true,\"out_tenant_id\":null,\"version\":\"1\",\"created_by\":[\"-10000\"],\"record_type\":\"default__c\",\"picture\":[],\"product_line\":\"\",\"unit\":\"604b28ec1c08f0000175fa8f\",\"data_own_department\":[\"999998\"],\"name\":\"带规格的商品9\",\"order_by\":null,\"_id\":\"617cfaac65f7f70001526e98\",\"category\":\"19\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"999998\",\"deptName\":\"待分配\",\"status\":0}],\"batch_sn__o\":\"\",\"object_describe_id\":\"604ad7631e5b210001c31a0e\",\"sku\":[{\"lock_rule\":null,\"batch_sn\":\"1\",\"is_saleable\":true,\"is_multiple_unit\":true,\"off_shelves_time\":null,\"extend_obj_data_id\":null,\"spu_id__relation_ids\":\"617cfaac65f7f70001526e98\",\"created_by__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"price\":\"1.00\",\"life_status_before_invalid\":null,\"owner_department_id\":\"999998\",\"total_num\":1,\"owner_department\":\"待分配\",\"model\":null,\"spu_id\":\"617cfaac65f7f70001526e98\",\"barcode\":null,\"maintenance_period\":null,\"spec_and_value\":[{\"spec_id\":\"617cfaac65f7f70001526db7\",\"spec_value_id\":\"617cfaac65f7f70001526e11\",\"spec_value_name\":\"spec9 \",\"order_field\":\"0\"}],\"lock_status\":\"0\",\"package\":\"CRM\",\"data_own_department__r\":{\"deptName\":\"待分配\",\"leaderName\":null,\"leaderUserId\":null,\"deptId\":\"999998\",\"parentId\":\"999999\",\"status\":0},\"is_giveaway\":\"0\",\"create_time\":1635580589217,\"purchase_unit_price\":null,\"order_field\":null,\"version\":\"1\",\"created_by\":[\"-10000\"],\"relevant_team\":[{\"teamMemberEmployee\":[\"1000\"],\"teamMemberType\":\"0\",\"teamMemberRole\":\"1\",\"teamMemberPermissionType\":\"2\"}],\"product_line\":null,\"unit\":\"604b28ec1c08f0000175fa8f\",\"data_own_department\":[\"999998\"],\"name\":\"带规格的商品9#spec9\",\"_id\":\"617cfaacb83ad600018efb54\",\"data_own_department__l\":[{\"parentId\":\"999999\",\"deptId\":\"999998\",\"deptName\":\"待分配\",\"status\":0}],\"tenant_id\":\"81961\",\"product_status\":\"1\",\"remark\":null,\"replacement_period\":null,\"product_code\":\"CH546z\",\"lock_user\":null,\"on_shelves_time\":1635580589149,\"safety_stock\":null,\"is_deleted\":false,\"object_describe_api_name\":\"ProductObj\",\"owner__l\":[{\"id\":\"1000\",\"tenantId\":\"81961\",\"name\":\"zsl\",\"picAddr\":\"\",\"email\":\"\",\"nickname\":\"zsl\",\"phone\":\"\",\"description\":\"\",\"status\":0,\"createTime\":1613787963408,\"modifyTime\":1613788077051,\"dept\":\"999998\",\"post\":\"\"}],\"out_owner\":null,\"relevant_team__r\":\"zsl\",\"spu_id__r\":\"带规格的商品9\",\"owner__r\":{\"picAddr\":\"\",\"description\":\"\",\"dept\":\"999998\",\"supervisorId\":null,\"title\":null,\"modifyTime\":1613788077051,\"post\":\"\",\"createTime\":1613787963408,\"phone\":\"\",\"name\":\"zsl\",\"nickname\":\"zsl\",\"tenantId\":\"81961\",\"id\":\"1000\",\"position\":null,\"email\":\"\",\"status\":0},\"owner\":[\"1000\"],\"picture_path\":null,\"is_package\":false,\"last_modified_time\":1635580589217,\"life_status\":\"normal\",\"last_modified_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"created_by__l\":[{\"id\":\"-10000\",\"name\":\"系统\"}],\"last_modified_by\":[\"-10000\"],\"data_right_flag\":\"write\",\"technical_parameter\":null,\"out_tenant_id\":null,\"record_type\":\"default__c\",\"field_p8uU2__c\":null,\"last_modified_by__r\":{\"picAddr\":null,\"description\":null,\"dept\":null,\"supervisorId\":null,\"title\":null,\"modifyTime\":null,\"post\":null,\"createTime\":null,\"phone\":null,\"name\":\"系统\",\"nickname\":null,\"tenantId\":null,\"id\":\"-10000\",\"position\":null,\"email\":null,\"status\":null},\"product_spec\":\"带规格的商品9:spec9\",\"max_stock\":null,\"order_by\":null,\"category\":\"19\",\"带规格的商品9\":\"617cfaac65f7f70001526e11\",\"nameIsFrozen\":true,\"is_removed\":false,\"status_flag\":1}],\"multi_unit_data\":[{\"tenant_id\":\"81961\",\"is_enable\":true,\"is_base\":true,\"extend_obj_data_id\":null,\"is_deleted\":false,\"is_pricing\":true,\"price\":\"1.00\",\"product_id\":null,\"object_describe_api_name\":null,\"owner_department\":null,\"out_owner\":null,\"id\":\"617cfaad65f7f70001526ed1\",\"spu_id\":\"617cfaac65f7f70001526e98\",\"unit_id\":\"604b28ec1c08f0000175fa8f\",\"places_decimal\":null,\"barcode\":null,\"owner\":null,\"package\":null,\"last_modified_time\":null,\"create_time\":null,\"is_editable\":true,\"last_modified_by\":null,\"out_tenant_id\":null,\"version\":null,\"created_by\":null,\"record_type\":null,\"data_own_department\":null,\"name\":null,\"conversion_ratio\":\"1.0000\",\"order_by\":null,\"_id\":\"617cfaad65f7f70001526ed1\"}]}}";
        ObjectData objectData=JSONObject.parseObject(json,ObjectData.class);
        HeaderObj headerObj = new HeaderObj(Integer.valueOf("81961"), CrmConstants.SYSTEM_USER);
        Result<ActionEditResult> actionEditResultResult = syncSkuSpuService.updateCrmObjectData(headerObj,
                ObjectApiNameEnum.FS_SPU.getObjApiName(),
                objectData, null);
        System.out.println(actionEditResultResult);
    }

    @Test
    public void queryBySearchTemplate2() {
        HeaderObj headerObj = new HeaderObj(Integer.valueOf("82777"), CrmConstants.SYSTEM_USER);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilter("warehouse_id",Lists.newArrayList("61275d70df1e130001a3cc05"),"EQ");
        searchTemplateQuery.addFilter("product_id",Lists.newArrayList("6188cf537ca52e0001db5c6c"),"EQ");

        ControllerListArg listArg = new ControllerListArg();

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset(0);
        searchQuery.setLimit(100);
        searchQuery.addFilter("warehouse_id",Lists.newArrayList("61275d70df1e130001a3cc05"),"EQ");
        searchQuery.addFilter("product_id",Lists.newArrayList("6188cf537ca52e0001db5c6c"),"EQ");

        listArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = metadataControllerService.list(headerObj,ObjectApiNameEnum.FS_STOCK.getObjApiName(),listArg);
        System.out.println(result);
    }
}