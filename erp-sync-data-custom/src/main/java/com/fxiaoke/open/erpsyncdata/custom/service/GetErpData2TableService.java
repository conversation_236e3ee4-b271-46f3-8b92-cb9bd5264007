package com.fxiaoke.open.erpsyncdata.custom.service;

import com.fxiaoke.open.erpsyncdata.custom.arg.GetErpData2TableArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * <AUTHOR>
 * @Date: 10:28 2021/2/4
 * @Desc:
 */
public interface GetErpData2TableService {

    /**
     * 仍有调用，如果去除，需要检查哪里函数调用的先去掉。
     */
    Result<String> pushErpData2Table(String tenantId, String userId, GetErpData2TableArg arg);
}
