package com.fxiaoke.open.erpsyncdata.converter.fieldconvert;

import com.google.common.collect.Maps;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class FieldConvertFactory implements ApplicationContextAware {
    private ApplicationContext applicationContext;
    private Map<Integer, FieldConverter> fieldConverterMap = Maps.newHashMap();

    @PostConstruct
    private void init() {
        Map<String, ? extends FieldConverter> map = applicationContext.getBeansOfType(FieldConverter.class);
        fieldConverterMap = map.values().stream().collect(Collectors.toMap(FieldConverter::getFieldMappingType, val -> val));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    public FieldConverter getConverter(Integer fieldMappingType) {
        return fieldConverterMap.get(fieldMappingType);
    }

}
