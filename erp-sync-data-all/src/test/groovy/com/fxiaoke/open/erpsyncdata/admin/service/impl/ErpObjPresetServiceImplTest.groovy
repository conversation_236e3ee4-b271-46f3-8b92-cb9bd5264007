package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.admin.model.InitK3Obj
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjPresetService
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class ErpObjPresetServiceImplTest extends BaseSpockTest {
    @Autowired
    private ErpObjPresetService erpObjPresetPresetService;

    @Test
    void analyzeK3Obj() {
        InitK3Obj.AnalyzeObjArg arg = new InitK3Obj.AnalyzeObjArg()
        arg.setFormId(K3CloudForm.BD_Department)
        arg.setCheckExist(false)
        def result = erpObjPresetPresetService.analyzeK3Obj("89029",
                "64a3c3a5199f940001344d1f",
                arg,
                "en")
        println(result)
    }

    @Test
    void preSetK3Obj() {
        InitK3Obj.PresetObjArg arg = new InitK3Obj.PresetObjArg();
        arg.setErpObjectApiName("SAL_SaleOrder")
        arg.setErpObjectApiNames(new LinkedHashSet<String>())
        arg.getErpObjectApiNames().add("SAL_SaleOrder.SaleOrderEntry")
        def result = erpObjPresetPresetService.preSetK3Obj("88466",
                "64e5cff5360421000104c537",
                arg,
                "en")
        println(result)
    }
}
