package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 数据中心信息
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class DbInfo implements Serializable {
    private static final long serialVersionUID = 7921742292877056213L;
    @ApiModelProperty("数据中心ID")
    private String dbId;
    @ApiModelProperty("数据中心名称")
    private String dbName;
}