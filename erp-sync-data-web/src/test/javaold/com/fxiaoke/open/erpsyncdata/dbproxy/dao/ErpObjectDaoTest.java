package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by fengyh on 2020/8/22.
 */
@Ignore
public class ErpObjectDaoTest extends BaseTest {

    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;

    @Test
    public void insert() {
        ErpObjectEntity record = new ErpObjectEntity();
        record.setId("x000001");
        record.setTenantId("001");
        record.setChannel(ErpChannelEnum.ERP_SAP);
        record.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        record.setErpObjectApiName("FS_FAKEOBJ_1_BD_Customer");
        record.setErpObjectName("客户");
        record.setDeleteStatus(false);
        record.setCreateTime(0L);
        record.setUpdateTime(0L);

        Integer result = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("001")).insert(record);
        System.out.println(result);
    }

    @Test
    public void queryByEi() {
        ErpObjectEntity arg = new ErpObjectEntity();
        arg.setTenantId("79675");
        List<ErpObjectEntity> erpConnectInfoEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).queryList(arg);
        System.out.println(erpConnectInfoEntities);
    }


}
