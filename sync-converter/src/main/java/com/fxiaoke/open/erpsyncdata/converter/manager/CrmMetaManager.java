package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldTypeContants;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/4/24 0:11
 * @Version 1.0
 */
@Component
@Slf4j
public class CrmMetaManager {
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Cached(expire = 10 * 60, cacheType = CacheType.LOCAL)
    public FieldDescribe getMasterDetailField(String tenantId, String objectApiName) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        ObjectDescribe describeResult = objectDescribeService.getDescribe(headerObj, objectApiName).getData().getDescribe();
        for (FieldDescribe fieldDescribe : describeResult.getFields().values()) {
            if (fieldDescribe.getType().equals(FieldTypeContants.MASTER_DETAIL) && fieldDescribe.getIsActive()) {
                return fieldDescribe;
            }
        }
        return null;
    }

    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    public ObjectDescribe getObjectDescribe(String tenantId, String objectApiName) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        return objectDescribeService.getDescribe(headerObj, objectApiName).getData().getDescribe();
    }

    @Cached(expire = 60, cacheType = CacheType.LOCAL)
    public Map<String, Object> getObjectDescribeAsMap(String tenantId, String objectApiName) {
        HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
        final Map<String, Object> data = objectDescribeService.getDescribeAsMap(headerObj, objectApiName).getData();
        if (MapUtils.isEmpty(data) || !data.containsKey("describe")) {
            return new HashMap<>();
        }
        return (Map<String, Object>) data.get("describe");
    }
}
