package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import com.fxiaoke.dispatcher.common.MessageField;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 15:27 2021/7/23
 * @Desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class InterfaceMonitorData {

    /**
     * id
     */
    @MessageField(name = "_id")
    private ObjectId id;

    /**
     * 企业id
     */
    @MessageField(name = "tenant_id")
    private String tenantId;

    /**
     * 数据中心id
     */
    @MessageField(name = "dc_id")
    private String dcId;

    /**
     * 对象apiName
     */
    @MessageField(name = "obj_api_name")
    private String objApiName;

    /**
     * 接口类型
     * @see ErpObjInterfaceUrlEnum
     */
    @MessageField(name = "type")
    private String type;
    /**
     * 入参
     */
    @MessageField(name = "arg")
    private String arg;
    /**
     * 结果数据
     */
    @MessageField(name = "result")
    private String result;

    /**
     * 调用状态(1.成功 2.失败)
     */
    @MessageField(name = "status")
    private Integer status;
    /**
     * 调用时间
     */
    @MessageField(name = "call_time")
    private Long callTime;
    /**
     * 返回时间
     */
    @MessageField(name = "return_time")
    private Long returnTime;
    /**
     * remark
     */
    @MessageField(name = "remark")
    private String remark;
    /**
     * traceId
     */
    @MessageField(name = "trace_id")
    @Builder.Default
    private String traceId = TraceUtil.get();
    /**
     * 花费时间
     */
    @MessageField(name = "cost_time")
    private Long costTime;
    /**
     * 创建时间
     */
    @MessageField(name = "create_time")
    private Long createTime;
    /**
     * 过期时间,保存的new Date()是UTC：世界标准时间，与东八区差了8个小时
     */
    @MessageField(name = "expire_time")
    private Date expireTime;
    /**
     * 同步记录Id
     */
    @MessageField(name = "sync_data_id")
    private String syncDataId;

    /**
     * logId
     */
    @MessageField(name = "log_id")
    @Builder.Default
    private String logId = LogIdUtil.get();


    /**
     * 时间筛选参数
     */
    private TimeFilterArg timeFilterArg;

}
