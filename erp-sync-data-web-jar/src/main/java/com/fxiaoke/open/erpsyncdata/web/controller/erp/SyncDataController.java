package com.fxiaoke.open.erpsyncdata.web.controller.erp;

import com.fxiaoke.open.erpsyncdata.admin.arg.ListSyncDataHistoryArg;
import com.fxiaoke.open.erpsyncdata.admin.model.FsSearchKnowledge;
import com.fxiaoke.open.erpsyncdata.admin.remote.KnowledgeManager;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataHistoryListResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataService;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.List;

@Slf4j
@Api(tags = "数据同步历史相关接口")
@RestController("erpSyncDataController")
@RequestMapping("cep/syncData")
public class SyncDataController extends AsyncSupportController {

    @Autowired
    private AdminSyncDataService adminSyncDataService;
    @Autowired
    private KnowledgeManager knowledgeManager;
    @Autowired
    private I18NStringManager i18NStringManager;

    @ApiOperation(value = "获取单条数据的同步历史")
    @PostMapping(value = "/listSyncDataHistory")
    public Result<List<SyncDataHistoryListResult>> listSyncDataHistory(@RequestBody ListSyncDataHistoryArg arg,
                                                                       @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return adminSyncDataService.listSyncDataHistory(getLoginUserTenantId(), arg,lang);
    }
    @ApiOperation(value = "帮我找数据")
    @PostMapping(value = "/listSyncDataHistoryBySource")
    public Result<List<SyncDataHistoryListResult>> listSyncDataHistoryBySource(@RequestBody ListSyncDataHistoryArg arg,
                                                                 @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        return adminSyncDataService.listSyncDataHistoryBySource(getLoginUserTenantId(), arg,lang);
    }

    @ApiOperation(value = "获取更多纷享知识库建议")
    @PostMapping(value = "/fsSearchKnowledge")
    public DeferredResult<Result<FsSearchKnowledge.Result>> fsSearchKnowledge(@RequestBody FsSearchKnowledge.Arg arg,
                                                                              @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang) {
        Result<FsSearchKnowledge.Result> timeoutResult = Result.newError(ResultCodeEnum.KNOWLEDGE_LOADING);
        String actionName = i18NStringManager.get(I18NStringEnum.s85,lang,getLoginUserTenantId());
        return asyncExecuteByCustomerConsumer(() -> fsSearchKnowledgeResult(arg), 10, false, actionName, generateConsumer(actionName, false,lang), timeoutResult,lang);
    }
    private Result<FsSearchKnowledge.Result> fsSearchKnowledgeResult(FsSearchKnowledge.Arg arg){
        if (StringUtils.isBlank(arg.getKeyword())) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        return Result.newSuccess(new FsSearchKnowledge.Result(knowledgeManager.searchKnowledge(arg.getKeyword(), arg.getSize())));
    }
}
