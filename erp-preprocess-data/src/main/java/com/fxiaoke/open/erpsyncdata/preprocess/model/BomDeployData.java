package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:26 2020/12/9
 * @Desc:
 */
@Data
public class BomDeployData implements Serializable {
    @SerializedName("deletedBomAndGroupList")
    private List<ObjectData> deletedBomAndGroupList;
    @SerializedName("nodeList")
    private List<ObjectData> nodeList;
    @SerializedName("rootProductId")
    private String rootProductId;
}
