package com.fxiaoke.open.erpsyncdata.dbproxy.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用来指定entity中哪个字段是dataId字段
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.PARAMETER})
public @interface DataId {
    /**
     * dataId字段名
     */
    String value() default "";
}
