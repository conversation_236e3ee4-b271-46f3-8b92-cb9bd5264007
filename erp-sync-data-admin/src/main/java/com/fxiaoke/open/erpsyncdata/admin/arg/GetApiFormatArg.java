package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.common.i18n.I18NModel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 17:56 2022/10/9
 * @Desc:
 */
@Data
@ApiModel
public class GetApiFormatArg extends I18NModel {
    @ApiModelProperty("数据id")
    private String id;
    @ApiModelProperty("渠道")
    private ErpChannelEnum channel;
    @ApiModelProperty("真实对象名称")
    private String erpObjectName;
    @ApiModelProperty("真实对象apiName")
    private String erpObjectApiName;
    @ApiModelProperty("拆分批次")
    public Integer splitSeq;

    @ApiModelProperty("虚拟对象apiName")
    private String splitObjectApiName;

}
