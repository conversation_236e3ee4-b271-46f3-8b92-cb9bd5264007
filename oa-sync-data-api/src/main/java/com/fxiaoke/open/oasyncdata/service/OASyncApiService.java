package com.fxiaoke.open.oasyncdata.service;


import com.fxiaoke.open.oasyncdata.constant.OABusinessTypeEnum;
import com.fxiaoke.open.oasyncdata.model.OAMessageTypeVO;
import com.fxiaoke.open.oasyncdata.model.OASyncApiSettingVo;
import com.fxiaoke.open.oasyncdata.model.OASyncApiVO;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/3/18
 */
public interface OASyncApiService {
    Result<List<OASyncApiVO>> getOASyncApiList(String tenantId, List<String> eventTypes,List<String> objApiName,String dataCenterId);

    Result<Map<String,Integer>> getOpenApiStatus(String tenantId,String dataCenterId);


    Result<String> updateOASyncApi(List<OASyncApiVO> oaSyncApiVOList, String tenantId,String dataCenterId);

    Result<Integer> updateOaApiStatus(OASyncApiSettingVo settingVo, String tenantId,String dataCenterId);

    Result<Integer> deleteOaEvent(String tenantId, List<String> apiIds,String dataCenterId);

    Result<Void> updateTodoCustomFunc(String tenantId, String aplApiName,String dataCenterId);


    Result<List<OAMessageTypeVO>> getBusinessType(String tenantId, String businessType,String dataCenterId,String lang);

}
