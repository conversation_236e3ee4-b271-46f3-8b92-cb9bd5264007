- name: 更新-返回id
  config:
    querySql: |
      #YAML
      updateSql: |
        UPDATE customers SET address=${address} WHERE id=${id}::INTEGER;
  detailConfig: []
  resultId: null

- name: 更新-查找id
  config:
    querySql: |
      #YAML
      updateSql: |
        UPDATE customers SET address=${address} WHERE id=${id}::INTEGER;
      queryIdSql:
        select id from customers where name = ${name}
  detailConfig: []
  resultId: 594

