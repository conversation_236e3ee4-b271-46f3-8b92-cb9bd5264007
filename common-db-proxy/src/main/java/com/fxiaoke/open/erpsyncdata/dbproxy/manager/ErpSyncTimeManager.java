package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import cn.hutool.core.date.DateTime;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PollingIntervalDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.PollingIntervalUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.CronPatternBuilder;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.cron.pattern.Part;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DayLimitType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ExtraEventTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.IntervalTimeUnitEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.PollingIntervalApiDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/8
 */
@Service
@Slf4j
public class ErpSyncTimeManager {
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;


    public String transAndUpdateDayCron(String tenantId, String sourceObjectApiName, PollingIntervalDto entityDto, PollingIntervalApiDto apiDto, String syncTimeId) {
        transDayCron(tenantId, sourceObjectApiName, apiDto);
        if (apiDto.getCronExpression() != null) {
            entityDto.setCronExpression(apiDto.getCronExpression());
            int i = erpSyncTimeDao.setGlobalTenant(tenantId).updatePollingInterval(tenantId, syncTimeId, GsonUtil.toJson(entityDto));
            log.info("update polling interval,{}", i);
            return apiDto.getCronExpression();
        } else {
            log.error("no trans,entityDto:{}", entityDto);
            return configCenterConfig.getCronBeginMinute(tenantId, sourceObjectApiName) + "/6 * * *";
        }
    }

    public void transDayCron(String tenantId, String sourceObjectApiName, PollingIntervalApiDto pollingInterval) {
        if (pollingInterval != null
                && DayLimitType.EVERY_DAY.equals(pollingInterval.getDayLimitType())
                && IntervalTimeUnitEnum.days.equals(pollingInterval.getTimeUnit())) {
            pollingInterval.setDayLimitType(DayLimitType.CRON);
            CronPatternBuilder builder = CronPatternBuilder.of();
            Long lastSyncTime = System.currentTimeMillis();
            List<ErpSyncTimeEntity> erpSyncTimeEntities = erpSyncTimeDao.setGlobalTenant(tenantId).listByTenantIdAndObjectApiName(tenantId, sourceObjectApiName);
            if (!erpSyncTimeEntities.isEmpty()) {
                Optional<ErpSyncTimeEntity> max = erpSyncTimeEntities.stream().max(Comparator.comparing(v -> v.getOperationType()));
                //取上次同步时间作为同步起始时间，取不到则会取当前时间。
                lastSyncTime = max.get().getLastSyncTime();
            }
            DateTime dateTime = DateTime.of(lastSyncTime);
            Integer interval = pollingInterval.getIntervalQuantity();
            builder.setValues(Part.HOUR, dateTime.hour(true));
            builder.setValues(Part.MINUTE, dateTime.minute());
            if (interval >= 360) {
                //大于等于360天的，固定日期时间执行
                builder.setValues(Part.MONTH, dateTime.monthBaseOne());
                builder.setValues(Part.DAY_OF_MONTH, dateTime.dayOfMonth());
            } else if (interval >= 30) {
                //大于等于30天，从1月按月间隔，固定日时间执行
                int monthInterval = interval / 30;
                builder.setLoop(Part.MONTH, 1, monthInterval);
                builder.setValues(Part.DAY_OF_MONTH, dateTime.dayOfMonth());
            } else if (interval > 1) {
                //小于30天大于1天，每月从1号按天间隔 固定时间执行
                builder.setLoop(Part.DAY_OF_MONTH, 1, interval);
            } else if (interval == 1) {
                //每天执行的，换成容易理解的每天执行一次 设置
                pollingInterval.setDayLimitType(DayLimitType.EVERY_DAY);
                pollingInterval.setTimeUnit(IntervalTimeUnitEnum.once);
                pollingInterval.setStartDataTime(dateTime.toString("HH:mm"));
            }
            pollingInterval.setCronExpression(builder.build());
        }
    }

    public PollingIntervalApiDto getApiDto(String tenantId,String objApiName){
        List<ErpSyncTimeEntity> syncTimes = erpSyncTimeDao.setGlobalTenant(tenantId).listByTenantIdAndObjectApiName(tenantId, objApiName);
        PollingIntervalApiDto result;
        if (syncTimes.isEmpty()){
            //默认值
            result =  new PollingIntervalApiDto();
            result.setTimeUnit(IntervalTimeUnitEnum.minutes);
            result.setIntervalQuantity(15);
            if(objApiName.contains("SAL_MATERIALGROUP")) {
                result.setIntervalQuantity(30);
            }
            result.setStartDataTime("00:00");
            result.setEndDataTime("23:59");
        }else {
            //取任意一个
            ErpSyncTimeEntity erpSyncTimeEntity = syncTimes.get(0);
            PollingIntervalDto dto = JacksonUtil.fromJson(erpSyncTimeEntity.getPollingInterval(),PollingIntervalDto.class);
            result = PollingIntervalUtil.dto2ApiDto(dto);
        }
        transDayCron(tenantId,objApiName,result);
        return result;
    }

    public Long getMinQueryMongoTime(String tenantId, String objApiName) {
        final List<ErpSyncTimeEntity> erpSyncTimeEntities = erpSyncTimeDao.setGlobalTenant(tenantId).listByTenantIdAndObjectApiName(tenantId, objApiName);
        if (CollectionUtils.isEmpty(erpSyncTimeEntities) || erpSyncTimeEntities.size() < 2) {
            return Optional.ofNullable(erpSyncTimeEntities)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(entity -> ExtraEventTypeEnum.permittedType.contains(entity.getOperationType()))
                    .findFirst()
                    .map(ErpSyncTimeEntity::getLastQueryMongoTime)
                    .orElse(null);
        }

        final Map<Integer, Long> collect = erpSyncTimeEntities.stream()
                .filter(entity -> ExtraEventTypeEnum.permittedType.contains(entity.getOperationType())
                        && Objects.nonNull(entity.getLastQueryMongoTime()))
                .collect(Collectors.toMap(
//                        可能为 ExtraEventTypeEnum.permittedType, %100 去重,都转为1,2,3
                        entity -> entity.getOperationType() % 100,
                        ErpSyncTimeEntity::getLastQueryMongoTime,
                        Math::min));
        Long addOrUpdateTime = collect.get(EventTypeEnum.UPDATE.getType());
        if (Objects.isNull(addOrUpdateTime)) {
            addOrUpdateTime = collect.get(EventTypeEnum.ADD.getType());
        }

        Long invalidTime = collect.get(EventTypeEnum.INVALID.getType());
        Long deleteTime = collect.get(EventTypeEnum.DELETE_DIRECT.getType());
        return Stream.of(invalidTime,deleteTime, addOrUpdateTime)
                .filter(Objects::nonNull)
                .min(Long::compare)
                .orElse(null);
    }
}

