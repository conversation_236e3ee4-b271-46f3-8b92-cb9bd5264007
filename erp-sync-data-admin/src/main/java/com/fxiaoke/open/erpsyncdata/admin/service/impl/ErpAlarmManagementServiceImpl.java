package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.qixin.api.model.message.content.TextInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetAlarmRecordArg;
import com.fxiaoke.open.erpsyncdata.admin.model.AlarmRuleListModel;
import com.fxiaoke.open.erpsyncdata.admin.model.GetAlarmRuleModel;
import com.fxiaoke.open.erpsyncdata.admin.model.QiXinTextInfo;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpAlarmManagementService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpAlarmRuleEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataIntegrationNotificationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpAlarmRuleManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataCenterModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.DataIntegrationNotificationModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.IntegrationStreamModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.PloyDetailModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.EmployeeProviderServiceUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.UserRoleUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.paasauthrestapi.service.PaasAuthService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ErpAlarmManagementServiceImpl implements ErpAlarmManagementService {
    @Autowired
    private ErpAlarmRuleManager erpAlarmRuleManager;
    @Autowired
    private DataIntegrationNotificationManager dataIntegrationNotificationManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private PaasAuthService paasAuthService;

    @Override
    public Result<Void> saveAlarmRule(ErpAlarmRuleEntity entity) {
        log.info("ErpAlarmManagementServiceImpl.saveAlarmRule,entity={}",entity);
        int count = 0;
        if(StringUtils.isEmpty(entity.getId())) {
            List<ErpAlarmRuleEntity> alarmRuleEntityList = erpAlarmRuleManager.findData(entity.getTenantId(),
                    entity.getDataCenterId(),
                    AlarmRuleType.CUSTOM);
            if (CollectionUtils.isNotEmpty(alarmRuleEntityList) && alarmRuleEntityList.size() >= 10) {
                return Result.newError(ResultCodeEnum.CUSTOM_ALARM_RULE_EXCEED_MAX_COUNT);
            }
            count = erpAlarmRuleManager.insert(entity);
        } else {
            entity.setUpdateTime(System.currentTimeMillis());
            count = erpAlarmRuleManager.update(entity);
        }

        return count == 1 ? Result.newSuccess() : Result.newError(ResultCodeEnum.SYSTEM_ERROR);
    }

    @Override
    public Result<Void> deleteAlarmRule(String tenantId, String id) {
        int count = erpAlarmRuleManager.delete(tenantId, id);
        return count == 1 ? Result.newSuccess() : Result.newError(ResultCodeEnum.SYSTEM_ERROR);
    }

    @Override
    public Result<AlarmRuleListModel> getAlarmRuleList(String tenantId, String dataCenterId, Integer pageSize, Integer pageNumber, String lang) {
        erpAlarmRuleManager.checkAndInitAlarmRuleData(tenantId, lang);

        List<ErpAlarmRuleEntity> alarmRuleEntityList = erpAlarmRuleManager.findData(tenantId, dataCenterId, pageSize, pageNumber * pageSize);

        List<GetAlarmRuleModel> list = new ArrayList<>();
        for (ErpAlarmRuleEntity entity : alarmRuleEntityList) {
            GetAlarmRuleModel model = new GetAlarmRuleModel();
            BeanUtils.copyProperties(entity, model);

            ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .findById(entity.getDataCenterId());
            if (connectInfoEntity == null) continue;
            model.setDataCenterName(connectInfoEntity.getDataCenterName());
            model.setAlarmCondition(model.getAlarmType().getCondition(i18NStringManager, lang, tenantId,model.getThreshold()+""));

            //组装集成流数据
            List<String> ployDetailIdList = Splitter.on(";").splitToList(entity.getPloyDetailIds());
            List<SyncPloyDetailEntity> ployDetailEntityList = null;
            if (CollectionUtils.isNotEmpty(ployDetailIdList)) {
                if (ployDetailIdList.contains("all")) {
                    model.setApplyAll(true);
                    ployDetailEntityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .queryByDcId(entity.getTenantId(),entity.getDataCenterId(),null);
                } else {
                    ployDetailEntityList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .listByIds(ployDetailIdList);
                }
            }
            if (CollectionUtils.isNotEmpty(ployDetailEntityList)) {
                ployDetailEntityList = ployDetailEntityList.stream()
                        .filter(ployDetailEntity -> StringUtils.isNotEmpty(ployDetailEntity.getIntegrationStreamName()))
                        .collect(Collectors.toList());
                List<PloyDetailModel> ployDetailModelList = ployDetailEntityList.stream()
                        .map(ployDetailEntity -> new PloyDetailModel(ployDetailEntity.getId(), ployDetailEntity.getIntegrationStreamName()))
                        .collect(Collectors.toList());
                model.setApplyPloyDetailList(ployDetailModelList);

                List<String> ployDetailIdList2 = ployDetailEntityList.stream()
                        .map(SyncPloyDetailEntity::getId)
                        .collect(Collectors.toList());
                List<PloyDetailModel> ployDetailListInProgress = dataIntegrationNotificationManager.getPloyDetailListInProgress(entity.getTenantId(),
                        entity.getDataCenterId(),
                        ployDetailIdList2,
                        entity.getAlarmType());
                model.setPloyDetailListInProgress(ployDetailListInProgress);

                int inProgress = CollectionUtils.isNotEmpty(model.getPloyDetailListInProgress()) ? model.getPloyDetailListInProgress().size() : 0;
                int total = ployDetailEntityList.size();
                model.setAlarmInProgressAndAll(inProgress + "/" + total);
            } else {
                if(!model.isApplyAll()) {
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(entity.getNotifyType())) {
                List<NotifyType> notifyType = entity.getNotifyType().stream().map(str-> NotifyType.valueOf(str)).collect(Collectors.toList());
                model.setNotifyType(notifyType);
            }
            if(entity.getAlarmSetting()!=null){
                model.setIsAlarmPollingNotFirstPageError(entity.getAlarmSetting().getIsAlarmPollingNotFirstPageError());
            }
            if (StringUtils.isNotEmpty(entity.getUserIds())) {
                List<String> userIdList = Splitter.on(";").splitToList(entity.getUserIds());
                model.setUserList(EmployeeProviderServiceUtils.batchGetEmployeeDto2(tenantId, userIdList, employeeProviderService));
            }

            if (StringUtils.isNotEmpty(entity.getRoleIds())) {
                List<String> roleIdList = Splitter.on(";").splitToList(entity.getRoleIds());
                model.setRoleList(UserRoleUtils.getRoleModelList(tenantId, roleIdList, paasAuthService));
            }

            list.add(model);
        }

        int totalCount = erpAlarmRuleManager.count(tenantId,dataCenterId,null,null);

        AlarmRuleListModel model = new AlarmRuleListModel();
        model.setTotalCount(totalCount);
        model.setDataList(list);
        return Result.newSuccess(model);
    }

    @Override
    public Result<List<DataCenterModel>> getAlarmRuleDcList(String tenantId, String lang) {
        List<DataCenterModel> dcList = new ArrayList<>();
        //dcList.add(new DataCenterModel("all", i18NStringManager.get(I18NStringEnum.s2260, lang, tenantId)));

        List<ErpConnectInfoEntity> entityList = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listByTenantId(tenantId);
        for (ErpConnectInfoEntity entity : entityList) {
            if(entity.getChannel()== ErpChannelEnum.CRM) continue;
            if(entity.getChannel().getConnectorType()== ConnectorTypeEnum.OA) continue;
            if(StringUtils.isEmpty(entity.getConnectParams())) continue;
            dcList.add(new DataCenterModel(entity.getId(), entity.getDataCenterName()));
        }
        return Result.newSuccess(dcList);
    }

    @Override
    public Result<List<IntegrationStreamModel>> getIntegrationStreamList(String tenantId, String dataCenterId, String lang) {
        List<DataCenterModel> dcList = new ArrayList<>();
        dcList.add(new DataCenterModel("all", i18NStringManager.get(I18NStringEnum.s2260, lang, tenantId)));

        List<SyncPloyDetailEntity> enablePloyDetailList = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryByDcId(tenantId, dataCenterId, null);

        List<IntegrationStreamModel> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(enablePloyDetailList)) {
            for(SyncPloyDetailEntity entity : enablePloyDetailList) {
                if(StringUtils.isEmpty(entity.getIntegrationStreamName())) continue;

                list.add(new IntegrationStreamModel(entity.getId(),
                        entity.getIntegrationStreamName(),
                        entity.getStatus()==SyncPloyDetailStatusEnum.DISABLE.getStatus()));
            }
        }

        return Result.newSuccess(list);
    }

    @Override
    public Result<List<DataCenterModel>> getAlarmRecordDcList(String tenantId, String lang) {
        return Result.newSuccess(dataIntegrationNotificationManager.getDcList(tenantId));
    }

    @Override
    public Result<DataIntegrationNotificationModel> getAlarmRecordList(GetAlarmRecordArg arg) {
        Date startTime = null;
        Date endTime = null;
        if (arg.getStartTime() != null) {
            startTime = new Date(arg.getStartTime());
        }
        if (arg.getEndTime() != null) {
            endTime = new Date(arg.getEndTime());
        }

        //前端无法识别企信通知的模式
        DataIntegrationNotificationModel dataListByPage = dataIntegrationNotificationManager.getDataListByPage(arg.getTenantId(),
                arg.getDataCenterId(),
                arg.getPloyDetailIdList(),
                null,
                null,
                arg.getAlarmType(),
                arg.getAlarmLevel(),
                startTime,
                endTime,
                arg.getPageSize(),
                arg.getPageNumber());

        if(ObjectUtils.isNotEmpty(dataListByPage) && CollectionUtils.isNotEmpty(dataListByPage.getDataList())) {
            for(DataIntegrationNotificationModel.Entry entry : dataListByPage.getDataList()) {
                if(StringUtils.isNotEmpty(entry.getMsg())) {
                    //判断是否是企信通知的模式
                    QiXinTextInfo qiXinTextInfo;
                    try {
                        qiXinTextInfo = JacksonUtil.fromJson(entry.getMsg(), new TypeReference<QiXinTextInfo>() {
                        });
                    } catch (Exception e) {
                        qiXinTextInfo = null;
                    }

                    if(ObjectUtils.isNotEmpty(qiXinTextInfo) && CollectionUtils.isNotEmpty(qiXinTextInfo.getTextInfoList())) {
                        StringBuilder dataBuilder = new StringBuilder();
                        //处理数据
                        for(TextInfo textInfo : qiXinTextInfo.getTextInfoList()) {
                            if(textInfo.getActionType() == TextInfo.None) {
                                //文本
                                dataBuilder.append(textInfo.getContent());
                                if(textInfo.isNewLine() == Boolean.TRUE) {
                                    dataBuilder.append(" <br/> ");
                                }
                            }
                        }
                        if(StringUtils.isNotEmpty(dataBuilder)) {
                            entry.setMsg(dataBuilder.toString());
                        }
                    }
                }
            }
        }
        return Result.newSuccess(dataListByPage);
    }
}
