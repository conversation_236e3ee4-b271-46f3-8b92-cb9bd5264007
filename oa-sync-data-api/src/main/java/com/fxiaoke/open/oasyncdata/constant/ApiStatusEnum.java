package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * crm对象
 */
@Getter
@ToString
@AllArgsConstructor
public enum ApiStatusEnum {
    OPEN("1", "开启", I18NStringEnum.s863.getI18nKey()),
    CLOSE("2", "关闭", I18NStringEnum.s1212.getI18nKey()),

    ;
    /**
     * oa配置状态
     */
    private final String status;
    /**
     * oa配置状态描述
     */
    private final String desc;
    private final String i18nKey;
}
