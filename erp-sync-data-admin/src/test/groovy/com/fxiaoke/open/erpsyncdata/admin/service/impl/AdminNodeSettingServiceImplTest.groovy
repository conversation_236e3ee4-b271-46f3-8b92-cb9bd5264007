package com.fxiaoke.open.erpsyncdata.admin.service.impl

import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.IntegrationStreamNodesData
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import spock.lang.Specification

class AdminNodeSettingServiceImplTest extends Specification {

    private AdminNodeSettingServiceImpl adminNodeSettingService;

    void setup() {
        adminNodeSettingService = new AdminNodeSettingServiceImpl(
        )
    }

    def "UpdateCheckSyncDataMappingNode"() {
//        UpdateIntegrationStreamArg arg = JacksonUtil.fromJson(jsonArg, UpdateIntegrationStreamArg.class)
//
//        when:
//        def node = adminNodeSettingService.updateCheckSyncDataMappingNode(arg)
//
//
//        then:
//        (node == null && result == null) || (JacksonUtil.toJson(node).equals(JacksonUtil.toJson(JacksonUtil.fromJson(result, IntegrationStreamNodesData.CheckSyncDataMappingNode.class))))
//
//        where:
//        result | jsonArg
//        null   | "{}"
//        "{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectApiName\":\"SalesOrderObj\",\"queryFieldMappings\":[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}],\"detailCheckSyncDataMappingData\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"SalesOrderProductObj\",\"queryFieldMappings\":[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}]}]}"     | "{\"checkSyncDataMappingNode\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destObjectApiName\":\"SalesOrderObj\",\"queryFieldMappings\":[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}],\"detailCheckSyncDataMappingData\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destObjectApiName\":\"SalesOrderProductObj\",\"queryFieldMappings\":[{\"destApiName\":\"name\",\"sourceApiName\":\"FName\",\"filterOperator\":\"EQ\"}]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}]}]}]}}"
//


    }
}
