package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.preprocess.arg.BaseArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.QueryFieldDataBindingArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.QueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.SpecialFieldMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 13:55 2020/8/19
 * @Desc:
 */
public interface SpecialFieldMappingService {
    Result<QueryResult<List<SpecialFieldMappingResult>>> querySpecialFieldMapping(String tenantId, int userId,String dataCenterId, QueryFieldDataBindingArg arg);
    Result<String> updateSpecialFieldMappings(String tenantId,int userId,String dataCenterId,List<SpecialFieldMappingResult> specialFieldMappingResults);
    Result<String> deleteSpecialFieldMapping(String tenantId, int userId, BaseArg deleteArg);
}
