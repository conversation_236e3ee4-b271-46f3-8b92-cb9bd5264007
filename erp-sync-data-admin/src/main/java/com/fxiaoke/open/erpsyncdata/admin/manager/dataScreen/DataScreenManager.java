package com.fxiaoke.open.erpsyncdata.admin.manager.dataScreen;


import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/1/4 16:20
 * 数据大屏
 * @desc
 */
@Slf4j
@Component
public class DataScreenManager {

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    public Result<Object> queryDataScreenByFilter(){
        String baseUrl="http://**************:30240/api/v1/view/data/query";
        Map<String,String> headerMap= Maps.newHashMap();
        headerMap.put("X-fs-Employee-Id","1000");
        headerMap.put("X-fs-Enterprise-Account","88521");
        headerMap.put("X-fs-Enterprise-Id","88521");
        Map<String,Object> bodyMap=Maps.newHashMap();
        bodyMap.put("viewId","BI_6583f2d6b9ec530001685854");

        Object objectResult = proxyHttpClient.postUrl(baseUrl, bodyMap, headerMap, new TypeReference<Object>() {
        });

        return Result.newSuccess();

    }

    public String getStatisticFilter(){

        String baseUrl="http://**************:30240/api/v1/view/getFiltersResult";
        Map<String,String> headerMap= Maps.newHashMap();
        headerMap.put("X-fs-Employee-Id","1000");
        headerMap.put("X-fs-Enterprise-Account","88521");
        headerMap.put("X-fs-Enterprise-Id","88521");
        Map<String,Object> bodyMap=Maps.newHashMap();
        bodyMap.put("viewId","BI_6583f2d6b9ec530001685854");

        Object objectResult = proxyHttpClient.postUrl(baseUrl, bodyMap, headerMap, new TypeReference<Object>() {
        });
        return "";
    }


}
