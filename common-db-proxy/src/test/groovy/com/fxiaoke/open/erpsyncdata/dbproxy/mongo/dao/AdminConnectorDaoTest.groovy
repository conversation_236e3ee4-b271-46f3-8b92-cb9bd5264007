package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao


import com.fxiaoke.open.erpsyncdata.dbproxy.model.AdminConnectorInfo
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.MongoTestUtil
import spock.lang.Specification

class AdminConnectorDaoTest extends Specification {
    AdminConnectorDao dao

    void setup() {
        dao = new AdminConnectorDao(
                store: MongoTestUtil.createStore()
        )
    }

    def "test pageInfo"() {
        dao.batchUpsert([
                new AdminConnectorInfo(
                        connectorId: "111",
                        enterpriseAccount: "111",
                        connectorKey: "A",
                        mongoLastSyncTime: new Date()
                ),
                new AdminConnectorInfo(
                        connectorId: "121",
                        enterpriseAccount: "121",
                        connectorKey: "B",
                        mongoLastSyncTime: new Date()
                ),
                new AdminConnectorInfo(
                        connectorId: "131",
                        enterpriseAccount: "131",
                        connectorKey: "B",
                        mongoLastSyncTime: new Date()
                ),
                new AdminConnectorInfo(
                        connectorId: "141",
                        enterpriseAccount: "141",
                        connectorKey: "C",
                        mongoLastSyncTime: new Date()
                )
        ])
        def filters = ["perPage": "50", "page": "1", "connectorKey": "A,C", "ea": "111,121,131,141,151"]
        def res = dao.pageInfo(filters)
        println(res.size())
        println(res)
        def count = dao.filteredCount(filters)
        expect:
        res.size() == 2
        count == 2
    }
}
