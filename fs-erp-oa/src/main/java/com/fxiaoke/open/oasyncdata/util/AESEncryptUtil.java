package com.fxiaoke.open.oasyncdata.util;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.oasyncdata.result.base.Result;
import com.fxiaoke.open.oasyncdata.result.base.ResultCodeEnum;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

public class AESEncryptUtil {
    public AESEncryptUtil() {
    }

    private static final String sha1(String text) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(text.getBytes());
            byte[] messageDigest = digest.digest();
            StringBuffer stringBuffer = new StringBuffer();

            for(int i = 0; i < messageDigest.length; ++i) {
                String shaHex = Integer.toHexString(messageDigest[i] & 255);
                if (shaHex.length() < 2) {
                    stringBuffer.append(0);
                }

                stringBuffer.append(shaHex);
            }

            return stringBuffer.toString();
        } catch (NoSuchAlgorithmException var6) {
            var6.printStackTrace();
            return "";
        }
    }

    private static String getMd5(String text) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] md5bytes = md5.digest(text.getBytes());
            return bytesToHex(md5bytes);
        } catch (NoSuchAlgorithmException var3) {
            var3.printStackTrace();
            return "";
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuffer md5str = new StringBuffer();

        for(int i = 0; i < bytes.length; ++i) {
            int digital = bytes[i];
            if (digital < 0) {
                digital += 256;
            }

            if (digital < 16) {
                md5str.append("0");
            }

            md5str.append(Integer.toHexString(digital));
        }

        return md5str.toString();
    }

    private static String aesCBCDecrypt(String appKey, String iv, String text) {
        try {
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes("UTF-8"));
            SecretKeySpec secretKeySpec = new SecretKeySpec(appKey.getBytes("UTF-8"), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(2, secretKeySpec, ivParameterSpec);
            byte[] original = cipher.doFinal((new Base64()).decode(text));
            return new String(original);
        } catch (NoSuchAlgorithmException var7) {
            var7.printStackTrace();
        } catch (InvalidKeyException var8) {
            var8.printStackTrace();
        } catch (InvalidAlgorithmParameterException var9) {
            var9.printStackTrace();
        } catch (NoSuchPaddingException var10) {
            var10.printStackTrace();
        } catch (BadPaddingException var11) {
            var11.printStackTrace();
        } catch (UnsupportedEncodingException var12) {
            var12.printStackTrace();
        } catch (IllegalBlockSizeException var13) {
            var13.printStackTrace();
        } catch (IOException var14) {
            var14.printStackTrace();
        }

        return null;
    }

    private static String getCurrentTimeStamp() {
        String timeStamp = System.currentTimeMillis() + "";
        return timeStamp.substring(0, 10);
    }

    private static String sort(String[] arr) {
        Arrays.sort(arr);
        StringBuilder builder = new StringBuilder();
        String[] var2 = arr;
        int var3 = arr.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            String s = var2[var4];
            builder.append(s);
        }

        return builder.toString();
    }

    public static Result<String> decrypt(String appkey, String token, String signature, String timeStamp, String nonce, String text) {
        timeStamp = timeStamp == null ? getCurrentTimeStamp() : timeStamp;
        String[] arr = new String[]{token, timeStamp, nonce, text};
        String sortedString = sort(arr);
        String sha1String = sha1(sortedString);
        if (!sha1String.equals(signature)) {
            return Result.newError(ResultCodeEnum.DECRYPT_ERROR);
        } else {
            String iv = getMd5(appkey).substring(0, 16);
            String decryptedText = aesCBCDecrypt(appkey, iv, text);
            return Result.newSuccess(decryptedText);
        }
    }

//    public static void main(String[] args) {
//        String appKey = "pneztreZCTOda7kQc5TW60nfvVCVpuTc";
//        String token = "zt-crm";
//        String signature = "a04cd8ea19d1c27e1a7a87eb5b5dfa0c18f65324";
//        String timestamp = "1625119490";
//        String nonce = "aK9dzlu7";
//        String text = "MCzArqZkbSoDNiX0yB3sdT8ZnEnDcb4JTM/TitGg7GXntqSQOp1whfuR4olc4wdCvQVYge6tG9ACwLJC90zv14lqomxTZI5U8juM4Vu5WZA=";
//
//        Result<String> jsonResult = decrypt(appKey, token, signature, timestamp, nonce, text);
//        if (!jsonResult.isSuccess() || StringUtils.isEmpty(jsonResult.getData())) {
//            System.out.println("解码失败");
//        }
//        System.out.println(jsonResult.getData());
////        https://www.ceshi112.com/erp/syncdata/open/oa/authorizeWeb/ccwork/login/725413/true/zt-crm?appid=7723038393010725&msgSignature=a04cd8ea19d1c27e1a7a87eb5b5dfa0c18f65324&timeStamp=1625119490&nonce=aK9dzlu7&encrypt=MCzArqZkbSoDNiX0yB3sdT8ZnEnDcb4JTM/TitGg7GXntqSQOp1whfuR4olc4wdCvQVYge6tG9ACwLJC90zv14lqomxTZI5U8juM4Vu5WZA=
//    }
}
