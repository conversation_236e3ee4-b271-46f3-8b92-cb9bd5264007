package com.fxiaoke.open.erpsyncdata.writer.manager;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataCenterManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantEnvManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * crm数据循环同步检测
 *
 * <AUTHOR> (^_−)☆
 */
@Service
@Slf4j
public class CrmCycleSyncCheckManager {
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private SyncPloyManager syncPloyManager;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private TenantEnvManager tenantEnvManager;
    @Autowired
    private I18NStringManager i18NStringManager;


    private final static String checkedField = "checked";
    private final static String fistTimeField = "firstTimeField";
    private final static String checkIdField = "checkId";

    /**
     * 检查入口
     * 返回true时缓存一分钟，本地缓存防止对redis缓存高频访问
     *
     * @param tenantId
     * @param objApiName
     * @param dataId
     * @return
     */
    @Cached(name = "crmCycleSyncCheck", key = "#tenantId+'.'+#ployDetailSnapId", postCondition = "#result",
            expire = 1, timeUnit = TimeUnit.MINUTES, cacheType = CacheType.LOCAL)
    public boolean check(String tenantId, String ployDetailSnapId, String objApiName, String dataId) {
        try {
            if (dataId.contains(";")) {
                //如果真有id中包含;的情况，不检查这条数据.
                log.info("crm cycle check,dataId has ;,no check");
                return false;
            }
            String redisKey = String.format(CommonConstant.REDIS_KEY_CRM_CYCLE_SYNC_CHECK, tenantId, ployDetailSnapId);
            if (redisDataSource.get(this.getClass().getSimpleName()).hexists(redisKey, checkedField)) {
                //已检查
                return true;
            }
            String firstTimeStr = redisDataSource.get(this.getClass().getSimpleName()).hget(redisKey, fistTimeField);
            long now = System.currentTimeMillis();
            if (firstTimeStr == null) {
                //首次触发检查
                firstTimeStr = String.valueOf(now);
                //初始记录时间，并设置超时时长
                String luaScript = "local q=redis.call('hset',KEYS[1],ARGV[1],ARGV[2])  if tostring(q) == '1' then redis.call('expireat',KEYS[1],ARGV[3]) return q else return q end";
                //次日九点半到十点有效，
                Long expireAt = DateUtil.beginOfDay(DateUtil.tomorrow())
                        .offset(DateField.HOUR, 9)
                        .offset(DateField.MINUTE, RandomUtil.randomInt(30, 60))
                        .getTime() / 1000;
                Object eval = redisDataSource.get(this.getClass().getSimpleName()).eval(luaScript, 1, redisKey, fistTimeField, firstTimeStr, expireAt.toString());
                log.info("crm cycle check,begin,ei:{},ployDetailSnapId:{},objApiName:{},res:{}", tenantId, ployDetailSnapId, objApiName, eval);
                if (!"1".equals(eval.toString())) {
                    //插入redis失败，下一分钟还会继续检查。
                    log.error("crm cycle check, set redis key failed.");
                    return true;
                }
                redisDataSource.get(this.getClass().getSimpleName()).hset(redisKey, checkIdField, dataId);
                //该id该分钟次数+1
                long miuNow = now / 60000;
                String field = dataId + miuNow;
                Long count = redisDataSource.get(this.getClass().getSimpleName()).hincrBy(redisKey, field, 1L);
                log.info("crm cycle check,first incr count:{}", count);
                return false;
            } else {
                Long firstTime = Long.valueOf(firstTimeStr);
                String checkId = redisDataSource.get(this.getClass().getSimpleName()).hget(redisKey, checkIdField);
                if (checkId != null && !checkId.equals(dataId)) {
                    return false;
                }
                //该id该分钟次数+1
                long miuNow = now / 60000;
                String field = dataId + miuNow;
                Long count = redisDataSource.get(this.getClass().getSimpleName()).hincrBy(redisKey, field, 1L);
                log.info("crm cycle check,count:{}", count);
                if (count == 1) {
                    //新一分钟，检查上一分钟是否存在同步
                    String lastField = dataId + (miuNow - 1);
                    String lastCount = redisDataSource.get(this.getClass().getSimpleName()).hget(redisKey, lastField);
                    if (lastCount == null) {
                        //如果上一分钟没有同步，认为不存在循环同步，不再继续检查。
                        redisDataSource.get(this.getClass().getSimpleName()).hincrBy(redisKey, checkedField, 1);
                        return true;
                    } else if (now - firstTime > 1800000) {
                        //检查超过30分钟数据都在持续同步，认为存在循环同步
                        handleCycleSync(tenantId, ployDetailSnapId, objApiName);
                        redisDataSource.get(this.getClass().getSimpleName()).hincrBy(redisKey, checkedField, 2);
                        return true;
                    }
                }
                return false;
            }
        } catch (Exception e) {
            log.error("check error", e);
            return true;
        }
    }

    private void handleCycleSync(String tenantId, String snapId, String objApiName) {
        log.info("crm cycle check begin handle,tenantId:{},objApiName:{},snapId:{}", tenantId, objApiName, snapId);
        //先发送一个消息，手工停用策略。
        //避免已经停用策略了，但是还不断触发通知
        String url = String.format("%s/erp/syncdatagray/superadmin/breakPloy/%s/%s/%s", ConfigCenter.ERP_DOMAIN_URL, tenantId, snapId, objApiName);
        String msg = i18NStringManager.getByEi2(I18NStringEnum.s956.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s956.getI18nValue(), objApiName, snapId),
                Lists.newArrayList(objApiName, snapId)) + "\n" + i18NStringManager.getByEi(I18NStringEnum.s957,tenantId) + "\n"+url;
        String dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId, snapId);
        SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                .tenantId(tenantId)
                .dcId(dcId)
                .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s955,tenantId))
                .msg(msg)
                .alwaysSendSuperAdmin(true).build();
        notificationService.sendSuperAdminNotice(sendAdminNoticeArg);
    }

    public void breakPloy(String tenantId, String snapId, String objApiName) {
        log.info("crm cycle check begin break ploy,tenantId:{},objApiName:{},snapId:{}", tenantId, objApiName, snapId);
        String msg = i18NStringManager.getByEi2(I18NStringEnum.s958.getI18nKey(),
                tenantId,
                String.format(I18NStringEnum.s958.getI18nValue(), objApiName),
                Lists.newArrayList(objApiName)) +
                "\n" + i18NStringManager.getByEi(I18NStringEnum.s959,tenantId) +
                "\n" + i18NStringManager.getByEi(I18NStringEnum.s960,tenantId) +
                "\n" + i18NStringManager.getByEi(I18NStringEnum.s961,tenantId);

        //熔断
        boolean disablePloyDetail = syncPloyManager.disablePloyDetail(tenantId, snapId, msg);
        log.info("break ploy，ploy disable:{}", disablePloyDetail);
        if (disablePloyDetail) {
            //避免已经停用策略了，但是还不断触发通知
            String dcId = dataCenterManager.getDataCenterBySnapshotId(tenantId, snapId);
            SendAdminNoticeArg sendAdminNoticeArg = SendAdminNoticeArg.builder()
                    .tenantId(tenantId)
                    .dcId(dcId)
                    .msgTitle(i18NStringManager.getByEi(I18NStringEnum.s962,tenantId))
                    .msg(msg)
                    .alwaysSendSuperAdmin(true).build();
            notificationService.sendTenantAdminNotice(sendAdminNoticeArg,
                    AlarmRuleType.GENERAL,
                    AlarmRuleType.GENERAL.getName(i18NStringManager,null,tenantId),
                    AlarmType.INTEGRATION_STREAM_BREAK,
                    AlarmLevel.URGENT);
        }
    }
}
