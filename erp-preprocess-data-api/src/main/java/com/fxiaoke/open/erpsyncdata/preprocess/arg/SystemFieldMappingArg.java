package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class SystemFieldMappingArg extends BaseArg {
    @ApiModelProperty("数据idList")
    public List<String> idList;
    @ApiModelProperty("字段类型枚举")
    public ErpFieldTypeEnum fieldType;
}
