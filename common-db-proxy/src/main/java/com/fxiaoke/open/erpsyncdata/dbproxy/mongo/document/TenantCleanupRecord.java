package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document;

import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.types.ObjectId;

import java.util.Date;

/**
 * 企业账号资源清理记录文档
 * 用于记录需要统计清理的企业账号资源信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@ToString
public class TenantCleanupRecord {

    @BsonId
    private ObjectId id;

    /**
     * 企业ID
     */
    private String tenantId;

    /**
     * 企业名称
     */
    private String tenantName;

    /**
     * 企业负责人
     */
    private String tenantOwner;

    /**
     * 企业级别/等级
     * 可选值：SMALL_MICRO(小微)、IMPORTANT(重要)、VIP、SVIP、TOP100
     * @see com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constants.TenantLevelConstants
     */
    private String tenantLevel;

    /**
     * 清理状态
     * 可选值：PENDING_DELETE(待删除)、DELETED(已删除)
     * @see com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constants.CleanStatusConstants
     */
    private String cleanStatus;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 许可证过期时间
     */
    private Date licenseExpireTime;

    /**
     * 是否已经操作删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注信息
     */
    private String remark;
}
