package com.fxiaoke.open.oasyncdata.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.oasyncdata.db.util.JacksonUtil;
import com.fxiaoke.open.oasyncdata.util.BizLogUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2020/2/18
 **/
@Slf4j
@Service
public class ProxyHttpClient {
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    @Resource(name = "okHttpSupport")
    private OkHttpSupport okHttpSupport;


    private RequestBody createJsonBody(Object params) {
        return params instanceof String
                ? RequestBody.create(JSON_TYPE, params.toString())
                : RequestBody.create(JSON_TYPE, JSON.toJSONString(params));
    }

    public <T> T postUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSON.toJSONString(params);
        return postUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T postUrlSerialNull(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JacksonUtil.toJson(params);
        return postUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T postUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        RequestBody requestBody = this.createJsonBody(paramsJson);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }

    public <T> T putUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        RequestBody requestBody = this.createJsonBody(paramsJson);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).put(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }

    public String putUrlByJson(String url, Object paramsJson, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        RequestBody requestBody = this.createJsonBody(paramsJson);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).put(requestBody).build();
        Object result = null;
        try {
            result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
            throw e;
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result.toString();
    }
    public <T> T deleteUrlByJson(String url,String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("deleteUrlByJson, url={}, params={}, headerMap={}", url, headerMap);
        RequestBody requestBody = this.createJsonBody(paramsJson);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).delete(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }
    public String deleteUrlByJson(String url,Object paramsJson, Map<String, String> headerMap) {
        log.debug("deleteUrlByJson, url={}, params={}, headerMap={}", url, headerMap);
        RequestBody requestBody = this.createJsonBody(paramsJson);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).delete(requestBody).build();
        Object result = null;
        try {
            result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
            throw e;
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result.toString();
    }



    public <T> T getUrl(String url, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        T result = this.okHttpSupport.parseObject(request, typeReference);
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result;
    }

    public String postUrl(String url, Object params, Map<String, String> headerMap) {
        StopWatch stopWatch = StopWatch.create("postOa");
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        RequestBody requestBody = this.createJsonBody(params);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        stopWatch.lap("execute");
        stopWatch.log();
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, params, headerMap, result);
        return result.toString();
    }

    public String postUrlSerialNull(String url, Object params, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        RequestBody requestBody = params instanceof String
                ? RequestBody.create(JSON_TYPE, params.toString())
                : RequestBody.create(JSON_TYPE, JacksonUtil.toJson(params));
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, requestBody, headerMap, result);
        return result.toString();
    }


    public String getUrl(String url, Map<String, String> headerMap) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        Object result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result.toString();
    }

    public String doAndLog(String tenantId, String url, Object params, Map<String, String> headerMap, String requestMode, Supplier<String> supplier){
        String httpResponse="";
        String exceptionType=null,exceptionMsg=null;
        Long callTime=System.currentTimeMillis();
        Integer status=1;
        try{
            httpResponse=supplier.get();
        }catch (Exception e){
            status=2;
            if(e.getClass()!=null){
                exceptionType=e.getClass().toString();
            }
            exceptionMsg=e.getMessage();
            if(e.getCause()!=null){
                exceptionMsg=exceptionMsg+":"+e.getCause().getMessage();
            }
            throw e;
        }finally {
            Long returnTime=System.currentTimeMillis();
            BizLogUtils.sendOAInterfaceMonitorLog(tenantId,url,JacksonUtil.toJson(params),JacksonUtil.toJson(headerMap),requestMode,exceptionType,exceptionMsg,
                    httpResponse,status,callTime,returnTime);
        }
        return httpResponse;
    }

    public static class ProxySyncCallback extends SyncCallback {

        @Override
        public Object response(Response response) throws Exception {
            return response.body() != null ? response.body().string() : "null";
        }

    }
}
