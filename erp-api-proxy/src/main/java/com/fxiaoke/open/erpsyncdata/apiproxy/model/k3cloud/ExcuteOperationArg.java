package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcuteOperationArg {

    @SerializedName("CreateOrgId")
    @JsonProperty("CreateOrgId")
    private Integer createOrgId;

    @SerializedName("Numbers")
    @JsonProperty("Numbers")
    private List<String> numbers;

    /**
     * K3Id，编码和id同时传时会使用id查找
     */
    @SerializedName("Ids")
    @JsonProperty("Ids")
    private String ids;

    @SerializedName("PkEntryIds")
    @JsonProperty("PkEntryIds")
    private String pkEntryIds;

    @SerializedName("NetworkCtrl")
    @JsonProperty("NetworkCtrl")
    private String networkCtrl;

    @Getter
    @Setter
    @ToString
    public static class Result {
        @SerializedName("Result")
        @JsonProperty("Result")
        private ExcuteOperationResult result;
    }

    public static class ExcuteOperationResult extends K3BaseResult{

    }

    public static String checkResult(com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<ExcuteOperationArg.Result> result){
        if (!result.isSuccess()){
            return result.getErrMsg();
        }else {
            ExcuteOperationArg.Result resultData = result.getData();
            if (resultData == null || resultData.getResult().getResponseStatus() == null) {
                return I18NStringManager.getByTraceLang(I18NStringEnum.s3797);
            }else {
                ResponseStatus responseStatus = resultData.getResult().getResponseStatus();
                if (!responseStatus.getIsSuccess()) {
                    return responseStatus.printErrors();
                }
            }
        }
        return null;
    }

}
