package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TaskNumInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2022/7/28 16:36 用来记录历史数据同步的任务编码
 * @Version 1.0
 */
@Slf4j
public class TaskUtil {
    private static final TransmittableThreadLocal<TaskNumInfo> TASK_NUM_HOLDER = new TransmittableThreadLocal<>();
    public static void setTaskNumInfo(TaskNumInfo taskNumInfo) {
        TASK_NUM_HOLDER.set(taskNumInfo);
    }
    public static TaskNumInfo getTaskNumInfo() {
       return TASK_NUM_HOLDER.get();
    }

    /**
     * 在线程结束前调用防止内存泄露
     */
    public static void clear() {
        TASK_NUM_HOLDER.remove();
    }
}
