package com.fxiaoke.open.erpsyncdata.custom.job;

import com.fxiaoke.open.erpsyncdata.custom.service.FeatureSyncService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.base.Splitter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/5/22
 */
//@JobHander(value = "FeatureSyncJobHandler")
@Component
@Slf4j
public class FeatureSyncJobHandler extends IJobHandler {
    @Autowired
    private FeatureSyncService featureSyncService;

    @Override
    public ReturnT<String> execute(TriggerParam triggerParam) throws Exception {
        log.info("execute sync job prepare,triggerParam:{}",triggerParam);
        try{
            String executorParams = triggerParam.getExecutorParams();
            Iterable<String> split = Splitter.on(";").split(executorParams);
            StringBuilder results = new StringBuilder();
            for (String s : split) {
                log.info("execute sync job begin，tenantId：{}",s);
                Result<String> result = featureSyncService.executeTenantFeatureSync(s, "job");
                log.info("execute sync job end，tenantId：{},result:{}",s,result);
                results.append(JacksonUtil.toJson(result));
            }
            return new ReturnT<>(results.toString());
        }catch (Exception e){
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }
}
