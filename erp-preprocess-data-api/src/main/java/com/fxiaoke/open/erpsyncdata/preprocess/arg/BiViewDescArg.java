package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/3/11 17:05
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BiViewDescArg implements Serializable {

    
    private String viewId;

    private int pageNumber=1;

    private int pageSize=200;

    private List<BIFieldDescArg.FilterList> filterList;



}
