package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.preprocess.model.node.NodeContext;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Data
@ApiModel("erp对象数据列表结果")
public class ListErpObjDataResult implements Serializable {
    private static final long serialVersionUID = -7995893623707208968L;
    @ApiModelProperty("是否全部数据")
    private boolean complete = true;
    @ApiModelProperty("erp数据列表")
    private List<SyncDataContextEvent> erpObjDataResultList ;

    /**
     * 下次起始时间
     */
    private Long maxTime;

    /**
     * 下次起始ID
     */
    private String maxId;

    @Override
    public String toString() {
        if (erpObjDataResultList.size()>200){
            return String.format("dataList size:%s,first:%s", erpObjDataResultList.size(), JSON.toJSONString(erpObjDataResultList.get(0)));
        }
        return JSON.toJSONString(this);
    }
}
