package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * crm对象
 */
@Getter
@ToString
@AllArgsConstructor
public enum CrmTypeMessageEnum {
    CRM_TODO_TYPE("crmToDo","待办", I18NStringEnum.s1227.getI18nKey()),

    CRM_NOTIFY("crmNotify" ,"提醒", I18NStringEnum.s1239.getI18nKey()),
    ;
    /**
     * 对象名称
     */
    private final String type;

    /**
     * businessType 描述
     */
    private final String desc;
    private final String i18nKey;

    public String getDesc(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,desc);
    }

    public static CrmTypeMessageEnum getObjApiEnumByBizType(String type) {
        Optional<CrmTypeMessageEnum> optional = Arrays.stream(CrmTypeMessageEnum.values())
                .filter(p -> p.getType().equals(type))
                .findFirst();
        if (optional.isPresent()) {
            CrmTypeMessageEnum statusEnum = optional.get();
            return statusEnum;
        } else {
            return null;
        }
    }


}
