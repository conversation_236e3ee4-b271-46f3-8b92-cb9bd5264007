package com.fxiaoke.open.erpsyncdata.converter.fieldconvert;

import cn.hutool.core.util.BooleanUtil;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldMappingTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.util.FieldTypeUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.GetOuterAccountByFsData;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class FixedValueFieldConverter implements FieldConverter {
    @Autowired
    private OuterServiceFactory outerServiceFactory;

    @Override
    public Object convert(String tenantId, ObjectData sourceData, Integer destEventType, String destTenantId, FieldMappingData fieldMappingData, Integer destTenantType, Integer sourceTenantType, String destObjectApiName) {
        String value = fieldMappingData.getValue();
        if (value != null && !BooleanUtil.isTrue(fieldMappingData.getUseSourceValueDirectly())) {
            value = value.trim();
        }
        String destType = fieldMappingData.getDestType();
        /**
         * 目前只有采集有 OUT_OWNER 字段
         */
        if (destType.equals(FieldType.EMPLOYEE) && ObjectDescribeContants.OUT_OWNER.equals(fieldMappingData.getSourceApiName())) {
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            GetOuterAccountByFsData data = outerServiceFactory.get(TenantTypeEnum.CRM.getType()).getOuterAccountByFs(Integer.valueOf(destTenantId), Integer.valueOf(sourceData.getTenantId()), Integer.valueOf(value)).getData();
            return Lists.newArrayList(String.valueOf(data.getOuterUid()));
        }
        return getSerializable(value, destType, fieldMappingData);
    }

    public static Object getSerializable(String value, String destType, FieldMappingData fieldMappingData) {
        if (FieldTypeUtil.isStringType(destType)) {
            return value;
        }
        if (FieldTypeUtil.isNumberType(destType)) {
            return Long.valueOf(value);
        }
        if (FieldTypeUtil.isBooleanType(destType)) {
            return Boolean.valueOf(value);
        }
        switch (destType) {
            case FieldType.EMPLOYEE:
            case FieldType.SELECT_MANY:
            case FieldType.DEPARTMENT:
                return Lists.newArrayList(value);
            case FieldType.EMPLOYEE_MANY:
            case FieldType.DEPARTMENT_MANY:
            case FieldType.OBJECT_REFERENCE_MANY:
                //人员多选和部门多选和查找关联多选支持固定值
                List<String> items = Splitter.on(",").splitToList(value);
                return Lists.newArrayList(items);
            default:
                return value;
        }
    }

    public static Object getSerializableByList(List<Object> values, String destType, FieldMappingData fieldMappingData) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        Object value = values.get(0);
        if (value == null) {
            return null;
        }
        if (FieldTypeUtil.isStringType(destType)) {//拼接
            StringBuffer sb = new StringBuffer();
            for(Object item : values) {
                if(item == null) {
                    continue;
                }
                sb.append(item).append(";");
            }
            return sb.toString();
        }
        if (FieldTypeUtil.isNumberType(destType)) {//只取第一个值
            return Long.valueOf(value.toString());
        }
        if (FieldTypeUtil.isBooleanType(destType)) {//只取第一个值
            return Boolean.valueOf(value.toString());
        }
        return values;
    }
    @Override
    public Integer getFieldMappingType() {
        return FieldMappingTypeEnum.FIXED_VAULE.getType();
    }
}
