package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncDataMappingGetDetailResult implements Serializable {
    @ApiModelProperty("源企业")
    private String sourceTenantId;
    @ApiModelProperty("源企业名称")
    private String sourceTenantName;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源企业对象名称")
    private String sourceObjectName;
    @ApiModelProperty("源数据Id")
    private String sourceDataId;
    @ApiModelProperty("源数据，结构同crm对象数据的结构")
    private ObjectData sourceData;
    @ApiModelProperty("目标企业")
    private String destTenantId;
    @ApiModelProperty("目标企业名称")
    private String destTenantName;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("目标企业对象名称")
    private String destObjectName;
    @ApiModelProperty("目标数据Id")
    private String destDataId;
    @ApiModelProperty("目标数据")
    private ObjectData destData;
    @ApiModelProperty("源数据与目标数据之间的映射关系")
    private List<FieldMappingResult> fieldMappings;
    @ApiModelProperty("通过源查询crm对象数据与目标数据之间的映射关系")
    private List<ObjectMappingResult> queryData2DestDataMappingBySource;
    @ApiModelProperty("通过目标查询crm对象数据与目标数据之间的映射关系")
    private List<ObjectMappingResult> queryData2DestDataMappingByDest;
    @ApiModelProperty("数据同步的syncLogId")
    private String syncLogId;
    @ApiModelProperty("源企业类型")
    private Integer sourceTenantType;
    @ApiModelProperty("仍存在数据库")
    private boolean exist;

}
