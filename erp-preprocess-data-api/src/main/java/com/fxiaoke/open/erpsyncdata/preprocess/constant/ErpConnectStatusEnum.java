package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/14 17:37:43
 */
public enum ErpConnectStatusEnum {
    NORMAL(1),
    DELETE(2),
    ;

    @Getter
    private Integer status;

    ErpConnectStatusEnum(Integer status) {
        this.status = status;
    }

    private static Map<Integer, ErpConnectStatusEnum> statusMap = Arrays.stream(values()).collect(Collectors.toMap(ErpConnectStatusEnum::getStatus, Function.identity()));

    public static ErpConnectStatusEnum valueOf(Integer status) {
        return statusMap.get(status);
    }
}
