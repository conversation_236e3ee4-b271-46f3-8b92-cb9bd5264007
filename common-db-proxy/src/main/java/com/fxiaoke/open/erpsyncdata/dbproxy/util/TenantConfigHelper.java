package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import lombok.Getter;
import lombok.ToString;

/**
 * 企业配置变化监听类，注册后实现消费变化数据。
 * 目前是同步处理，请谨慎使用。
 * 仅支持指定企业+dcId+type,单条监听
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/4/27
 */
@Getter
@ToString
public class TenantConfigHelper {

    /**
     * 每个租户下的configuration用 dcid+type作为key缓存
     */
    private static final String cacheKeyFormat = "dcid%stype%s";

    public static String buildKey(String dcId, String type) {
        return String.format(cacheKeyFormat, dcId, type);
    }
}
