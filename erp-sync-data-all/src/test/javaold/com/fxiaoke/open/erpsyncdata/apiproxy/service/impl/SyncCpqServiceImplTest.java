package com.fxiaoke.open.erpsyncdata.apiproxy.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.restful.client.FRestApiProxyFactory;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ObjectDataGetByIdResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.SyncCpqService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.otherrestapi.eservice.common.util.UUIDUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date: 15:22 2020/12/28
 * @Desc:
 */
@Ignore
public class SyncCpqServiceImplTest extends BaseTest {

    @Autowired
    private SyncCpqService syncCpqService;
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Test
    public void listErpProductByTime() {
    }

    @Test
    public void handleSapCpq() {
    }

    @Test
    public void getErpProductById() {
    }

    @Test
    public void test() {
        Integer tenantId = 79675;
        HeaderObj headerObj = new HeaderObj(tenantId, CrmConstants.SYSTEM_USER);
        Result<ObjectDataGetByIdResult> byId = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(), "5fe42f8c2dfc710001f17feb", false, false, false, false);
        ActionEditArg actionEditArg = new ActionEditArg();
        byId.getData().getObjectData().put("bom_path", byId.getData().getObjectData().get("parent_bom_id") + "." + byId.getData().getObjectData().get("_id"));
        actionEditArg.setObjectData(byId.getData().getObjectData());
        Result<ActionEditResult> edit = metadataActionService.edit(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(),false,false, null, null, actionEditArg);
        Result<ObjectDataGetByIdResult> byId1 = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(), "5fe442112dfc710001f19ac7", false, false, false, false);

        Result<ObjectDataGetByIdResult> byId2 = objectDataService.getById(headerObj, ObjectApiNameEnum.FS_BOM_OBJ.getObjApiName(), "5fd0b57a8e54fc000184d3f1", false, false, false, false);
        System.out.println("");
    }

    @Test
    public void testAccount() throws Exception {

        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        batchGetEmployeeDtoArg.setEnterpriseId(Integer.valueOf(ConfigCenter.FS_ENTERPRISE_ID));
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);
        ArrayList<Integer> integers = Lists.newArrayList(1005, 1027, 1034, 1000, 1011);
        batchGetEmployeeDtoArg.setEmployeeIds(integers);
        BatchGetEmployeeDtoResult batchGetEmployeeDtoResult  = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);


//        SendTextNoticeArg sendTextNoticeArg=new SendTextNoticeArg();
//        sendTextNoticeArg.setMsg("熔断通知111111");
//        sendTextNoticeArg.setMsgTitle("熔断通知");
//        sendTextNoticeArg.setReceivers(Lists.newArrayList(CrmConstants.SYSTEM_USER));
//        sendTextNoticeArg.setTenantId("81772");
//        List<Integer> strings = Lists.newArrayList(1000);
//        com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result<Void> voidResult = notificationService.sendCustomerRoomNotice(sendTextNoticeArg,strings);
//        System.out.println(voidResult);


        MessageServiceV2 messageServiceV2 = FRestApiProxyFactory.getInstance().create(MessageServiceV2.class);


        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        HeaderObj headerObj = new HeaderObj(81243, 1000);
        searchTemplateQuery.addFilter("UDInt1__c", Lists.newArrayList("81772"), "LIKE");
        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> oldSpuDataResult = objectDataService.queryBySearchTemplate(headerObj, ObjectApiNameEnum.FS_ACCOUNT_OBJ.getObjApiName(), searchTemplateQuery);
        if (CollectionUtils.isNotEmpty(oldSpuDataResult.getData().getQueryResult().getData())) {
            String accountId = oldSpuDataResult.getData().getQueryResult().getData().get(0).getId();
            com.fxiaoke.model.message.SendTextMessageArg sendTextMessageArg = new com.fxiaoke.model.message.SendTextMessageArg();
            sendTextMessageArg.setEi(81243);
            sendTextMessageArg.setReceiverChannelType(ReceiverChannelType.TRUST_GROUP);
            sendTextMessageArg.setMessageContent("熔断通知*********");
            sendTextMessageArg.setUuid(UUIDUtil.getUUID());
            //TODO 获取客户对象Id
            sendTextMessageArg.setReceiverChannelData(ReceiverChannelUtils.buildTrustChannelData("AccountObj", accountId));
            sendTextMessageArg.setMessageContent("ERP熔断通知");
            sendTextMessageArg.setReceiverIds(Lists.newArrayList(CrmConstants.SYSTEM_USER));
            MessageResponse messageResponse = messageServiceV2.sendTextMessage(sendTextMessageArg);
            System.out.println(messageResponse);
        }
        System.out.println("oldSpuDataResult");
    }
}