package com.fxiaoke.open.oasyncdata.model;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.fxiaoke.open.erpsyncdata.i18n.ExcelConstant;
import com.fxiaoke.open.oasyncdata.constant.ErpFieldTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class OAExcelTemplateWriteHandler implements SheetWriteHandler {



    private String fileName;//模板名称

    public OAExcelTemplateWriteHandler(String fileName) {
        this.fileName=fileName;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

        if (!ExcelConstant.ADD_FIELD_IMPORT_TEMPLATE.getValue().equals(fileName)){
            return;
        }

        //下拉选项列表
        List<String> fieldTypeList = Arrays.stream(ErpFieldTypeEnum.values()).map(v -> v.name()).collect(Collectors.toList());
        List<String>mustInput=Lists.newArrayList("FALSE","TRUE");

        //创建隐藏列
        String hideSheetName="hideSheet";
        Sheet hideSheet = createHideSheet(hideSheetName,writeWorkbookHolder);

        //获取所有的sheet页
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Iterator<Sheet> sheetIterator = workbook.sheetIterator();


        //为所有的sheet页处理下拉选项
        while (sheetIterator.hasNext()){
            Sheet sheet = sheetIterator.next();
            if (sheet==hideSheet){
                continue;
            }
            //指定处理的字段所在的列和行
            CellRangeAddressList rangeList = new CellRangeAddressList(1, 1000, 2, 2);
            DataValidationHelper helper = sheet.getDataValidationHelper();

            //设置下拉选项规则
            DataValidation dataValidation = createSheetCellSelected(writeWorkbookHolder, fieldTypeList, "A", helper, rangeList,0);

            //当前sheet为当前列应用下拉规则
            sheet.addValidationData(dataValidation);

            //下拉选项太长会有无法显示的bug.
            //String[] fieldArrays = fieldTypeList.toArray(new String[fieldTypeList.size()]);
            //DataValidationConstraint  constraint = helper.createExplicitListConstraint();
            CellRangeAddressList mustInputRangeList = new CellRangeAddressList(1, 1000, 7, 7);

            DataValidation mustInputDataValidation = createSheetCellSelected(writeWorkbookHolder, mustInput, "B", helper, mustInputRangeList,1);
            sheet.addValidationData(mustInputDataValidation);

        }

    }


    /**
     * 创建字段类型隐藏的sheet页
     * @param hideSheetName
     * @param writeWorkbookHolder
     * @return
     */
    private Sheet createHideSheet(String hideSheetName,WriteWorkbookHolder writeWorkbookHolder){
        Sheet hideSheet = writeWorkbookHolder.getWorkbook().getSheet(hideSheetName);
        if (hideSheet!=null){
            return hideSheet;
        }
        hideSheet = writeWorkbookHolder.getWorkbook().createSheet(hideSheetName);
        int sheetIndex = writeWorkbookHolder.getWorkbook().getSheetIndex(hideSheet);
        writeWorkbookHolder.getWorkbook().setSheetHidden(sheetIndex,true);
        return hideSheet;
    }

    /**
     * 设置下拉选项规则
     * @param writeWorkbookHolder
     * @param cellList 下拉选项值
     * @param cellFlag 下拉选项对应的列 A,B,C,D....Z
     * @param helper
     * @param rangeList
     * @return
     */
    private DataValidation createSheetCellSelected(WriteWorkbookHolder writeWorkbookHolder,
                                                   List<String> cellList,
                                                   String cellFlag,
                                                   DataValidationHelper helper,
                                                   CellRangeAddressList rangeList,int column){
        String hideSheetName="hideSheet";
        Sheet hideSheet = createHideSheet(hideSheetName,writeWorkbookHolder);
        for (int i = 1; i <= cellList.size(); i++) {
            Row row =hideSheet.getRow(i);
            if (row==null){
                row=hideSheet.createRow(i);
            }
            row.createCell(column).setCellValue(cellList.get(i-1));
        }

        if (writeWorkbookHolder.getWorkbook().getName("cellRefName"+cellFlag)==null){
            String cellRef=hideSheetName+"!$"+cellFlag+"$1:$"+cellFlag+"$"+(cellList.size()+1);
            Name cellFlagRefName = writeWorkbookHolder.getWorkbook().createName();
            cellFlagRefName.setNameName("cellRefName"+cellFlag);
            cellFlagRefName.setRefersToFormula(cellRef);
        }

        DataValidationConstraint fieldTyeRefConstraint = helper.createFormulaListConstraint("cellRefName"+cellFlag);
        DataValidation dataValidation = helper.createValidation(fieldTyeRefConstraint, rangeList);
        dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        dataValidation.setShowErrorBox(true);
        dataValidation.setSuppressDropDownArrow(true);
        dataValidation.createErrorBox(ExcelConstant.TIPS.getValue(), ExcelConstant.SELECT_VALUE_FROM_SINGLE_CHOICE_PRESET.getValue());
        return dataValidation;
    }


}
