package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.service.EmployeeMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.ToolService;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.ToolsService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Api(tags = "工具接口")
@RestController("toolsController")
@RequestMapping("erp/syncdata/superadmin/tools")
public class ToolsController {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private EmployeeMappingService employeeMappingService;
    @Autowired
    private ToolsService toolsService;
    @Autowired
    private ToolService toolService;

    private ExecutorService executorService = Executors.newSingleThreadExecutor();

    @ApiOperation(value = "post接口测试")
    @RequestMapping(value = "/post", method = RequestMethod.POST)
    public Result<String> post(@RequestBody PostArg body) {
        return Result.newSuccess(proxyHttpClient.postUrl(body.getUrl(), body.getBody(), body.getHeader(), (Long)null).getBody());
    }

    @ApiOperation(value = "迁移当前企业K3C渠道所有数据中心的用户数据到用户表")
    @RequestMapping(value = "/migrateK3CUserData", method = RequestMethod.POST)
    public Result<Void> migrateK3CUserData(@RequestParam String tenantId) {
        String traceId = TraceUtil.get();
        executorService.submit(()->{
            TraceUtil.initTrace(traceId);
            employeeMappingService.migrateUserData(tenantId, ErpChannelEnum.ERP_K3CLOUD);
        });
        return Result.newSuccess();
    }

    @ApiOperation(value = "迁移当前DB所有企业K3C渠道所有数据中心的用户数据到用户表")
    @RequestMapping(value = "/migrateUserData4AllTenant", method = RequestMethod.POST)
    public Result<Void> migrateUserData4AllTenant() {
        String traceId = TraceUtil.get();
        executorService.submit(()->{
            TraceUtil.initTrace(traceId);
            employeeMappingService.migrateUserData4AllTenant(ErpChannelEnum.ERP_K3CLOUD);
        });
        return Result.newSuccess();
    }

    @ApiOperation(value = "迁移部门集成流数据到部门字段表")
    @RequestMapping(value = "/migrateDepartmentData", method = RequestMethod.POST)
    public Result<Void> migrateDepartmentData(@RequestParam String tenantId,
                                              @RequestParam String dcId,
                                              @RequestParam boolean fsDataIdIsNumber) {
        String traceId = TraceUtil.get();
        executorService.submit(()->{
            TraceUtil.initTrace(traceId);
            toolsService.migrateDepartmentData(tenantId,dcId,fsDataIdIsNumber);
        });
        return Result.newSuccess();
    }

    @ApiOperation(value = "迁移员工集成流数据到员工字段表")
    @RequestMapping(value = "/migrateEmployeeData", method = RequestMethod.POST)
    public Result<Void> migrateEmployeeData(@RequestParam String tenantId,
                                            @RequestParam String dcId,
                                            @RequestParam boolean fsDataIdIsNumber) {
        String traceId = TraceUtil.get();
        executorService.submit(()->{
            TraceUtil.initTrace(traceId);
            toolsService.migrateEmployeeData(tenantId,dcId,fsDataIdIsNumber);
        });
        return Result.newSuccess();
    }

    @ApiOperation(value = "针对SAP和标准渠道，从中间表迁移数据到erp_processed_data表")
    @RequestMapping(value = "/initErpProcessedData", method = RequestMethod.POST)
    public Result<Void> initErpProcessedData(@RequestParam String tenantId,
                                             @RequestParam String dcId,
                                             @RequestParam List<String> ployDetailIdList) {
        String traceId = TraceUtil.get();
        executorService.submit(()->{
            TraceUtil.initTrace(traceId);
            toolService.initErpProcessedData(tenantId, dcId, ployDetailIdList);
        });
        return Result.newSuccess();
    }

    @Data
    public static class PostArg implements Serializable {
        private String url;
        private Map<String,String> header;
        private String body;
    }
}
