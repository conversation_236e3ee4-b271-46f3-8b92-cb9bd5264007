package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.core.collection.CollUtil;
import com.fxiaoke.crmrestapi.arg.v3.QueryListByIdsArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ObjectDataQueryListByIdsResult;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.BaseResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.crm.ModifyLog;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.converter.manager.CrmMetaManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncPloyDetailSnapshotManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisCacheManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ParallelUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.SyncDataContextUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 切换流状态动作
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/7/20
 */
@Service
@Slf4j
public class SwitchStreamStatusManager {
    @Autowired
    private SyncPloyDetailSnapshotManager syncPloyDetailSnapshotManager;
    @Autowired
    private RedisCacheManager redisCacheManager;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private AllModelDubboService allModelDubboServiceRest;
    @Autowired
    private CrmMetaManager crmMetaManager;
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ConfigCenterConfig configCenterConfig;

    /**
     * @param tenantId
     * @param needSyncDuringStop 控制是否同步历史数据
     * @param syncPloyDetailResult
     */
    public void asyncProcessAfterEnablePloy(String tenantId,boolean needSyncDuringStop, SyncPloyDetailResult syncPloyDetailResult) {
        ParallelUtils.createBackgroundTask().submit(() -> {
            processAfterEnablePloy(tenantId,needSyncDuringStop, syncPloyDetailResult);
        }).run();
    }

    /**
     * 启用策略后动作
     * 异步动作
     * 不先通过修改时间查询数据，是因为数据的最后修改时间是变化的，但是日志的时间是不变的。
     * 通过日志判断是否需要同步，然后再查询最新数据来触发数据同步即可。
     *
     * @param tenantId
     */
    public void processAfterEnablePloy(String tenantId,boolean needSyncDuringStop, SyncPloyDetailResult syncPloyDetailResult) {
        log.info("begin process after enable ploy,tenantId:{},{},syncPloyDetailResult:{}", tenantId, needSyncDuringStop, syncPloyDetailResult);
        Integer sourceTenantType = syncPloyDetailResult.getSourceTenantType();
        String objApiName = syncPloyDetailResult.getSourceObjectApiName();
        //获取并删除同步时间
        Long lastModifyTime = configCenterConfig.getPloyDetailLastSyncTimeAndDel(tenantId, syncPloyDetailResult.getId());

        //        todo 上线10天后可删除
        if (lastModifyTime == null) {
            String redisKey = String.format(CommonConstant.REDIS_KEY_STOP_PLOY_TIME, tenantId, objApiName);
            //获取并删除redisKey
            String cacheValue = redisCacheManager.getDelCache(redisKey, this.getClass().getSimpleName());
            //不存在或者客户选择不需要同步时，直接返回
            if (cacheValue != null) {
                lastModifyTime = Long.valueOf(cacheValue);
            }
        }

        //不存在或者客户选择不需要同步时，直接返回
        if (lastModifyTime == null || !needSyncDuringStop) {
            return;
        }
        Long now = System.currentTimeMillis();
        log.info("need check from:{},to:{}", lastModifyTime, now);
        if (TenantType.CRM.equals(sourceTenantType)) {
            processEnableCrm2Erp(tenantId, syncPloyDetailResult, objApiName, lastModifyTime, now);
        } else if (TenantType.ERP.equals(sourceTenantType)) {
            //erp的，检查轮询临时库时间，如果停用策略的时间更前，则更新。
            processEnableErp2Crm(tenantId, objApiName, lastModifyTime);
        }
    }

    @SneakyThrows
    protected void processEnableErp2Crm(String tenantId, String objApiName, Long lastModifyTime) {
        String lockName = CommonConstant.lockMongoPrefix + tenantId;
        RLock rlock = redissonClient.getLock(lockName);
        if (rlock.tryLock(2L, TimeUnit.MINUTES)){
            try{
                //先抢锁防止刷新时间后又被轮询中的任务刷新了轮询时间。
                List<ErpSyncTimeEntity> erpSyncTimeEntities = erpSyncTimeDao.setGlobalTenant(tenantId).listByTenantIdAndObjectApiName(tenantId, objApiName);
                for (ErpSyncTimeEntity erpSyncTimeEntity : erpSyncTimeEntities) {
                    if (erpSyncTimeEntity.getLastQueryMongoTime() == null
                            || erpSyncTimeEntity.getLastQueryMongoTime() > lastModifyTime) {
                        int rows = erpSyncTimeDao.setGlobalTenant(tenantId).updateLastQueryMongoTimeById(lastModifyTime, erpSyncTimeEntity.getId());
                        log.info("trace processAfterEnablePloy,update lastQuery mongo time,entity:{},newTime:{},rows:{}", erpSyncTimeEntity, lastModifyTime, rows);
                    }
                }
            }finally {
                rlock.unlock();
            }
        }else {
            log.warn("get lock failed,obj:{},newTime:{}", objApiName, lastModifyTime);
        }
    }

    public Pair<Set<String>,Set<String>> processEnableCrm2Erp(String tenantId, SyncPloyDetailResult syncPloyDetailData, String objApiName, Long lastModifyTime, Long now) {
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        //检查主对象
        List<com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData> masterFieldMappings = syncPloyDetailData.getFieldMappings();
        com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData masterConditions = syncPloyDetailData.getSyncConditions();
        //查询日志，
        ModifyLog.Arg arg = new ModifyLog.Arg();
        arg.setCondition(ModifyLog.Condition.builder()
                .module(objApiName)
                .operationTimeFrom(lastModifyTime)
                .operationTimeTo(now)
                .build());
        Set<String> mainUpdateIds = new HashSet<>();
        Set<String> mainInvalidIds = new HashSet<>();
        Set<String> triggerFields = getTriggerFields(masterFieldMappings, masterConditions);
        checkTrigger(tenantId, triggerFields, arg, mainUpdateIds, mainInvalidIds);
        //检查从对象
        List<SyncPloyDetailResult.ObjectMappingInfo> detailObjectMappings = syncPloyDetailData.getDetailObjectMappings();
        if (detailObjectMappings != null && !detailObjectMappings.isEmpty()) {
            Map<String, SyncConditionsData> detailSyncConditionMap = syncPloyDetailData.getDetailObjectSyncConditions().stream().collect(Collectors.toMap(v -> v.getApiName(), u -> u, (v, u) -> v));
            for (SyncPloyDetailResult.ObjectMappingInfo detailObjectMapping : detailObjectMappings) {
                String detailApiName = detailObjectMapping.getSourceObjectApiName();
                arg.getCondition().setModule(detailApiName);
                arg.getCondition().setLogId(null);
                arg.getCondition().setSearchAfter(null);
                Set<String> detailUpdateIds = new HashSet<>();
                Set<String> detailInvalidIds = new HashSet<>();
                Set<String> detailTriggerFields = getTriggerFields(detailObjectMapping.getFieldMappings(), detailSyncConditionMap.get(detailApiName));
                checkTrigger(tenantId, detailTriggerFields, arg, detailUpdateIds, detailInvalidIds);
                //明细作废触发主数据更新
                detailUpdateIds.addAll(detailInvalidIds);
                if (!detailUpdateIds.isEmpty()) {
                    FieldDescribe masterDetailField = crmMetaManager.getMasterDetailField(tenantId, detailApiName);
                    String mainApiName = masterDetailField.getApiName();
                    List<List<String>> dataIdSplit = CollUtil.split(detailUpdateIds, 100);
                    for (List<String> dataIds : dataIdSplit) {
                        QueryListByIdsArg queryListByIdsArg = new QueryListByIdsArg();
                        queryListByIdsArg.setDescribeApiName(detailApiName);
                        queryListByIdsArg.setDataIdList(dataIds);
                        queryListByIdsArg.setIncludeInvalid(true);
                        queryListByIdsArg.setSelectFields(Collections.singletonList(mainApiName));
                        Result<ObjectDataQueryListByIdsResult> queryMainRes = objectDataServiceV3.queryListByIds(headerObj, queryListByIdsArg);
                        log.info("query master Ids,arg:{},result:{}", queryListByIdsArg, queryMainRes);
                        if (!queryMainRes.isSuccess()) {
                            log.warn("findMainIds failed,{}", queryMainRes.getMessage());
                            return Pair.of(null, null);
                        }
                        queryMainRes.getData().getDataList().stream().map(v -> v.getString(mainApiName)).forEach(mainUpdateIds::add);
                    }
                }
            }
        }
        //触发
        triggerCrmEvent(tenantId, objApiName, mainUpdateIds, mainInvalidIds);
        return Pair.of(mainUpdateIds, mainInvalidIds);
    }

    private void checkTrigger(String tenantId, Set<String> triggerFields, ModifyLog.Arg arg, Set<String> updateIds, Set<String> invalidIds) {
        arg.setPageSize(100);
        for (int i = 0; i < 10000; i++) {
            //最大查100万条日志。(极端情况下，更新的id会有100万）
            BaseResult<ModifyLog.Result> logResult = sfaApiManager.queryModifyLog(tenantId, arg);
            if (!logResult.isSuccess()) {
                log.warn("trace processAfterEnablePloy query modify log error,{}", logResult.getMessage());
                break;
            }
            List<ModifyLog.Info> modifyLogInfos = logResult.getData().getModifyLogInfos();
            if (modifyLogInfos.isEmpty()) {
                break;
            }
            String logId = modifyLogInfos.get(modifyLogInfos.size() - 1).getLogId();
            List<Object> searchAfter = modifyLogInfos.get(modifyLogInfos.size() - 1).getSearchAfter();
            arg.getCondition().setLogId(logId);
            arg.getCondition().setSearchAfter(searchAfter);
            for (ModifyLog.Info modifyLogInfo : modifyLogInfos) {
                String dataId = modifyLogInfo.getDataId();
                switch (modifyLogInfo.getBizOperationName()) {
                    case "3":
                        //作废
                        invalidIds.add(dataId);
                        break;
                    case "4":
                        //恢复
                        invalidIds.remove(dataId);
                        break;
                    case "1":
                        //新增
                        updateIds.add(dataId);
                        break;
                    case "2":
                        //更新 修改了映射字段或数据范围字段
                        if (modifyLogInfo.getObjectData().stream().anyMatch(v -> triggerFields.contains(v.getFieldApiName()))) {
                            updateIds.add(dataId);
                        }
                        break;
                    default:
                }
            }
            if (!logResult.getData().getHasMore()) {
                break;
            }
        }
    }

    private Set<String> getTriggerFields(List<com.fxiaoke.open.erpsyncdata.admin.data.FieldMappingData> fieldMappingsData, com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData syncConditionsData) {
        Set<String> triggerFields = fieldMappingsData.stream().map(v -> v.getSourceApiName()).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(syncConditionsData.getFilters())) {
            for (List<FilterData> orFilterData : syncConditionsData.getFilters()) {
                for (FilterData andFilterData : orFilterData) {
                    triggerFields.add(andFilterData.getFieldApiName());
                }
            }
        }
        return triggerFields;
    }

    private void triggerCrmEvent(String tenantId, String objApiName, Set<String> updateDataIds, Set<String> invalidDataIds) {
        //查找主数据最新的同步记录
        com.fxiaoke.crmrestapi.common.data.HeaderObj headerObj = new com.fxiaoke.crmrestapi.common.data.HeaderObj(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        //所有数据都查询
        updateDataIds.addAll(invalidDataIds);
        List<List<String>> dataIdSplit = CollUtil.split(updateDataIds, 100);
        for (List<String> dataIds : dataIdSplit) {
            QueryListByIdsArg queryListByIdsArg = new QueryListByIdsArg();
            queryListByIdsArg.setDescribeApiName(objApiName);
            queryListByIdsArg.setDataIdList(dataIds);
            queryListByIdsArg.setIncludeInvalid(true);
            Result<ObjectDataQueryListByIdsResult> findMainDataRes = objectDataServiceV3.queryListByIds(headerObj, queryListByIdsArg);
            if (!findMainDataRes.isSuccess()) {
                log.warn("findMainData failed,{}", findMainDataRes.getMessage());
                return;
            }
            for (com.fxiaoke.crmrestapi.common.data.ObjectData crmObjData : findMainDataRes.getData().getDataList()) {
                EventTypeEnum eventType = EventTypeEnum.UPDATE;
                if (invalidDataIds.contains(crmObjData.getId())
                        && "invalid".equals(crmObjData.getString("life_status"))) {
                    //作废数据
                    eventType = EventTypeEnum.INVALID;
                }
                String syncLogId = syncLogManager.getInitLogId(tenantId, objApiName);
                SyncDataContextEvent eventData = SyncDataContextEvent.convertByCrmObject(crmObjData, eventType, syncLogId, null);
                //不能设置快照Id，不然可能会修改了策略不生效。
                //调用dubbo的数据保留旧接口
                BatchSendEventDataArg.EventData batchEvent = SyncDataContextUtils.convertSendArgResultByContext(eventData);
                // 添加dataVersion上报监控
                batchEvent.setDataVersion(System.currentTimeMillis());
                BatchSendEventDataArg batchSendEventDataArg = new BatchSendEventDataArg(Lists.newArrayList(batchEvent));
                Result2<Void> sendResult = allModelDubboServiceRest.batchSendEventData2DispatcherMq(batchSendEventDataArg);
            }
        }
    }

}
