package com.fxiaoke.open.erpsyncdata.common.interceptor

import spock.lang.Specification
import spock.lang.Unroll

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/6/7
 */
@Unroll
class TableNameParserTest extends Specification {


    def "tableName"() {
        def tableNames = []
        when:
        TableNameParser tableNameParser = new TableNameParser(sql);
        tableNameParser.accept(new TableNameParser.TableNameVisitor(){
            @Override
            void visit(TableNameParser.SqlToken name) {
                println(name)
                tableNames.add(name.getValue())
            }
        })
        then:
        tableNames == expectTables
        where:
        sql                                                 | expectTables
        "vacuum (skip_locked ,analyse) sync_data_mappings;" | []
        "truncate Table sync_data_mappings"                 | ["sync_data_mappings"]
    }
}
