package com.fxiaoke.open.erpsyncdata.dbproxy.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ClassUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.aop.support.AopUtils;
import org.springframework.util.StopWatch;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
public class ApiExceptionInterceptor {

    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        StopWatch totalStopWatch = new StopWatch();
        totalStopWatch.start();
        //判断切面方法是否可以返回Result，，，不返回Result则直接抛异常
        boolean returnStdResult = judgeIsReturnStdResult(proceedingJoinPoint);
        try {
            return proceedingJoinPoint.proceed();
        } catch (ConstraintViolationException e) {
            if (!returnStdResult) {
                throw e;
            }
            return wrapperValidateResult(e.getConstraintViolations());
        } catch (IllegalArgumentException e) {
            printError(proceedingJoinPoint, e, totalStopWatch);
            if (!returnStdResult) {
                throw e;
            }
            //子类可以强转父类
            return Result2.newErrorByI18N(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(),
                    getDetailExceptionMsg(e),
                    TraceContext.get().getTraceId(),
                    null,
                    null);
        } catch (ErpSyncDataException e) {
            printError(proceedingJoinPoint, e, totalStopWatch);
            if (!returnStdResult) {
                throw e;
            }
            //这个是业务异常，直接返回错误的errMsg就行了。
            String errMsg = e.getErrMsg();
            //国际化异常信息支持
            if (StringUtils.isNotEmpty(e.getI18nKey())) {
                String format = e.getNameByTraceLocale();
                if (CollUtil.isNotEmpty(e.getI18nExtra())) {
                    try {
                        errMsg = String.format(format, e.getI18nExtra().toArray());
                    } catch (Exception ignore) {
                    }
                }
            }
            return Result2.newErrorByI18N(e.getErrCode(),
                    errMsg,
                    TraceContext.get().getTraceId(),
                    null,
                    null);

        } catch (Exception e) {
            if (judgeException(e)) {
                LogIdUtil.setNeedRetry(true);
            }
            printError(proceedingJoinPoint, e, totalStopWatch);
            if (!returnStdResult) {
                throw e;
            }
            return Result2.newErrorByI18N(ResultCodeEnum.SYSTEM_ERROR.getErrCode(),
                    getDetailExceptionMsg(e),
                    TraceContext.get().getTraceId(),
                    null,
                    null);
        }
    }

    private boolean judgeIsReturnStdResult(ProceedingJoinPoint proceedingJoinPoint) {
        try {
            // 获取当前方法签名
            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            // 获取方法自身
            Method method = methodSignature.getMethod();
            // 获取返回值的类型
            Class<?> returnType = method.getReturnType();
            return ClassUtil.isAssignable(returnType, Result2.class);
        } catch (Exception e) {
            log.error("judgeIsReturnStdResult exception", e);
        }
        //维持原逻辑
        return true;
    }

    private void printError(ProceedingJoinPoint point, Throwable e, StopWatch totalStopWatch) {
        totalStopWatch.stop();
        String methodName = point.getSignature().getName();
        String className = AopUtils.getTargetClass(point.getTarget()).getSimpleName();
        String args = point.getArgs() == null ? "" : Opt.ofTry(() -> JSON.toJSONString(point.getArgs())).orElse("");
        if (e instanceof ErpSyncDataException) {
            log.warn("ApiExceptionInterceptor.printError,{}-{}, cost:{} , args:{}, exception:", className, methodName, totalStopWatch.getTotalTimeMillis(), args, e);
        } else {
            log.error("ApiExceptionInterceptor.printError,{}-{}, cost:{} , args:{}, exception:", className, methodName, totalStopWatch.getTotalTimeMillis(), args, e);
        }
    }

    /**
     * 包装校验异常结果
     *
     * @param constraintViolations 绑定结果
     * @return 异常结果
     */
    private Result2<?> wrapperValidateResult(Set<ConstraintViolation<?>> constraintViolations) {
        StringBuilder msg = new StringBuilder();
        constraintViolations.forEach(constraintViolation -> {
            msg.append(", ");
            msg.append(constraintViolation.getMessage() == null ? "" : constraintViolation.getMessage());
        });
        Result2<?> result = Result2.newError(-1, msg.substring(2));
        return result;
    }

    /**
     * 判断是不是指定的报错
     * @param exception
     * @return
     */
    public boolean judgeException(Exception exception) {
        String className = exception.getClass().getName();
        if (ConfigCenter.needRetryException.contains(className)) {
            log.info("Retry needed for exception: " + className);
            return true;
        }
        Throwable cause = exception.getCause();
        if (cause != null) {
            return judgeException((Exception) cause);
        }
        return false;
    }


    private String getDetailExceptionMsg(Throwable throwable) {
//        StringBuilder sb = new StringBuilder();
//        sb.append("异常类型: " + throwable.getClass().getName() + "\n");
//        sb.append("异常信息: " + throwable.getMessage() + "\n");

        return ExceptionUtil.getRootCauseMessage(throwable);
    }
}
