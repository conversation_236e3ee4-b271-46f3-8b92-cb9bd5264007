package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(40)
@AllArgsConstructor
@NoArgsConstructor
public class CategoryFieldDataMappingExcelVo {
    /**
     * 纷享数据name
     */
    @ExcelProperty(value = "erpdss.global.global.s1071",index = 0)
    private String fsDataName;

    /**
     * 保存中间表不是使用这个
     */
    @ExcelProperty(value = "erpdss.global.global.s1072",index = 1)
    private String fsCategoryCode;

    /**
     * erp数据name
     */
    @ExcelProperty(value = "erpdss.global.global.s1073",index = 2)
    private String erpDataName;

    /**
     * erp分类编码
     */
    @ExcelProperty(value = "erpdss.global.global.s1074",index = 3)
    private String erpDataId;

    @ExcelProperty(value = "erpdss.global.global.s1075",index = 4)
    private String erpParentDataId;

    /**
     * crm上级分类编码
     */
    @ExcelIgnore
    private String fsParentCategoryCode;
    /**
     * erp数据id
     */
    @ExcelProperty(value = "erpdss.global.global.s19",index = 5)
    private String remark;

    @ExcelIgnore
    private boolean ignore = false;

    public void appendRemark(String appendRemark) {
        if (this.remark == null || this.remark.isEmpty()) {
            remark = appendRemark;
        }else {
            remark = remark + ";" + appendRemark;
        }
    }
}
