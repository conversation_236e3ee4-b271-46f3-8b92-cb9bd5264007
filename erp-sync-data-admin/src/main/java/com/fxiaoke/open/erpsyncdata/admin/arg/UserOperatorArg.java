package com.fxiaoke.open.erpsyncdata.admin.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import java.io.Serializable;

@ApiModel
@Data
public class UserOperatorArg implements Serializable {

    private static final long serialVersionUID = 3217744451794701902L;
    @ApiModelProperty("操作模块id")
    private String moduleId;

    @ApiModelProperty("操作模块")
    private String module;

    @ApiModelProperty("页码")
    private Integer pageNum=1;

    @ApiModelProperty("查询数量")
    @Max(100) //最多只能获取到100条数据
    private Integer pageSize=20;

}
