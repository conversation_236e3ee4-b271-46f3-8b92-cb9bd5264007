package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ExcelListener.HistoryTaskTypeIdsListener;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdsErpHistoryTaskManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ProbeErpDataManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.Send2DispatcherSpeedLimitManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DataIdExcelVo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.FileData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ReadExcel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.stream.Collectors;

import static com.alibaba.dubbo.rpc.protocol.protobuffer.def.FcpRequestMethod.FileData;

/**
 * <AUTHOR>
 * @date 2023/7/18 17:57:45
 */
@Component
@Slf4j
public class FileIdsErpHistoryTaskServiceImpl extends AbstractErpHistoryTaskServiceImpl {
    @Autowired
    private IdsErpHistoryTaskManager idsErpHistoryTaskManager;
    @Autowired
    private FileManager fileManager;

    @Override
    public ErpHistoryDataTaskTypeEnum taskType() {
        return ErpHistoryDataTaskTypeEnum.TYPE_FILE_IDS;
    }

    @Override
    public boolean checkParamsError(ErpHistoryDataTaskResult task) {
        return task.getFileData()!=null&&StrUtil.isBlank(task.getFileData().getPath());
    }

    @Override
    public boolean checkTaskValid(ErpHistoryDataTaskEntity task) {
        return StrUtil.isBlank(task.getDataIds());
    }

    @Override
    public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        entity.setTaskType(task.getTaskType());
        entity.setDataIds(JSONObject.toJSONString(task.getFileData()));
        return createHistoryTask(tenantId, Lists.newArrayList(entity));
    }

    @Override
    public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
        entity.setTaskType(task.getTaskType());
        entity.setDataIds(JSONObject.toJSONString(task.getFileData()));
        return editHistorySnapshotTask(tenantId, entity);
    }

    @Override
    public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
        return verifyDataIdByFile(tenantId, task);
    }

    @Override
    public void afterConvert2Vo(ErpHistoryDataTaskResult copy, ErpHistoryDataTaskEntity entity) {
        copy.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());
        copy.setIdsInputType(ErpHistoryDataTaskResult.IdsInputType.fileImport);
        copy.setFileData(JSONObject.parseObject(entity.getDataIds(),com.fxiaoke.open.erpsyncdata.preprocess.model.FileData.class));
        entity.setDataIds(null);
    }

    public Result<Void> verifyDataIdByFile(String tenantId, ErpHistoryDataTaskEntity task) {
        FileData fileData=JSONObject.parseObject(task.getDataIds(),com.fxiaoke.open.erpsyncdata.preprocess.model.FileData.class);
        byte[] bytes = fileManager.downTnFile(tenantId, null, null, fileData.getPath());
        Long ignoreSize=1L;
        if(task.getOffset()!=null){
            ignoreSize=task.getOffset() + 1;
        }
        HistoryTaskTypeIdsListener listener = new HistoryTaskTypeIdsListener(tenantId, task, ignoreSize, idsErpHistoryTaskManager);//除去一行标题
        //读取excel
        ReadExcel.Arg<DataIdExcelVo> readExcelArg = new ReadExcel.Arg<>();
        readExcelArg.setExcelListener(listener);
        readExcelArg.setType(DataIdExcelVo.class);
        readExcelArg.setInputStream(new ByteArrayInputStream(bytes));
        fileManager.readExcel(readExcelArg);
        return listener.getResult();
    }
}
