package com.fxiaoke.open.erpsyncdata.common.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志工具类，为了方便日志开关。
 */
@Slf4j
public class LogUtil {
    public static final String BREAK_KEY = "[.BREAK]";

    public static void changeLevel(Logger log, Level level) {
        changeLevel(log.getName(), level);
    }

    public static void changeLevel(String name, Level level) {
        try {
            LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();
            ch.qos.logback.classic.Logger logger = lc.getLogger(name);
            logger.setLevel(level);
        } catch (Throwable th) {
            log.error("change log level failed", th);
        }
    }

    public static void log(Logger logger, LogLevelEnum logLevel, String string, Object... args) {
        switch (logLevel) {
            case TRACE:
                logger.trace(string, args);
                break;
            case DEBUG:
                logger.debug(string, args);
                break;
            case INFO:
                logger.info(string, args);
                break;
            case WARN:
                logger.warn(string, args);
                break;
            case ERROR:
                logger.error(string, args);
                break;
        }
    }

    public static boolean needLog(Logger logger, LogLevelEnum logLevel) {
        switch (logLevel) {
            case TRACE:
                return logger.isTraceEnabled();
            case DEBUG:
                return logger.isDebugEnabled();
            case INFO:
                return logger.isInfoEnabled();
            case WARN:
                return logger.isWarnEnabled();
            case ERROR:
                return logger.isErrorEnabled();
        }
        return false;
    }
}
