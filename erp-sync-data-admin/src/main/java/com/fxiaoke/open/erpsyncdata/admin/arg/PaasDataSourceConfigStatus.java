package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 12:41 2024/5/30
 * @Desc:
 */
@Data
@ApiModel
public class PaasDataSourceConfigStatus implements Serializable {
    @ApiModelProperty("crm主对象apiName")
    private String crmObjectApiName;
    @ApiModelProperty("状态，开启：true,关闭：false")
    private Boolean status=false;//开启导入触发同步，跟TENANT_NEED_PASS_DATASOURCE配置是相反的，配置配的是过滤的dataSource
    @ApiModelProperty("开启触发的动作")
    private List<String> dataSourceList= Lists.newArrayList();//跟TENANT_NEED_PASS_DATASOURCE配置是相反的，配置配的是过滤的dataSource
}
