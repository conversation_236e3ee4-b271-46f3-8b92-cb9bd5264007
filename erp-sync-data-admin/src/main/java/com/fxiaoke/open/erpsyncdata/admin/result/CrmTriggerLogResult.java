package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class CrmTriggerLogResult implements Serializable {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("日志id")
    private String syncLogId;
    @ApiModelProperty("数据id")
    private String dataId;
    @ApiModelProperty("数据主属性")
    private String dataNumber;
    @ApiModelProperty("数据")
    private String data;
    @ApiModelProperty("创建时间")
    private Long createTime;

}
