package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.common.i18n.I18NModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
@ApiModel
public class ErpDBProxyConfigArg extends I18NModel {
    @ApiModelProperty(value = "对象名称")
    private String objName;
    @ApiModelProperty(value = "虚拟对象apiName")
    private String splitObjectApiName;
    @ApiModelProperty(value = "拆分类型")
    private String splitType;
    @ApiModelProperty(value = "对象扩展信息")
    private String erpObjectExtendValue;

    @ApiModelProperty(value = "数据id")
    private String id;
    @ApiModelProperty(value = "企业id")
    private String tenantId;
    @NotNull
    @ApiModelProperty(value = "对象apiName[非空]")
    private String objApiName;
    @ApiModelProperty(value = "数据中心ID")
    private String dataCenterId;

    @NotNull
    @ApiModelProperty(value = "数据主键字段[非空]")
    private String dbKey;
    @ApiModelProperty(value = "查询对象数据sql语句")
    private String querySql;//查询数据sql
    @ApiModelProperty(value = "是否offset一直为0，为true则每次调用更新开始时间参数")
    private boolean alwaysOffsetZero;
    @ApiModelProperty(value = "对象查询时间条件")
    private String dateTimeConditionField;
    @ApiModelProperty(value = "查询字段日期格式")
    private String dateFormat;
    @ApiModelProperty(value = "查询ID的sql语句")
    private String queryIdSql;
    @ApiModelProperty(value = "新增sql语句")
    private String insertSql;
    @ApiModelProperty(value = "更新sql语句")
    private String updateSql;
    @ApiModelProperty(value = "作废sql语句")
    private String invalidSql;

    @ApiModelProperty(value = "明细")
    private Map<String, ErpDBProxyDetailConfigArg> details = new HashMap<>();
    @ApiModelProperty(value = "最后修改人[系统指定]")
    private String lastModifyBy;


    @Data
    @ApiModel
    public static class ErpDBProxyDetailConfigArg{
        @ApiModelProperty(value = "对象名称")
        private String objName;
        @ApiModelProperty(value = "虚拟对象apiName")
        private String splitObjectApiName;
        @ApiModelProperty(value = "拆分类型")
        private String splitType;
        @ApiModelProperty(value = "对象扩展信息")
        private String erpObjectExtendValue;

        @ApiModelProperty(value = "数据id")
        private String id;
        @ApiModelProperty(value = "企业id")
        private String tenantId;
        @NotNull
        @ApiModelProperty(value = "对象apiName[非空]")
        private String objApiName;
        @ApiModelProperty(value = "数据中心ID")
        private String dataCenterId;

        @NotNull
        @ApiModelProperty(value = "数据主键字段[非空]")
        private String dbKey;
        @ApiModelProperty(value = "查询对象数据sql语句")
        private String querySql;//查询数据sql
        @ApiModelProperty(value = "对象查询时间条件")
        private String dateTimeConditionField;
        @ApiModelProperty(value = "查询字段日期格式")
        private String dateFormat;
        @ApiModelProperty(value = "查询ID的sql语句")
        private String queryIdSql;
        @ApiModelProperty(value = "新增sql语句")
        private String insertSql;
        @ApiModelProperty(value = "更新sql语句")
        private String updateSql;
        @ApiModelProperty(value = "作废sql语句,暂不使用")
        private String invalidSql;
        @ApiModelProperty(value = "最后修改人[系统指定]")
        private String lastModifyBy;
    }

}
