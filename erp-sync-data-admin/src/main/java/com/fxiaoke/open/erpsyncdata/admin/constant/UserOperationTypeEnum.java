package com.fxiaoke.open.erpsyncdata.admin.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date: 17:34 2021/12/7
 * @Desc:
 */
@Getter
@AllArgsConstructor
public enum UserOperationTypeEnum {
    /**
     * 删除中间表
     */
    DELETE_MAPPING("删除映射", I18NStringEnum.s795.getI18nKey()),
    //${操作人} 新建了集成流 ${集成流名称}。
    CREATE("新建", I18NStringEnum.s796.getI18nKey()),
    //${操作人} 编辑了集成流 ${集成流名称}。
    EDIT("编辑", I18NStringEnum.s797.getI18nKey()),
    //${操作人} 启用了集成流 ${集成流名称}，启用成功。
    START("启用", I18NStringEnum.s694.getI18nKey()),
    STOP("停用", I18NStringEnum.s695.getI18nKey()),
    DELETE("删除", I18NStringEnum.s798.getI18nKey()),
    DELETE_ALL("删除所有", I18NStringEnum.s799.getI18nKey());
    private String description;
    private String i18nKey;

    public String getDescription(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,description);
    }
}
