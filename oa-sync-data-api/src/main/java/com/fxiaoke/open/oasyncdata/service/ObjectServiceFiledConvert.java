package com.fxiaoke.open.oasyncdata.service;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/11/18 16:18 不同的代办对象实现接口，做对应的字段转换或者特殊处理
 * @Version 1.0
 */
public interface ObjectServiceFiledConvert {

    void dealSpecialField(ControllerGetDescribeResult objectData, String tenantId,String dataCenterId);

    String specialHand(OAObjectFieldVO oaObjectFieldVO, ObjectData data, String tenantId, String resultJson,String dataCenterId);


    Map<String,String> builderTitle(ObjectData objectData,String bizType, String tenantId,String dataCenterId);

}
