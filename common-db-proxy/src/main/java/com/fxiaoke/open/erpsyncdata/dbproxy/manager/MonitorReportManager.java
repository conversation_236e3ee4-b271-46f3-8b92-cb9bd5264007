package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.common.IpUtil;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.DataNodeMsgLogDTO;
import com.fxiaoke.log.dto.DataSyncErrorLogDTO;
import com.fxiaoke.log.dto.TenantCheckStatusLogDTO;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataNodeMsgDoc;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpTempData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.MonitorUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ReportDataNodeMsgPoolUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.vo.TenantCheckStatusMsg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.DataNodeTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DataNodeMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @Date: 15:44 2023/1/10
 * @Desc:
 * 延迟节点监控。
 * crm->erp, 从接受到paas的元数据变更事件开始 监控，
 * erp->crm, 从外部数据进入临时库开始监控。
 * 在mongo记录好每个节点的处理时间， 当该事件正常结束 时，上报到bizlog并删除mongo中的记录。
 * 到达指定的超时时间，也会 上报到bizlog并删除mongo中的记录。
 */
@Component
@Slf4j
public class MonitorReportManager {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ErpTempDataDao erpTempDataDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    public void sendEnterTempDataMsg(String tenantId, String dataCenterId, String objApiName, String splitObjApiName, List<ErpTempData> needSyncTempDataList, Long nodeTime, Integer order, String remark, boolean byNum) {
        try {
            if (notNeedProcessNodesMsg(tenantId)) {
                return;
            }
            if (needSyncTempDataList.isEmpty()) {
                return;
            }
            //重新查库填充syncTime
            erpTempDataDao.fillNewLastSyncTime(tenantId, dataCenterId, objApiName, needSyncTempDataList, byNum);
            List<DataNodeMsg> msgList = Lists.newArrayList();
            for (ErpTempData erpTempData : needSyncTempDataList) {
                if (erpTempData.getNewLastSyncTime() == null) {
                    continue;
                }
                DataNodeMsg.DataNodeMsgBuilder dataNodeMsgBuilder = DataNodeMsg.builder()
                        .appName(ConfigHelper.getProcessInfo().getName())
                        .serverIp(IpUtil.getSiteLocalIp())
                        .tenantId(tenantId)
                        .dataCenterId(dataCenterId)
                        .objApiName(objApiName)
                        .splitObjApiName(splitObjApiName)
                        .dataId(byNum ? erpTempData.getDataNumber() : erpTempData.getDataId())
                        .nodeType(DataNodeTypeEnum.start)
                        .nodeName(DataNodeNameEnum.EnterTempData)
                        .nodeTime(nodeTime)
                        .order(order)
                        .remark(remark)
                        .version(erpTempData.getNewLastSyncTime().getTime())
                        .traceId(TraceUtil.get());
                DataNodeMsg msg = dataNodeMsgBuilder.build();
                msgList.add(msg);
            }
            sendDataNodeMsg(msgList);
        } catch (Exception e) {
            log.error("sendEnterTempDataMsg Exception", e);
        }
    }

    public void sendOutTempDataMsg(String tenantId, String objApiName, Map<String, String> mongoId2DataId, Map<String, Long> mongoId2DataVersion, Long nodeTime, Integer order, String remark) {
        try {
            if (notNeedProcessNodesMsg(tenantId)) {
                return;
            }
            List<Document> dataList = erpTempDataDao.listErpObjDataByMongoIds(tenantId, Lists.newArrayList(mongoId2DataId.keySet()));
            List<DataNodeMsg> msgList = Lists.newArrayList();
            for (Document document : dataList) {
                if(mongoId2DataVersion.get(document.getObjectId("_id"))!=null){
                    document.put("new_last_sync_time",mongoId2DataVersion.get(document.getObjectId("_id").toString()));
                }
                DataNodeMsg msg = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
                        .tenantId(tenantId).objApiName(objApiName).dataId(mongoId2DataId.get(document.getObjectId("_id").toString()))
                        .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.OutTempData).nodeTime(nodeTime).order(order).remark(remark)
                        .msgDetail(JacksonUtil.toJson(document)).traceId(TraceUtil.get()).build();
                msgList.add(msg);
            }
            sendDataNodeMsg(msgList);
        } catch (Exception e) {
            log.error("sendOutTempDataMsg Exception={}", e);
        }
    }

    public List<Future> sendConsumeCrmMqEvent(List<BatchSendEventDataArg.EventData> eventDataList) {
        try {
            List<DataNodeMsg> msgList = Lists.newArrayList();
            for (BatchSendEventDataArg.EventData eventData : eventDataList) {
                if (eventData.getDataVersion() == null ) {//没有设置版本的不发送节点
                    continue;
                }
                DataNodeMsg msg = buildConsumeCrmMqMsg(eventData.getSourceData().getTenantId(), eventData.getSourceData().getApiName(), eventData.getSourceData().getId(), eventData.getDataVersion(), System.currentTimeMillis(), 1, i18NStringManager.getByEi(I18NStringEnum.s3720, null));
                if (msg != null) {
                    msgList.add(msg);
                }
            }
            return sendDataNodeMsgAndNotWait(msgList);
        } catch (Exception e) {
            log.error("sendConsumeCrmMqEvent Exception={}", e);
        }
        return Lists.newArrayList();
    }

    public DataNodeMsg buildConsumeCrmMqMsg(String tenantId, String objApiName, String dataId, Long version, Long nodeTime, Integer order, String remark) {
        if (notNeedProcessNodesMsg(tenantId)) {
            return null;
        }
        DataNodeMsg msg = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
                .tenantId(tenantId).objApiName(objApiName).dataId(dataId).version(version)
                .nodeType(DataNodeTypeEnum.start).nodeName(DataNodeNameEnum.ConsumeCrmMq).nodeTime(nodeTime).order(order).remark(remark)
                .traceId(TraceUtil.get()).build();
        return msg;
    }

    public void sendDispatcherEventMsg(String tenantId, String objApiName, String mainObjApiName, String dataId, Long dataVersion, String streamId, Long nodeTime, Integer order, String remark, List<Long> dataVersionList) {
        try {
            if (notNeedProcessNodesMsg(tenantId) || !objApiName.equals(mainObjApiName)) {
                return;
            }
            DataNodeMsg msg = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
                    .tenantId(tenantId).objApiName(objApiName).dataId(dataId).version(dataVersion).streamId(streamId).remark(remark)
                    .nodeType(DataNodeTypeEnum.process).nodeName(DataNodeNameEnum.DataTriggerProcess).nodeTime(nodeTime).order(order)
                    .traceId(TraceUtil.get()).build();
            sendDataNodeMsg(msg);
            //删除聚合的版本
            if (dataVersion != null && dataVersionList != null && dataVersionList.size() > 1) {
                List<Long> list = Lists.newArrayList();
                for (Long l : dataVersionList) {
                    if (dataVersion.equals(l)) {
                        continue;
                    }
                    list.add(l);
                }
                deleteNodesMsg(tenantId, objApiName, dataId, streamId, list);
            }
        } catch (Exception e) {
            log.error("sendDispatcherEventMsg Exception={}", e);
        }
    }

    public void sendProcessNodeMsgByCtx(SyncDataContextEvent syncDataContext, DataNodeNameEnum nodeNameEnum) {
        String msg = I18NStringEnum.kstart.getText() + " " + nodeNameEnum.getText();
        int order = nodeNameEnum.getOrder();
        sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum.process,
                nodeNameEnum,
                syncDataContext.getTenantId(),
                syncDataContext.getObjectApiName(),
                syncDataContext.getMainObjApiName(),
                syncDataContext.getDataId(),
                syncDataContext.getDataVersion(),
                syncDataContext.getStreamId(),
                System.currentTimeMillis(),
                order,
                msg
        );
    }


    public void sendEndNodeMsgByCtx(SyncDataContextEvent syncDataContext, DataNodeNameEnum nodeNameEnum, int order, String msg){
        sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum.end,
                nodeNameEnum,
                syncDataContext.getTenantId(),
                syncDataContext.getObjectApiName(),
                syncDataContext.getMainObjApiName(),
                syncDataContext.getDataId(),
                syncDataContext.getDataVersion(),
                syncDataContext.getStreamId(),
                System.currentTimeMillis(),
                order,
                msg
        );
    }

    public void sendNodeMsgByNodeTypeAndName(DataNodeTypeEnum nodeType, DataNodeNameEnum nodeName, String tenantId, String objApiName, String mainObjApiName, String dataId, Long version, String streamId, long nodeTime, int order, String remark) {
        try {
            if (notNeedProcessNodesMsg(tenantId) || !objApiName.equals(mainObjApiName)) {
                return;
            }
            DataNodeMsg msg = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
                    .tenantId(tenantId).objApiName(objApiName).dataId(dataId).version(version).streamId(streamId).remark(remark)
                    .nodeType(nodeType).nodeName(nodeName).nodeTime(nodeTime).order(order)
                    .traceId(TraceUtil.get()).build();
            sendDataNodeMsg(msg);
        } catch (Exception e) {
            log.error("sendNodeMsgByNodeTypeAndName Exception={}", e);
        }
    }

    void sendDataNodeMsg(List<DataNodeMsg> msgList) {
        ReportDataNodeMsgPoolUtil.sendDataNodeMsgAndWait(msgList);
    }
    List<Future> sendDataNodeMsgAndNotWait(List<DataNodeMsg> msgList) {
        return ReportDataNodeMsgPoolUtil.sendDataNodeMsgAndNotWait(msgList);
    }
    void sendDataNodeMsg(DataNodeMsg msg) {
        ReportDataNodeMsgPoolUtil.sendDataNodeMsg(msg);
    }

    @LogLevel(LogLevelEnum.DEBUG)
    public boolean notNeedProcessNodesMsg(String tenantId) {
        List<String> needProcessNodesTenantConfig = tenantConfigurationManager.getNeedProcessNodesTenantConfig();
        if (needProcessNodesTenantConfig.contains("*")) {//*全部
            return false;
        }
        return !needProcessNodesTenantConfig.contains(tenantId);
    }

    //合并数据节点，发送bizlog
    @LogLevel
    public void sendBizLog(String tenantId, String objApiName, String dataId, Long version, String streamId, Boolean isEnd, DataNodeMsgDoc dataNodeMsgDoc) {
        if (dataNodeMsgDoc == null) {
            return;
        }

        String firstNodeName = dataNodeMsgDoc.getNodeNames().get(0);
        String lastNodeName = dataNodeMsgDoc.getNodeNames().get(dataNodeMsgDoc.getNodeNames().size() - 1);
        DataNodeMsgLogDTO logDTO = DataNodeMsgLogDTO.builder()
                .tenantId(tenantId)
                .objApiName(objApiName)
                .dataId(dataId)
                .version(String.valueOf(version))
                .streamId(streamId)
                .isEnd(isEnd)
                .uniqueKey(dataNodeMsgDoc.getUniqueKey())
                .firstNodeName(firstNodeName)
                .lastNodeName(lastNodeName)
                .nodesListDetail(JacksonUtil.toJson(dataNodeMsgDoc))
                .appName(ConfigHelper.getProcessInfo().getName())
                .createTime(System.currentTimeMillis())
                .serverIp(IpUtil.getSiteLocalIp())
                .build();
        try {
            BizLogClient.send("biz-log-dss-data-node", Pojo2Protobuf.toMessage(logDTO, com.fxiaoke.log.DataNodeMsgLog.class).toByteArray());
        } catch (Exception e) {
            log.info("NodesMsg.send,exception={}", e.getMessage());
        }
    }

    //把聚合的版本的数据节点信息删除
    public void deleteNodesMsg(String tenantId, String objApiName, String dataId, String streamId, List<Long> versionList) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(objApiName) || StringUtils.isBlank(dataId) || StringUtils.isBlank(streamId) ||
                CollectionUtils.isEmpty(versionList)) {
            return;
        }
        Map<String, Object> detail = Maps.newHashMap();
        detail.put("deleteInVersionNodesMsg", versionList);
        DataNodeMsg msg = DataNodeMsg.builder().appName(ConfigHelper.getProcessInfo().getName()).serverIp(IpUtil.getSiteLocalIp())
                .tenantId(tenantId).objApiName(objApiName).dataId(dataId).streamId(streamId).nodeType(DataNodeTypeEnum.operate)
                .nodeName(DataNodeNameEnum.DataTriggerProcess).remark("删除聚合版本节点").msgDetail(JacksonUtil.toJson(detail)).traceId(TraceUtil.get()).build();
        sendDataNodeMsg(msg);
        //dataNodeMsgDao.deleteInVersionNodesMsg(tenantId, objApiName, dataId, streamId, versionList);
    }

    @LogLevel
    public void send2SyncDataErrorMonitor(Collection<SyncDataEntity> dataList) {
        try {
            for(SyncDataEntity syncDataEntity:dataList){
                if(!SyncDataStatusEnum.isFailed(syncDataEntity.getStatus())){
                    continue;
                }
                ObjectData sourceData = syncDataEntity.getSourceData();
                ObjectData destData = syncDataEntity.getDestData();
                syncDataEntity.setSourceData(null);//不需要这部分，防止数据过大
                syncDataEntity.setDestData(null);
                MonitorUtil.send(syncDataEntity, MonitorType.SYNC_DATA_ERROR);
                syncDataEntity.setSourceData(sourceData);
                syncDataEntity.setDestData(destData);
            }
        }catch (Exception e){
            log.error("send2SyncDataErrorMonitor is exception e",e);
        }
    }

    public void sendSyncDataError2BizLog(SyncDataEntity syncDataEntity){
        try {
            DataSyncErrorLogDTO logDTO=DataSyncErrorLogDTO.builder()
                    .tenantId(syncDataEntity.getTenantId())
                    .sourceObjApiName(syncDataEntity.getSourceObjectApiName())
                    .destObjApiName(syncDataEntity.getDestObjectApiName())
                    .dataId(syncDataEntity.getSourceDataId())
                    .syncPloyDetailSnapshotId(syncDataEntity.getSyncPloyDetailSnapshotId())
                    .syncDataId(syncDataEntity.getId())
                    .errorCode(syncDataEntity.getErrorCode())
                    .errorMsg(syncDataEntity.getRemark())
                    .syncLogId(syncDataEntity.getSyncLogId())
                    .appName(ConfigHelper.getProcessInfo().getName())
                    .createTime(System.currentTimeMillis())
                    .serverIp(IpUtil.getSiteLocalIp())
                    .build();
            BizLogClient.send("biz-log-sync-data-error", Pojo2Protobuf.toMessage(logDTO, com.fxiaoke.log.DataSyncErrorLog.class).toByteArray());
        } catch (Exception e) {
            log.info("sendSyncDataError2BizLog,exception={}", e.getMessage());
        }
    }
    public void sendTenantCheckStatus2BizLog(TenantCheckStatusMsg checkStatusMsg){
        try {
            TenantCheckStatusLogDTO logDTO=TenantCheckStatusLogDTO.builder()
                    .tenantId(checkStatusMsg.getTenantId())
                    .checkUrl(checkStatusMsg.getCheckUrl())
                    .msg(checkStatusMsg.getMsg())
                    .isSuccess(checkStatusMsg.getIsSuccess())
                    .createTime(checkStatusMsg.getCreateTime())
                    .appName(ConfigHelper.getProcessInfo().getName())
                    .createTime(System.currentTimeMillis())
                    .serverIp(IpUtil.getSiteLocalIp())
                    .build();
            BizLogClient.send("biz-log-tenant-check-status", Pojo2Protobuf.toMessage(logDTO, com.fxiaoke.log.TenantCheckStatusLog.class).toByteArray());
        } catch (Exception e) {
            log.info("sendTenantCheckStatus2BizLog,exception={}", e.getMessage());
        }
    }
}
