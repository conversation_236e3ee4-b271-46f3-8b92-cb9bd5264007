package com.fxiaoke.open.erpsyncdata.monitor.model.result;

import com.fxiaoke.open.erpsyncdata.monitor.model.CompareData;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
@Data
public class ListDataByTimeResult {
    /**
     * 数据
     */
    private List<CompareData> compareDataList = new ArrayList<>();
    /**
     * 用于下次请求的开始时间，当为空时，证明没有下一页了。
     */
    private Long nextBeginTime;
    /**
     * 用于下次请求的开始Id
     */
    private String nextBeginId;
}
