package com.fxiaoke.open.erpsyncdata.apiproxy.manager;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.handler.TimeoutSettings;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/18
 **/
@Slf4j
@Service
@LogLevel
public class ProxyHttpClient {
    public static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    @Resource(name = "okHttpSupport")
    private OkHttpSupport okHttpSupport;


    private RequestBody createJsonBody(Object params) {
        return params instanceof String
                ? RequestBody.create(JSON_TYPE, params.toString())
                : RequestBody.create(JSON_TYPE, JSON.toJSONString(params));
    }

    public <T> T postUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSON.toJSONString(params);
        return postUrlByJson(url, paramsJson, headerMap, typeReference);
    }

    public <T> T postUrlSerialNull(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JacksonUtil.toJson(params);
        return postUrlByJson(url, paramsJson, headerMap, typeReference);
    }

    public <T> T postUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        RequestBody requestBody = this.createJsonBody(paramsJson);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("post url failed, url={}, params={}, headerMap={}", url, paramsJson, headerMap, e);
        }
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, paramsJson, headerMap, result);
        return result;
    }

    public <T> T postMultipartData(String url, List<MultipartBody.Part> partList, Map<String, String> headerMap, TypeReference<T> typeReference) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        for (MultipartBody.Part part : partList) {
            builder.addPart(part);
        }
        builder.setType(MediaType.parse(MultipartBody.FORM.toString()));
        MultipartBody requestBody = builder.build();

        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        T result = null;
        try {
            result = this.okHttpSupport.parseObject(request, typeReference);
        } catch (Exception e) {
            log.error("post url failed, url={}, headerMap={}", url, headerMap, e);
        }
        log.debug("postUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result;
    }

    public <T> T getUrl(String url, Map<String, String> headerMap, TypeReference<T> typeReference) {
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        T result = this.okHttpSupport.parseObject(request, typeReference);
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result;
    }


    public HttpRspLimitLenUtil.ResponseBodyModel postUrl(String url, Object params, Map<String, String> headerMap, Long rspReadLimitLenByte) {
        return postUrlWithTimeOutSecond(url, params, headerMap, rspReadLimitLenByte, null);
    }

    /**
     * @param timeOutSecond 传null则使用默认的，不为null则设置read和writeTimeOut
     */
    public HttpRspLimitLenUtil.ResponseBodyModel postUrlWithTimeOutSecond(String url, Object jsonBody, Map<String, String> headerMap, Long rspReadLimitLenByte, Integer timeOutSecond) {
        RequestBody requestBody = this.createJsonBody(jsonBody);
        Request.Builder builder = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody);
        if (timeOutSecond != null) {
            builder.tag(TimeoutSettings.class,
                    TimeoutSettings.builder().connect(2).read(timeOutSecond).write(timeOutSecond).build());
        }
        Request request = builder.build();
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)
                this.okHttpSupport.syncExecute(request, new ProxySyncCallback(rspReadLimitLenByte));
        log.debug("postUrl result, url={}, jsonBody={}, headerMap={}, result={}", url, jsonBody, headerMap, result);
        return result;
    }


    /**
     * 非200也会返回错误
     * @param timeOutSecond 传null则使用默认的，不为null则设置read和writeTimeOut
     */
    public Result<String> requestWrapException(Request.Builder resBuilder, Integer timeOutSecond) {
        try {
            String s = simpleRequest(resBuilder, timeOutSecond);
            return Result.newSuccess(s);
        } catch (Throwable e) {
            log.info("request throw exception",e);
            return Result.wrapException(e);
        }
    }


    /**
     * @param timeOutSecond 传null则使用默认的，不为null则设置read和writeTimeOut
     */
    public String simpleRequest(Request.Builder resBuilder, Integer timeOutSecond) {
        if (timeOutSecond != null) {
            resBuilder.tag(TimeoutSettings.class,
                    TimeoutSettings.builder().connect(2).read(timeOutSecond).write(timeOutSecond).build());
        }
        Request request = resBuilder.build();
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)
                this.okHttpSupport.syncExecute(request, new ProxySyncCallback(ConfigCenter.CONTENT_LENGTH_LIMIT));
        if (!result.isOk()){
            throw new ErpSyncDataException(result.buildUnOkMessage(),null,null);
        }
        if (result.isReachLengthLimit()){
            throw new ErpSyncDataException(I18NStringEnum.s3632, null);
        }
        return result.getBody();
    }


    /**
     * 基础post接口
     *
     * @param url
     * @param contentType
     * @param headerMap
     * @param body
     * @return
     */
    public HttpRspLimitLenUtil.ResponseBodyModel postUrlOriginal(String url, MediaType contentType, Map<String, String> headerMap, String body) {
        RequestBody requestBody = RequestBody.create(contentType, body);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
        log.debug("postUrl result, url={}, headerMap={}, body={}, result={}", url, headerMap, body, result);
        return result;
    }

    /**
     * 这个方法会判断response是否成功，包装为Result。
     *
     * @param url
     * @param params
     * @param headerMap
     * @return
     */
    public SimpleHttpResult postUrlResult(String url, Object params, Map<String, String> headerMap) {
        RequestBody requestBody = this.createJsonBody(params);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        Object result = this.okHttpSupport.syncExecute(request, new ResultProxySyncCallback());
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, params, headerMap, result);
        return (SimpleHttpResult) result;
    }

    public HttpRspLimitLenUtil.ResponseBodyModel postUrlSerialNull(String url, Object params, Map<String, String> headerMap, Long rspReadLimitLenByte) {
        RequestBody requestBody = params instanceof String
                ? RequestBody.create(JSON_TYPE, params.toString())
                : RequestBody.create(JSON_TYPE, JacksonUtil.toJson(params));
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)
                this.okHttpSupport.syncExecute(request, new ProxySyncCallback(rspReadLimitLenByte));
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}", url, requestBody, headerMap, result);
        return result;
    }


    public HttpRspLimitLenUtil.ResponseBodyModel getUrl(String url, Map<String, String> headerMap, Long rspReadLimitLenByte) {
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)
                this.okHttpSupport.syncExecute(request, new ProxySyncCallback(rspReadLimitLenByte));
        log.debug("getUrl result, url={}, headerMap={}, result={}", url, headerMap, result);
        return result;
    }
    /**
     * 限制返回结果的请求
     */
    public HttpRspLimitLenUtil.ResponseBodyModel limitExecute(Request request) {
        return limitExecute(request,null);
    }

    /**
     * 限制返回结果的请求
     */
    public HttpRspLimitLenUtil.ResponseBodyModel limitExecute(Request request, Long rspReadLimitLenByte) {
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)
                this.okHttpSupport.syncExecute(request, new ProxySyncCallback(rspReadLimitLenByte));
        return result;
    }

    /**
     * 限制返回结果的请求
     */
    public HttpRspLimitLenUtil.ResponseBodyModel limitExecute(Request request, Long rspReadLimitLenByte,long timeoutMillis) {
        HttpRspLimitLenUtil.ResponseBodyModel result = (HttpRspLimitLenUtil.ResponseBodyModel)
                this.okHttpSupport.syncExecute(request, new ProxySyncCallback(rspReadLimitLenByte),timeoutMillis);
        return result;
    }

    public static class ProxySyncCallback extends SyncCallback {

        private final long rspLenLimit;

        public ProxySyncCallback(Long rspLenLimit) {
            this.rspLenLimit = (null != rspLenLimit) ? rspLenLimit : ConfigCenter.LIST_CONTENT_LENGTH_LIMIT;
        }

        public ProxySyncCallback() {
            this.rspLenLimit = ConfigCenter.LIST_CONTENT_LENGTH_LIMIT;
        }

        @Override
        public HttpRspLimitLenUtil.ResponseBodyModel response(Response response) throws Exception {
            Headers headers = response.headers();
            String headerContentLenStr = response.header("Content-Length");
            //安全转换
            long headerContentLen = Convert.toLong(headerContentLenStr, 0L);
            //如果header中明确的待了 content-length且不为0， 则使用这个content-length。
            if (headerContentLen > 0) {
                //这种情况可以用header中的content length直接判断
                HttpRspLimitLenUtil.ResponseBodyModel.ResponseBodyModelBuilder builder = HttpRspLimitLenUtil.ResponseBodyModel.builder()
                        .headers(headers)
                        .code(response.code());
                if (headerContentLen > rspLenLimit) {
                    return builder
                            .reachLengthLimit(true).build();
                } else {
                    return builder
                            .reachLengthLimit(false)
                            .body(safeBody(response)).build();
                }
            }
            //header中没有有效的content-length, 那么最多读取rspLenLimit字节数据
            return HttpRspLimitLenUtil.getLimitLengthRspString(response, rspLenLimit);
        }
    }

    private static String safeBody(Response response) throws IOException {
        return response.body() != null ? response.body().string() : "";
    }

    public static class ResultProxySyncCallback extends SyncCallback {
        @Override
        public SimpleHttpResult response(Response response) throws Exception {
            return SimpleHttpResult.of(response);
        }
    }

    public static class SimpleHttpResult extends Result<String> {
        public static SimpleHttpResult of(Response response) throws IOException {
            SimpleHttpResult result = new SimpleHttpResult();
            result.setData(safeBody(response));
            if (!response.isSuccessful()) {
                result.setErrCode(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrCode());
                result.setErrMsg(String.format(ResultCodeEnum.CALL_OUT_HTTP_FAILED.getErrMsg(), response.code() + response.message()));
                log.info("call out failed,result:{},{},{}", response.code(), response.message(), result.getData());
            }
            return result;
        }
    }
}
