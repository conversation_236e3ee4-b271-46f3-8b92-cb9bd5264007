package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjCustomFunctionResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 */
@Component
public class AplConfigManager {
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;

    /**
     * url 到 函数设置<br>
     *
     * 会同时返回对象级别和连接器级别的数据
     */
    public Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> queryUrl2Apl(String tenantId, String dcId, String realObjApiName) {
        Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy = Maps.newHashMap();
        List<String> objApiNames = null;
        if (StringUtils.isNotBlank(realObjApiName)) {
            objApiNames = Lists.newArrayList(realObjApiName, "ALL");
        }
        List<ErpObjGroovyEntity> erpObjGroovyEntities = erpObjGroovyDao.queryByObjectApiNames2(tenantId, dcId, objApiNames);
        for (ErpObjGroovyEntity erpObjGroovyEntity : erpObjGroovyEntities) {
            ErpObjCustomFunctionResult connectInfoResult = new ErpObjCustomFunctionResult();
            BeanUtils.copyProperties(erpObjGroovyEntity, connectInfoResult);
            url2Groovy.put(erpObjGroovyEntity.getUrl(), connectInfoResult);
        }
        return url2Groovy;
    }
}
