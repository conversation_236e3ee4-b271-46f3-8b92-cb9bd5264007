package com.fxiaoke.open.erpsyncdata.admin.manager;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.multi.ListValueMap;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.constant.SyncTypeEnum;
import com.fxiaoke.open.erpsyncdata.apiproxy.aop.annotation.InvokeMonitor;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorDataHandler;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.ConnectorHandlerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.CheckAuthArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardListData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.NodePush2TempProcessor;
import com.fxiaoke.open.erpsyncdata.apiproxy.node.context.Push2TempCtx;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.FormatConvertUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.common.annotation.LogLevel;
import com.fxiaoke.open.erpsyncdata.common.constant.DataReceiveTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.LogLevelEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.DataMonitorScreen;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpPushIdentifyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.InvokeFeature;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.ActionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.elastic.entity.InvokeTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendAdminNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncPloyDetailSnapshotData2;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ObjectDataSyncMsg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.AllModelDubboService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncPloyDetailSnapshotService;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum.PUSH_PARALLEL_THREAD_NUM;
import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.SYSTEM_ERROR;

@Slf4j
@Component
public class ErpObjDataPushManager {

    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private ErpPushDataDao erpPushDataDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ErpPushIdentifyDao erpPushIdentifyDao;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private SpecialWayDataService specialWayDataService;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private SpeedLimitManager speedLimitManager;
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SyncPloyDetailSnapshotService syncPloyDetailSnapshotService;
    @Autowired
    private AllModelDubboService allModelDubboServiceRest;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private ErpConnectInfoManager connectInfoManager;
    @Autowired
    private NodePush2TempProcessor nodePush2TempProcessor;
    @Autowired
    private PushIdentifyManager pushIdentifyManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpTempDataManager erpTempDataManager;
    @Autowired
    private IdFieldKeyManager idFieldKeyManager;
    @Autowired
    private TriggerPollingMongoManager triggerPollingMongoManager;
    @Autowired
    private ErpConnectInfoManager erpConnectInfoManager;
    @Autowired
    private AplManager aplManager;

    @ReloadableProperty("dss.task.trigger.url")
    @Deprecated
    private String dssTaskTriggerUrl;

    //单个企业推送线程并发数单pod限制
    private static int tenantConcurrentNum = 10;

    private static final String keyAddStr = "!&(*)Ash&^d%";
    private static final int THREAD_COUNT = 100;
    private static final ThreadPoolExecutor DEFAULT_EXECUTOR = new NamedThreadPoolExecutor("CommonPoolUtil", THREAD_COUNT, Integer.MAX_VALUE);

    private static final ThreadPoolExecutor triggerQueryTempExecutor = new NamedThreadPoolExecutor("trigger_query_temp", 10, Integer.MAX_VALUE);

    private static final TimedCache<String, Semaphore> semaphoreMap = CacheUtil.newTimedCache(1000 * 60 * 5);//为防止对象随企业越来越多，设置过期时间

    static {
        //定时清理，不然超时后不会自动清理。10分钟
        semaphoreMap.schedulePrune(TimeUnit.MINUTES.toMillis(10));
    }

    static {
        //tenantConcurrentNum招商局是在配置中心配置的，后面要改为tenantconfig配置, 然后把配置中心配置的去掉
        ConfigFactory.getInstance().getConfig("variables_erp_sync", config -> {
            tenantConcurrentNum = config.getInt("dss_tenant_concurrent_num", 5);
            log.info("trace tenantConcurrentNum:{} from variables_erp_sync", tenantConcurrentNum);
        });
        log.info("trace tenantConcurrentNum: {}", tenantConcurrentNum);
    }

    public String getTokenByTenantId(String tenantId, String version, String lang) {
        ErpPushIdentifyEntity entity = erpPushIdentifyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTokenByTenantId(tenantId);
        if (entity == null) {
            return generatorTokenKey(tenantId, version, lang);
        } else {
            return entity.getToken();
        }
    }

    public String generatorTokenKey(String tenantId, String version, String lang) {
        return pushIdentifyManager.generatorTokenKey(tenantId, version);
    }

    public String validateErpObjApiName(String tenantId, String objApiName, String dataCenterId) {
        return erpObjManager.validateErpObjApiName(tenantId, objApiName, dataCenterId);
    }

    /**
     * 每次都split，可优化缓存
     */
    @LogLevel(LogLevelEnum.TRACE)
    public Result<Void> checkIp(String tenantId, HttpServletRequest request) {
        //无论是否配置，都尝试打印一下ip
        //检查请求Ip是否在配置白名单，目前仅在对配置项的企业
        ErpTenantConfigurationEntity entity = tenantConfigurationManager.findOne(tenantId, "ALL", "ALL", TenantConfigurationTypeEnum.IP_REQUEST_TENANT_WHITE_LIST.name());
        if (ObjectUtils.isNotEmpty(entity) && StringUtils.isNotBlank(entity.getConfiguration())) {
            List<String> ipLists = Splitter.on(";").splitToList(entity.getConfiguration());
            String ipAddr = IPUtils.getIpAddr(request);
            if (!ipLists.contains(ipAddr)) {
                log.info("checkIp IPUtils.getIpAddr,tenantId:{},{}", tenantId, ipAddr);
                return Result.newErrorExtra(ResultCodeEnum.REQUEST_IP_NOT_EXISTS_LIST, ipAddr);
            }
        }
        return Result.newSuccess();
    }


    @LogLevel(LogLevelEnum.TRACE)
    public Result<Void> checkAuth(String tenantId, String dcId, HttpHeaders headers, String token, String version, boolean isTlsV10OrV11) {
        //先验证连接器

        ErpPushIdentifyEntity pushIdentifyEntity =
                erpPushIdentifyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTokenByTenantIdAndVersion(tenantId, version);

        if (pushIdentifyEntity != null && pushIdentifyEntity.getToken().equals(token)) {
            if (isTlsV10OrV11) {
                log.info("receive request use TLSv1 or TLSv1.1,tenantId: {}", tenantId);
                if (pushIdentifyEntity.getCreateTime() > ConfigCenter.notSupportTlsV11Time) {
                    // 2024-08-17 00:00:00 后，不允许 TLSv1和TLSv1.1连接
                    log.warn("reject request use TLSv1 or TLSv1.1,tenantId:{} ", tenantId);
                    return Result.newError(ResultCodeEnum.NOT_SUPPORT_PROTOCOL_VERSION);
                }
            }
            return Result.newSuccess();
        }

        //尝试走连接器的实现
        ErpConnectInfoEntity connectInfo = connectInfoManager.getByIdAndTenantId(tenantId, dcId);
        if (connectInfo != null) {
            ConnectorDataHandler dataHandler = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
            if (dataHandler != null) {
                CheckAuthArg checkAuthArg = CheckAuthArg.builder().headers(headers).build();
                Result<Void> processResult = dataHandler.checkPushAuth(checkAuthArg, connectInfo);
                return processResult;
            }
        }
        return Result.newError(ResultCodeEnum.NO_USER);
    }


    /**
     * <p>erp推送数据到平台处理逻辑。</p>
     *
     * @param tenantId
     * @param dataCenterId
     * @param realObjectApiName 真实对象apiName
     * @param operationType
     * @param obj
     * @param id
     * @param directSync        直接同步
     * @return
     * <AUTHOR>
     */
    @InvokeMonitor(tenantId = "#tenantId", dcId = "#dataCenterId", objAPIName = "#realObjectApiName", invokeType = InvokeTypeEnum.ERP, data = "#directSync?#result:#obj", count = "#directSync?1:(#result?.data?.get('dataSize')?:1)", dataId = "#id", sourceEventType = "#operationType!=null && new Integer(#operationType) > 0 ? new Integer(#operationType) : null", action = ActionEnum.PUSH, invokeFeatures = {InvokeFeature.calculateResultSize, InvokeFeature.sendBizLog})
    @DataMonitorScreen(tenantId = "#tenantId", dataCenterId = "#dataCenterId", outSideObjApiName = "#realObjectApiName", outDataCount = "#directSync?1:(#result?.data?.get('dataSize')?:1)", sourceSystemType = "2", operationType = CommonConstant.READ_OPERATE_TYPE, operationTypeDetail = ErpObjInterfaceUrlEnum.push, operateStatus = "'s106240000'.equals(#result?.getErrCode())?1:2")
    public Result<?> stdErpPushDataToDss(String tenantId,
                                         String dataCenterId,
                                         String realObjectApiName,
                                         String operationType,
                                         String obj,
                                         HttpHeaders headers,
                                         String id,
                                         boolean directSync,
                                         String destObjectApiName) {
        StringRequest stringRequest = new StringRequest();
        ListValueMap<String, String> headersMap = Opt.ofNullable(headers).map(v -> new ListValueMap<>(v)).orElseGet(ListValueMap::new);
        stringRequest.setBody(obj).setHeaders(headersMap);
        StdPushArg stdPushArg = new StdPushArg();
        stdPushArg.setOperationType(operationType)
                .setDataId(id)
                .setDirectSync(directSync)
                .setRealObjectApiName(realObjectApiName).setDestObjectApiName(destObjectApiName);
        return erpPushDataToDssCombine(tenantId, dataCenterId, true, stringRequest, stdPushArg);
    }


    /**
     * <p>erp推送数据到平台处理逻辑。</p>
     */
    public Result<?> erpPushDataToDssCombine(String tenantId,
                                             String dataCenterId,
                                             boolean isStdPush,
                                             StringRequest request,
                                             StdPushArg pushArg) {
        Semaphore semaphore = getSemaphore(tenantId);
        boolean concurrency = false;
        try {
            //控制并发
            concurrency = semaphore.tryAcquire(100, TimeUnit.MILLISECONDS);
            if (!concurrency) {
                return Result.newSystemError(I18NStringEnum.s2011);
            }
            //request => pushData => pushResult =>response
            if (isStdPush) {
                //标准推送 标准参数=>标准推送数据
                Result<PushData> pushDataResult = stdProcessRequest(tenantId, dataCenterId, request.getBody(), GsonUtil.toJson(request.getHeaders()), pushArg);
                //标准格式数据处理
                PushResult pushResult = processPushDataResult(tenantId, dataCenterId, pushDataResult);
                //后处理
                return stdProcessResult(pushArg, pushResult);
            } else {
                //非标准推送
                ErpConnectInfoEntity connectInfo = erpConnectInfoManager.getByIdAndTenantId(tenantId, dataCenterId);
                if (connectInfo == null) {
                    return Result.newError(ResultCodeEnum.NOT_FOUND_CONNECTOR);
                }
                //查询是否有函数，有函数统一走函数处理
                ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao
                        .getByTenantIdAndApiNameAndUrl(tenantId, dataCenterId, "ALL", ErpObjInterfaceUrlEnum.webhook.name());
                String aplApiName = Opt.ofNullable(erpObjGroovyEntity).map(v -> v.getFuncApiName()).get();
                boolean useApl = StrUtil.isNotBlank(aplApiName);
                if (useApl) {
                    //apl类实现，转换数据格式
                    Result<PushData> pushDataResult = aplManager.executeAplMethod(tenantId, dataCenterId, "ALL", ErpObjInterfaceUrlEnum.webhookProcessRequest, aplApiName, Lists.newArrayList(request), new TypeReference<PushData>() {
                    });
                    //标准格式数据处理, 和上面的调用是一样的
                    PushResult pushResult = processPushDataResult(tenantId, dataCenterId, pushDataResult);
                    pushResult.setRequest(request);
                    Result<StringResponse> stringResponseResult = aplManager.executeAplMethod(tenantId, dataCenterId, "ALL", ErpObjInterfaceUrlEnum.webhookProcessResponse, aplApiName, Lists.newArrayList(pushResult), new TypeReference<StringResponse>() {
                    });
                    return stringResponseResult;
                } else {
                    //连接器实现
                    ConnectorDataHandler erpDataManager = ConnectorHandlerFactory.getDataHandler(connectInfo.getChannel(), connectInfo.getConnectParams());
                    Result<PushData> pushDataResult = erpDataManager.webhookProcessRequest(request, connectInfo);
                    if (!pushDataResult.isSuccess() && ResultCodeEnum.HUB_UNSUPPORTED.match(pushDataResult.getErrCode())) {
                        //如果未实现新接口，尝试调用旧逻辑
                        log.info("webhook HUB_UN_SUPPORT, try use v1 impl,tenantId:{},dcId:{}", tenantId, dataCenterId);
                        return webhookV1(tenantId, dataCenterId, request, erpDataManager, connectInfo);
                    }
                    //标准格式数据处理, 和上面的调用是一样的
                    PushResult pushResult = processPushDataResult(tenantId, dataCenterId, pushDataResult);
                    Result<StringResponse> stringResponseResult = erpDataManager.webhookProcessResponse(pushResult, connectInfo);
                    return stringResponseResult;
                }

            }
        } catch (Exception e) {
            log.warn("execute erpPushDataToDss error:tenantId:{},datas:{}", tenantId, request.getBody(), e);
            //对于平台异常，直接返回异常信息，方便调用方感知。
            return Result.wrapException(e);
        } finally {
            if (concurrency) {
                semaphore.release();
            }
        }
    }


    private Result<StringResponse> webhookV1(String tenantId, String dcId, StringRequest arg, ConnectorDataHandler erpDataManager, ErpConnectInfoEntity connectInfo) {
        Result<StringResponse> result = erpDataManager.webhook(arg, connectInfo);
        //数据处理
        if (!result.isSuccess()) {
            return result.error();
        }
        StringResponse webhookRes = result.getData();
        if (webhookRes.getDataMap() != null) {
            webhookRes.getDataMap().forEach(((eventType, dataList) -> {
                push2TempAsync(tenantId, dcId, eventType, dataList);
            }));
        }
        return Result.newSuccess(webhookRes);
    }

    @NotNull
    private Result<?> stdProcessResult(StdPushArg pushArg, PushResult pushResult) {
        if (pushResult.isSuccess()) {
            //成功时
            if (pushArg.isDirectSync()) {
                return Result.newSuccess(pushResult.getDirectSyncResultData());
            } else {
                Result<StandardData> asyncSuccessResult = new Result<>();
                asyncSuccessResult.setErrMsg(I18NStringEnum.s1159.getText());
                return asyncSuccessResult;
            }
        } else {
            //异常的 都是 返回有一个dataSize的结构（可能为null）
            Result<DataSizeResult> dataSizeResult = new Result<>();
            dataSizeResult.copyFrom(pushResult);
            dataSizeResult.setData(new DataSizeResult(pushResult.getDataSize()));
            return dataSizeResult;
        }
    }


    private PushResult processPushDataResult(String tenantId, String dcId, Result<PushData> dataResult) {
        //计算dataSize
        PushResult pushResult = new PushResult();
        if (!dataResult.isSuccess()) {
            pushResult.copyFrom(dataResult);
            return pushResult;
        }
        PushData pushData = dataResult.getData();
        Integer dataCount = pushData.countData();
        pushResult.setDataSize(dataCount);
        String realObjApiName = pushData.extraFirstObjApiName();
        //发送数据数量太多的告警，但只是发送告警。
        sendDataSizeTooMoreNotify(tenantId, dcId, realObjApiName, dataCount, I18nUtil.getLocaleFromTrace());
        String destObjApiName = pushData.getDestObjApiName();
        try {
            for (Map.Entry<EventTypeEnum, List<StandardData>> entry : pushData.getDataMap().entrySet()) {
                EventTypeEnum eventTypeEnum = entry.getKey();
                List<StandardData> dataList = entry.getValue();
                Integer eventTypeInt = null;
                if (eventTypeEnum.getType() > 2) {
                    eventTypeInt = eventTypeEnum.getType();
                }
                Result<StandardData> result = processPushDataList(tenantId, dcId, eventTypeInt, pushData.isDirectSync(), destObjApiName, dataList);
                if (!result.isSuccess()) {
                    //异常时，直接返回结果
                    pushResult.copyFrom(result);
                    return pushResult;
                }
                if (pushData.isDirectSync()) {
                    //同步推送的需要设置一下同步推送结果,并且同步推送只会有一条数据
                    pushResult.setDirectSyncResultData(result.getData());
                    return pushResult;
                }
            }
        } catch (Exception e) {
            //异常时，包装异常结果返回
            pushResult.copyFrom(Result.wrapException(e));
        }
        return pushResult;
    }


    /**
     * 处理标准推送参数
     *
     * @return 可能为 {@link DataSizeResult} 或者 {@link StandardData}
     */
    public Result<StandardData> processPushDataList(String tenantId, String dataCenterId, @Nullable Integer eventTypeInt, boolean directSync, String destObjectApiName, List<StandardData> dataList) {
        String locale = I18nUtil.getLocaleFromTrace();
        if (CollUtil.isEmpty(dataList)) {
            return Result.newSuccess();
        }
        //同步推送的预检查
        Result<Void> passPreCheckDirectSync = preCheckDirectSync(tenantId, directSync, destObjectApiName, dataList);
        if (!passPreCheckDirectSync.isSuccess()) return Result.copy(passPreCheckDirectSync);


        //如果返回的数据有objapiName。根据objectAPIname进行入库操作。支持拆分到不同的对象操作
        Map<String, List<StandardData>> objectDataListMap =
                dataList.stream().collect(Collectors.groupingBy(StandardData::getObjAPIName));
        String splitObjApiName = null, dataId = null;//同步推送用到，这里只允许一条数据
        SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData2 = null;
        for (Map.Entry<String, List<StandardData>> entry : objectDataListMap.entrySet()) {
            String realObjApiName = entry.getKey();
            splitObjApiName = validateErpObjApiName(tenantId, realObjApiName, dataCenterId);
            if (splitObjApiName == null) {
                //无法找到对象
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s1163, dataCenterId, realObjApiName);
            }
            if (directSync) {
                //直接推送的校验
                List<SyncPloyDetailSnapshotData2> list = syncPloyDetailSnapshotService.listNewestEnableSyncPloyDetailsSnapshots(tenantId, splitObjApiName, TenantType.ERP, Lists.newArrayList(tenantId)).getData();
                if (!CollectionUtils.isEmpty(list)) {
                    for (SyncPloyDetailSnapshotData2 snapshotData2 : list) {
                        if (snapshotData2.getDestObjectApiName().equals(destObjectApiName)) {
                            syncPloyDetailSnapshotData2 = snapshotData2;
                        }
                    }
                }
                if (syncPloyDetailSnapshotData2 == null) {
                    return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s2014, splitObjApiName, destObjectApiName);
                } else {
                    if (syncPloyDetailSnapshotData2.getSyncPloyDetailData() != null && syncPloyDetailSnapshotData2.getSyncPloyDetailData().getSyncRules() != null
                            && syncPloyDetailSnapshotData2.getSyncPloyDetailData().getSyncRules().getSyncTypeList() != null
                            && !syncPloyDetailSnapshotData2.getSyncPloyDetailData().getSyncRules().getSyncTypeList().contains(SyncTypeEnum.push.name())) {
                        return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s2015, splitObjApiName, destObjectApiName);
                    }
                }
            }

            List<ErpObjectFieldEntity> idFieldList = erpFieldManager.findByObjApiNameAndType(tenantId, splitObjApiName, Lists.newArrayList(ErpFieldTypeEnum.id));
            if (!CollectionUtils.isEmpty(idFieldList)) {//校验
                String idFieldKey = idFieldList.get(0).getFieldApiName();
                for (StandardData standardData : entry.getValue()) {
                    if (StringUtils.isBlank(standardData.getMasterFieldVal().getString(idFieldKey))) {//id字段为空,遇到一个就返回了
                        return new Result<>(ResultCodeEnum.PUSH_DATA_ID_IS_NULL, JacksonUtil.toJson(standardData));
                    }
                    dataId = standardData.getMasterFieldVal().getString(idFieldKey);//只允许一个的时候用到
                }
            } else {
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL.getErrCode(), I18NStringEnum.s2016, dataCenterId, realObjApiName);
            }
            IdFieldKey idFieldKey = idFieldKeyManager.buildIdFieldKey(tenantId, dataCenterId, splitObjApiName, realObjApiName);
            StandardListData standardListData = new StandardListData();
            standardListData.setDataList(dataList);
            try {
                //入临时库
                erpTempDataManager.batchUpsertErpTempData(tenantId, dataCenterId, realObjApiName, eventTypeInt, standardListData, idFieldKey, !directSync, locale);
            } catch (Exception e) {
                //入库异常，返回错误
                log.info("process push data batchUpsertErpTempData error", e);
                return Result.wrapException(e);
            }
            if (!directSync) {//异步触发轮询临时库
                String finalSplitObjApiName = splitObjApiName;
                triggerQueryTempExecutor.submit(() -> triggerPollingTemp(tenantId, finalSplitObjApiName, realObjApiName));
            } else {
                //同步推送 只会有一条，处理后直接范湖及结果
                Result<StandardData> directResult = directSyncPushData2Dest(tenantId, dataCenterId, realObjApiName, splitObjApiName, dataId, destObjectApiName, eventTypeInt, syncPloyDetailSnapshotData2, locale);
                return directResult;
            }
        }
        //异步推送 data为空
        return Result.newSuccess();
    }


    @NotNull
    public Result<PushData> stdProcessRequest(String tenantId,
                                              String dataCenterId,
                                              String obj,
                                              String headerStr,
                                              StdPushArg pushArg
    ) {
        final EventTypeEnum eventTypeEnum = ObjectUtil.defaultIfNull(EventTypeEnum.byTypeStr(pushArg.getOperationType()), EventTypeEnum.UPDATE);
        final String realObjApiName = pushArg.getRealObjectApiName();
        List<StandardData> dataList;
        if (StringUtils.isNotEmpty(pushArg.getDataId())) {
            JSONObject jsonObject = JSON.parseObject(obj);
            StandardData standardData = FormatConvertUtil.json2Std(jsonObject);
            dataList = Lists.newArrayList(standardData);
        } else {
            //1. 执行groovy脚本生成标准结构数据，数组形式返回
            Result<List<StandardData>> dataResult = processDataByApl(tenantId, dataCenterId, realObjApiName, headerStr, obj);
            if (!dataResult.isSuccess()) {
                return Result.copy(dataResult);
            }
            dataList = dataResult.getData();
        }
        dataList.forEach(t -> {
            if (StringUtils.isEmpty(t.getObjAPIName())) {
                t.setObjAPIName(realObjApiName);
            }
            t.setDataReceiveType(DataReceiveTypeEnum.OUTSIDE_PUSH_DATA.getType());//外部推送
        });
        //标准推送只会有一种类型
        PushData pushData = new PushData();
        pushData.setDestObjApiName(pushArg.getDestObjectApiName());
        pushData.setDirectSync(pushArg.isDirectSync());
        pushData.getDataMap().put(eventTypeEnum, dataList);
        return Result.newSuccess(pushData);
    }

    private void sendDataSizeTooMoreNotify(String tenantId, String dataCenterId, String realObjApiName, Integer count, String locale) {
        if (count > ConfigCenter.PUSH_DATA_SIZE_NOTIFY) {
            String msg = i18NStringManager.get2(I18NStringEnum.s1162.getI18nKey(),
                    locale,
                    tenantId,
                    String.format(I18NStringEnum.s1162.getI18nValue(), realObjApiName, count, ConfigCenter.PUSH_DATA_SIZE_NOTIFY),
                    Lists.newArrayList(realObjApiName, count + "", ConfigCenter.PUSH_DATA_SIZE_NOTIFY + ""));
            notificationService.sendSuperAdminNotice(SendAdminNoticeArg.builder()
                    .tenantId(tenantId)
                    .dcId(dataCenterId)
                    .msgTitle(i18NStringManager.get(I18NStringEnum.s1161, locale, tenantId))
                    .msg(msg)
                    .build());
        }
    }

    @NotNull
    private Result<Void> preCheckDirectSync(String tenantId, boolean directSync, String destObjectApiName, List<StandardData> dataList) {
        if (directSync) {
            if (dataList.size() > 1) {
                //同步推送只支持单条
                return Result.newError(ResultCodeEnum.NOT_SUPPORT_GT2);
            }
            if (StringUtils.isBlank(destObjectApiName)) {
                return Result.newError(ResultCodeEnum.DEST_OBJECT_API_NAME_IS_NULL);
            } else {
                Map<String, List<String>> batchWrite2Crm = configCenterConfig.getBatchWrite2CrmTenantAndObjApiName();
                if (batchWrite2Crm != null && batchWrite2Crm.containsKey(tenantId) && batchWrite2Crm.get(tenantId) != null
                        && batchWrite2Crm.get(tenantId).contains(destObjectApiName)) {
                    //不支持批量写
                    return Result.newError(ResultCodeEnum.NOT_SUPPORT_BATCH_WRITE_OBJ);
                }
            }

            // 只在主判段,只有主过了，就继续同步主及所有从. 同步推送接口不支持批量接口。
            long remain = speedLimitManager.getRemainLimit(tenantId, SpeedLimitTypeEnum.TO_CRM);
            //long remain = speedLimitManager.getObjRemainLimit(tenantId, TenantType.ERP,  Lists.newArrayList(destObjectApiName), objectApiName);
            if (remain <= 0) {
                return Result.newError(ResultCodeEnum.PUSH_ERP_DATA_LIMIT);
            }
        }
        return Result.newSuccess();
    }

    @NotNull
    private Result<List<StandardData>> processDataByApl(String tenantId, String dataCenterId, String objectApiName, String header, String obj) {

        ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getByTenantIdAndApiNameAndUrl(tenantId, dataCenterId, objectApiName,
                        ErpObjInterfaceUrlEnum.push.name());
        HashMap<String, Object> map = new HashMap<>();
        map.put("pushData", obj);
        map.put("header", header);
        map.put("objAPIName", objectApiName);
        Result<List<StandardData>> dataResult = executeCustomFunction(tenantId, dataCenterId, erpObjGroovyEntity.getFuncApiName(), map);
        if (!dataResult.isSuccess()) {
            return Result.copy(dataResult);
        }
        return dataResult;
    }


    private Semaphore getSemaphore(String tenantId) {
        if (semaphoreMap.get(tenantId) == null) {
            synchronized (this) {
                if (semaphoreMap.get(tenantId) == null) {
                    int configConcurrentNum = tenantConcurrentNum;
                    //读不到配置，采用默认值
                    Integer configIntValue = tenantConfigurationManager.getIntegerValue(tenantId, PUSH_PARALLEL_THREAD_NUM);
                    if (null != configIntValue) {
                        log.debug("trace param threadnum,ei:{}, configIntValue:{}", tenantId, configIntValue);
                        configConcurrentNum = configIntValue;
                    }
                    log.info("trace param threadnum,ei:{}, configConcurrentNum:{}", tenantId, configConcurrentNum);
                    Semaphore semaphore = new Semaphore(configConcurrentNum);
                    semaphoreMap.put(tenantId, semaphore);
                }
            }
        }
        return semaphoreMap.get(tenantId);
    }

    @LogLevel(LogLevelEnum.TRACE)
    public Result<StandardData> directSyncPushData2Dest(String tenantId, String dataCenterId, String realApiName, String erpVisualApiName, String dataId,
                                                        String destObjectApiName, Integer operationType, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData2, String locale) {
        Result<StandardData> objectResult = Result.newSuccess();
        if (syncPloyDetailSnapshotData2 == null) {
            objectResult.setErrCode(ResultCodeEnum.SYSTEM_ERROR.getErrCode());
            objectResult.setErrMsg(i18NStringManager.get2(I18NStringEnum.s2014,
                    locale,
                    tenantId,
                    erpVisualApiName, destObjectApiName));
            return objectResult;
        }
        boolean masterIsDelete = EventTypeEnum.DELETE_DIRECT.match(operationType);
        boolean masterIsInvalid = EventTypeEnum.INVALID.match(operationType);
        SyncDataContextEvent masterEvent = null;
        List<SyncDataContextEvent> detailEvents = Lists.newArrayList();
        List<SyncDataContextEvent> removeDetailEvents = Lists.newArrayList();
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setObjAPIName(erpVisualApiName);
        erpIdArg.setIncludeDetail(true);
        erpIdArg.setDataId(dataId);
        erpIdArg.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotData2.getId());
        if(!masterIsDelete){
            int sourceEventType = operationType == null ? EventTypeEnum.ADD.getType() : operationType;
            erpIdArg.setSourceEventType(sourceEventType);
            Result<List<SyncDataContextEvent>> erpObjDataResult = erpDataPreprocessService.getReSyncObjDataById(erpIdArg);
            if (!erpObjDataResult.isSuccess() || CollectionUtils.isEmpty(erpObjDataResult.getData())) {
                return Result.copy(erpObjDataResult);
            }
            List<SyncDataContextEvent> dataContextEvents = erpObjDataResult.getData();//只有一个是主数据
            for (SyncDataContextEvent event : dataContextEvents) {
                if (erpVisualApiName.equals(event.getSourceData().getApiName())) {
                    //主数据
                    event.setSourceTenantType(TenantType.ERP);
                    event.setSourceDataCenterId(dataCenterId);
                    SyncDataContextEvent syncDataContextEvent = new SyncDataContextEvent();
                    BeanUtils.copyProperties(event, syncDataContextEvent);
                    syncDataContextEvent.setDetailData(null);//去掉从对象，主从拆开;
                    masterEvent = syncDataContextEvent;
                    masterEvent.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotData2.getId());
                    masterEvent.setLocale(locale);
                    if (masterIsInvalid) {
                        masterEvent.setSourceEventType(EventTypeEnum.INVALID.getType());
                    }
                    //从对象数据
                    if (event.getDetailData() == null) {
                        continue;
                    }
                    //从对象数据
                    for (List<ObjectData> detailDatas : event.getDetailData().values()) {//还存在的明细数据
                        for (ObjectData detailData : detailDatas) {
                            SyncDataContextEvent eventData = new SyncDataContextEvent();
                            eventData.setSourceEventType(event.getSourceEventType());
                            eventData.setSourceData(detailData);
                            eventData.setSourceTenantType(TenantType.ERP);
                            event.setSourceDataCenterId(dataCenterId);
                            eventData.setSyncLogId(event.getSyncLogId());
                            eventData.setDataReceiveType(event.getDataReceiveType());
                            eventData.setDataVersion(event.getDataVersion());
                            eventData.setSyncPloyDetailSnapshotId(syncPloyDetailSnapshotData2.getId());
                            eventData.setLocale(locale);
                            if (masterIsInvalid) {
                                eventData.setSourceEventType(EventTypeEnum.INVALID.getType());
                            }
                            detailEvents.add(eventData);
                        }
                    }
                } else {//作废的明细
                    event.setSourceTenantType(TenantType.ERP);
                    detailEvents.add(event);
                    removeDetailEvents.add(event);
                }
            }
        }else{
            erpIdArg.setSourceEventType(EventTypeEnum.DELETE_DIRECT.getType());
            Result<SyncDataContextEvent> result=erpDataPreprocessService.getErpObjDataFromMongoIfExist(erpIdArg);
            if (!result.isSuccess()) {
                return Result.copy(result);
            }
            masterEvent=result.getData();
            masterEvent.setSourceTenantType(TenantType.ERP);
            masterEvent.setSourceEventType(EventTypeEnum.DELETE_DIRECT.getType());
        }
        List<ObjectDataSyncMsg.SingleDataSyncMsg> dataSyncResultList = Lists.newArrayList();
        if (masterEvent != null) {
            //日志id
            LogIdUtil.reset(masterEvent.getSyncLogId());
            LogIdUtil.setRealObjApiName(realApiName);
            Result2<ObjectDataSyncMsg> mainResult = allModelDubboServiceRest.syncDataMain(masterEvent);
            //不成功就直接返回了
            if (!mainResult.isSuccess() || mainResult.getData() == null || CollectionUtils.isEmpty(mainResult.getData().getDataSyncResultList())) {
                objectResult.setErrCode(mainResult.getErrCode());
                objectResult.setErrMsg(mainResult.getErrMsg());
                return objectResult;
            }
            List<ObjectDataSyncMsg.SingleDataSyncMsg> masterSyncResultList = mainResult.getData().getDataSyncResultList();
            dataSyncResultList.addAll(masterSyncResultList);
            StandardData needReturnData = new StandardData();
            if (masterSyncResultList.size() == 1) {//目前通过这个来判断是否需要处理从对象
                if (masterSyncResultList.get(0).getNeedReturnDestObjectData() != null) {
                    needReturnData.setMasterFieldVal(masterSyncResultList.get(0).getNeedReturnDestObjectData());
                }
                if (!CollectionUtils.isEmpty(detailEvents)) {
                    for (SyncDataContextEvent detailEvent : detailEvents) {
                        Result2<ObjectDataSyncMsg> detailResult = allModelDubboServiceRest.syncDataMain(detailEvent);
                        if (!detailResult.isSuccess() || detailResult.getData() == null || CollectionUtils.isEmpty(detailResult.getData().getDataSyncResultList())) {
                            ObjectDataSyncMsg.SingleDataSyncMsg detailSyncMsg = new ObjectDataSyncMsg.SingleDataSyncMsg();
                            detailSyncMsg.setSourceDataId(detailEvent.getDataId());
                            detailSyncMsg.setSourceObjectApiName(detailEvent.getSourceData().getApiName());
                            detailSyncMsg.setWriteDestSucc(false);
                            detailSyncMsg.setLastSyncNode(DataNodeNameEnum.Exception.getName());
                            detailSyncMsg.setMsg(detailResult.getErrMsg());
                            dataSyncResultList.add(detailSyncMsg);
                        } else {
                            ObjectDataSyncMsg.SingleDataSyncMsg detailMsg = detailResult.getData().getDataSyncResultList().get(0);
                            if (detailMsg != null && detailMsg.getNeedReturnDestObjectData() != null) {
                                if (needReturnData.getDetailFieldVals().containsKey(detailMsg.getDestObjectApiName())) {
                                    needReturnData.getDetailFieldVals().get(detailMsg.getDestObjectApiName()).add(detailMsg.getNeedReturnDestObjectData());
                                } else {
                                    needReturnData.getDetailFieldVals().put(detailMsg.getDestObjectApiName(), Lists.newArrayList(detailMsg.getNeedReturnDestObjectData()));
                                }
                            }
                            dataSyncResultList.addAll(detailResult.getData().getDataSyncResultList());
                        }
                    }
                }
            } else {
                for (ObjectDataSyncMsg.SingleDataSyncMsg msg : masterSyncResultList) {
                    if (msg.getNeedReturnDestObjectData() != null) {
                        if (erpVisualApiName.equals(msg.getSourceObjectApiName())) {
                            needReturnData.setMasterFieldVal(msg.getNeedReturnDestObjectData());
                        } else {

                            if (needReturnData.getDetailFieldVals().containsKey(msg.getDestObjectApiName())) {
                                needReturnData.getDetailFieldVals().get(msg.getDestObjectApiName()).add(msg.getNeedReturnDestObjectData());
                            } else {
                                needReturnData.getDetailFieldVals().put(msg.getDestObjectApiName(), Lists.newArrayList(msg.getNeedReturnDestObjectData()));
                            }
                        }
                    }
                }
                // 监听了crm删除事件全网后,从对象数据会作废并删除中间表,无需在处理从对象作废
            }
            List<ObjectDataSyncMsg.SingleDataSyncMsg> failedSyncResultList = dataSyncResultList.stream().filter(v -> v.getWriteDestSucc() != null && !v.getWriteDestSucc()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(failedSyncResultList)) {
                StringBuilder msg = new StringBuilder();
                if (operationType != null) {
                    if(operationType == EventTypeEnum.INVALID.getType()){
                        msg.append(i18NStringManager.getByEi(I18NStringEnum.s365, tenantId)).append("[").append(masterEvent.getSourceData().getId()).append("]。");
                    } else if (operationType == EventTypeEnum.DELETE_DIRECT.getType()) {
                        msg.append(i18NStringManager.getByEi(I18NStringEnum.s676, tenantId)).append("[").append(masterEvent.getSourceData().getId()).append("]。");
                    }
                }
                if (!CollectionUtils.isEmpty(removeDetailEvents)) {
                    msg.append(i18NStringManager.getByEi(I18NStringEnum.s2017, tenantId));
                    List<String> ids = removeDetailEvents.stream().map(data -> data.getSourceData().getId()).collect(Collectors.toList());
                    msg.append(ids);
                    objectResult.setErrMsg(msg.toString());
                }
                if (!masterIsInvalid&&!masterIsDelete) {
                    if (needReturnData != null) {
                        if (needReturnData.getMasterFieldVal() != null || !(needReturnData.getDetailFieldVals() == null || needReturnData.getDetailFieldVals().isEmpty())) {
                            objectResult.setData(needReturnData);
                        }
                    }
                }
                return objectResult;
            } else {
                StringBuilder builder = new StringBuilder();
                for (ObjectDataSyncMsg.SingleDataSyncMsg dataSyncMsg : failedSyncResultList) {
                    builder.append(dataSyncMsg.getSourceDataId()).append(":").append(dataSyncMsg.getMsg()).append(System.lineSeparator());
                }
                objectResult.setErrCode(ResultCodeEnum.PUSH_DIRECT_SYNC_FAILED.getErrCode());
                objectResult.setErrMsg(builder.toString());
                return objectResult;
            }
        }
        return objectResult;
    }

    /**
     *
     */
    public void pushErpData2Table(String tenantId,
                                  String objectApiName,
                                  String visualObjectApiName,
                                  String operationType,
                                  List<StandardData> dataList,
                                  String id,
                                  String dataCenterId) {
        List<ErpPushDataEntity> insertData = new ArrayList<>();
        List<ErpPushDataEntity> updateData = new ArrayList<>();
        executeAndWait(dataList, insertData, updateData, tenantId, objectApiName, visualObjectApiName, operationType, id, dataCenterId);
        if (insertData.size() > 0) {
            checkInsertData(insertData);
            erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).batchInsert(insertData);
        }
        for (ErpPushDataEntity updateDatum : updateData) {
            erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(updateDatum);
        }

        triggerPollingErp(tenantId, visualObjectApiName, objectApiName, dataList);
        //上层有一个catch，这个catch不要了。
    }

    /**
     * 触发轮询ERP（会同时触发轮询临时库）
     *
     * @param tenantId
     * @param visualObjectApiName
     * @param objectApiName
     * @param dataList
     */
    private void triggerPollingErp(String tenantId, String visualObjectApiName, String objectApiName, List<StandardData> dataList) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "UTF-8");
        String url = dssTaskTriggerUrl + "?tenantId=" + tenantId + "&objApiName=" + visualObjectApiName;
        HttpRspLimitLenUtil.ResponseBodyModel triggerResponse = proxyHttpClient.postUrl(url, Collections.emptyMap(), header, ConfigCenter.LIST_CONTENT_LENGTH_LIMIT);
        log.info("receive data success and trigger tenantId:{},objApiName:{},datas:{},trigger response:{}", tenantId, objectApiName, dataList, triggerResponse);
    }

    public void push2TempAsync(String tenantId,
                               String dataCenterId,
                               EventTypeEnum eventType,
                               List<StandardData> multiDataList) {
        Map<String, List<StandardData>> objectDataListMap = multiDataList.stream()
                //未返回 objApiName将忽略
                .filter(v -> v.getObjAPIName() != null)
                .collect(Collectors.groupingBy(StandardData::getObjAPIName));
        for (Map.Entry<String, List<StandardData>> entry : objectDataListMap.entrySet()) {
            String objApiName = entry.getKey();
            List<StandardData> dataList = entry.getValue();
            String erpVisualApiName = validateErpObjApiName(tenantId, objApiName, dataCenterId);
            if (erpVisualApiName == null) {
                log.warn("not found obj,{},{},{}", tenantId, dataCenterId, objApiName);
                return;
            }
            pushErpData2TempData(tenantId, objApiName, erpVisualApiName, eventType.getType(), dataList, dataCenterId, false, null);
        }
    }

    /**
     * 将数据推送到临时库
     *
     * @param objectApiName       根据前面的代码推测，objectApiName应该是不能为空的。
     * @param visualObjectApiName 根据前面的代码推测，erpVisualApiName应该是不能为空的。
     * @param locale
     */
    public void pushErpData2TempData(String tenantId,
                                     String objectApiName,
                                     String visualObjectApiName,
                                     Integer operationTypeInt,
                                     List<StandardData> dataList,
                                     String dataCenterId,
                                     boolean directSync,
                                     String locale) {
        Push2TempCtx ctx = new Push2TempCtx()
                .setBasic(tenantId, dataCenterId)
                .setRealObjApiName(objectApiName)
                .setSplitObjApiName(visualObjectApiName)
                .setOperationType(operationTypeInt)
                .setDataList(dataList)
                .setDirectSync(directSync)
                .setLocale(locale);
        nodePush2TempProcessor.processMessage(ctx);
    }

    private void checkInsertData(List<ErpPushDataEntity> insertData) {
        //检查插入数据
        Set<String> dup = new HashSet<>();
        for (ErpPushDataEntity v : insertData) {
            boolean add = dup.add(v.getTenantId() + v.getObjectApiName() + v.getSourceDataId() + v.getDataCenterId());
            if (!add) {
                throw new ErpSyncDataException(I18NStringEnum.s132, v.getTenantId());
            }
        }

    }

    public Map<String, ErpPushDataEntity> getEntity(String tenantId,
                                                    String objectApiName,
                                                    String visualObjectApiName,
                                                    String operationType,
                                                    StandardData standardData,
                                                    String id,
                                                    String dataCenterId) {
        standardData.setObjAPIName(objectApiName);
        String sourceDataId = id;
        Map<String, ErpPushDataEntity> result = Maps.newHashMap();
        if (StringUtils.isEmpty(sourceDataId)) {
            List<ErpObjectFieldEntity> fieldEntityList = erpFieldManager.findByObjApiNameAndType(tenantId,
                    visualObjectApiName,
                    Lists.newArrayList(ErpFieldTypeEnum.id));
            String idFieldApiName = fieldEntityList.get(0).getFieldApiName();
            sourceDataId = standardData.getMasterFieldVal().getString(idFieldApiName);
        }
        ErpPushDataEntity erpPushDataEntity =
                erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTenantObjectId(tenantId, objectApiName, sourceDataId, dataCenterId);
        if (erpPushDataEntity == null || StringUtils.isEmpty(sourceDataId)) {
            erpPushDataEntity = new ErpPushDataEntity();
            erpPushDataEntity.setCreateTime(new Date().getTime());
            erpPushDataEntity.setId(idGenerator.get());
            erpPushDataEntity.setOperationType(1);
            result.put("insert", erpPushDataEntity);
        } else {
            erpPushDataEntity.setOperationType(2);
            result.put("update", erpPushDataEntity);
        }
        if (operationType != null && Integer.valueOf(operationType) > 2) {
            erpPushDataEntity.setOperationType(Integer.valueOf(operationType));
        }
        erpPushDataEntity.setTenantId(tenantId);
        erpPushDataEntity.setObjectApiName(objectApiName);
        erpPushDataEntity.setSourceDataId(sourceDataId);
        erpPushDataEntity.setDataCenterId(dataCenterId);

        erpPushDataEntity.setSourceData("{}");
        erpPushDataEntity.setStandardFormat(JSON.toJSONString(standardData));
        erpPushDataEntity.setUpdateTime(new Date().getTime());
        return result;
    }

    public void executeAndWait(Collection<StandardData> srcDatas,
                               Collection<ErpPushDataEntity> insertData,
                               Collection<ErpPushDataEntity> updateData,
                               String tenantId,
                               String objectApiName,
                               String visualObjectApiName,
                               String operationType,
                               String id,
                               String dataCenterId) {
        List<Future> futures = new ArrayList<>();
        for (StandardData srcData : srcDatas) {
            Future future = DEFAULT_EXECUTOR.submit(() -> getEntity(tenantId, objectApiName, visualObjectApiName, operationType, srcData, id, dataCenterId));
            futures.add(future);
        }

        for (int i = 0; i < futures.size(); i++) {
            Future future = futures.get(i);
            try {
                Map<String, ErpPushDataEntity> result = (Map<String, ErpPushDataEntity>) future.get(60 * 2, TimeUnit.SECONDS);
                if (result.containsKey("insert")) {
                    insertData.add(result.get("insert"));
                } else if (result.containsKey("update")) {
                    updateData.add(result.get("update"));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return;
    }

    public Result<List<StandardData>> executeCustomFunction(String tenantId, String dataCenterId, String funcApiName, Map<String, Object> arg) {
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("push data executeCustomFunction start,arg={}", arg);
            Result<String> customFunctionResult = specialWayDataService.executeCustomFunction(tenantId, dataCenterId, funcApiName, arg, ErpObjInterfaceUrlEnum.push, null, null, null);
            if (!customFunctionResult.isSuccess()) {
                Result result = Result.newError(customFunctionResult.getErrCode(), customFunctionResult.getErrMsg());
                log.warn("push data executeCustomFunction funcApiName:{} ", funcApiName);
                return result;
            }
            String jsonStr = customFunctionResult.getData();
            StandardListData listErpObjDataResult = JacksonUtil.fromJson(jsonStr, StandardListData.class);
            log.info("push data executeCustomFunction end,result={}", listErpObjDataResult.getDataList());
            return Result.newSuccess(listErpObjDataResult.getDataList());
        } catch (Exception e) {
            Result result = Result.newError(SYSTEM_ERROR);
            result.setErrMsg(result.getErrMsg() + ":" + e.getMessage());
            log.warn("push data executeCustomFunction funcApiName:{} ", funcApiName);
            return result;
        } finally {
            stopWatch.stop();
            log.info("push data custom function get header map cost:{}", stopWatch.toString());
        }
    }

    public Result<Integer> deleteErpDataByCreateAndUpdateTime(String tenantId, String objApiName, Long createTime, Long updateTime) {
        Integer result = erpPushDataDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteErpDataByCreateAndUpdateTime(tenantId, objApiName, createTime, updateTime);
        log.info("deleteErpDataByCreateAndUpdateTime tenantId={},objApiName={},createTime={},updateTime={},result={}", tenantId, objApiName, createTime, updateTime, result);
        return Result.newSuccess(result);
    }


    /**
     * 触发轮询临时库
     */
    private void triggerPollingTemp(String tenantId, String visualObjectApiName, String objectApiName) {
        TriggerPollingData triggerPollingData = new TriggerPollingData();
        triggerPollingData.setTenantId(tenantId);
        triggerPollingData.setObjApiName(Sets.newHashSet(visualObjectApiName));
        triggerPollingData.setTriggerTime(System.currentTimeMillis());
        triggerPollingMongoManager.triggerPolling(triggerPollingData);
        log.info("receive data success and trigger tenantId:{},objApiName:{}", tenantId, objectApiName);
    }
}
