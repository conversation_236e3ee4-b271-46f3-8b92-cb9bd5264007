package com.fxiaoke.open.erpsyncdata.apiproxy.model.push;

import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 标准格式数据处理结果
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
@Accessors(chain = true)
public class PushResult extends Result<Void> {
    /**
     * 数量
     */
    private Integer dataSize;
    /**
     * 同步推送的结果数据
     */
    private StandardData directSyncResultData;
    /**
     * 请求体
     */
    private StringRequest request;

}
