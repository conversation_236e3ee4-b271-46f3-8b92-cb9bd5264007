package com.fxiaoke.open.erpsyncdata.admin.service

import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.admin.constant.ExcelTypeEnum
import com.fxiaoke.open.erpsyncdata.admin.model.excel.BuildExcelFile
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile
import com.fxiaoke.open.erpsyncdata.admin.model.excel.TestExcelMod
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import com.google.common.collect.Lists
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Ignore
class FileServiceImplTest extends BaseTest {
    @Autowired
    private FileService fileService

    @Test
    void buildExcelTest() {
        BuildExcelFile.Arg<TestExcelMod> arg = new BuildExcelFile.Arg()
        arg.setTenantId("79675")
        arg.setFileName("testExcel")
        arg.setSheetNames(Lists.newArrayList("testsheet1","testsheet2"))
        def dataList = [new TestExcelMod("学生1","语文",100)]
        arg.setDataList(dataList)
        def result = fileService.buildExcelFile( arg)
        println(result)
        assert result.success
    }

    @Test
    void testImportExcelFile() {
        File file1=new File("C:\\Users\\<USER>\\Desktop\\产品分类.xlsx");
        InputStream inputStream=new FileInputStream(file1);
        MultipartFile file=new MockMultipartFile("categoryTest.xlsx",inputStream)
        ImportExcelFile.FieldDataMappingArg arg=new ImportExcelFile.FieldDataMappingArg();
        arg.setTenantId("81772");
        arg.setDataType(ErpFieldTypeEnum.category);
        arg.setExcelType(ExcelTypeEnum.FIELD_DATA_MAPPING);
        arg.setFile(file)
        arg.setDataCenterId("642530472589131776")
        def list = fileService.importExcelFile(arg,"642530472589131776");
        println(list)
        assert list.success
    }

    @Test
    void test() {
        ImportExcelFile.FieldDataMappingArg arg = new ImportExcelFile.FieldDataMappingArg()
        arg.setExcelType(ExcelTypeEnum.FIELD_DATA_MAPPING)
        arg.setDataType(ErpFieldTypeEnum.category)
        def template = fileService.buildExcelTemplate("81772", arg);
        println(template)
        assert template.success
    }

    @Test
    void testImportObjectDataMapping() {
        File file1=new File("D:\\11.xlsx");
        InputStream inputStream=new FileInputStream(file1);
        MultipartFile file=new MockMultipartFile("11.xlsx",inputStream)
        ImportExcelFile.ObjectDataMappingArg arg=new ImportExcelFile.ObjectDataMappingArg();
        arg.setTenantId("80787");
        arg.setPloyDetailId("4da9098955334cbd9cd837d5713b9aeb");
        arg.setExcelType(ExcelTypeEnum.OBJ_DATA_MAPPING);
        arg.setFile(file);
        def list = fileService.importObjectDataMapping(arg);
        println(list)
        assert list.success
    }
    @Test
    void buildObjectDataMappingTemplateTest() {
        def result = fileService.buildObjectDataMappingTemplate( "80787","4da9098955334cbd9cd837d5713b9aeb")
        println(result)
        assert result.success
    }

    @Test
    void buildObjectFieldTemplate() {
        def result = fileService.buildObjectFieldTemplate("84149","754744010769825792","test2");
        println(result)
    }

    @Test
     void testGetDeptExcels() {
        def result = fileService.getCrmDeptExcel(81772,1001)
        println(result)
    }
}
