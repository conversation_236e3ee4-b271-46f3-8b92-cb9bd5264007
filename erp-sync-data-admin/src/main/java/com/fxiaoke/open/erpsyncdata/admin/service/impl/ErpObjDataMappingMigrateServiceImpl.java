package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjDataMappingMigrateService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.K3Model;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.QueryArg;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjIdNumberMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingsManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.custom.ErpObjIdNumberMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpIdNumberNameKeyMapping;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * K3C物料服务
 * <AUTHOR>
 * @date 2023.03.14
 */
@Service("erpObjDataMappingMigrateService")
@Slf4j
public class ErpObjDataMappingMigrateServiceImpl implements ErpObjDataMappingMigrateService {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private SyncDataMappingsManager syncDataMappingsManager;
    @Autowired
    private ErpObjIdNumberMappingManager erpObjIdNumberMappingManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    @Override
    public Result<Void> initData(String tenantId,String erpObjApiName) {
        ErpIdNumberNameKeyMapping keyMapping = tenantConfigurationManager.getK3cObjIdNumberNameKeyMapping(tenantId, erpObjApiName);
        if(keyMapping==null) {
            return Result.newSystemError(I18NStringEnum.s721);
        }
        List<ErpObjectRelationshipEntity> relationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .getSplitObjApiName(tenantId,
                erpObjApiName);
        log.info("ErpObjDataMappingMigrateServiceImpl.initMaterialData,relationshipEntityList={}",relationshipEntityList);
        if(CollectionUtils.isNotEmpty(relationshipEntityList)) {
            for(ErpObjectRelationshipEntity relationshipEntity : relationshipEntityList) {
                if(relationshipEntity.getChannel()!= ErpChannelEnum.ERP_K3CLOUD) continue;
                ErpConnectInfoEntity connectInfoEntity = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .getByIdAndTenantId(tenantId, relationshipEntity.getDataCenterId());
                log.info("ErpObjDataMappingMigrateServiceImpl.initMaterialData,connectInfoEntity={}",connectInfoEntity);
                if(connectInfoEntity==null) {
                    log.info("ErpObjDataMappingMigrateServiceImpl.initMaterialData,erp connect info not exist,dcId={}",relationshipEntity.getDataCenterId());
                    continue;
                }
                if(StringUtils.isEmpty(connectInfoEntity.getConnectParams())) {
                    log.info("ErpObjDataMappingMigrateServiceImpl.initMaterialData,erp connect info invalid");
                    continue;
                }

                K3CloudApiClient apiClient = null;
                try {
                    apiClient = K3CloudApiClient.newInstance(tenantId, connectInfoEntity.getConnectParams(),
                            relationshipEntity.getDataCenterId());
                    log.info("ErpObjDataMappingMigrateServiceImpl.initMaterialData,apiClient={}",apiClient);
                } catch (Exception e) {
                    log.info("ErpObjDataMappingMigrateServiceImpl.initMaterialData,new apiClient failed,connect params={}",connectInfoEntity.getConnectParams(),e);
                    continue;
                }
                migrateData(tenantId,
                        apiClient.getDataCenterId(),
                        erpObjApiName,
                        relationshipEntity.getErpSplitObjectApiname(),
                        keyMapping,
                        apiClient);
            }
        }
        return Result.newSuccess();
    }

    private void migrateData(String tenantId,
                             String dcId,
                             String erpObjApiName,
                             String splitObjApiName,
                             ErpIdNumberNameKeyMapping keyMapping,
                             K3CloudApiClient apiClient) {
        log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,begin,tenantId={},dcId={},erpObjApiName={},splitObjApiName={}",
                tenantId,dcId,erpObjApiName,splitObjApiName);
        int offset = 0;
        int limit = 100;
        while (true) {
            List<SyncDataMappingsEntity> entityList = syncDataMappingsManager.listByObjApiName(tenantId,
                    splitObjApiName,
                    limit,
                    offset);
            if(CollectionUtils.isEmpty(entityList)) break;
            offset+=limit;
            for(SyncDataMappingsEntity entity : entityList) {
                try {
                    String dataNumber = null;
                    if(StringUtils.containsIgnoreCase(entity.getSourceObjectApiName(),splitObjApiName)) {
                        dataNumber = entity.getSourceDataId();
                    } else {
                        dataNumber = entity.getDestDataId();
                    }
                    log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,dataNumber={}",dataNumber);

                    K3Model dataModel = queryData(erpObjApiName,dataNumber,keyMapping,apiClient);
                    log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,dataModel={}",dataModel);
                    if(dataModel!=null) {
                        String dataId = dataModel.getString(keyMapping.getDataIdKey());
                        String dataName = dataModel.getString(keyMapping.getDataNameKey());
                        ErpObjIdNumberMappingEntity erpObjIdNumberMappingEntity = erpObjIdNumberMappingManager.queryByDataId(tenantId,
                                dcId,
                                erpObjApiName,
                                dataId);
                        if(erpObjIdNumberMappingEntity ==null) {
                            int insert = erpObjIdNumberMappingManager.insert(tenantId,
                                    dcId,
                                    erpObjApiName,
                                    dataId,
                                    dataNumber,
                                    dataName);
                            if(insert!=1) {
                                log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,insert db failed,entity={}",entity);
                            }
                        } else {
                            log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,jump insert db,entity={}",entity);
                        }
                    } else {
                        log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,query erp data failed,dataNumber={}",dataNumber);
                    }
                } catch (Exception e) {
                    log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,migrate failed,entity={}",entity,e);
                }
            }
        }
        log.info("ErpObjDataMappingMigrateServiceImpl.migrateMaterialData,end,tenantId={},dcId={},erpObjApiName={},splitObjApiName={}",
                tenantId,dcId,erpObjApiName,splitObjApiName);
    }

    private K3Model queryData(String erpObjApiName,String number,ErpIdNumberNameKeyMapping keyMapping,K3CloudApiClient apiClient) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(erpObjApiName);
        queryArg.setFieldKeysByList(Lists.newArrayList(keyMapping.getDataIdKey(),keyMapping.getDataNumberKey(),keyMapping.getDataNameKey()));
        queryArg.setFilterString(String.format("%s='%s'", keyMapping.getDataNumberKey(), number));
        queryArg.setOrderString(keyMapping.getDataIdKey()+" ASC");
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            return result.getData().get(0);
        }
        return null;
    }
}
