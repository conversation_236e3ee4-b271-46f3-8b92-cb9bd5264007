package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.common.annotation.SecurityField;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.K3CloudApiConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/9
 */
@Data
@ApiModel
public class K3CloudConnectParam extends BaseLangConnectParam {
    private static final long serialVersionUID = 2369962196456072951L;

    @ApiModelProperty(value = "基础路径",example = "http://172.31.100.60/k3cloud/")
    private String baseUrl;

    @ApiModelProperty(value = "原始基础路径，可以通过这个路径，打开WEB版本的云星空",example = "http://172.31.100.60/k3cloud/")
    private String oldBaseUrl;

    @ApiModelProperty("数据中心ID")
    private String dbId;

    @ApiModelProperty("数据中心名称")
    private String dbName;

    /**
     * @see com.fxiaoke.open.erpsyncdata.preprocess.constant.K3CloudAuthType
     */
    @ApiModelProperty(value = "授权类型",allowableValues = "1,2",notes = "1：用户账号授权（默认）；2：应用授权")
    private Integer authType = 1;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户密码/应用密钥")
    @SecurityField
    private String password;

    @ApiModelProperty(value = "应用ID")
    private String appId;

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames;

    @ApiModelProperty("是否使用纷享httpclient")
    private boolean useFsHttpClient = true;

    /**
     * api配置
     */
    private K3CloudApiConfig config;

    public boolean configIsNull() {
        return config == null;
    }

    /**
     * 不能用get开头不然序列化会调用
     * @return
     */
    public K3CloudApiConfig newConfigIfNull() {
        if (config == null) {
            config = new K3CloudApiConfig();
        }
        return config;
    }

    public static K3CloudConnectParam newUserParam(String baseUrl,String dbId,String userName,String password){
        K3CloudConnectParam userParam = new K3CloudConnectParam();
        userParam.setBaseUrl(baseUrl);
        userParam.setDbId(dbId);
        userParam.setUserName(userName);
        userParam.setPassword(password);
        return userParam;
    }
}
