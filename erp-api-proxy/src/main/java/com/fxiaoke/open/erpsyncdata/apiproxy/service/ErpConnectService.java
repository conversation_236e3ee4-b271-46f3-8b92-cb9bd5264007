package com.fxiaoke.open.erpsyncdata.apiproxy.service;

import com.fxiaoke.open.erpsyncdata.preprocess.arg.GetConnectorIntroArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorAuthType;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DBProxyConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DbInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3CloudConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3UltimateConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.ConnectorIntro;
import com.fxiaoke.open.erpsyncdata.preprocess.model.proxyservice.DbProxyServiceInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * erp连接服务
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/11/9
 */
public interface ErpConnectService {

    /**
     * 获取K3CLoud数据中心列表
     *
     * @param baseUrl
     * @return
     */
    Result<List<DbInfo>> getK3CloudDbInfos(String tenantId, String dcId, String baseUrl);

    /**
     * 检查k3cloud连接参数
     *
     * @param connectParam
     * @return
     */
    Result<Void> checkK3CloudParam(K3CloudConnectParam connectParam);

    /**
     * 检查云星空旗舰版连接参数
     *
     * @param connectParam
     * @return
     */
    Result<Void> checkK3CloudUltimateParam(K3UltimateConnectParam connectParam);

    /**
     * 获取代理服务信息
     */
    Result<DbProxyServiceInfo> getDbProxyServiceInfo(DBProxyConnectParam connectParam,String tenantId);

    Result<List<ConnectorAuthType>> getConnectorAuthTypeList(String tenantId, String dcId);

    /**
     * @param tenantId
     * @param dcId
     * @param arg
     * @return
     */
    Result<ConnectorIntro> getConnectorIntro(String tenantId, String dcId, GetConnectorIntroArg arg);

    /**
     * 获取OAuth2授权接口
     */
    Result<String> getOAuth2AuthUrl(String tenantId,String dcId,  SystemParams systemParams);

    Result<Void> checkAuthStatus(String tenantId, String dcId);

    /**
     * 测试代理服务器的地址
     * 现在是专用sap,测试函数
     * @param serviceUrl
     *
     * @return
     */
    Result<Void> actuatorSAPProxyStatus(String serviceUrl);

    Result<SystemParams> processUserInputSystemParams(String tenantId, String dcId, SystemParams systemParams);
}
