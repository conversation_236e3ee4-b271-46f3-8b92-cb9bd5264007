package com.fxiaoke.open.oasyncdata.db.manager;

import com.facishare.id.client.IDClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/20
 */
@Slf4j
@Service
public class IdGenerator {
    @Autowired
    private IDClient idClient;

    public String get() {
        String idStr;
        try {
            long id = idClient.get();
            idStr = String.valueOf(id);
        } catch (Exception e) {
            log.warn("id client get exception", e);
            idStr = UUID.randomUUID().toString().replaceAll("-", "");
        }
        return idStr;
    }
}
