package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 14:47 2022/11/4
 * @Desc:
 */
@Data
@ApiModel
public class MultipartFileArg implements Serializable {
    @ApiModelProperty(value = "文件流")
    @JSONField(serialize = false)
    private MultipartFile file;
}
