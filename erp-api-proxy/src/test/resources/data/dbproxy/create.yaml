- name: 创建-返回id
  config:
    querySql: |
      #YAML
      insertSql: |
        INSERT INTO customers
        ("name", address, code)
        VALUES(${name}, ${address}, ${code});
  detailConfig: []
- name: 创建-查找id
  config:
    querySql: |
      #YAML
      insertSql: |
        INSERT INTO customers
        ("name", address, code)
        VALUES(${name}, ${address}, ${code});
      queryIdSql:
        select id from customers where name = ${name}
  detailConfig: []

