package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.admin.service.WebhookService;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringRequest;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.push.StringResponse;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.LogIdUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> (^_−)☆
 */
@Slf4j
@Service
public class WebhookServiceImpl implements WebhookService {
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private SyncLogManager syncLogManager;


    @Override
    public Result<StringResponse> webhook(String tenantId, String dcId, StringRequest arg) {
        //此入口，重置trace和logId
        LogIdUtil.clearTrace();
        String logId = syncLogManager.initLogId(tenantId, "all");
        TraceUtil.initTrace(logId);
        //noinspection unchecked
        Result<StringResponse> pushResult = (Result<StringResponse>) erpObjDataPushManager.erpPushDataToDssCombine(tenantId, dcId, false, arg, null);
        return pushResult;
    }
}
