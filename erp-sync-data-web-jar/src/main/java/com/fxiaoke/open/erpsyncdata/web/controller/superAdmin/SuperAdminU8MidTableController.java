package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 超级管理：执行sql
 * <AUTHOR>
 * @Date: 19:58 2021/5/6
 * @Desc:
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/u8midtable")
public class SuperAdminU8MidTableController {
    @ReloadableProperty("dss.u8.middle.url")
    private String u8MiddleServerUrl;
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @PostMapping("/superInsert")
    public Result<Void> superInsert(@RequestBody String sql) {
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(
                u8MiddleServerUrl + "/erp/syncdata/superadmin/u8midtable/superInsert", sql, Collections.emptyMap(),(Long)null);
        Result<Void> voidResult=JsonUtil.fromJson(result.getBody(),Result.class);
        return voidResult;
    }

    @PostMapping("/superUpdate")
    public Result<Void> superUpdate(@RequestBody String sql) {
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(
                u8MiddleServerUrl + "/erp/syncdata/superadmin/u8midtable/superUpdate", sql, Collections.emptyMap(),(Long)null);
        Result<Void> voidResult =JsonUtil.fromJson(result.getBody(),Result.class);
        return voidResult;
    }

    @PostMapping("/superDelete")
    public Result<Void> superDelete(@RequestBody String sql) {
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(
                u8MiddleServerUrl + "/erp/syncdata/superadmin/u8midtable/superDelete", sql, Collections.emptyMap(),(Long)null);
        Result<Void> voidResult= JsonUtil.fromJson(result.getBody(),Result.class);
        return voidResult;
    }

    @PostMapping("/superQuery")
    public Result<List<Map<String, Object>>> superQuery(@RequestBody String sql) {
        HttpRspLimitLenUtil.ResponseBodyModel result = proxyHttpClient.postUrl(
                u8MiddleServerUrl + "/erp/syncdata/superadmin/u8midtable/superQuery", sql, Collections.emptyMap(),(Long)null);
        Result<List<Map<String, Object>>> voidResult=JsonUtil.fromJson(result.getBody(),Result.class);
        return voidResult;
    }
}
