package com.fxiaoke.open.oasyncdata.db.entity.data;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FieldMappingData implements Serializable {
    private String sourceApiName;
    private String sourceType;
    private String sourceTargetApiName;
    private String sourceQuoteFieldType;
    private String sourceQuoteRealField;
    private String sourceQuoteFieldTargetObjectApiName;
    private String sourceQuoteFieldTargetObjectField;
    private String destApiName;
    private String destType;
    private String quoteField;
    private String destQuoteFieldType;
    private String destTargetApiName;
    private List<OptionMappingData> optionMappings;
    private Integer mappingType;
    private String function;
    private String value;
    private String defaultValue;//默认值
    private Integer valueType;//值类型，固定值1，默认值2
}
