package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.fxiaoke.open.erpsyncdata.preprocess.model.CrmFileModel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 15:09 2023/4/19
 * @Desc:
 */
@Data
public class AttachmentsResult implements Serializable {
    private Map<String,Result<CrmFileModel>> crmResults;
    private Map<String,Result<Map<String, Object>>> erpResults;
}
