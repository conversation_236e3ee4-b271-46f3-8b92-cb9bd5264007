package com.fxiaoke.open.erpsyncdata.preprocess.service;

import com.fxiaoke.open.erpsyncdata.preprocess.annotation.ContextEi;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CountSyncDataAndSendArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.HandleDeleteArg;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/17
 */
public interface ErpSyncService {

    /**
     *
     * @param tenantId
     * @param endUpdateTime
     * @return
     */
    Result<Void> syncDataTimeOutUpdateStatusToError(@ContextEi() String tenantId, Long startUpdateTime, Long endUpdateTime);

    /**
     * 同步等待的数据
     * @param tenantId
     * @return
     */
    Result<Void> syncWaitingData(@ContextEi() String tenantId);

    /**
     * 重新拉取最新数据走一遍mq
     * erp->crm
     * @param erpIdArg
     * @return
     */
    Result<Void> syncSingletonData(@ContextEi("?.getTenantId()") ErpIdArg erpIdArg, String destObjApiName);


    /**
     * 发送聚合框架Mq
     *
     * @param tenantId
     * @param eventDataList
     * @return
     */
    Result<Void> sendDoDispatchMq(@ContextEi() String tenantId,List<SyncDataContextEvent> eventDataList);


    /**
     * 根据Id扫描临时路未触发的数据，并修改最后同步时间让其触发。
     * 扫描前一天的
     * @param tenantId
     * @return
     */
    Result<Void> scanTempNotTrigger(@ContextEi() String tenantId);

    void handleDelete(@ContextEi("?.getTenantId()") HandleDeleteArg arg);
}
