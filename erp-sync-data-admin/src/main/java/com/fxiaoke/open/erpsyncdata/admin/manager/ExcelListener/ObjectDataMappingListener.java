package com.fxiaoke.open.erpsyncdata.admin.manager.ExcelListener;

import com.alibaba.excel.context.AnalysisContext;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.AdminSyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ImportExcelFile;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ObjectDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.data.GetOrCreateByTwoWayData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.CrmObjectApiName;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ExcelListener.BaseListener;
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncDepartmentOrPersonnelService;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 以源数据侧数据Id作为唯一id。导入数据按顺序执行。
 *
 * <AUTHOR>
 * @Date: 15:20 2020/9/14
 * @Desc:
 */
@Slf4j
@Getter
public class ObjectDataMappingListener extends BaseListener<ObjectDataMappingExcelVo> {


    private final AdminSyncDataMappingManager adminSyncDataMappingManager;

    private final AdminSyncDataManager adminSyncDataManager;

    private final SyncDataMappingsDao syncDataMappingsDao;

    private final SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService;

    private final String tenantId;

    private final SyncPloyDetailResult syncPloyDetailResult;

    private final ImportExcelFile.Result importResult;

    private  String sourceObjectApiName;

    private  String destObjectApiName;

    private  String sheetName;

    private I18NStringManager i18NStringManager;

    private String lang;

    public ObjectDataMappingListener(String tenantId,
                                     SyncPloyDetailResult syncPloyDetailResult,
                                     AdminSyncDataMappingManager adminSyncDataMappingManager,
                                     AdminSyncDataManager adminSyncDataManager,
                                     SyncDataMappingsDao syncDataMappingsDao,
                                     SyncDepartmentOrPersonnelService syncDepartmentOrPersonnelService,
                                     I18NStringManager i18NStringManager,
                                     String lang) {
        this.adminSyncDataMappingManager = adminSyncDataMappingManager;
        this.adminSyncDataManager = adminSyncDataManager;
        this.syncDataMappingsDao = syncDataMappingsDao;
        this.syncDepartmentOrPersonnelService = syncDepartmentOrPersonnelService;
        this.tenantId = tenantId;
        this.syncPloyDetailResult = syncPloyDetailResult;
        this.importResult = new ImportExcelFile.Result();
        this.i18NStringManager = i18NStringManager;
        this.lang = lang;
    }

    @Override
    public void invoke(ObjectDataMappingExcelVo data, AnalysisContext context) {
        super.invoke(data, context);
        importResult.incrInvoke(1);
        Integer rowNo = context.readRowHolder().getRowIndex();
        dealWithObjectDataMapping(data, rowNo);
    }

    private void dealWithObjectDataMapping(ObjectDataMappingExcelVo data, Integer rowNo) {
        if (StringUtils.isEmpty(data.getSourceDataId()) || StringUtils.isEmpty(data.getSourceDataName()) || StringUtils.isEmpty(data.getDestDataId()) || StringUtils.isEmpty(data.getDestDataName())) {
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s788,lang,tenantId));
        } else {
            String tenantId = syncPloyDetailResult.getPloyTenantId();
            ObjectData sourceData = new ObjectData();
            ObjectData destData = new ObjectData();

            sourceData.putApiName(sourceObjectApiName);
            sourceData.putId(data.getSourceDataId());
            sourceData.put("name", data.getSourceDataName());
            sourceData.putTenantId(syncPloyDetailResult.getPloyTenantId());
            sourceData.put("version", 1);

            destData.putId(data.getDestDataId());
            destData.put("name", data.getDestDataName());
            destData.putTenantId(syncPloyDetailResult.getPloyTenantId());
            destData.putApiName(destObjectApiName);
            SyncDataMappingsEntity oldEntity = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, sourceData.getApiName(), destData.getApiName(), sourceData.getId());
            if(oldEntity==null){//新增
                try{
                    String syncDataId = new ObjectId().toString();
                    GetOrCreateByTwoWayData getOrCreateByTwoWayData = adminSyncDataMappingManager.createSyncDataMapping(syncDataId,
                            SyncDataStatusEnum.WRITE_SUCCESS.getStatus(),
                            destData.getApiName(),
                            destData.getTenantId(),
                            destData.getId(),
                            destData.getName(),
                            sourceData,
                            true,
                            data.getMasterDataId());

                    SyncDataMappingsEntity sourcedMappingsData = getOrCreateByTwoWayData.getSourcedData();
                    if(sourcedMappingsData==null){
                        importResult.addImportError(rowNo, i18NStringManager.get2(I18NStringEnum.s789,
                                lang,
                                tenantId,
                                sheetName,"createOrGet sourcedMappingsData failed"));
                    }else{
                        importResult.incrInsert(1);
                        // 特殊对象绑定,暂时只处理账号绑定
                        bindSpecialObjectMapping(sourceData, destData, rowNo);
                    }
                }catch (Exception e){
                    log.info("dealWithObjectDataMapping Exception rowNo={} e={}",rowNo,e);
                    importResult.addImportError(rowNo, i18NStringManager.get2(I18NStringEnum.s789.getI18nKey(),
                            lang,
                            tenantId,
                            String.format(I18NStringEnum.s789.getI18nValue(), sheetName,e.getMessage()),
                            Lists.newArrayList(sheetName,e.getMessage())));
                }
            }else{//不更新
                importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s790,lang,tenantId));
            }

        }
    }

    private void bindSpecialObjectMapping(ObjectData sourceData, ObjectData destData, Integer rowNo) {
        // 暂时只处理账号绑定,只有erp->crm人员\部门集成流才支持账号绑定
        if (!Objects.equals(CrmObjectApiName.Employee_API_NAME, destData.getApiName())
                &&!Objects.equals(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName(), destData.getApiName())) {
            return;
        }

        try {
            if(Objects.equals(ObjectApiNameEnum.FS_DEPARTMENTOBJ.getObjApiName(), destData.getApiName())){
                syncDepartmentOrPersonnelService.updateFieldMappingByIntegrationMapping(destData.getTenantId(),null,syncPloyDetailResult.getSourceDataCenterId(),
                        ErpFieldTypeEnum.department,sourceData.getId(),sourceData.getName(),destData.getId(),destData.getName());
            }else{
                syncDepartmentOrPersonnelService.updateFieldMappingByIntegrationMapping(destData.getTenantId(),null,syncPloyDetailResult.getSourceDataCenterId(),
                        ErpFieldTypeEnum.employee,sourceData.getId(),sourceData.getName(),destData.getId(),destData.getName());
            }
        } catch (Exception e) {
            log.warn("ObjectDataMappingListener 导入员工成功,账号绑定失败 sourceData:{}, destData:{}",sourceData, destData, e);
            importResult.addImportError(rowNo, i18NStringManager.get(I18NStringEnum.s3621.getI18nKey(), lang, tenantId, I18NStringEnum.s3621.getI18nValue()));
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        super.doAfterAllAnalysed(context);
        //组装打印信息
        StringBuffer printMsg = new StringBuffer();
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s772.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s772.getI18nValue(), importResult.getInvokedNum(), importResult.getInsertNum(), importResult.getDeleteNum(), importResult.getUpdateNum()),
                Lists.newArrayList(importResult.getInvokedNum()+"", importResult.getInsertNum()+"", importResult.getDeleteNum()+"", importResult.getUpdateNum()+""))).append("\n");

        printMsg.append(i18NStringManager.get2(I18NStringEnum.s773.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s773.getI18nValue(), importResult.getImportErrorRows().size()),
                Lists.newArrayList(importResult.getImportErrorRows().size()+""))).append("\n");

        Map<String, List<Integer>> importErrorMap = importResult.getImportErrorRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        importErrorMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        printMsg.append(i18NStringManager.get2(I18NStringEnum.s775.getI18nKey(),
                lang,
                tenantId,
                String.format(I18NStringEnum.s775.getI18nValue(), importResult.getInvokeExceptionRows().size()),
                Lists.newArrayList(importResult.getInvokeExceptionRows().size()+""))).append("\n");

        Map<String, List<Integer>> invokeExceptionMap = importResult.getInvokeExceptionRows().stream().collect(
                Collectors.groupingBy(ImportExcelFile.ErrorRow::getErrMsg, LinkedHashMap::new,
                        Collectors.mapping(ImportExcelFile.ErrorRow::getRowNo, Collectors.toList())));
        invokeExceptionMap.forEach((k, v) -> {
            printMsg.append(i18NStringManager.get2(I18NStringEnum.s774.getI18nKey(),
                    lang,
                    tenantId,
                    String.format(I18NStringEnum.s774.getI18nValue(), Joiner.on(",").join(v), k),
                    Lists.newArrayList(Joiner.on(",").join(v), k))).append("\n");
        });
        importResult.setPrintMsg(printMsg.toString());
    }

    /**
     * All listeners receive this method when any one Listener does an error report. If an exception is thrown here, the
     * entire read will terminate.
     *
     * @param exception
     * @param context
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        importResult.addInvokeExceptionRow(context.readRowHolder().getRowIndex(), exception.toString());
    }

    public void setApiName(String sourceObjectApiName,String destObjectApiName){
        this.sourceObjectApiName=sourceObjectApiName;
        this.destObjectApiName=destObjectApiName;
    }
    public void setSheetName(String sheetName){
        this.sheetName=sheetName;

    }
}
