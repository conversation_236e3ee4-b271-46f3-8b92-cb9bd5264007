package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/8/15 17:59
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuickCopySettingArg implements Serializable {
    private String tenantId;
    private String sourceDataCenterId;
    private String destDataCenterId;
    private String destDataCenterName;
    private ErpChannelEnum erpChannelEnum;
    private Boolean hasCopyExistsConnect=true;//是否复制源数据中心信息，false则只创建新数据中心
    /**
     * 连接器key
     */
    private String connectorKey;

}
