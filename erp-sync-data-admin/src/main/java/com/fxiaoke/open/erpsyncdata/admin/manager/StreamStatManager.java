package com.fxiaoke.open.erpsyncdata.admin.manager;

import com.fxiaoke.common.Pair;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataFixDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.DataIntegrationNotificationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.GroupStreamStatDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.DataIntegrationNotificationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.TraceUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.GroupStreamStat;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 集成流统计
 */
@Service

public class StreamStatManager {
    @Autowired
    private DataIntegrationNotificationDao dataIntegrationNotificationDao;
    @Autowired
    private SyncDataFixDao syncDataFixDao;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private AdminSyncPloyDetailDao syncPloyDetailDao;
    @Autowired
    private GroupStreamStatDao groupStreamStatDao;


    public void groupStatByDc(String upTenantId, String downstreamId, String dcId) {
        //获取所有集成流
        List<SyncPloyDetailEntity> syncPloyDetailEntities = syncPloyDetailDao.queryByDcId(downstreamId, dcId, null);
        Long failedTotal = 0L;
        int failedStreamCount = 0;
        List<Pair<String, String>> allObjApiNames = new ArrayList<>();
        for (SyncPloyDetailEntity entity : syncPloyDetailEntities) {
            List<Pair<String, String>> objApiNames = new ArrayList<>();
            objApiNames.add(Pair.of(entity.getSourceObjectApiName(), entity.getDestObjectApiName()));
            if (entity.getDetailObjectMappings() != null) {
                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : entity.getDetailObjectMappings()) {
                    objApiNames.add(Pair.of(detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName()));
                }
            }
            allObjApiNames.addAll(objApiNames);
            Long thisCount = 0L;
            for (Pair<String, String> pair : objApiNames) {
                long count = syncDataMappingManager.countByObjLimit1000(downstreamId, pair.getKey(), pair.getValue(), SyncStatusEnum.FAILED.getStatus());
                thisCount += count;
            }
            if (thisCount > 0) {
                failedTotal += thisCount;
                failedStreamCount++;
            }
        }
        if (failedTotal >= 1000L) {
            failedTotal = 1000L;
        }
        //获取告警中信息
        List<String> streamIdList = syncPloyDetailEntities.stream().map(v -> v.getId()).collect(Collectors.toList());
        List<DataIntegrationNotificationEntity> notificationInProgress = dataIntegrationNotificationDao.distinct(downstreamId, dcId, streamIdList);
        int alertingNum = notificationInProgress.size();
        Long lastAlertTime = notificationInProgress.stream().mapToLong(v -> v.getTime()).max().orElse(0L);
        Long lastSyncTime = getLastSyncTime(downstreamId, allObjApiNames);
        GroupStreamStat groupStreamStat = GroupStreamStat.builder()
                .upTenantId(upTenantId)
                .dcId(dcId)
                .downstreamId(downstreamId)
                .traceId(TraceUtil.get())
                .streamCount(syncPloyDetailEntities.size())
                .alertingStreamCount(alertingNum)
                .mappingFailedTotal(failedTotal)
                .lastAlertTime(lastAlertTime)
                .lastSyncTime(lastSyncTime)
                .mappingFailedStreamCount(failedStreamCount)
                .createTime(new Date())
                .build();
        groupStreamStatDao.insert(upTenantId, groupStreamStat);
    }

    public Long getLastSyncTime(String tenantId, List<Pair<String, String>> objPairs) {
        Long lastSyncTime = 0L;
        for (Pair<String, String> objPair : objPairs) {
            List<SyncDataMappingsEntity> lastSyncData = syncDataMappingManager.getLastSyncData(tenantId, objPair.getKey(), objPair.getValue(), 0, 1);
            if (CollectionUtils.isNotEmpty(lastSyncData) && lastSyncData.get(0).getUpdateTime() > lastSyncTime) {
                lastSyncTime = lastSyncData.get(0).getUpdateTime();
            }
        }
        return lastSyncTime;
    }
    //短时间保留，上线前删掉

//    @Deprecated
//    public void statByDc(String tenantId, String dcId, String upTenantId) {
//        String traceId = TraceUtil.get();
//        //获取所有集成流
//        List<SyncPloyDetailEntity> syncPloyDetailEntities = syncPloyDetailDao.queryByDcId(tenantId, dcId, null);
//        List<StreamStat> streamStats = syncPloyDetailEntities.stream().map(entity -> {
//            ObjMappingStat mainMappingStat = ObjMappingStat.builder().objMappingType(StreamStat.ObjMappingType.main).build();
//            mainMappingStat.setObjApiNames(TenantTypeEnum.getByType(entity.getDestTenantType()), entity.getSourceObjectApiName(), entity.getDestObjectApiName());
//            long count = syncDataMappingManager.countByObjLimit1000(tenantId, entity.getSourceObjectApiName(), entity.getDestObjectApiName(), SyncStatusEnum.FAILED.getStatus());
//            mainMappingStat.setMappingFailedTotal(count);
//            List<ObjMappingStat> detailMappingStats = new ArrayList<>();
//            long streamFailedCount = count;
//            if (entity.getDetailObjectMappings() != null) {
//                for (DetailObjectMappingsData.DetailObjectMappingData detailObjectMapping : entity.getDetailObjectMappings()) {
//                    ObjMappingStat detailMappingStat = ObjMappingStat.builder().objMappingType(StreamStat.ObjMappingType.detail).build();
//                    detailMappingStat.setObjApiNames(TenantTypeEnum.getByType(entity.getDestTenantType()), detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName());
//                    long count2 = syncDataMappingManager.countByObjLimit1000(tenantId, detailObjectMapping.getSourceObjectApiName(), detailObjectMapping.getDestObjectApiName(), SyncStatusEnum.FAILED.getStatus());
//                    mainMappingStat.setMappingFailedTotal(count2);
//                    streamFailedCount += count2;
//                    detailMappingStats.add(detailMappingStat);
//                }
//            }
//            StreamStat streamStat = StreamStat.builder()
//                    .tenantId(upTenantId)
//                    .downstreamId(tenantId)
//                    .dcId(dcId)
//                    .traceId(traceId)
//                    .streamId(entity.getId())
//                    .status(entity.getStatus())
//                    .mainMapping(mainMappingStat)
//                    .detailMappings(detailMappingStats)
//                    .mappingFailedTotal(streamFailedCount)
//                    .build();
//            return streamStat;
//        }).collect(Collectors.toList());
//
//        fillAlertInfo(tenantId, dcId, streamStats);
//        fillLastSyncTime(tenantId, streamStats);
//        //不再查询精确值
//        streamStatDao.batchInsert(upTenantId, streamStats);
//    }
//
//    private static void sendBizLog(String tenantId, String dcId, String upTenantId, StreamStat streamStat, List<ObjMappingStat> mappingStats, long beginTime) {
//        long endTime = System.currentTimeMillis();
//        for (ObjMappingStat mappingStat : mappingStats) {
//            //上报结果
//            ErpSyncStreamStatDTO dto = ErpSyncStreamStatDTO.builder()
//                    .tenantId(tenantId)
//                    .upTenantId(upTenantId)
//                    .traceId(TraceUtil.get())
//                    .dcId(dcId)
//                    .streamId(streamStat.getStreamId())
//                    .createTime(endTime)
//                    .deleteTime(endTime + Duration.ofDays(90).toMillis())
//                    .crmObjApiName(mappingStat.getCrmObjApiName())
//                    .erpObjApiName(mappingStat.getErpObjApiName())
//                    .destTenantType(mappingStat.getDestTenantType().name())
//                    .objMappingType(mappingStat.getObjMappingType().name())
//                    .streamStatus(streamStat.getStreamStatus().name())
//                    .lastAlertTime(streamStat.getLastAlertTime())
//                    .lastSyncTime(streamStat.getLastSyncTime())
//                    .mappingTotal(mappingStat.getMappingTotal())
//                    .mappingFailedTotal(mappingStat.getMappingFailedTotal())
//                    .statCost((int) (endTime - beginTime))
//                    .build();
//            BizLogClient.send("biz-log-erp-sync-stream-stat", Pojo2Protobuf.toMessage(dto, com.fxiaoke.log.TenantCheckStatusLog.class).toByteArray());
//        }
//    }
//
//
//    public void fillAlertInfo(String tenantId, String dcId, List<StreamStat> streamStats) {
//        //获取告警中信息
//        List<String> streamIdList = streamStats.stream().map(v -> v.getStreamId()).collect(Collectors.toList());
//        List<DataIntegrationNotificationEntity> notificationInProgress = dataIntegrationNotificationDao.distinct(tenantId, dcId, streamIdList);
//        //告警中集成流Id
//        Map<String, DataIntegrationNotificationEntity> alertingStreamMap = notificationInProgress.stream()
//                .flatMap(entity -> entity.getPloyDetailIdList().stream().map(streamId -> Pair.of(streamId, entity)))
//                .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (v1, v2) -> v1));
//        for (StreamStat streamStat : streamStats) {
//            DataIntegrationNotificationEntity notificationEntity = alertingStreamMap.get(streamStat.getStreamId());
//            if (notificationEntity != null) {
//                //集成流告警状态
//                streamStat.setStreamStatus(StreamStatus.alerting);
//                streamStat.setLastAlertTime(notificationEntity.getTime());
//            } else {
//                streamStat.setStreamStatus(StreamStatus.normal);
//            }
//        }
//    }

//    public void fillLastSyncTime(String tenantId, List<StreamStat> streamStats) {
//        for (StreamStat streamStat : streamStats) {
//            ObjMappingStat mainMapping = streamStat.getMainMapping();
//            String sourceObjApiName = mainMapping.getSourceObjApiName();
//            String destObjApiName = mainMapping.getDestObjApiName();
//            List<SyncDataEntity> lastSyncData = syncDataFixDao.getLastSyncData(tenantId, tenantId, sourceObjApiName, tenantId, destObjApiName, 0, 1);
//            if (CollectionUtils.isNotEmpty(lastSyncData)) {
//                streamStat.setLastSyncTime(lastSyncData.get(0).getUpdateTime());
//            }
//        }
//    }
}
