package com.fxiaoke.open.erpsyncdata.admin.result;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class SyncDataMappingResult implements Serializable {
    @ApiModelProperty("策略类型，1 erp->crm 2 crm->rep")
    private Integer type;
    @ApiModelProperty("数据映射的id")
    private String id;
    @ApiModelProperty("源企业id")
    private String sourceTenantId;
    @ApiModelProperty("源企业名称")
    private String sourceTenantName;
    @ApiModelProperty("源企业对象apiName")
    private String sourceObjectApiName;
    @ApiModelProperty("源企业对象名称")
    private String sourceObjectName;
    @ApiModelProperty("源企业数据id")
    private String sourceDataId;
    @ApiModelProperty("源企业数据主属性字段")
    private String sourceDataName;
    @ApiModelProperty("目标企业id")
    private String destTenantId;
    @ApiModelProperty("目标企业名称")
    private String destTenantName;
    @ApiModelProperty("目标企业对象apiName")
    private String destObjectApiName;
    @ApiModelProperty("目标企业对象名称")
    private String destObjectName;
    @ApiModelProperty("目标企业数据id")
    private String destDataId;
    @ApiModelProperty("目标企业数据主属性字段")
    private String destDataName;
    /**
     * {@link SyncStatusEnum}
     */
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("状态对应的名称")
    private String statusName;
    @ApiModelProperty("更新时间")
    private Long updateTime;
    private Boolean isCreated;
    private String lastSyncDataId;
    private String remark;
    private Integer lastSyncStatus;
    @ApiModelProperty("主对象源数据id")
    private String masterDataId;
    @ApiModelProperty("是否是主对象")
    private Boolean isMasterData;

    public void setStatus(Integer status) {
        this.status = status;
        this.statusName = SyncStatusEnum.getNameByStatus(status);
    }
}
