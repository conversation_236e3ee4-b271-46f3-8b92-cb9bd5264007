package com.fxiaoke.open.erpsyncdata.admin.service;


import com.fxiaoke.open.erpsyncdata.admin.arg.CreateConnectorArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetDcBindArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.InitDataCenterInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryDataCenterInfoArg;
import com.fxiaoke.open.erpsyncdata.admin.model.NPathModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.AddOAConnectorArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 11:56 2020/8/19
 * @Desc:
 */
public interface ConnectInfoService {
    Result<ConnectInfoResult> getConnectInfoByDataCenterId(String tenantId, int userId,String dataCenterId);

    Result<ConnectInfoResult> getConnectInfoCheckStatus(String tenantId, int userId, String dataCenterId);


    /**
     * 获取数据库数据中心信息
     *
     * @param tenantId
     * @param userId
     * @return
     */
    Result<List<DataCenterInfoResult>> getAllDCInfo(String tenantId, int userId, String lang);

    /**
     * 获取数据中心信息，会校验订单
     *
     * @param tenantId
     * @param userId
     * @return
     */
    Result<List<DataCenterInfoResult>> queryDataCenterInfo(String tenantId, int userId, String lang, QueryDataCenterInfoArg arg);

    /**
     * 检查并初始化OA连接器
     * @param tenantId
     * @return
     */
    Result<List<DataCenterInfoResult>> checkAndInitOAConnector(String tenantId, int userId,String lang);

    /**
     * 获取可以新增的数据中心
     */
    Result<List<DataCenterInfoResult>> listAddableDataCenter(String tenantId, ConnectorTypeEnum connectorType, String lang);

    /**
     * 新建OA连接器
     */
    Result<String> addOAConnector(String tenantId, AddOAConnectorArg arg, String lang);

    /**
     * 更新数据中心信息
     * @param tenantId
     * @param userId
     * @param connectInfo
     * @return
     */
    Result<DataCenterInfoResult> updateDataCenterInfo(String tenantId,int userId,DataCenterInfoResult connectInfo,String lang);

    /**
     * 新增数据中心
     *
     * @param needInitErpConfig 是否初始化账套信息，如果复制的时候选择否，则hasInitDataCenterInfo为true,会初始化预设的账套信息，
     *                          反之，不预设账套信息，直接复制源账套信息
     * @return
     */
    Result<String> initDataCenterInfo(String tenantId, int userId, ConnectInfoResult connectInfo, boolean needInitErpConfig, String lang);

    /**
     * 弃用，
     * @param tenantId
     * @param userId
     * @return
     */
    @Deprecated
    Result<List<ConnectInfoResult>> queryConnectInfo(String tenantId, int userId);

    Result<ConnectInfoResult> updateConnectInfo(String tenantId,int userId,ConnectInfoResult connectInfo,String lang,boolean needUpdateAfterInsert);


    /**
     * @param needUpdateAfterInsert 更新失败时，会执行删除操作
     * @return
     */
    Result<ConnectInfoResult> upsertConnectInfoAndCopySetting(String tenantId,int userId,ConnectInfoResult connectInfo,String lang,boolean needUpdateAfterInsert,boolean needCopySetting);

    /**
     * 刷新数据中心的最后更新时间
     * @param tenantId
     * @return
     */
    Result<Boolean> refreshUpdateTime(String tenantId,String dataCenterId);

    Result<ErpConnectInfoEntity> getOrCreateCrmDc(String tenantId, String lang);

    /**
     * 上传icon，使用npath，转换为cpath并拼接预览地址
     * @param nPathModel
     * @return
     */
    Result<String> getIconUrlByTempPath(String tenantId, NPathModel nPathModel);

    /**
     * 更新连接信息
     * @param tenantId
     * @param dataCenterId 数据中心ID
     * @param dataCenterName 数据中心名称
     * @param connectParams 连接参数
     * @return
     */
    Result<Void> updateConnectParams(String tenantId, String dataCenterId, String dataCenterName, String connectParams);

    /**
     * 获取连接器信息
     * @param tenantId
     * @param dataCenterId
     * @return
     */
    Result<ErpConnectInfoEntity> getDcInfo(String tenantId, String dataCenterId);

    /**
     * 检查纷享和企微是否已经绑定
     * @param arg
     * @return
     */
    Result<ErpConnectInfoEntity> getDcBind(GetDcBindArg arg);

    /**
     * 创建连接器
     * @param arg
     * @return
     */
    Result<ErpConnectInfoEntity> createConnector(CreateConnectorArg arg);

    Result<Void> deleteConnectInfo(String tenantId, Integer loginUserId, String dcId, String lang);

    Result<DataCenterInfoResult> initErpOrOADataCenterInfo(String tenantId, Integer userId, InitDataCenterInfoArg dataCenterInfo, String lang);

    Result<Void> updateStandardConnectSysName(StandardConnectParam connectParam, String tenantId, Integer userId, String dataCenterId, String lang);
}
