package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.UnsupportedCharsetException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.alibaba.fastjson.util.IOUtils.UTF8;

@Slf4j
@Api(tags = "帝迈SAP接口")
@RestController("diMaiController")
@RequestMapping(path = {"inner/erp/syncdata/customfunction/722038", "inner/erp/syncdata/customfunction/615315"})
public class DiMaiController {

    private static String getContentFromRbody(ResponseBody responseBody) throws IOException {
        BufferedSource source = responseBody.source();
        source.request(Long.MAX_VALUE);
        Buffer buffer = source.buffer();
        Charset charset = UTF8;
        MediaType contentType = responseBody.contentType();
        if (contentType != null) {
            charset = contentType.charset(UTF8);
        }
        return buffer.clone().readString(charset);
    }

    @ApiOperation(value = "SAP执行请求")
    @RequestMapping(value = "/dowork", method = RequestMethod.POST)
    public Result<String> getXCsrfTokenByAuthorization(@RequestHeader(value="x-fs-ei") Integer tenantId,
                                        @RequestHeader Map<String, String> headerMap,
                                        @RequestBody Map<String, String> bodyMap){

        log.info("trace dowork tenantId:{}, headerMap:{}, bodymap: {} ",tenantId, headerMap, bodyMap);
        String mapData = bodyMap.get("param");
        Map<String,String> params = JacksonUtil.fromJson(mapData, Map.class);
        String url = params.get("tokenUrl");
        String postRequestUrl=params.get("postRequestUrl");
        String postData=params.get("postData");

        if (StringUtils.isEmpty(url)) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "params [tokenUrl] is not null");
        }
        if (StringUtils.isEmpty(postRequestUrl)) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "params [postRequestUrl] is not null");
        }
        if (StringUtils.isEmpty(postData)) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "params [postData] is not null");
        }

        String authorization=params.get("authorization");

        if (StringUtils.isEmpty(authorization)) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "params [authorization] is not null");
        }

        // 3 进行请求操作
        try {

            // 1 创建okhttp客户端对象
            OkHttpClient client = new OkHttpClient.Builder()
              .connectTimeout(1,TimeUnit.MINUTES)
              .readTimeout(3,TimeUnit.MINUTES)
              .writeTimeout(2,TimeUnit.MINUTES)
              .build();
            // 2 request 默认是get请求
            Request request = new Request.Builder().url(url)
                                                   .addHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                                                   .addHeader("Authorization", authorization)
                                                   .addHeader("x-csrf-token", "fetch")
                                                   .addHeader("Accept", "application/json")
                                                   .build();

            Response response = client.newCall(request).execute();

            // 4 判断是否请求成功
            if (response.isSuccessful()) {
                log.info("authorization success");

                MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

                okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(JSON_TYPE, postData);
                Headers requestHeader=response.headers();

                Request request2 = new Request.Builder().url(postRequestUrl)
                                                               .post(requestBody)
                                                               .addHeader("Cookie",requestHeader.values("set-cookie").get(0))
                                                               .addHeader("Cookie",requestHeader.get("set-cookie"))
                                                               .removeHeader("Content-Type")
                                                               .addHeader("Content-Type","application/json")
                                                               .addHeader("Accept","application/json")
                                                               .addHeader("x-csrf-token",requestHeader.get("x-csrf-token"))
                                                               .build();

                Response response2 = client.newCall(request2).execute();

                String result = getContentFromRbody(response2.body());

                return Result.newSuccess(result);
            }else{
                log.error("trace tenantId:{}  headerMap:{}, bodyMap:{} ,response:{}, ",tenantId,headerMap, bodyMap, response);
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR,"authorization error "+ response.message());
            }
        } catch (IOException e) {
            log.error("trace tenantId:{}  headerMap:{}, bodyMap:{} , ",tenantId,headerMap, bodyMap, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, e.getMessage());
        }


    }






}
