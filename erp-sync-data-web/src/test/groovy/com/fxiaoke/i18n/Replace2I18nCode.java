package com.fxiaoke.i18n;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Ignore;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/8/15 10:40:40
 */
@Ignore
public class Replace2I18nCode {
    private static Set<File> files = new HashSet<>();

    String filePath = "/Users/<USER>/Downloads/iLangScan/fs-erp-sync-data-git.txt"; // 替换为实际文件路径

    String directoryPath = "/Users/<USER>/workspaces/java/fs-erp-sync-data-git"; // 替换为源代码目录路径
    File i18nEnumFile = new File("/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-i18n/src/main/java/com/fxiaoke/open/erpsyncdata/i18n/I18NStringEnum.java");

    Map<String, String> i18nMap = Arrays.stream(I18NStringEnum.values()).collect(Collectors.toMap(I18NStringEnum::getI18nValue, Enum::name, (a, b) -> a));
    int startIndex;
    AtomicInteger index;


    public void init() {
        i18nMap.values().stream()
                .filter(s -> s.startsWith("s37") && s.length() == 5)
                .max(String::compareTo)
                .ifPresent(s -> startIndex = Integer.parseInt(s.substring(1)));
        index = new AtomicInteger(startIndex);
    }

    @Test
    @Ignore
    public void test() {
        init();
        readAndFindSourceFiles(filePath, directoryPath);

//        replaceBizExceptionI18nValue();
//        replaceExceptionI18nValue();
//        replaceChineseVariable();
//        replaceBizResult();
//        replaceI18nManagerChinese();
//        replaceStringFormat();
        replaceChineseArg();

//        replaceControllerActionName();

//        replaceReturnChinese();
    }

    private void replaceControllerActionName() {
        replaceChinese(true, "asyncExecute\\w+\\(.*\\s*(\"[^\"\n]*[\\u4e00-\\u9fff]+[^\"\n]*\")",
                matcher -> matcher.group(1),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(i18nValue, "i18NStringManager.get(I18NStringEnum."+code+", getLang(), getLoginUserTenantId())");
                    replaceMap.put(group, replace);
                });
    }

    private void replaceChineseArg() {
        replaceChinese(false, "\\.\\w+\\(.*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")(\\s*\\+\\s*[^,;]*)*\\)",
                matcher -> matcher.group(1),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(i18nValue, getReplacement(code));
                    replaceMap.put(group, replace);
                });
    }

    private void replaceStringFormat() {
        // String.format("失败超过5000，failedId:%s", failedIds)
        replaceChinese(false, Pattern.compile("(String\\.format\\((\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")(,\\s*[^);]*))\\)"),
                matcher -> matcher.group(2),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(matcher.group(1), "i18NStringManager.getByEi2(I18NStringEnum." + code + ", tenantId" + matcher.group(3));
                    replaceMap.put(group, replace);
                });
    }

    private void replaceI18nManagerChinese() {
        // i18NStringManager.get(I18NStringEnum.s104.getI18nKey(),lang,tenantId,"调试获取单条数据接口")
        replaceChinese(false, Pattern.compile("i18NStringManager\\.get\\(.*(,\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\"))\\)"),
                matcher -> matcher.group(2),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    if (!group.contains("I18NStringEnum." + code)) {
                        System.out.println("未知:" + group);
                        return;
                    }
                    final String replace = group.replace(matcher.group(1), "").replace("I18NStringEnum." + code + ".getI18nKey()", "I18NStringEnum." + code);
                    replaceMap.put(group, replace);
                }
        );
    }

    private void replaceBizResult() {
        replaceChinese(false, "Result.newError\\([^,]*\\s*,\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")(\\s*\\+\\s*[^,;]*)*\\);",
                matcher -> matcher.group(1),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(i18nValue, "I18NStringEnum." + code);
                    replaceMap.put(group, replace);
                });
        replaceChinese(false, "(Result.newError\\(\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")\\))",
                matcher -> matcher.group(2),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String s = group.replace(matcher.group(1), "Result.newSystemError(I18NStringEnum." + code + ")");
                    replaceMap.put(group, s);
                });

        // Result.newErrorByI18N("计算超时", I18NStringEnum.s93.getI18nKey(), null))
        replaceChinese(false, "(Result.newErrorByI18N\\((\\s*SYSTEM_ERROR\\s*,)?\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\"),\\s*I18NStringEnum\\.(.*)\\.getI18nKey\\(\\)\\s*(,\\s*.*)?\\))",
                matcher -> matcher.group(3),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String c = matcher.group(4);
                    if (!c.equals(code)) {
                        System.out.println("code和错误信息对不上:" + group);
                        return;
                    }
                    final String arg = matcher.group(5);
                    if (arg.equals(",null") || arg.equals(", null")) {
                        final String s = group.replace(matcher.group(1), "Result.newSystemError(I18NStringEnum." + code + ")");
                        replaceMap.put(group, s);
                        return;
                    }
                    final String s = group.replace(matcher.group(1), "Result.newSystemError(I18NStringEnum." + code + arg + ")");
                    replaceMap.put(group, s);
                });

        // Result.newErrorByI18N(ResultCodeEnum.ERROR_MSG,"k3订单id字段不允许删除",I18NStringEnum.s390.getI18nKey(),null)
        replaceChinese(false, "(Result.newErrorByI18N\\(\\s*([\\w\\.]+\\s*)?,?\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\"),\\s*I18NStringEnum\\.(.*)\\.getI18nKey\\(\\)\\s*(,\\s*.*)?\\))",
                matcher -> matcher.group(3),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String c = matcher.group(4);
                    if (!c.equals(code)) {
                        System.out.println("code和错误信息对不上:" + group);
                        return;
                    }
                    final String arg = matcher.group(5);
                    if (arg.equals(",null") || arg.equals(", null")) {
                        final String s = group.replace(matcher.group(1), "Result.newError("+ matcher.group(2) +".getErrCode(), I18NStringEnum." + code + ")");
                        replaceMap.put(group, s);
                        return;
                    }
                    final String s = group.replace(matcher.group(1), "Result.newSystemError(I18NStringEnum." + code + arg + ")");
                    replaceMap.put(group, s);
                });


        // Result.newErrorByI18N(ResultCodeEnum.SYSTEM_ERROR,
        //                        "调用接口错误： "+ response.message(),
        //                        I18NStringEnum.s30.getI18nKey(),
        //                        null)
        replaceChinese(true, "(Result.newErrorByI18N\\(\\s*([\\w\\.]+\\s*)?,?\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")(\\s*\\+\\s*[^,]*)?,\\s*I18NStringEnum\\.(.*)\\.getI18nKey\\(\\)\\s*(,\\s*.*)?\\))",
                matcher -> matcher.group(3),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String c = matcher.group(5);
                    if (!c.equals(code)) {
                        System.out.println("code和错误信息对不上:" + group);
                        return;
                    }
                    final String arg = matcher.group(6);
                    if (arg.contains("null")) {
                        final String s = group.replace(matcher.group(1), "Result.newError("+ matcher.group(2) +".getErrCode(), I18NStringEnum." + code + ")");
                        replaceMap.put(group, s);
                        return;
                    }
                    final String s = group.replace(matcher.group(1), "Result.newSystemError(I18NStringEnum." + code + arg + ")");
                    replaceMap.put(group, s);
                });
    }

    private void replaceExceptionI18nValue() {
        replaceChinese(false, "new \\w+Exception\\((\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\").*\\)",
                matcher -> matcher.group(1),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(i18nValue, getReplacement(code));
                    replaceMap.put(group, replace);
                });
    }

    private static @NotNull String getReplacement(String code) {
        return "i18NStringManager.getByEi(I18NStringEnum." + code + ", tenantId)";
    }

    private void replaceChineseVariable() {
        replaceChinese(true, Pattern.compile(".*String \\w+\\s*=\\s*(\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")"),
                matcher -> matcher.group(1),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(i18nValue, getReplacement(code));
                    replaceMap.put(group, replace);
                });
    }

    private void replaceReturnChinese() {
        replaceChinese(false, Pattern.compile(".*return (\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")(\\s*\\+?\\s*[^;]*)*;"),
                matcher -> {
                    final String group = matcher.group(1);
                    if (group.startsWith("\"--")) {
                        return null;
                    }
                    return group;
                },
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    final String replace = group.replace(i18nValue, getReplacement(code));
                    replaceMap.put(group, replace);
                });
    }

    private void replaceBizExceptionI18nValue() {
        Pattern bizNullExceptionChinese = Pattern.compile("new ErpSyncDataException\\((\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")((\\s*,\\s*)null){2}\\)");

        replaceChinese(false, Pattern.compile("new ErpSyncData(Monitored)?Exception\\((\"[^\"]*[\\u4e00-\\u9fff]+[^\"]*\")(\\s*,\\s*).*\\)"),
                matcher -> matcher.group(2),
                (context, group, i18nValue, code, matcher, replaceMap) -> {
                    if (group.contains("I18NStringEnum." + code + ".getI18nKey()")) {
                        final String s1 = group.replace(i18nValue + matcher.group(3), "").replace(".getI18nKey()", "");
                        replaceMap.put(group, s1);
                    } else if (bizNullExceptionChinese.matcher(group).find()) {
                        final String s1 = group.replace(i18nValue + matcher.group(3), "").replace(".getI18nKey()", "");
                        replaceMap.put(group, "new ErpSyncDataException(I18NStringEnum." + code + ", null)");
                    } else {
                        System.out.println("未知异常格式:" + group);
                    }
                });
    }

    interface ReplaceI18nValue {
        void addReplaceMap(String context, String group, String i18nValue, String code, Matcher matcher, Map<String, String> replaceMap);
    }

    private void replaceChinese(boolean test, String regex, Function<Matcher, String> i18nValueFunc, ReplaceI18nValue replaceI18nValue) {
        if (!regex.startsWith(".*")) {
            regex = ".*" + regex;
        }
        replaceChinese(test, Pattern.compile(regex), i18nValueFunc, replaceI18nValue);
    }

    @SneakyThrows
    private void replaceChinese(boolean test, Pattern pattern, Function<Matcher, String> i18nValueFunc, ReplaceI18nValue replaceI18nValue) {
        for (File file : files) {
            String s = FileUtils.readFileToString(file, "UTF-8");
            List<String> addI18nList = new ArrayList<>();
            Map<String, String> replaceMap = new HashMap<>();
            Matcher matcher = pattern.matcher(s);
            while (matcher.find()) {
                String group = matcher.group();
                if (group.trim().startsWith("//")) {
                    continue;
                }
                String i18nValue = i18nValueFunc.apply(matcher);
                if (StringUtils.isEmpty(i18nValue)) {
                    continue;
                }
                String realI18nValue = i18nValue;
                if (realI18nValue.startsWith("\"") && realI18nValue.endsWith("\"")) {
                    realI18nValue = realI18nValue.substring(1, realI18nValue.length() - 1);
                }
                if (!i18nMap.containsKey(realI18nValue)) {
                    addI18nEnum(realI18nValue, addI18nList);
                }

                final String code = i18nMap.get(realI18nValue);
                replaceI18nValue.addReplaceMap(s, group, i18nValue, code, matcher, replaceMap);
            }

            if (MapUtils.isNotEmpty(replaceMap)) {
                saveI18nEnum(addI18nList);
                replaceFile(file, replaceMap, s);
                if (test) {
                    break;
                }
            }
        }
    }

    private static void replaceFile(File file, Map<String, String> replaceMap, String s) throws IOException {
        if (!replaceMap.isEmpty()) {
            s = addI18nService(s);

            for (Map.Entry<String, String> entry : replaceMap.entrySet()) {
                s = s.replace(entry.getKey(), entry.getValue());
            }
            FileUtils.writeStringToFile(file, s, "UTF-8");
            System.out.println(file.getName() + "替换成功:");
            replaceMap.forEach((k, v) -> System.out.println(k + " -> " + v));
            System.out.println("----------------\n\n");
        }
    }

    private static String addI18nService(String s) {
        if (s.contains("private I18NStringManager i18NStringManager;")) {
            return s;
        }
        if (!s.contains("import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;")) {
            final String ps = StringUtils.substringBefore(s, ";");
            s = s.replace(ps + ";\n", ps + ";\n\nimport com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;");
        }

        if (!s.contains("import org.springframework.beans.") && !s.contains("import org.springframework.web.bind.annotation")) {
            return s;
        }

        s = s.replace("import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;", "import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;\nimport com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;");

        final Matcher matcher = Pattern.compile("public .*class .*\\{").matcher(s);
        matcher.find();
        final String group = matcher.group();

        return s.replace(group, group + "\n    @Autowired\n    private I18NStringManager i18NStringManager;\n");
    }

    @SneakyThrows
    private void addI18nEnum(String value, List<String> addI18nList) {
        final int i = index.incrementAndGet();
        addI18nList.add("    s" + i + "(\"" + value + "\"),");
        i18nMap.put(value, "s" + i);
    }

    @SneakyThrows
    private void saveI18nEnum(List<String> addI18nList) {
        if (addI18nList.isEmpty()) {
            return;
        }
        final String s = FileUtils.readFileToString(i18nEnumFile, "Utf-8");
        final String s1 = StringUtils.substringBetween(s, "s" + startIndex, "\n");
        final String join = String.join("\n", addI18nList);
        final String replace = s.replace(s1, s1 + "\n" + join);
        FileUtils.writeStringToFile(i18nEnumFile, replace, "Utf-8");
        startIndex = index.get();
        System.out.println("++++++++++++++++++++");
        System.out.println("添加枚举:\n" + join);
    }

    private static void readAndFindSourceFiles(String filePath, String directoryPath) {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            Path sourceDirectory = Paths.get(directoryPath); // 项目的源文件目录

            String line;
            Set<String> names = Sets.newLinkedHashSet();
            while ((line = reader.readLine()) != null) {
                names.add(StringUtils.substringBefore(line, ":"));
            }
            findSourceFiles(sourceDirectory, names);
        } catch (IOException e) {
            System.err.println("Error reading file: " + filePath);
            e.printStackTrace();
        }
    }

    static Set<String> notScanFiles = Sets.newHashSet(
            // 实施后台
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-sync-data-web-jar/src/main/java/com/fxiaoke/open/erpsyncdata/web/tools",
            // 配置中心配置
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/common-db-proxy/src/main/java/com/fxiaoke/open/erpsyncdata/dbproxy/util/ConfigCenter.java",
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/fs-erp-oa/src/main/java/com/fxiaoke/open/oasyncdata/db/util/ConfigCenter.java",
            // 研发管理后台
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-sync-data-web-jar/src/main/java/com/fxiaoke/open/erpsyncdata/web/controller/superAdmin",
            // 模板中心专项处理
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-sync-data-admin/src/main/java/com/fxiaoke/open/erpsyncdata/admin/service/impl/TemplateServiceImpl.java",
            // excel导入解析,产品表示先不改
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/erp-sync-data-admin/src/main/java/com/fxiaoke/open/erpsyncdata/admin/manager/ExcelListener/IntegrationStreamMultiSheetListener.java",
            // 常量
            "/Users/<USER>/workspaces/java/fs-erp-sync-data-git/sync-common/src/main/java/com/fxiaoke/open/erpsyncdata/common/constant/SyncCompareConstant.java"
    );

    /**
     * 根据全限定类名查找源文件。
     *
     * @param sourceDirectory 项目的源文件目录
     * @param classNames       全限定类名
     * @return 匹配的源文件列表
     */
    private static void findSourceFiles(Path sourceDirectory, Set<String> classNames) throws IOException {
        final Map<String, String> map = classNames.stream().collect(Collectors.toMap(className -> className.replace('.', '/') + ".java", Function.identity()));
        try (Stream<Path> stream = Files.walk(sourceDirectory)) {
            stream.filter(path -> !path.toFile().isDirectory() &&
                    !path.toString().contains("/target/") &&
                    !path.toString().contains("/test/") &&
                    path.toString().endsWith(".java")
            ).forEach(path -> {
                final String pathString = path.toString();
                map.keySet().stream()
                        .filter(pathString::contains)
                        .findFirst()
                        .ifPresent(name -> {
                            map.remove(name);
                            if (name.endsWith("Arg.java") || name.endsWith("Result.java")) {
                                return;
                            }
                            if (notScanFiles.stream().anyMatch(pathString::contains)) {
//                                System.out.println("不扫描的数据:" + pathString);
                                return;
                            }
                            try {
                                final String s = FileUtils.readFileToString(path.toFile(), "utf-8");
                                if (s.contains("import lombok.Data;\n") && !s.contains("import org.springframework.beans.")) {
                                    final Matcher matcher = Pattern.compile(".*@Data\n").matcher(s);
                                    if (matcher.find() && matcher.group().startsWith("@Data")) {
//                                        System.out.println("Dto: " + name);
                                        return;
                                    }
                                }
                                files.add(path.toFile());
                            } catch (IOException e) {
                                e.printStackTrace();
                                files.add(path.toFile());
                            }
                        });
            });
        }

        if (MapUtils.isNotEmpty(map)) {
            System.out.println("找不到类 : " + map.size() + "\n" + String.join("\n", map.values()) + "\n\n");
        }
    }
}

