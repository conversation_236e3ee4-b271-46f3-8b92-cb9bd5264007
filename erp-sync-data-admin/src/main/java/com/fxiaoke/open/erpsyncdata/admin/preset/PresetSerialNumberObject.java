package com.fxiaoke.open.erpsyncdata.admin.preset;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PresetSerialNumberObject extends AbstractPresetObject {
    @Override
    protected List<String> getFormId() {
        return Lists.newArrayList(K3CloudForm.BD_SerialMainFile);
    }

    @Override
    public List<String> getFieldMappingJson() {
        String json = "{\"id\":\"\",\"masterObjectMapping\":{\"sourceObjectApiName\":\"BD_SerialMainFile.BillHead\",\"destObjectApiName\":\"SerialNumberObj\",\"fieldMappings\":[{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"default__c\",\"optionMappings\":[],\"destApiName\":\"record_type\",\"destType\":\"record_type\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":3,\"value\":\"1000\",\"optionMappings\":[],\"destApiName\":\"owner\",\"destType\":\"employee\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FMaterialID.FNumber\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_MATERIAL.BillHead\",\"destApiName\":\"product_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"ProductObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"FNumber\",\"sourceType\":\"text\",\"destApiName\":\"name\",\"destType\":\"text\"},{\"valueType\":1,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"virtual_stock_id\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"STK_Inventory.BillHead\",\"destApiName\":\"stock_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"StockObj\"},{\"valueType\":null,\"defaultValue\":null,\"mappingType\":1,\"optionMappings\":[],\"sourceApiName\":\"warehouseComId\",\"sourceType\":\"object_reference\",\"sourceTargetApiName\":\"BD_STOCK.BillHead\",\"destApiName\":\"warehouse_id\",\"destType\":\"object_reference\",\"destTargetApiName\":\"WarehouseObj\"}]},\"detailObjectMappings\":[]}";
        return Lists.newArrayList(json);
    }

    @Override
    public List<SyncPloyDetailCreateArg> getSyncPloyDetailCreateArg() {
        SyncPloyDetailCreateArg arg = new SyncPloyDetailCreateArg();
        arg.setPloyId(tenantId);
        arg.setSourceTenantIds(Lists.newArrayList(tenantId));
        arg.setSourceTenantType(TenantType.ERP);
        arg.setSourceObjectApiName("BD_SerialMainFile.BillHead");
        arg.setDestTenantIds(Lists.newArrayList(tenantId));
        arg.setDestTenantType(TenantType.CRM);
        arg.setDestObjectApiName("SerialNumberObj");
        arg.setDcId(dataCenterId);
        return Lists.newArrayList(arg);
    }
}
