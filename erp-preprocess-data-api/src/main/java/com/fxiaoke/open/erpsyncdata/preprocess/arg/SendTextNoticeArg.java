package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/2/1
 */
@Data
public class SendTextNoticeArg implements Serializable {

    /**
     * 企业ea
     */
    private String enterpriseAccount;

    /**
     * 企业id
     */
    private String tenantId;

    /**
     * 原始企业id，发送通知到CSM会改写tenantId，这里专门用一个字段，记录原始的企业id
     */
    private String tenantIdOld;

    /**
     * 数据中心ID 2023.08.09
     */
    private String dataCenterId;

    /**
     * 集成流ID，可能为空 2023.08.09
     */
    private String ployDetailId;

    /**
     * 接收者
     */
    private List<Integer> receivers;

    /**
     * 消息标题，转文本使用
     */
    private String msgTitle;
    /**
     * 使用原始标题
     */
    private boolean useOriginalTitle = false;

    /**
     * 消息体
     */
    private String msg;
}
