<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpInterfaceLogDao">
  <resultMap id="BaseResultMap" type="com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpInterfaceLogEntity">
    <!--@mbg.generated-->
    <!--@Table erp_interface_log-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="trace_id" jdbcType="VARCHAR" property="traceId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="object_api_name" jdbcType="VARCHAR" property="objectApiName" />
    <result column="data_id" jdbcType="VARCHAR" property="dataId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="operation_type" jdbcType="SMALLINT" property="operationType" />
    <result column="request" jdbcType="VARCHAR" property="request" />
    <result column="response" jdbcType="VARCHAR" property="response" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, trace_id, tenant_id, object_api_name, data_id, "name", operation_type, request, 
    response, create_time, update_time
  </sql>
</mapper>