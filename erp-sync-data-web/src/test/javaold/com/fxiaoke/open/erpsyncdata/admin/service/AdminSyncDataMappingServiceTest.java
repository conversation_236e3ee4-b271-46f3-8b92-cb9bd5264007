package com.fxiaoke.open.erpsyncdata.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.CreateSyncDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QuerySyncDataMappingArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncDataMappingListByPloyDetailIdArg;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingListByPloyDetailIdResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingResult;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.StatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.SplitObjectManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SapConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CrmRemoteService;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 14:02 2021/1/19
 * @Desc:
 */

public class AdminSyncDataMappingServiceTest extends BaseTest {

    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private CrmRemoteService crmRemoteService;
    @Autowired
    private InterfaceFormatService interfaceFormatService;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;
    @Autowired
    private SplitObjectManager splitObjectManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private IdGenerator idGenerator;

    private static String curlPostAPIPrefix = "<p><p>POST ";
    private static String curlGetAPIPrefix = "<p><p>GET ";
    private static String curlAPIRequest = "<p> --header 'Content-Type: application/json' "+"<p> --header '通过groovy脚本获取的header参数'" ;
    private static String curlAPIDataRaw = "<p> --data-raw ";
    private static String curlAPIResponse = "<p> http 请求response <p>";
    private static String curlAPINewLine = "<br>";



    public Result<List<ErpObjCustomFunctionResult>> getInterfaceFormat( ErpObjectDescResult arg) {
        List<ErpObjCustomFunctionResult> erpObjCustomFunctionResults = Lists.newArrayList();
        String tenantId = "71658";
        Integer userId = 10001;
        String dataCenterId="615224561780785152";
        Result<ConnectInfoResult> connectInfo = connectInfoService.getConnectInfoByDataCenterId(tenantId, userId,dataCenterId);
        Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy= Maps.newHashMap();
        Result<List<ErpObjCustomFunctionResult>> groovyListResult = erpObjCustomFunctionService.queryByApiName(tenantId,dataCenterId, userId, arg.getErpObjectApiName());
        if(groovyListResult!=null&& CollectionUtils.isNotEmpty(groovyListResult.getData())){//区分不同数据中心
            url2Groovy=groovyListResult.getData().stream().filter(function ->function.getDataCenterId()!=null&&function.getDataCenterId().equals(dataCenterId)).collect(Collectors.toMap(ErpObjCustomFunctionResult::getUrl, groovy->groovy));
        }
        String baseUrl="";
        if(connectInfo != null) {
            ConnectInfoResult.ConnectParams connectParams = connectInfo.getData().getConnectParams();
            if (connectParams !=null) {
                if (connectParams.getSap() != null && connectParams.getSap().getBaseUrl() != null) {
                    baseUrl = connectParams.getSap().getBaseUrl();
                }
                if (connectParams.getK3Cloud() != null && connectParams.getK3Cloud().getBaseUrl() != null) {
                    baseUrl = connectParams.getK3Cloud().getBaseUrl();
                }
                if (connectParams.getU8() != null && connectParams.getU8().getBaseUrl() != null) {
                    baseUrl = connectParams.getU8().getBaseUrl();
                }
                if (connectParams.getStandard() != null && connectParams.getStandard().getBaseUrl() != null) {
                    baseUrl = connectParams.getStandard().getBaseUrl();
                }
            }
        }
        if(StringUtils.isNotEmpty(baseUrl)&&!baseUrl.endsWith("/")){
            baseUrl=baseUrl+"/";
        }
        String erpInterfaceFormatResults="";
        Result<ErpInterfaceFormatResult> addDataInterfaceFormat = interfaceFormatService.getAddDataInterfaceFormat(tenantId, userId, arg,dataCenterId, null, null,null,null);
        Result<ErpInterfaceFormatResult> queryDataListInterfaceFormat = interfaceFormatService.getQueryDataListInterfaceFormat(tenantId, userId, arg,dataCenterId, null, null,null);
        Result<ErpInterfaceFormatResult> queryDataDetailByIdInterfaceFormat = interfaceFormatService.getQueryDataDetailByIdInterfaceFormat(tenantId, userId, arg,dataCenterId, null, null,null);
        Result<ErpInterfaceFormatResult> invalidDataByIdInterfaceFormat = interfaceFormatService.getQueryDataDetailByIdInterfaceFormat(tenantId, userId, arg,dataCenterId, null, null,null);


        SapConnectParam.ServicePath servicePath=new SapConnectParam.ServicePath();
        if(addDataInterfaceFormat.isSuccess()&&addDataInterfaceFormat.getData()!=null){
            erpInterfaceFormatResults += curlPostAPIPrefix+ baseUrl+servicePath.getCreate()+curlAPIRequest + curlAPIDataRaw + addDataInterfaceFormat.getData().getRequest();
            erpInterfaceFormatResults += curlAPIResponse + addDataInterfaceFormat.getData().getResponse() + curlAPINewLine+curlAPINewLine;
            if(url2Groovy.keySet().contains(ErpObjInterfaceUrlEnum.create)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.create).setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.create));
            }else{
                ErpObjCustomFunctionResult erpObjCustomFunctionResult =new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.create);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
            erpInterfaceFormatResults = curlPostAPIPrefix+ baseUrl+servicePath.getUpdate()+curlAPIRequest + curlAPIDataRaw + addDataInterfaceFormat.getData().getRequest();
            erpInterfaceFormatResults += curlAPIResponse + addDataInterfaceFormat.getData().getResponse()+ curlAPINewLine+curlAPINewLine;
            if(url2Groovy.keySet().contains(ErpObjInterfaceUrlEnum.update)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.update).setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.update));
            }else{
                ErpObjCustomFunctionResult erpObjCustomFunctionResult =new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.update);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
            String pushInterfaceFormat="push接口数据转换"+ curlAPINewLine+addDataInterfaceFormat.getData().getRequest();
            if(url2Groovy.keySet().contains(ErpObjInterfaceUrlEnum.push)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.push).setInterfaceFormat(pushInterfaceFormat);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.push));
            }else{
                ErpObjCustomFunctionResult erpObjCustomFunctionResult =new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(pushInterfaceFormat);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.push);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
        }
        if((queryDataListInterfaceFormat.isSuccess()&&queryDataListInterfaceFormat.getData()!=null)){
            erpInterfaceFormatResults = curlGetAPIPrefix+ baseUrl+servicePath.getQueryByTime()+ "?" + queryDataListInterfaceFormat.getData().getRequest() + curlAPIRequest;
            erpInterfaceFormatResults += curlAPIResponse + queryDataListInterfaceFormat.getData().getResponse()+curlAPINewLine+curlAPINewLine;
            if(url2Groovy.keySet().contains(ErpObjInterfaceUrlEnum.queryMasterBatch)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.queryMasterBatch).setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.queryMasterBatch));
            }else{
                ErpObjCustomFunctionResult erpObjCustomFunctionResult =new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.queryMasterBatch);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
        }
        if((queryDataDetailByIdInterfaceFormat.isSuccess()&&queryDataDetailByIdInterfaceFormat.getData()!=null)){
            erpInterfaceFormatResults = curlGetAPIPrefix+ baseUrl+servicePath.getView()+ "?" + queryDataDetailByIdInterfaceFormat.getData().getRequest() + curlAPIRequest;
            erpInterfaceFormatResults += curlAPIResponse + queryDataDetailByIdInterfaceFormat.getData().getResponse()+curlAPINewLine+curlAPINewLine;
            if(url2Groovy.keySet().contains(ErpObjInterfaceUrlEnum.queryMasterById)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.queryMasterById).setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.queryMasterById));
            }else{
                ErpObjCustomFunctionResult erpObjCustomFunctionResult =new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.queryMasterById);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
        }

        {
            /**
             * POST http://test.fxiaoke.com/invalid
             * --header 'Content-Type: application/json'
             * --header '通过groovy脚本获取的header参数'
             * --data-raw { "objAPIName": "productObj", "masterFieldVal": { "_id": " id" }}
             * http 请求response
             * { "errCode": "错误返回码", "errMsg": "错误提示语"}   */

            String invalidRequestFormat = "{ \"objAPIName\": \"" + arg.getErpObjectApiName() + " \", \"masterFieldVal\": { \"_id\": \" id\" }}";
            String invalidResponseFormat = "{ \"errCode\": \"错误返回码\", \"errMsg\": \"错误提示语\"}";
            erpInterfaceFormatResults = curlPostAPIPrefix+ baseUrl+servicePath.getInvalid()+curlAPIRequest + curlAPIDataRaw + invalidRequestFormat;
            erpInterfaceFormatResults += curlAPIResponse + invalidResponseFormat + curlAPINewLine+curlAPINewLine;
            if(url2Groovy.keySet().contains(ErpObjInterfaceUrlEnum.invalid)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.invalid).setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.invalid));
            }else{
                ErpObjCustomFunctionResult erpObjCustomFunctionResult = new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.invalid);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
        }

        {
            /**
             * POST www.test.com/invalidDetail
             *
             * --header 'Content-Type: application/json'
             *
             * --header '通过groovy脚本获取的header参数'
             *
             * --data-raw {
             *     "objAPIName": "erpAccount ",
             *     "masterFieldVal": {
             *         "_id": "id"
             *     },
             *     "detailFieldVal": {
             *         "detailObj1": "id"
             *     }
             * }
             *
             * http 请求response
             *
             * { "errCode": "错误返回码", "errMsg": "错误提示语"}
             */

            String invalidRequestFormat = String.format("{\n" +
                    "    \"objAPIName\": \"%s\", \n" +
                    "    \"masterFieldVal\": {\n" +
                    "        \"_id\": \"id\"\n" +
                    "    }, \n" +
                    "    \"detailFieldVal\": {\n" +
                    "        \"【明细apiName】\": \"id\"\n" +
                    "    }\n" +
                    "}", arg.getErpObjectApiName());
            String invalidResponseFormat = "{ \"errCode\": \"错误返回码\", \"errMsg\": \"错误提示语\"}";
            erpInterfaceFormatResults = curlPostAPIPrefix+ baseUrl+servicePath.getInvalidDetail()+curlAPIRequest + curlAPIDataRaw + invalidRequestFormat;
            erpInterfaceFormatResults += curlAPIResponse + invalidResponseFormat + curlAPINewLine+curlAPINewLine;
            if(url2Groovy.containsKey(ErpObjInterfaceUrlEnum.invalidDetail)){
                url2Groovy.get(ErpObjInterfaceUrlEnum.invalidDetail).setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResults.add(url2Groovy.get(ErpObjInterfaceUrlEnum.invalidDetail));
            }else {
                ErpObjCustomFunctionResult erpObjCustomFunctionResult = new ErpObjCustomFunctionResult();
                erpObjCustomFunctionResult.setInterfaceFormat(erpInterfaceFormatResults);
                erpObjCustomFunctionResult.setUrl(ErpObjInterfaceUrlEnum.invalidDetail);
                erpObjCustomFunctionResult.setObjApiName(arg.getErpObjectApiName());
                erpObjCustomFunctionResults.add(erpObjCustomFunctionResult);
            }
        }
        return Result.newSuccess(erpObjCustomFunctionResults);
    }
    @Test
    public void updateSyncDataMappingBySourceDataId() {
        long ll=System.currentTimeMillis();
        SyncDataMappingListByPloyDetailIdArg syncDataMappingListByPloyDetailIdArg=new SyncDataMappingListByPloyDetailIdArg();
        syncDataMappingListByPloyDetailIdArg.setPloyDetailId("5c4ce802de584b989b3265a429acdc96");
        syncDataMappingListByPloyDetailIdArg.setStatus(0);
        syncDataMappingListByPloyDetailIdArg.setPageSize(100);
        syncDataMappingListByPloyDetailIdArg.setPageNumber(1);
        syncDataMappingListByPloyDetailIdArg.setStartTime(1628220285820L);
        syncDataMappingListByPloyDetailIdArg.setEndTime(1628220290314L);
        Result<SyncDataMappingListByPloyDetailIdResult> list = adminSyncDataMappingService.listByPloyDetailId("81243",
                syncDataMappingListByPloyDetailIdArg,null);
        long result1=System.currentTimeMillis()-ll;
        syncDataMappingListByPloyDetailIdArg.setPageSize(500);
        syncDataMappingListByPloyDetailIdArg.setPageNumber(100);
        Result<SyncDataMappingListByPloyDetailIdResult> list1 = adminSyncDataMappingService.listByPloyDetailId("82370", syncDataMappingListByPloyDetailIdArg,null);
        long result11=System.currentTimeMillis()-ll-result1;
        SyncDataMappingResult arg=new SyncDataMappingResult();
        arg.setSourceObjectApiName("AccountObj");
        arg.setSourceDataId("sourceDataId123");
        arg.setSourceDataName("sourceDataName1233");
        arg.setDestObjectApiName("BD_Customer.BillHead");
        arg.setDestDataId("destDataId12355");
        arg.setDestDataName("destDataName123");
        arg.setRemark("测试1124");
        Result<String> result = adminSyncDataMappingService.updateSyncDataMappingBySourceDataId(81138, arg,null);
        System.out.println("");
    }

    @Test
    public void listByPloyDetail(){
        long ll=System.currentTimeMillis();
        SyncDataMappingListByPloyDetailIdArg syncDataMappingListByPloyDetailIdArg=new SyncDataMappingListByPloyDetailIdArg();
        syncDataMappingListByPloyDetailIdArg.setPloyDetailId("9146108531fd402f8cbd692f0e1d9341");
        syncDataMappingListByPloyDetailIdArg.setRemark("数据筛选");
        syncDataMappingListByPloyDetailIdArg.setStatus(0);
        syncDataMappingListByPloyDetailIdArg.setPageSize(100);
        syncDataMappingListByPloyDetailIdArg.setPageNumber(1);
        Result<SyncDataMappingListByPloyDetailIdResult> list = adminSyncDataMappingService.listByPloyDetailId("82777",
                syncDataMappingListByPloyDetailIdArg,null);

    }


    @Test
    public void createSyncDataMapping() {
        CreateSyncDataMappingArg arg=new CreateSyncDataMappingArg();
        arg.setPloyDetailId("eb87efa75d214fcd8303154adbf59784");
        arg.setSourceObjectApiName("BD_MATERIAL.BillHead");
        arg.setSourceDataId("sourceDataId224");
        arg.setSourceDataName("sourceDataName1233");
        arg.setDestObjectApiName("ProductObj");
        arg.setDestDataId("destDataId123");
        arg.setDestDataName("destDataName123");
        arg.setRemark("测试112");
        arg.setEnableUpdateSourceDataId(true);
        Result<SyncDataMappingsEntity> syncDataMapping = adminSyncDataMappingService.createSyncDataMapping( 81138, arg,null);
        System.out.println("");
    }

    @Test
    public void getSyncDataMappingBySourceDataId() {
        QuerySyncDataMappingArg arg=new QuerySyncDataMappingArg();
        arg.setSourceObjectApiName("AccountObj");
        arg.setDestObjectApiName("customer_fake_1599898109325");
        arg.setSourceDataId(Lists.newArrayList("5f5cb84e9d328b00011e88e1","5f5cbf539d328b00011e97ca"));
        arg.setDestDataId(Lists.newArrayList("5f5cbf545ffa920001316e99","***********"));
        Result<Map<String, SyncDataMappingResult>> result = adminSyncDataMappingService.getSyncDataMappingBySourceDataIdOrDestDataId(79675, arg,null);
        System.out.println("");
    }

    @Test
    public void test(){

        String str111="1;747849;737220;732441;750052;749408;750048;719556;735572;742877;732559;728580;736991;727543;747457;722037; ********;********;744940;733767;743019;744018;730724;734516;727890;748862;739416;744659;748060;721788;743956;743672;733378;731656;741048;737001;726265;749747;702454;744767;726902;742968;730910;748983;739733;736516;712701;633969;747010;709662;719267;339913;706089;707609;710938;711811;711878;712298;712713;736750;722056;744672;1;747849;737220;732441;750052;749408;750048;719556;735572;742877;732559;728580;736991;727543;747457;722037; ********;********;744940;733767;743019;744018;730724;734516;727890;748862;739416;744659;748060;721788;743956;743672;733378;731656;741048;737001;726265;749747;702454;744767;726902;742968;730910;748983;739733;736516;712701;633969;747010;709662;719267;339913;706089;707609;710938;711811;711878;712298;712713;736750;722056;744672;1;747849;737220;732441;750052;749408;750048;719556;735572;742877;732559;728580;736991;727543;747457;722037; ********;********;744940;733767;743019;744018;730724;734516;727890;748862;739416;744659;748060;721788;743956;743672;733378;731656;741048;737001;726265;749747;702454;744767;726902;742968;730910;748983;739733;736516;712701;633969;747010;709662;719267;339913;706089;707609;710938;711811;711878;712298;712713;736750;722056;744672;713301;713356;713569;714592;715486;716355;716580;716996;717155;717315;717817;718296;718357;718700;718774;718954;719032;719127;719438;720002;721214;721788;722037;722038;722341;722743;722747;723995;724234;724316;724761;724957;725100;717944;709381";
        Set<String> gray= ImmutableSet.copyOf((Splitter.on(";").split(str111)));
        String str222="720684;721549;722160;722438;723795;723819;723943;724640;724920;724968;724971;725179;725267;725881;726239;726282;726640;728052;730754;731104;731174;732089;732112;732208;732219;733606;734190;735791;735897;736497;736788;736840;736985;737611;738217;738229;738976;738996;739023;739029;739034;739065;739073;739078;743109;743943;744231;744240;746511;747048;747357;747611";
        Set<String> urgent= ImmutableSet.copyOf((Splitter.on(";").split(str222)));
        List<String> listll=Lists.newArrayList();
        for(String tenant:urgent){
            if(gray.contains(tenant)){
                listll.add(tenant);
            }
        }
        SyncDataContextEvent obj=new SyncDataContextEvent();
        ObjectData master=new ObjectData();
        master.putApiName("test");
        master.putTenantId("71658");
        master.put("id","1341241234");
        obj.setSourceData(master);
        ObjectData detail1=new ObjectData();
        detail1.putApiName("testref1");
        detail1.putTenantId("71658");
        detail1.put("id","1341243433341234");
        ObjectData detail2=new ObjectData();
        detail2.putApiName("testref2");
        detail2.putTenantId("71658");
        detail2.put("id","1341211141234");
        obj.getDetailData().put("testref1",Lists.newArrayList(detail1));
        obj.getDetailData().put("testref2",Lists.newArrayList(detail2));
        List<SyncDataContextEvent> newObjList = splitObjectManager.erpRealObj2SplitObjData(obj, "615224561780785152");

        ErpObjectDescResult arg=new ErpObjectDescResult();
        arg.setChannel(ErpChannelEnum.ERP_SAP);
        arg.setErpObjectApiName("test");
        arg.setId("783476442316537856");
        Result<List<ErpObjCustomFunctionResult>>rrr=getInterfaceFormat(arg);
//        Result<Void> result1 = adminSyncDataMappingService.redoSyncData("81243", Lists.newArrayList("30f92a3af3964ea18414015045c7f1b2","c89355ff9a0448ceac3cb1c09f59186c","ab46eec0fb644d738912cdccb89c5646","92081bc9a210475d85f0e4261904a096","2710f8c5c9b84fb393ffac08d752a106","3de662f2ce314f0691b3318e2add8f0d","e98099e543c241069849612fd18ee4ed","b2d77c5a6c444789b4c92b99350fea68","82c1d593e87444b7823cf0b8c0e02460","d5adca370fc1419ba969d14869585604","1e7ac02c9750474bb368a359ea5f0442","e39288591adb41e18cb0653653fa1511","630ea140eaf540c2974e6c6a1bf5c7b0","a71ae5c840a9496ab51f302a6568bf9e","dcf9e93d99b74a1089a44cd225537786","4ca26e227e494b0fbeaf658d5089d69a","7568f81d6ee144668d9b27627c4b310e","c61c33802c4244e9b433e6533b98eccf","885c51152b7a4b71b8d36410ae9122ca","2732446aec704030bdc52a229d6bb876","661f9a0ec3154a508072728a809a2537","9d9fb81506ca41ba862f4e6b87e8ab24","8a0886f02d7842e1aaeb2a2503731072","780ecb35253c49468009a5ff69eb08e8","d41ff89f6c2346afa79d7648f2011951","c0584cfb882b4f6d9512d499d474b7fe","e87e1af1fb4e4ccb9afcd38f62f96832","66b444884af642b48081a80795188aac","92e76472e1794f61af86aecfecf427b4","a0a7950c108f48b9a1c40818405d8066","78aeead3937e4711af80da3e02bcd15e","82e34171222348679ad24f179c98036e","542a3065d41243ac9f23a9898cd18db8","a9feb3a882194f80b64df47d8cfcf5a0","b421b1c9949d4d568dec5aaaacbd70b6","b10e8874bb09426e8fad3b1be0fb1615","02638a85f3bf405dab570365f9d97dc9","cbe69f99b7c349c48d1b624e5a7b4d7e","685b4d44bc5e445989f4ea4b59fe67af","1cc127b489994678a394b714a52c84eb","f9618bcc787547e2a784c7bbf9d93601","3b1e3ab789e0497da724a7f6cd1a8b02","075d91b5e94a4a7a9486b2cf6896a2c5","ed946fe60fd04fe39c77f2f52b40269b","7fe2ae0450b14a4fbc7e4fceed25d00f","e589df24420549c1bef0d366933ba81b","39e8812276ba4b6d8f1379225af8190f","647f4fe9840a4dfabb69102e3b136417","81182082a3c24cddac808a310f517dcd","fb3cb41d111940f1a00fdc2e99b748c6"),null);
        Result<JSONObject> jsonObjectResult = crmRemoteService.checkModuleStatus("81031", "multiple_unit");
        if(jsonObjectResult!=null&&jsonObjectResult.isSuccess()&&jsonObjectResult.getData().getJSONObject("value")!=null&&
                StatusEnum.IsMultipleUnitOpen.getValue().equals(jsonObjectResult.getData().getJSONObject("value").getString("openStatus"))){
            System.out.println("");
        }
        HeaderObj headerObj = HeaderObj.newInstance(81031, -10000);
        com.fxiaoke.crmrestapi.common.result.Result<ListObjectDescribeResult> result = objectDescribeService.list(headerObj, true, Lists.newArrayList("MultiUnitRelatedObj"));
        System.out.println("");
    }

    @Test
    public void testBatchMappings(){
        List<SyncDataMappingsEntity> syncDataMappingsEntities=Lists.newArrayList();
       ThreadPoolExecutor executor = new NamedThreadPoolExecutor("batchDoWriteDataMq", 50, 50);
        CountDownLatch countDownLatch = new CountDownLatch(10000);
        for (int i = 0; i < 10000; i++) {

            int finalValue = i;
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    String sourceDataId = idGenerator.get();
                    String destDataId = idGenerator.get();
                    SyncDataMappingsEntity syncDataMappingsEntity=new SyncDataMappingsEntity();
                    syncDataMappingsEntity.setSourceTenantId("84307");
                    syncDataMappingsEntity.setSourceObjectApiName("push_main_1ftrccnf3");
                    syncDataMappingsEntity.setDestObjectApiName("object_q010y__c");
                    syncDataMappingsEntity.setSourceDataId(sourceDataId);
                    syncDataMappingsEntity.setDestDataId(destDataId);
                    syncDataMappingsEntity.setSourceDataName("筛选源数据"+"___"+ finalValue);
                    syncDataMappingsEntity.setDestDataName("筛选目标数据"+"___"+ finalValue);
                    syncDataMappingsEntity.setRemark("从对象新的备注"+idGenerator.get());
                    syncDataMappingsEntity.setLastSyncStatus(5);
                    syncDataMappingsEntity.setIsCreated(false);
                    syncDataMappingsEntity.setLastSourceDataVserion(1L);
                    syncDataMappingsEntity.setId(idGenerator.get());
                    syncDataMappingsEntity.setLastSyncDataId(sourceDataId);
                    syncDataMappingsEntity.setCreateTime(System.currentTimeMillis());
                    syncDataMappingsEntity.setUpdateTime(System.currentTimeMillis());
                    syncDataMappingsEntity.setTenantId("84307");
                    syncDataMappingsEntity.setIsCreated(false);
                    syncDataMappingsEntity.setDestTenantId("84307");
                    syncDataMappingsEntity.setIsDeleted(false);
                    syncDataMappingsEntities.add(syncDataMappingsEntity);
                    if(finalValue%100==0&&finalValue!=0){
                       syncDataMappingsDao.setTenantId("84307").batchInsert("84307",syncDataMappingsEntities);
                       syncDataMappingsEntities.clear();
                    }
                    countDownLatch.countDown();
                }
            });
        }
        try {
            //线程等待 等countDownLatch计数归0、子线程执行完毕主线程才执行
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }



        System.out.println(111);
    }

}