package com.fxiaoke.open.erpsyncdata.common.thread;

import com.github.trace.executor.MonitorTaskWrapper;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 该工厂可以传递trace
 */
public class NamedThreadFactory implements ThreadFactory {
    private static final AtomicInteger POOL_NUMBER = new AtomicInteger(1);
    private final AtomicInteger threadNumber = new AtomicInteger(1);
    private final ThreadGroup group;
    private final String namePrefix;
    private final boolean isDaemon;

    public NamedThreadFactory() {
        this("erp-comm-pool");
    }

    public NamedThreadFactory(String name) {
        this(name, false);
    }

    public NamedThreadFactory(String preffix, boolean daemon) {
        if (!preffix.endsWith("-")) {
            preffix = preffix + "-";
        }
        SecurityManager s = System.getSecurityManager();
        this.group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
        this.namePrefix = preffix + POOL_NUMBER.getAndIncrement();
        this.isDaemon = daemon;
    }

    @Override
    public Thread newThread(Runnable r) {
        Runnable wrap = MonitorTaskWrapper.wrap(r);
        Thread t = new Thread(this.group, wrap, this.namePrefix + this.threadNumber.getAndIncrement(), 0);
        t.setDaemon(this.isDaemon);
        if (t.getPriority() != Thread.NORM_PRIORITY) {
            t.setPriority(Thread.NORM_PRIORITY);
        }
        return t;
    }

}
