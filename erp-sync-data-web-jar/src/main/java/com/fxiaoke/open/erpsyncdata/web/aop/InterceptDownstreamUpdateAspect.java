package com.fxiaoke.open.erpsyncdata.web.aop;

import com.fxiaoke.open.erpsyncdata.admin.model.UserVo;
import com.fxiaoke.open.erpsyncdata.dbproxy.aop.AbstractReplaceEnterpriseAspect;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.i18n.I18nUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.annontation.ManagedTenantIntercept;
import com.fxiaoke.open.erpsyncdata.web.interceptor.UserContextHolder;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/3/20 17:51:30
 *
 * 拦截下游企业的写操作
 */
@Component
@Aspect
@Slf4j
public class InterceptDownstreamUpdateAspect extends AbstractReplaceEnterpriseAspect {
    private final Set<String> writeMethodPrefix = Sets.newHashSet("create", "update", "delete", "checkAndUpdate", "add", "save", "refresh", "reverse", "preSetK3Obj", "setK3", "batchDelete");

    @Around("execution(* com.fxiaoke.open.erpsyncdata.web.controller..*.*(..)) ")
    public Object interceptDownstreamUpdate(ProceedingJoinPoint jp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final Method method = methodSignature.getMethod();
        final String methodName = method.getName();
        if (method.getDeclaringClass().equals(Object.class) || writeMethodPrefix.stream().noneMatch(methodName::startsWith)) {
            return jp.proceed();
        }

        final Class<?> declaringType = methodSignature.getDeclaringType();
        final ManagedTenantIntercept annotation = AnnotationUtils.findAnnotation(declaringType, ManagedTenantIntercept.class);
        if (Objects.isNull(annotation)) {
            return jp.proceed();
        }

        UserVo hostedUserVo = UserContextHolder.getHostedUserVo();
        if (Objects.nonNull(hostedUserVo)) {
            final String tenantId = String.valueOf(hostedUserVo.getEnterpriseId());
            return errorResult(method, tenantId);
        }

        String tenantId = UserContextHolder.getUserVo().getTenantId();
        if (Objects.nonNull(tenantId) && managedEnterprise(tenantId)) {
            return errorResult(method, tenantId);
        }

        return jp.proceed();
    }

    /**
     * 文件的导入导出需要直接调用文件服务,此时身份为上游,找不到存在下游的文件
     */
    @Around("execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.ExcelFileCepController.*(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.ExcelFileImportCepController.*(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.tool.ExcelFileController.*(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.ErpTempDataMonitorController.downloadTempDataMonitor(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.ErpInterfaceMonitorController.downloadObjInterfaceMonitor(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.DataVerificationController.asyncDownloadIdDetail(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.tool.ExcelFileController.asyncImportObjectDataMapping(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.InterfaceFormatController.exportInterfaceFormat(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.InterfaceFormatController.batchExportInterfaceFormat(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.ErpObjectFieldsController.exportObjectFieldData(..)) || " +
            "execution(* com.fxiaoke.open.erpsyncdata.web.controller.setUp.DataVerificationController.asyncVerifyTimeHistoryDataId(..))")
    public Object interceptDownstreamExcel(ProceedingJoinPoint jp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) jp.getSignature();
        final Method method = methodSignature.getMethod();

        // 导入导出只拦截上游使用下游身份的时候
        UserVo hostedUserVo = UserContextHolder.getHostedUserVo();
        if (Objects.nonNull(hostedUserVo)) {
            final String tenantId = String.valueOf(hostedUserVo.getEnterpriseId());
            return errorExcelResult(method, tenantId);
        }

        return jp.proceed();
    }

    @NotNull
    private Object errorExcelResult(Method method, String tenantId) {
        log.error("InterceptDownstreamUpdateAspect cannot upload/download, tenantId:{} method:{}", tenantId, method.getName());
        final Class<?> returnType = method.getReturnType();
        final ResultCodeEnum code = ResultCodeEnum.MANAGED_2_DOWNSTREAM_CANNOT_LOAD;
        return errorResult(tenantId, returnType, code);
    }

    @NotNull
    private Object errorResult(Method method, String tenantId) {
        log.error("InterceptDownstreamUpdateAspect cannot write, tenantId:{} method:{}", tenantId, method.getName());
        final Class<?> returnType = method.getReturnType();
        final ResultCodeEnum code = ResultCodeEnum.MANAGED_ENTERPRISE_CANNOT_WRITE;
        return errorResult(tenantId, returnType, code);
    }
}
