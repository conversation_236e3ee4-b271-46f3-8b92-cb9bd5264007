package com.fxiaoke.open.oasyncdata.model;

import com.fxiaoke.open.oasyncdata.arg.CepArg;
import com.fxiaoke.open.oasyncdata.constant.OATenantEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * oa对接日志
 *
 * <AUTHOR>
 * @date 2021/3/17
 */
@Data
public class OASettingVO extends CepArg implements Serializable {
    private static final long serialVersionUID = 3739723344368834569L;

    private String id;

    /**
     * 企业id
     */

    private String tenantId;
    /**
     * 企业名称
     */

    private String enterpriseName;

    /**
     * 数据中心id
     */

    private String dataCenterId;

    /**
     * 数据type
     * @see OATenantEnum
     */
    private String type;

    /**
     * 数据配置信息
     */
    private String configuration;

}