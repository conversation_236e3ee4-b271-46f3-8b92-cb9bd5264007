package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.util.NumberUtil;
import com.mongodb.client.model.IndexModel;
import groovy.util.logging.Slf4j;
import lombok.experimental.UtilityClass;
import org.bson.BsonDocument;
import org.bson.BsonNumber;
import org.bson.Document;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * mongodb工具
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/26
 */
@UtilityClass
@Slf4j
public class MongoUtil {
    /**
     * 移除已经存在的索引
     * 使用字段名+正序顺序，未考虑其他情况
     *
     * @param existIndexes      [{"v":1,"key":{"_id":1},"name":"_id_","ns":"fs-erp-sync-data-5.recycle_bin_data"},{"v":1,"key":{"tenantId":1,"recycleType":1,"createTime":1},"name":"tenantId_1_recycleType_1_createTime_1","ns":"fs-erp-sync-data-5.recycle_bin_data","background":true},{"v":1,"key":{"createTime":1},"name":"createTime_1","ns":"fs-erp-sync-data-5.recycle_bin_data","background":true,"expireAfterSeconds":15552000}]
     * @param needCreateIndexes 需要创建的索引
     */
    public void removeExistIndexes(List<Document> existIndexes, List<IndexModel> needCreateIndexes) {
        Set<String> existIndexUniKeys = new HashSet<>();
        for (Document existIndex : existIndexes) {
            String uniKey = calUniKeyFromDocument(existIndex);
            existIndexUniKeys.add(uniKey);
        }
        needCreateIndexes.removeIf(indexModel -> {
            String uniKey = calUniKeyFromIndexModel(indexModel);
            return existIndexUniKeys.contains(uniKey);
        });
    }

    private static String calUniKeyFromIndexModel(IndexModel indexModel) {
        BsonDocument bsonDocument = indexModel.getKeys().toBsonDocument(null, null);
        String uniKey = bsonDocument
                .entrySet().stream().map(v -> v.getKey() + "_" + ((BsonNumber) v.getValue()).intValue()).collect(Collectors.joining("_"));
        return uniKey;
    }

    private static String calUniKeyFromDocument(Document existIndex) {
        Document keys = existIndex.get("key", Document.class);
        String uniKey = keys.entrySet().stream().map(v -> v.getKey() + "_" + v.getValue()).collect(Collectors.joining("_"));
        return uniKey;
    }

    /**
     * 只支持修改过期索引
     * 获取需要修改的index，只支持修改超时时间为更大的
     *
     * @param existIndexes
     * @param needCreateIndexes
     * @return collMod command 修改命令
     */
    public Document getNeedModifyCollExpireIndex(String collName, List<Document> existIndexes, List<IndexModel> needCreateIndexes) {
        Optional<IndexModel> first = needCreateIndexes.stream()
                .filter(v -> v.getOptions().getExpireAfter(TimeUnit.SECONDS) != null)
                .findAny();
        if (!first.isPresent()) {
            return null;
        }
        IndexModel expireIndex = first.get();
        Long minExpireSeconds = expireIndex.getOptions().getExpireAfter(TimeUnit.SECONDS);
        assert minExpireSeconds != null;
        String uniKey = calUniKeyFromIndexModel(expireIndex);
        Optional<Document> anyExist = existIndexes.stream().filter(v -> calUniKeyFromDocument(v).equals(uniKey)).findAny();
        if (!anyExist.isPresent()) {
            return null;
        }
        Document existIndex = anyExist.get();
        Long expireAfterSeconds = safeParseLong(existIndex.get("expireAfterSeconds"));
        if (expireAfterSeconds != null && expireAfterSeconds >= minExpireSeconds) {
            return null;
        }
        //需要调整索引
        Document keyPattern = existIndex.get("key", Document.class);
        //超时索引
        Document command = new Document();
        command.put("collMod", collName);
        Document index2 = new Document();
        index2.put("keyPattern", keyPattern);
        index2.put("expireAfterSeconds", minExpireSeconds);
        command.put("index", index2);
        return command;
    }


    private Long safeParseLong(Object obj) {
        if (obj == null) {
            return 0L;
        }
        if (obj instanceof Long) {
            return ((Long) obj);
        }
        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        }
        return NumberUtil.parseLong(obj.toString());
    }

}
