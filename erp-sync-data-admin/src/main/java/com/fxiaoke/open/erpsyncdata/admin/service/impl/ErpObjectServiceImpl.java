package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.ErpIntegrationStreamExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.admin.remote.CrmRemoteManager;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectFieldDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.NumUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.CascaderInfoAndObjResult;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.DeleteErpObjectArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpObjectSimpleInfo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SubDetailExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.Connector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjNameDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.UpdateErpObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.AssertUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum.*;

/**
 * <AUTHOR>
 * @Date: 14:02 2020/8/19
 * @Desc:
 */
@Service
@Slf4j
public class ErpObjectServiceImpl implements ErpObjectService {

    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjectFieldDao erpObjectFieldDao;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private ErpFieldManager erpFieldManager;
    @Autowired
    private DataCenterManager dataCenterManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private NeedSingleSyncManager needSingleSyncManager;
    @Autowired
    private CrmRemoteManager crmRemoteManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Override
    public Result<List<ErpObjectDescResult>> queryErpFakeObjectByTenantIdAndDcId(String tenantId, int userId, String dataCenterId) {
        ErpObjectEntity arg = new ErpObjectEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        arg.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        return query(tenantId, arg);
    }

    @Override
    public Result<List<ErpObjectDescResult>> queryErpFakeMasterObjectByTenantId(String tenantId, int userId, String dataCenterId) {
        List<ErpObjectDescResult> allMasterErpObjects = Lists.newArrayList();
        Map<String, List<ErpObjectDescResult>> masterObjApiName2Detail = Maps.newHashMap();
//        findErpMasterAndDetailObj(tenantId, userId, allMasterErpObjects, masterObjApiName2Detail, dataCenterId);
        //直接获取所有对象，与对象列表使用统一入口，该接口已经优化，去除缓存并且访问数据库较少。
        List<ErpObjectRelationshipResult> realResult = queryRealErpObjectByTenantIdAndDcId(tenantId, userId, null, dataCenterId).safeData();
        //构建结果
        for (ErpObjectRelationshipResult erpObjectRelationshipResult : realResult) {
            for (ErpObjectDescResult erpObjectDescResult : erpObjectRelationshipResult.getFakeErpObject()) {
                if (ErpObjSplitTypeEnum.TYPES_ALLOW_INDEPENDENT_STREAM.contains(erpObjectDescResult.getSplitType())) {
                    allMasterErpObjects.add(erpObjectDescResult);
                } else {
                    Boolean needSingleSync = needSingleSyncManager.getErpNeedSingleSync(tenantId, dataCenterId, erpObjectRelationshipResult.getActualErpObject(), erpObjectDescResult);
                    if (needSingleSync) {
                        allMasterErpObjects.add(erpObjectDescResult);
                    }
                }
                if (erpObjectDescResult.getChildren() != null) {
                    for (ErpObjectDescResult child : erpObjectDescResult.getChildren()) {
                        if (ErpObjSplitTypeEnum.TYPES_ALLOW_INDEPENDENT_STREAM.contains(child.getSplitType())) {
                            allMasterErpObjects.add(child);
                        }
                    }
                }
            }
        }
        return Result.newSuccess(allMasterErpObjects);
    }

    @Override
    public Result<List<CascaderInfoAndObjResult>> queryAllErpFakeMasterObject(String tenantId, int userId) {
        final List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.listByTenantIdAndStatus(tenantId, SyncPloyDetailStatusEnum.ENABLE.getStatus());
        final Map<String, Set<String>> connectorObjectApiNameMap = syncPloyDetailEntities.stream()
                .collect(Collectors.groupingBy(SyncPloyDetailEntity::getSourceDataCenterId, Collectors.mapping(SyncPloyDetailEntity::getSourceObjectApiName, Collectors.toSet())));

        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);

        // erp连接器和对象
        List<CascaderInfoAndObjResult> objResults = erpConnectInfoEntities.stream()
                .filter(item -> !item.getChannel().equals(ErpChannelEnum.CRM))
                .filter(item -> item.getChannel().getConnectorType() != ConnectorTypeEnum.OA)
                .filter(item -> StringUtils.isNotEmpty(item.getConnectParams()))
                .map(erpConnectInfoEntity -> {
                    // 部分连接器没有(erp->crm)集成流
                    final Set<String> sourceApiNames = connectorObjectApiNameMap.computeIfAbsent(erpConnectInfoEntity.getId(), k -> new HashSet<>());
                    final List<ErpObjNameDescResult> objectDescResults = sourceApiNames.stream()
                            .map(apiName -> {
                                final String erpObjName = erpObjManager.getErpObjName(tenantId, erpConnectInfoEntity.getId(), apiName);
                                return new ErpObjNameDescResult(apiName, erpObjName);
                            }).collect(Collectors.toList());

                    CascaderInfoAndObjResult cascaderInfoAndObjResult = new CascaderInfoAndObjResult();
                    cascaderInfoAndObjResult.setDataCenterId(erpConnectInfoEntity.getId());
                    cascaderInfoAndObjResult.setChannelEnum(erpConnectInfoEntity.getChannel());
                    Connector connector = AllConnectorUtil.getByChannelAndConnectParam(erpConnectInfoEntity.getChannel(), erpConnectInfoEntity.getConnectParams());
                    cascaderInfoAndObjResult.setConnectorKey(connector.getKey());
                    cascaderInfoAndObjResult.setIconUrl(connector.getIconUrl());
                    cascaderInfoAndObjResult.setDataCenterName(erpConnectInfoEntity.getDataCenterName());
                    cascaderInfoAndObjResult.setObjectDescResults(objectDescResults);
                    return cascaderInfoAndObjResult;
                }).collect(Collectors.toList());

        // 添加crm连接器和对象
        final ErpConnectInfoEntity connectInfo = erpConnectInfoEntities.stream()
                .filter(item -> item.getChannel().equals(ErpChannelEnum.CRM))
                .findFirst()
                .orElseThrow(() -> new ErpSyncDataException(NOT_FOUND_CONNECTOR, tenantId));
        // 没有crm->erp的集成流
        final Set<String> sourceApiNames = connectorObjectApiNameMap.computeIfAbsent(connectInfo.getId(), k -> new HashSet<>());
        final Map<String, String> objectMap = crmRemoteManager.listObjectNamesByApiNames(tenantId, Lists.newArrayList(sourceApiNames));
        final List<ErpObjNameDescResult> objectDescResults = objectMap.entrySet().stream()
                .map(entry -> new ErpObjNameDescResult(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        CascaderInfoAndObjResult cascaderInfoAndObjResult = new CascaderInfoAndObjResult();
        cascaderInfoAndObjResult.setDataCenterId(connectInfo.getId());
        cascaderInfoAndObjResult.setDataCenterName(connectInfo.getDataCenterName());
        cascaderInfoAndObjResult.setChannelEnum(connectInfo.getChannel());
        cascaderInfoAndObjResult.setObjectDescResults(objectDescResults);
        Connector connector = AllConnectorUtil.getByChannelAndConnectParam(connectInfo.getChannel(), connectInfo.getConnectParams());
        cascaderInfoAndObjResult.setConnectorKey(connector.getKey());
        cascaderInfoAndObjResult.setIconUrl(connector.getIconUrl());
        objResults.add(cascaderInfoAndObjResult);

        return Result.newSuccess(objResults);
    }

    @Override
    public Result<List<ErpObjectDescResult>> queryErpFakeDetailObjectByMasterApiNameAndDcId(String tenantId, int userId, String masterObjApiName, String dataCenterId) {
        //直接获取所有对象，与对象列表使用统一入口，该接口已经优化，去除缓存并且访问数据库较少。
        List<ErpObjectRelationshipResult> realResult = queryRealErpObjectByTenantIdAndDcId(tenantId, userId, null, dataCenterId).safeData();
        //构建结果 旧逻辑在这里查出所有字段然后找有主从字段的对象。改为在创建时校验
        for (ErpObjectRelationshipResult erpObjectRelationshipResult : realResult) {
            if (Objects.equals(erpObjectRelationshipResult.getMasterObjectApiName(), masterObjApiName)) {
                //获取在集成流里面作为明细的对象
                List<ErpObjectDescResult> streamDetailObjDesc = erpObjectRelationshipResult.getFakeErpObject().stream().filter(v -> !ErpObjSplitTypeEnum.TYPES_ALLOW_INDEPENDENT_STREAM.contains(v.getSplitType())).collect(Collectors.toList());
                return Result.newSuccess(streamDetailObjDesc);
            }
        }
        return Result.newSuccess(Lists.newArrayList());
    }


    public Result<List<ErpObjectDescResult>> query(String tenantId, ErpObjectEntity arg) {
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        if (CollectionUtils.isEmpty(erpObjectEntities)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        List<ErpObjectDescResult> erpObjectDescs = Lists.newArrayList();
        for (ErpObjectEntity erpObjectEntity : erpObjectEntities) {
            ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
            query.setTenantId(tenantId);
            query.setDataCenterId(erpObjectEntity.getDataCenterId());
            query.setErpSplitObjectApiname(erpObjectEntity.getErpObjectApiName());
            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
            BeanUtils.copyProperties(erpObjectEntity, erpObjectDescResult);
            String erpObjectExtendValue = erpObjectEntity.getErpObjectExtendValue();
            if (StringUtils.isNotEmpty(erpObjectExtendValue)) {
                Object fieldExtendValue = GsonUtil.fromJson(erpObjectExtendValue, Object.class);
                erpObjectDescResult.setErpObjectExtendValue(fieldExtendValue);
            }
            if (CollectionUtils.isNotEmpty(erpObjectRelationshipEntities)) {
                erpObjectDescResult.setSplitType(erpObjectRelationshipEntities.get(0).getSplitType());
            }
            erpObjectDescs.add(erpObjectDescResult);
        }
        return Result.newSuccess(erpObjectDescs);
    }

    @Override
    public Result<ErpObjectDescResult> queryErpObjectByObjApiName(String tenantId, String dataCenterId, int userId, String objApiName) {
        ErpObjectEntity arg = new ErpObjectEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        arg.setErpObjectApiName(objApiName);
        Result<List<ErpObjectDescResult>> result = query(tenantId, arg);
        if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            return Result.newSuccess(result.getData().get(0));
        }
        return Result.newSuccess();
    }

    /**
     * 更新或新增
     *
     * @param tenantId
     * @param dataCenterId
     * @param erpObjectDescResult
     * @return
     */
    @Override
    public Result<String> updateErpObject(String tenantId, String dataCenterId, int userId, ErpObjectDescResult erpObjectDescResult) {
        ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
        BeanUtils.copyProperties(erpObjectDescResult, erpObjectEntity);
        erpObjectEntity.setTenantId(tenantId);
        erpObjectEntity.setDataCenterId(dataCenterId);
        erpObjectEntity.setErpObjectExtendValue(GsonUtil.toJson(erpObjectDescResult.getErpObjectExtendValue()));
        if (StringUtils.isEmpty(erpObjectDescResult.getId())) {//插入
            ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
            if (connectInfo == null) {
                return Result.newError(THE_ENTERPRISE_CON_NOT_EXIST);
            }
            erpObjectEntity.setChannel(connectInfo.getChannel());
            ErpObjectEntity query = new ErpObjectEntity();
            query.setTenantId(tenantId);
            query.setDataCenterId(dataCenterId);
            query.setErpObjectApiName(erpObjectDescResult.getErpObjectApiName());
            List<ErpObjectEntity> oldEntry = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            if (CollectionUtils.isNotEmpty(oldEntry)) {
                return Result.newError(ResultCodeEnum.THE_ERP_OBJECT_EXIST);
            }
            final String id = idGenerator.get();
            erpObjectEntity.setId(id);
            erpObjectEntity.setCreateTime(System.currentTimeMillis());
            erpObjectEntity.setUpdateTime(System.currentTimeMillis());
            int insertResult = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpObjectEntity);
            if (insertResult == 1) {
                return Result.newSuccess(id);
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        } else {//更新
            erpObjectEntity.setUpdateTime(System.currentTimeMillis());
            int updateResult = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(erpObjectEntity);
            if (updateResult == 1) {
                return Result.newSuccess(erpObjectEntity.getId());
            } else {
                return Result.newError(SYSTEM_ERROR);
            }
        }
    }

    @Override
    @Transactional
    public Result<UpdateErpObjectResult> updateErpObjects(String tenantId,
                                                          String dataCenterId,
                                                          int userId,
                                                          ErpObjectRelationshipResult erpObjectRelationshipResult,
                                                          String lang) {
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId, dataCenterId);
        if (connectInfo == null) {
            return Result.newError(THE_ENTERPRISE_CON_NOT_EXIST);
        }
        ErpObjectDescResult actualErpObj = erpObjectRelationshipResult.getActualErpObject();
        if (actualErpObj.getErpObjectApiName() == null) {
            throw new ErpSyncDataException(i18NStringManager.get(I18NStringEnum.s143, lang, tenantId), null, null);
        }
        actualErpObj.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        Result<String> actualErpObjResult = updateErpObject(tenantId, dataCenterId, userId, actualErpObj);
        //非K3渠道的，允许多批次，所以允许对象apiName重复。即，当成功或者除K3的渠道obj重复时，接着走下面的流程。
        boolean canContinue = actualErpObjResult.isSuccess() ||
                (!ErpChannelEnum.ERP_K3CLOUD.equals(actualErpObj.getChannel())
                        && THE_ERP_OBJECT_EXIST.getErrCode().equals(actualErpObjResult.getErrCode()));
        if (!canContinue) {
            return Result.copy(actualErpObjResult);
        }
        //虚拟对象
        List<ErpObjectDescResult> fakeErpObjs = erpObjectRelationshipResult.getFakeErpObject();
        if (CollectionUtils.isEmpty(fakeErpObjs)) {//默认生成一条虚拟对象
            ErpObjectDescResult fakeErpObj = new ErpObjectDescResult();
            fakeErpObj.setErpObjectName(actualErpObj.getErpObjectName());
            fakeErpObj.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
            fakeErpObjs.add(fakeErpObj);
        }
        //批次校验，全部新增时，不需要传批次。存在更新时，需要传输批次。
        Integer splitSeq = erpObjectRelationshipResult.getSplitSeq();
        boolean allInsert = splitSeq == null;
        if (allInsert) {
            //校验
            for (ErpObjectDescResult fakeErpObj : fakeErpObjs) {
                if (StrUtil.isNotBlank(fakeErpObj.getId())) {
                    throw new ErpSyncDataException(i18NStringManager.get(I18NStringEnum.s144, lang, tenantId), null, null);
                }
                if (CollUtil.isNotEmpty(fakeErpObj.getChildren())) {
                    for (ErpObjectDescResult child : fakeErpObj.getChildren()) {
                        if (StrUtil.isNotBlank(child.getId())) {
                            throw new ErpSyncDataException(i18NStringManager.get(I18NStringEnum.s144, lang, tenantId), null, null);
                        }
                    }
                }
            }
            //设置新批次号
            splitSeq = getOnlySplitSeq(tenantId, dataCenterId, actualErpObj.getErpObjectApiName());
            erpObjectRelationshipResult.setSplitSeq(splitSeq);

        }
        //校验一些对象
        ErpChannelEnum channel = connectInfo.getChannel();
        checkFakeErpObj(tenantId, actualErpObj, fakeErpObjs);
        for (ErpObjectDescResult fakeErpObj : fakeErpObjs) {
            fakeErpObj.setSplitSeq(splitSeq);
            checkOrSetObjApiName(tenantId, dataCenterId, actualErpObj, fakeErpObj);
            //新增或更新对象和对象关系
            updateRelationAndObject(tenantId, dataCenterId, userId, channel, actualErpObj, fakeErpObj);
            //孙对象处理
            if (fakeErpObj.getChildren() != null) {
                for (ErpObjectDescResult subDetailObj : fakeErpObj.getChildren()) {
                    //检查或填充ObjApiName
                    checkOrSetObjApiName(tenantId, dataCenterId, actualErpObj, subDetailObj);
                    subDetailObj.setSplitSeq(splitSeq);
                    //处理扩展信息
                    SubDetailExtend subDetailExtend = SubDetailExtend.parse(subDetailObj.getErpObjectExtendValue());
                    if (subDetailExtend == null || subDetailExtend.getRealObjectApiName() == null) {
                        //不填值时，报错
                        throw new ErpSyncDataException(i18NStringManager.get(I18NStringEnum.s145, lang, tenantId), null, null);
                    }
                    subDetailExtend.setDetailObjectApiName(fakeErpObj.getErpObjectApiName());
                    subDetailObj.setErpObjectExtendValue(subDetailExtend);
                    updateRelationAndObject(tenantId, dataCenterId, userId, channel, actualErpObj, subDetailObj);
                }
            }
        }
        return Result.newSuccess(new UpdateErpObjectResult(actualErpObjResult.getData(), splitSeq));
    }

    private void checkOrSetObjApiName(String tenantId, String dataCenterId, ErpObjectDescResult actualErpObj, ErpObjectDescResult fakeErpObj) {
        if (StringUtils.isEmpty(fakeErpObj.getId())) {
            if (StringUtils.isBlank(fakeErpObj.getErpObjectApiName())) {
                //中间对象apiName为空则获取
                fakeErpObj.setErpObjectApiName(getFakeObjectApiName(tenantId, dataCenterId, actualErpObj, fakeErpObj));
            }
        } else {
            if (StringUtils.isBlank(fakeErpObj.getErpObjectApiName())) {
                //中间对象apiName为空则报错
                throw new ErpSyncDataException(I18NStringEnum.s146, tenantId);
            }
        }
    }

    private void updateRelationAndObject(String tenantId, String dataCenterId, int userId, ErpChannelEnum channel, ErpObjectDescResult actualErpObj, ErpObjectDescResult fakeErpObj) {
        if (StringUtils.isEmpty(fakeErpObj.getId())) {
            //插入对象关系表
            ErpObjectRelationshipEntity entity = new ErpObjectRelationshipEntity();
            entity.setTenantId(tenantId);
            entity.setDataCenterId(dataCenterId);
            entity.setErpSplitObjectApiname(fakeErpObj.getErpObjectApiName());
            entity.setSplitType(fakeErpObj.getSplitType());
            entity.setChannel(channel);
            entity.setErpRealObjectApiname(actualErpObj.getErpObjectApiName());
            entity.setSplitSeq(fakeErpObj.getSplitSeq());
            int insert = updateErpObjectRelationship(entity);
            if (insert != 1) {
                throw new ErpSyncDataException(THE_DATABASE_ERROR, tenantId);
            }
        } else {
            //不允许修改批次和拆分类型了
//            ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
//            query.setTenantId(tenantId);
//            query.setDataCenterId(dataCenterId);
//            query.setErpRealObjectApiname(actualErpObj.getErpObjectApiName());
//            query.setErpSplitObjectApiname(fakeErpObj.getErpObjectApiName());
//            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
//            if (CollectionUtils.isNotEmpty(erpObjectRelationshipEntities)) {//更新拆分关系
//                erpObjectRelationshipEntities.get(0).setSplitType(fakeErpObj.getSplitType());
//                erpObjectRelationshipEntities.get(0).setSplitSeq(fakeErpObj.getSplitSeq());
//                int insert = updateErpObjectRelationship(erpObjectRelationshipEntities.get(0));
//                if (insert != 1) {
//                    throw new ErpSyncDataException(THE_DATABASE_ERROR);
//                }
//            }
        }
        if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(fakeErpObj.getSplitType())) {
            //主对象使用真实对象的对象名称
            fakeErpObj.setErpObjectName(actualErpObj.getErpObjectName());
        }
        fakeErpObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        Result<String> fakeErpObjResult = updateErpObject(tenantId, dataCenterId, userId, fakeErpObj);
        if (!fakeErpObjResult.isSuccess()) {//如果失败把对象关系表删除掉
            ErpObjectRelationshipEntity entity = new ErpObjectRelationshipEntity();
            entity.setTenantId(tenantId);
            entity.setDataCenterId(dataCenterId);
            entity.setErpSplitObjectApiname(fakeErpObj.getErpObjectApiName());
            entity.setErpRealObjectApiname(actualErpObj.getErpObjectApiName());
            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(entity);
            if (CollectionUtils.isNotEmpty(erpObjectRelationshipEntities)) {
                int delete = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, erpObjectRelationshipEntities.get(0).getId());
                if (delete != 1) {
                    throw new ErpSyncDataException(THE_DATABASE_ERROR, tenantId);
                }
            }
            throw new ErpSyncDataException(fakeErpObjResult.getErrCode(), fakeErpObjResult.getErrMsg(), fakeErpObjResult.getI18nKey(), tenantId);
        }
    }

    private void checkFakeErpObj(String tenantId, final ErpObjectDescResult actualObj, final List<ErpObjectDescResult> fakeObjs) {
        //暂时只需要校验K3C
        if (!actualObj.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD)) {
            return;
        }

        for (ErpObjectDescResult fakeObj : fakeObjs) {
            // 主对象编码是生成的,不需要校验
            if (Objects.equals(fakeObj.getSplitType(), ErpObjSplitTypeEnum.NOT_SPLIT)) {
                continue;
            }
            String erpObjectExtendValue = fakeObj.getErpObjectExtendValue().toString();
            if (StringUtils.isBlank(erpObjectExtendValue)) {
                throw new ErpSyncDataException(I18NStringEnum.s147, tenantId);
            } else if (!erpObjectExtendValue.contains(actualObj.getErpObjectApiName() + ".")) {
                String defaultVal = i18NStringManager.getByEi2(I18NStringEnum.s148, tenantId, fakeObj.getErpObjectName(), erpObjectExtendValue, actualObj.getErpObjectApiName());
                throw new ErpSyncDataException(i18NStringManager.getByEi2(I18NStringEnum.s148.getI18nKey(),
                        tenantId,
                        defaultVal,
                        Lists.newArrayList(fakeObj.getErpObjectName(), erpObjectExtendValue, actualObj.getErpObjectApiName())),
                        null,
                        null);
            }
        }
    }

    private int updateErpObjectRelationship(ErpObjectRelationshipEntity entity) {
        int i;
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(idGenerator.get());
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(System.currentTimeMillis());
            i = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId())).insert(entity);
        } else {
            entity.setUpdateTime(System.currentTimeMillis());
            i = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId())).updateById(entity);
        }
        erpObjectRelationshipDao.invalidCacheErpObj(entity.getTenantId(), entity.getDataCenterId());
        return i;
    }

    private Integer getOnlySplitSeq(String tenantId, String dataCenterId, String erpObjectApiName) {
        ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpRealObjectApiname(erpObjectApiName);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        if (CollectionUtils.isEmpty(erpObjectRelationshipEntities)) {
            return 1;
        }
        List<Integer> splitSeq = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getSplitSeq).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(splitSeq)) {
            return 1;
        }
        for (int i = 1; i < 255; i++) {
            if (!splitSeq.contains(i)) {
                return i;
            }
        }
        return null;
    }

    @Override
    public Result<String> deleteErpObject(String tenantId, int userId, DeleteErpObjectArg deleteArg) {
        ErpObjectEntity queryArg = ErpObjectEntity.builder().id(deleteArg.getId()).build();
        List<ErpObjectEntity> erpObjectEntityList = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryArg);
        ErpObjectEntity erpObjectEntity = (null != erpObjectEntityList) ? erpObjectEntityList.get(0) : null;
        if (erpObjectEntity == null) {
            return Result.newError(PARAM_ILLEGAL);
        }
        String dataCenterId = erpObjectEntity.getDataCenterId();
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listBySourceTenantId(tenantId, null, null, null, null, 0, 1000);
        if (ErpObjectTypeEnum.REAL_OBJECT.name().equals(erpObjectEntity.getErpObjectType().name())) {//真实对象删除
            ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
            query.setTenantId(tenantId);
            query.setDataCenterId(dataCenterId);
            query.setSplitSeq(deleteArg.getSplitSeq());
            query.setErpRealObjectApiname(erpObjectEntity.getErpObjectApiName());
            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            if (!deleteArg.isForce()) {
                for (ErpObjectRelationshipEntity relationshipEntity : erpObjectRelationshipEntities) {//校验真实对象下的所有虚拟对象是否都没有策略
                    for (SyncPloyDetailEntity ployDetailEntity : syncPloyDetailEntities) {
                        if (relationshipEntity.getErpSplitObjectApiname().equals(ployDetailEntity.getSourceObjectApiName()) || relationshipEntity.getErpSplitObjectApiname().equals(ployDetailEntity.getDestObjectApiName())) {
                            return Result.newError(THE_OBJECT_EXIST_PLOY_DETAIL);
                        }
                        for (DetailObjectMappingsData.DetailObjectMappingData detail : ployDetailEntity.getDetailObjectMappings()) {
                            if (relationshipEntity.getErpSplitObjectApiname().equals(detail.getSourceObjectApiName()) || relationshipEntity.getErpSplitObjectApiname().equals(detail.getDestObjectApiName())) {
                                return Result.newError(THE_OBJECT_EXIST_PLOY_DETAIL);
                            }
                        }
                    }
                }
            }
            for (ErpObjectRelationshipEntity entity : erpObjectRelationshipEntities) {
                deleteErpObjectSplitObjApiName(tenantId, dataCenterId, entity.getErpSplitObjectApiname()).safeData();
            }
            query.setSplitSeq(null);
            erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
            if (CollectionUtils.isEmpty(erpObjectRelationshipEntities)) {//如果真实对象已经没有另外批次的拆分了就直接删除
                erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, erpObjectEntity.getId());
                return Result.newSuccess();
            } else {
                return Result.newSuccess();
            }
        } else {//虚拟对象删除
            if (!deleteArg.isForce()) {
                for (SyncPloyDetailEntity entity : syncPloyDetailEntities) {
                    if (erpObjectEntity.getErpObjectApiName().equals(entity.getSourceObjectApiName()) || erpObjectEntity.getErpObjectApiName().equals(entity.getDestObjectApiName())) {
                        return Result.newError(THE_OBJECT_EXIST_PLOY_DETAIL);
                    }
                    for (DetailObjectMappingsData.DetailObjectMappingData detail : entity.getDetailObjectMappings()) {
                        if (erpObjectEntity.getErpObjectApiName().equals(detail.getSourceObjectApiName()) || erpObjectEntity.getErpObjectApiName().equals(detail.getDestObjectApiName())) {
                            return Result.newError(THE_OBJECT_EXIST_PLOY_DETAIL);
                        }
                    }
                }
            }
            return deleteErpObjectSplitObjApiName(tenantId, dataCenterId, erpObjectEntity.getErpObjectApiName());
        }
    }

    /**
     * 以前
     * 1. realApiName查relation，
     * 2. 用relation查obj
     * 3. 然后用obj删除，删除时查relation进行删除
     * 以上，当有relation没有obj时，就永远删不掉了。
     * <p>
     * 这个是用splitObjApiName删除，只要有用relation，就一定删除
     * 但是，对于splitObjApiName只有obj没有relation的，无法删除，这类数据不会有使用。
     *
     * @param tenantId
     * @param dcId
     * @param splitObjApiName
     * @param force
     * @return
     */
    Result<String> deleteErpObjectSplitObjApiName(String tenantId, String dcId, String splitObjApiName) {
        //删除字段
        erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByObjApiName(tenantId, splitObjApiName, dcId);
        //删除对象关系
        ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dcId);
        query.setErpSplitObjectApiname(splitObjApiName);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        if (erpObjectRelationshipEntities.isEmpty()) {
            //不存在
            return Result.newSuccess();
        }
        for (ErpObjectRelationshipEntity objRelation : erpObjectRelationshipEntities) {
            //应该只会有一条。。。
            erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, objRelation.getId());
            String realMainObjApiName = objRelation.getErpRealObjectApiname();
            ErpObjectEntity objEntity = erpObjectDao.getByObjApiName(tenantId, dcId, splitObjApiName);
            if (objEntity!=null){
                int deleteResult = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteByEiAndId(tenantId, objEntity.getId());//删除对象
                //k3渠道删除明细时执行调整明细字段
                if (ErpChannelEnum.ERP_K3CLOUD.equals(objEntity.getChannel()) && realMainObjApiName != null) {
                    erpFieldManager.checkK3detailField(tenantId, dcId, realMainObjApiName);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Map<String, String> queryErpObjectNameByApiName(String oneTenantId, String dataCenterId, int userId, List<String> apiNames) {
        Map<String, String> erpObjectApiName2Name = Maps.newHashMap();
        for (String apiName : apiNames) {

            Result<ErpObjectDescResult> result = queryErpObjectByObjApiName(oneTenantId, dataCenterId, userId, apiName);
            if (result != null && !Objects.isNull(result.getData())) {
                erpObjectApiName2Name.put(result.getData().getErpObjectApiName(), result.getData().getErpObjectName());
            }
        }
        return erpObjectApiName2Name;
    }

    @Override
    public Result<List<ErpObjectRelationshipResult>> queryRealErpObjectByTenantIdAndDcId(String tenantId, int userId, String queryStr, String dataCenterId) {
        //查询所有erp对象
        ErpObjectEntity arg = new ErpObjectEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        if (CollectionUtils.isEmpty(erpObjectEntities)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        //查询所有erp对象关系
        ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        Map<String, ErpObjectEntity> erpObjMap = new HashMap<>();
        for (ErpObjectEntity erpObjectEntity : erpObjectEntities) {
            erpObjMap.put(erpObjectEntity.getErpObjectApiName(), erpObjectEntity);
        }
        //先按批次排序
        erpObjectRelationshipEntities.sort(Comparator.comparingInt(v -> v.getSplitSeq() == null ? 0 : v.getSplitSeq()));
        //真实对象，批次，对象关系,保持顺序
        Map<String, Map<Integer, List<ErpObjectRelationshipEntity>>> table = erpObjectRelationshipEntities.stream().collect(
                Collectors.groupingBy(v -> v.getErpRealObjectApiname(),
                        Collectors.groupingBy(v -> v.getSplitSeq(),
                                Collectors.toList())));
        List<ErpObjectRelationshipResult> erpObjectRelationshipResults = Lists.newArrayList();
        //遍历每个真实对象的每个批次
        table.forEach((realObjApiName, seqMap) -> seqMap.forEach((seq, relations) -> {
            ErpObjectRelationshipResult erpObjectRelationshipResult = buildRelationshipResult(erpObjMap, realObjApiName, seq, relations);
            if (erpObjectRelationshipResult == null) return;
            erpObjectRelationshipResults.add(erpObjectRelationshipResult);
        }));
        //查询
        if (StringUtils.isNotBlank(queryStr)) {
            List<ErpObjectRelationshipResult> objectRelationshipResults;
            objectRelationshipResults = erpObjectRelationshipResults.stream().filter(obj -> obj.getActualErpObject().getErpObjectName().contains(queryStr)).collect(Collectors.toList());
            return Result.newSuccess(objectRelationshipResults);
        }
        return Result.newSuccess(erpObjectRelationshipResults);
    }

    private static ErpObjectRelationshipResult buildRelationshipResult(Map<String, ErpObjectEntity> erpObjMap, String realObjApiName, Integer seq, List<ErpObjectRelationshipEntity> relations) {
        //构建真实对象描述
        ErpObjectEntity realObj = erpObjMap.get(realObjApiName);
        if (realObj == null) {
            return null;
        }
        //和单条接口使用相同的构建方法
        ErpObjectDescResult realErpObjDesc = buildObjectDesc(realObj, seq, null);
        //构建中间对象描述
        List<ErpObjectDescResult> fakeErpDescResults = new ArrayList<>();
        for (ErpObjectRelationshipEntity relation : relations) {
            ErpObjectEntity fakeObj = erpObjMap.get(relation.getErpSplitObjectApiname());
            if (fakeObj == null) {
                continue;
            }
            fakeErpDescResults.add(buildObjectDesc(fakeObj, seq, relation.getSplitType()));
        }
        //构建结果
        ErpObjectRelationshipResult erpObjectRelationshipResult = new ErpObjectRelationshipResult();
        erpObjectRelationshipResult.setActualErpObject(realErpObjDesc);
        erpObjectRelationshipResult.setSplitSeq(seq);
        erpObjectRelationshipResult.allSetFakeErpObject(fakeErpDescResults);
        return erpObjectRelationshipResult;
    }

    @Override
    public Result<ErpObjectRelationshipResult> getErpObjectRelationshipByRealObjApiName(String tenantId,
                                                                                        String realApiName,
                                                                                        Integer splitSeq,
                                                                                        String dataCenterId,
                                                                                        String lang) {
        //先查出所有的中间对象
        ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpRealObjectApiname(realApiName);
        query.setSplitSeq(splitSeq);
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        //批量查询对象
        List<String> erpObjApis = erpObjectRelationshipEntities.stream().map(v -> v.getErpSplitObjectApiname()).collect(Collectors.toList());
        erpObjApis.add(realApiName);
        List<ErpObjectEntity> erpObjectList = erpObjectDao.queryByApiNames2(tenantId, dataCenterId, erpObjApis);
        Map<String, ErpObjectEntity> erpObjMap = erpObjectList.stream().collect(Collectors.toMap(v -> v.getErpObjectApiName(), v -> v, (v, u) -> v));
        ErpObjectRelationshipResult erpObjectRelationshipResult = buildRelationshipResult(erpObjMap, realApiName, splitSeq, erpObjectRelationshipEntities);
        AssertUtil.notNull(erpObjectRelationshipResult, I18NStringEnum.s235, tenantId);

        // 海外对象需要多返回信息
        final ErpObjectDescResult erpObjectDescResult = ListUtils.emptyIfNull(erpObjectRelationshipResult.getFakeErpObject()).stream()
                .filter(v -> Objects.equals(v.getChannel(), ErpChannelEnum.ERP_FACEBOOK) || Objects.equals(v.getChannel(), ErpChannelEnum.ERP_LINKEDIN) /*|| Objects.equals(v.getChannel(), ErpChannelEnum.ERP_GOOGLE)*/)
                .findFirst()
                .orElse(null);
        if (erpObjectDescResult != null) {
            final String splitObjApiName = erpObjectDescResult.getErpObjectApiName();
            final ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, splitObjApiName);
            if (Objects.nonNull(idField)) {
                erpObjectRelationshipResult.setExtendValue(JSON.parseObject(idField.getFieldExtendValue()));
            }
        }

        return Result.newSuccess(erpObjectRelationshipResult);
    }

    private static ErpObjectDescResult buildObjectDesc(ErpObjectEntity erpObjectEntity, Integer splitSeq, ErpObjSplitTypeEnum splitType) {
        ErpObjectDescResult erpObjectDescResult = new ErpObjectDescResult();
        BeanUtils.copyProperties(erpObjectEntity, erpObjectDescResult);
        String erpObjectExtendValue = erpObjectEntity.getErpObjectExtendValue();
        if (StringUtils.isNotEmpty(erpObjectExtendValue)) {
            Object fieldExtendValue = GsonUtil.fromJson(erpObjectExtendValue, Object.class);
            erpObjectDescResult.setErpObjectExtendValue(fieldExtendValue);
        }
        erpObjectDescResult.setSplitType(splitType);
        erpObjectDescResult.setSplitSeq(splitSeq);
        return erpObjectDescResult;
    }

    String getFakeObjectApiName(String tenantId, String dataCenterId, ErpObjectDescResult actualObj, ErpObjectDescResult fakeObj) {
        if (actualObj.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD)) {
            //k3渠道拼接序号
            String objSuffix = "_";
            int dataCenterSeq = dataCenterManager.getDataCenterSeq(tenantId, dataCenterId);
            if (dataCenterSeq > 0) {
                objSuffix += dataCenterSeq;
            } else {
                objSuffix = "";
            }
            if (fakeObj.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT)) {
                //主对象拼接.BillHead后，拼接后缀
                return actualObj.getErpObjectApiName() + ".BillHead" + objSuffix;
            }
            //明细对象使用真实编码拼接后缀
            return fakeObj.parseDetailApiCode() + objSuffix;
        }
        //其他渠道拼接时间
        return actualObj.getErpObjectApiName() + "_" + NumUtils.longTo32Str(System.currentTimeMillis());
    }

    @Override
    public List<String> getErpFakeObjApiNames(String tenantId, String dataCenterId) {
        ErpObjectEntity arg = new ErpObjectEntity();
        arg.setTenantId(tenantId);
        arg.setDataCenterId(dataCenterId);
        arg.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(arg);
        if (CollectionUtils.isEmpty(erpObjectEntities)) {
            return Lists.newArrayList();
        }
        List<String> erpFakeObjApiNames = erpObjectEntities.stream().map(ErpObjectEntity::getErpObjectApiName).collect(Collectors.toList());
        return erpFakeObjApiNames;
    }

    @Override
    public Result<ErpObjectSimpleInfo> getErpObjSimpleInfo(String tenantId, int userId, String objApiName, String lang) {
        List<ErpObjectEntity> erpObjectEntities = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryByApiNames(tenantId, Collections.singletonList(objApiName));
        AssertUtil.notEmpty(erpObjectEntities, I18NStringEnum.s231, tenantId);
        ErpObjectEntity erpObjectEntity = erpObjectEntities.get(0);
        ErpObjectFieldEntity idField = erpFieldManager.findIdField(tenantId, objApiName);
        AssertUtil.notEmpty(idField, I18NStringEnum.s232, tenantId);
        ErpObjectSimpleInfo simpleInfo = ErpObjectSimpleInfo.builder().objApiName(erpObjectEntity.getErpObjectApiName())
                .objName(erpObjectEntity.getErpObjectName())
                .idFieldApiName(idField.getFieldApiName())
                .idFieldName(idField.getFieldLabel()).build();
        return Result.newSuccess(simpleInfo);
    }

    @Override
    public Result<Map<String, String>> importObjectApiNameData(String tenantId,
                                                               int userId,
                                                               String dataCenterId,
                                                               ErpChannelEnum channel,
                                                               String lang,
                                                               List<ErpIntegrationStreamExcelVo> integrationStreamExcelVos) {
        //判断第三方对象是否已经预设了相关对象
        //不支持多批次，第三方主对象重复，不新增
        //主从对象不更新
        ErpObjectDescResult actualErpObject = null;
        List<ErpObjectDescResult> distinctResults = new LinkedList<>();
        List<String> isHasAddErpObject = new LinkedList<>();
        List<String> isHasAddCrmObject = new LinkedList<>();
        for (ErpIntegrationStreamExcelVo erpIntegrationStreamExcelVo : integrationStreamExcelVos) {
            if (ObjectUtils.isEmpty(actualErpObject)) {
                //判断crm对象是否存在
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResultResult = objectDescribeService.getDescribe(HeaderObj.newInstance(Integer.valueOf(tenantId), SuperUserConstants.USER_ID), erpIntegrationStreamExcelVo.getCrmObjectApiName());
                if (!describeResultResult.isSuccess()) {
                    return Result.newError(ResultCodeEnum.CRM_OBJECT_INVALID.getErrCode(),
                            String.format(i18NStringManager.get(I18NStringEnum.s4037,
                                            lang,
                                            tenantId),
                                    erpIntegrationStreamExcelVo.getCrmObjectApiName()));
                }

                actualErpObject = new ErpObjectDescResult();
                actualErpObject.setErpObjectApiName(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName());
                actualErpObject.setErpObjectName(erpIntegrationStreamExcelVo.getThirdPartyObjectLabel());
                actualErpObject.setChannel(channel);
                actualErpObject.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
                actualErpObject.setErpObjectExtendValue(ErpObjSplitTypeEnum.FIELD_SPLIT);
                isHasAddErpObject.add(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName());
                isHasAddCrmObject.add(erpIntegrationStreamExcelVo.getCrmObjectApiName());
                continue;
            }

            if (isHasAddErpObject.contains(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName())) {
                if (!isHasAddCrmObject.contains(erpIntegrationStreamExcelVo.getCrmObjectApiName())) {
                    //映射有问题
                    return Result.newError(ResultCodeEnum.CHECK_OBJECT_MAPPING_ERROR.getErrCode(),
                            String.format(i18NStringManager.get(I18NStringEnum.s4036,
                                            lang,
                                            tenantId),
                                    erpIntegrationStreamExcelVo.getCrmObjectApiName(),
                                    erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
                }
                continue;
            }
            //校验
            if (isHasAddCrmObject.contains(erpIntegrationStreamExcelVo.getCrmObjectApiName())) {
                //映射有问题
                return Result.newError(ResultCodeEnum.CHECK_OBJECT_MAPPING_ERROR.getErrCode(),
                        String.format(i18NStringManager.get(I18NStringEnum.s4036,
                                        lang,
                                        tenantId),
                                erpIntegrationStreamExcelVo.getCrmObjectApiName(),
                                erpIntegrationStreamExcelVo.getThirdPartyObjectApiName()));
            }

            //判断crm对象是否存在
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResultResult = objectDescribeService.getDescribe(HeaderObj.newInstance(Integer.valueOf(tenantId), SuperUserConstants.USER_ID), erpIntegrationStreamExcelVo.getCrmObjectApiName());
            if (!describeResultResult.isSuccess()) {
                return Result.newError(ResultCodeEnum.CRM_OBJECT_INVALID.getErrCode(),
                        String.format(i18NStringManager.get(I18NStringEnum.s4037,
                                        lang,
                                        tenantId),
                                erpIntegrationStreamExcelVo.getCrmObjectApiName()));
            }

            ErpObjectDescResult result = new ErpObjectDescResult();
            result.setErpObjectName(erpIntegrationStreamExcelVo.getThirdPartyObjectLabel());
            result.setErpObjectExtendValue(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName());
            result.setChannel(channel);
            result.setSplitType(ErpObjSplitTypeEnum.DETAIL2DETAIL_SPLIT);
            distinctResults.add(result);
            isHasAddErpObject.add(erpIntegrationStreamExcelVo.getThirdPartyObjectApiName());
            isHasAddCrmObject.add(erpIntegrationStreamExcelVo.getCrmObjectApiName());
        }

        //判断主对象是否已经存在
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpObjectApiName(actualErpObject.getErpObjectApiName());
        List<ErpObjectEntity> oldEntry = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        final String id = idGenerator.get();
        if (CollectionUtils.isEmpty(oldEntry)) {
            //新建
            ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
            BeanUtils.copyProperties(actualErpObject, erpObjectEntity);
            erpObjectEntity.setTenantId(tenantId);
            erpObjectEntity.setDataCenterId(dataCenterId);
            erpObjectEntity.setErpObjectExtendValue(GsonUtil.toJson(erpObjectEntity.getErpObjectExtendValue()));
            erpObjectEntity.setChannel(channel);
//            final String id = idGenerator.get();
            erpObjectEntity.setId(id);
            erpObjectEntity.setCreateTime(System.currentTimeMillis());
            erpObjectEntity.setUpdateTime(System.currentTimeMillis());
            int insertResult = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpObjectEntity);
            log.info("ErpObjectServiceImpl.importObjectApiNameData,tenantId={},objectApiName={},insertResult={}", tenantId, actualErpObject.getErpObjectApiName(), insertResult);
            if (insertResult != 1) {
                //失败处理
                //主对象处理失败，就不往下走了
                return Result.newError(ResultCodeEnum.THE_DATABASE_ERROR.getErrCode(),
                        i18NStringManager.get(ResultCodeEnum.THE_DATABASE_ERROR.getI18nKey(), lang, tenantId, ResultCodeEnum.THE_DATABASE_ERROR.getErrMsg()));
            }
        } else {
            //判断主对象是否使用的是真实对象
            ErpObjectRelationshipEntity queryAllObject = new ErpObjectRelationshipEntity();
            queryAllObject.setTenantId(tenantId);
            queryAllObject.setDataCenterId(dataCenterId);
            queryAllObject.setErpRealObjectApiname(actualErpObject.getErpObjectApiName());
            List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryAllObject);
            List<String> erpAllObjects = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());
            log.info("ErpObjectServiceImpl.importObjectApiNameData,tenantId={},erpObjectRelationshipEntities={}", tenantId, erpObjectRelationshipEntities);
            if (CollectionUtils.isEmpty(erpAllObjects)) {
                return Result.newError(ResultCodeEnum.CHECK_REAL_OBJECT_API_NAME_ERROR.getErrCode(),
                        String.format(i18NStringManager.get(I18NStringEnum.s4035,
                                        lang,
                                        tenantId),
                                actualErpObject.getErpObjectApiName()));
            } else {
                //判断批次，不支持多批次
                boolean allSplitSeqsAreOne = erpObjectRelationshipEntities.stream()
                        .allMatch(entity -> entity.getSplitSeq().equals(1));
                if (!allSplitSeqsAreOne) {
                    return Result.newError(ResultCodeEnum.NOT_SUPPORT_SPLIT_SEQ.getErrCode(),
                            String.format(i18NStringManager.get(I18NStringEnum.s4038,
                                            lang,
                                            tenantId),
                                    actualErpObject.getErpObjectApiName()));
                }
            }
        }

        //处理明细对象
        if (CollectionUtils.isEmpty(oldEntry)) {//默认生成一条虚拟对象
            ErpObjectDescResult fakeErpObj = new ErpObjectDescResult();
            fakeErpObj.setErpObjectName(actualErpObject.getErpObjectName());
            fakeErpObj.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
            distinctResults.add(fakeErpObj);
        }

        //证明已经创建过了，而且没有新的从对象
        if(CollectionUtils.isEmpty(distinctResults)) {
            return Result.newSuccess();
        }


        //一些安全检验
        //批次默认为1，需明确告诉实施或者客户不支持多批次
        Integer splitSeq = 1;

        Map<String, String> checkImportFakeErpObjMap = checkImportFakeErpObj(tenantId, actualErpObject, distinctResults, lang);
        //意在查询是否所有对象都失败了，主对象不会失败，所以只能是从对象
        if (!checkImportFakeErpObjMap.isEmpty()) {
            if (distinctResults.size() == checkImportFakeErpObjMap.size()) {
                return Result.newSuccess(checkImportFakeErpObjMap);
            }
        }

        //批次默认为1，查找主对象下的全部从对象
        ErpObjectRelationshipEntity queryAllObject = new ErpObjectRelationshipEntity();
        queryAllObject.setTenantId(tenantId);
        queryAllObject.setDataCenterId(dataCenterId);
        queryAllObject.setSplitSeq(splitSeq);
        queryAllObject.setErpRealObjectApiname(actualErpObject.getErpObjectApiName());
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryAllObject);

        List<String> erpAllObjects = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).collect(Collectors.toList());

        //此时还需要到erp_object表查询真实对象的数据
        Set<String> erpAllObjectSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(erpAllObjects)) {
            List<ErpObjectEntity> erpObjectList = erpObjectDao.queryByApiNames2(tenantId, dataCenterId, erpAllObjects);
            erpAllObjectSet = erpObjectList.stream().map(ErpObjectEntity::getErpObjectExtendValue).collect(Collectors.toSet());
        }

        //校验从对象
        for (ErpObjectDescResult fakeErpObj : distinctResults) {
            //主对象不校验
            if (!fakeErpObj.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT) && !erpAllObjectSet.contains(fakeErpObj.getErpObjectExtendValue())) {
                //对象关系不存在，但是对象表里又有
                //看是否是真实对象
                ErpObjectEntity queryEntity = new ErpObjectEntity();
                queryEntity.setTenantId(tenantId);
                queryEntity.setDataCenterId(dataCenterId);
                queryEntity.setErpObjectApiName(fakeErpObj.getErpObjectExtendValue().toString());
                List<ErpObjectEntity> oldObjectEntry = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(queryEntity);
                if (CollectionUtils.isNotEmpty(oldObjectEntry)) {
                    //如果主对象的中间对象还没有新增，就删除主对象
                    if (CollectionUtils.isEmpty(oldEntry)) {
                        int deleteCount = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).deleteById(id);
                        log.info("ErpObjectServiceImpl.importObjectApiNameData,tenantId={},objectApiName={},deleteCount={}", tenantId, actualErpObject.getErpObjectApiName(), deleteCount);
                    }
                    return Result.newError(ResultCodeEnum.CHECK_REAL_OBJECT_API_NAME_ERROR.getErrCode(),
                            String.format(i18NStringManager.get(I18NStringEnum.s4035,
                                            lang,
                                            tenantId),
                                    fakeErpObj.getErpObjectExtendValue().toString()));
                }
            }
        }

        Map<String, String> objectErrorMap = new HashMap<>();
        for (ErpObjectDescResult fakeErpObj : distinctResults) {
            //判断是否已经创建主从对象的中间对象
            if (fakeErpObj.getSplitType().equals(ErpObjSplitTypeEnum.NOT_SPLIT) || !erpAllObjectSet.contains(fakeErpObj.getErpObjectExtendValue())) {
                //设置中间对象
                fakeErpObj.setErpObjectApiName(getFakeObjectApiName(tenantId, dataCenterId, actualErpObject, fakeErpObj));
                fakeErpObj.setSplitSeq(splitSeq);
                //新增对象和对象关系
                Map<String, String> importRelationAndObjectMap = importRelationAndObject(tenantId, dataCenterId, lang, userId, channel, actualErpObject, fakeErpObj);
                if (!importRelationAndObjectMap.isEmpty()) {
                    objectErrorMap.putAll(importRelationAndObjectMap);
                }
                //孙对象不处理
            }
        }
        return Result.newSuccess(objectErrorMap);
    }

    private Map<String, String> checkImportFakeErpObj(String tenantId, final ErpObjectDescResult actualObj, final List<ErpObjectDescResult> fakeObjs, String lang) {
        Map<String, String> objectErrorMap = new HashMap<>();
        //暂时只需要校验K3C
        if (!actualObj.getChannel().equals(ErpChannelEnum.ERP_K3CLOUD)) {
            return objectErrorMap;
        }

        for (ErpObjectDescResult fakeObj : fakeObjs) {
            // 主对象编码是生成的,不需要校验
            if (Objects.equals(fakeObj.getSplitType(), ErpObjSplitTypeEnum.NOT_SPLIT)) {
                continue;
            }
            String erpObjectExtendValue = fakeObj.getErpObjectExtendValue().toString();

            if (!erpObjectExtendValue.contains(actualObj.getErpObjectApiName() + ".")) {
                String defaultVal = i18NStringManager.getByEi2(I18NStringEnum.s148, tenantId, fakeObj.getErpObjectName(), erpObjectExtendValue, actualObj.getErpObjectApiName());
                objectErrorMap.put(erpObjectExtendValue, i18NStringManager.get(I18NStringEnum.s148.getI18nKey(), lang, tenantId, defaultVal));
            }
        }
        return objectErrorMap;
    }

    private Map<String, String> importRelationAndObject(String tenantId, String dataCenterId, String lang, int userId, ErpChannelEnum channel, ErpObjectDescResult actualErpObj, ErpObjectDescResult fakeErpObj) {
        log.info("ErpObjectServiceImpl.importRelationAndObject,tenantId={},fakeErpObj={}", tenantId, fakeErpObj);
        Map<String, String> objectErrorMap = new HashMap<>();
        //插入对象关系表
        ErpObjectRelationshipEntity entity = new ErpObjectRelationshipEntity();
        entity.setTenantId(tenantId);
        entity.setDataCenterId(dataCenterId);
        entity.setErpSplitObjectApiname(fakeErpObj.getErpObjectApiName());
        entity.setSplitType(fakeErpObj.getSplitType());
        entity.setChannel(channel);
        entity.setErpRealObjectApiname(actualErpObj.getErpObjectApiName());
        entity.setSplitSeq(fakeErpObj.getSplitSeq());
        entity.setId(idGenerator.get());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        int insert = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId())).insert(entity);
        if (insert != 1) {
            if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(fakeErpObj.getSplitType())) {
                objectErrorMap.put(actualErpObj.getErpObjectName(), i18NStringManager.get(THE_DATABASE_ERROR.getI18nKey(), lang, tenantId, THE_DATABASE_ERROR.getErrMsg()));
            } else {
                objectErrorMap.put(fakeErpObj.getErpObjectExtendValue().toString(), i18NStringManager.get(THE_DATABASE_ERROR.getI18nKey(), lang, tenantId, THE_DATABASE_ERROR.getErrMsg()));
            }
            return objectErrorMap;
        }

        erpObjectRelationshipDao.invalidCacheErpObj(entity.getTenantId(), entity.getDataCenterId());


        if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(fakeErpObj.getSplitType())) {
            //主对象使用真实对象的对象名称
            fakeErpObj.setErpObjectName(actualErpObj.getErpObjectName());
        }
        fakeErpObj.setErpObjectType(ErpObjectTypeEnum.SPLIT_OBJECT);

        ErpObjectEntity erpObjectEntity = new ErpObjectEntity();
        BeanUtils.copyProperties(fakeErpObj, erpObjectEntity);
        erpObjectEntity.setTenantId(tenantId);
        erpObjectEntity.setDataCenterId(dataCenterId);
        erpObjectEntity.setErpObjectExtendValue(GsonUtil.toJson(fakeErpObj.getErpObjectExtendValue()));


        erpObjectEntity.setChannel(channel);
        ErpObjectEntity query = new ErpObjectEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpObjectApiName(fakeErpObj.getErpObjectApiName());
        List<ErpObjectEntity> oldEntry = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        if (CollectionUtils.isNotEmpty(oldEntry)) {
            //不更新
            return objectErrorMap;
        }
        final String id = idGenerator.get();
        erpObjectEntity.setId(id);
        erpObjectEntity.setCreateTime(System.currentTimeMillis());
        erpObjectEntity.setUpdateTime(System.currentTimeMillis());
        int insertResult = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(erpObjectEntity);
        if (insertResult != 1) {
            if (ErpObjSplitTypeEnum.NOT_SPLIT.equals(fakeErpObj.getSplitType())) {
                objectErrorMap.put(actualErpObj.getErpObjectName(), i18NStringManager.get(THE_DATABASE_ERROR.getI18nKey(), lang, tenantId, THE_DATABASE_ERROR.getErrMsg()));
            } else {
                objectErrorMap.put(fakeErpObj.getErpObjectExtendValue().toString(), i18NStringManager.get(THE_DATABASE_ERROR.getI18nKey(), lang, tenantId, THE_DATABASE_ERROR.getErrMsg()));
            }
            return objectErrorMap;
        }
        return objectErrorMap;
    }
}
