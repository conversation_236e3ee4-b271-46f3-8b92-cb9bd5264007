{"type": "page", "title": "ERP字段扩展", "remark": null, "name": "fieldExtendList", "toolbar": [], "body": [{"type": "crud", "name": "fieldExtends", "api": {"url": "../listFieldExtends", "sendOn": "objApiName != null"}, "loadDataOnce": true, "defaultParams": {"perPage": 100}, "headerToolbar": ["export-csv", "reload", "bulkActions", {"type": "tpl", "tpl": "修改按钮也可以新增和删除"}], "bulkActions": [], "filter": {"mode": "horizontal", "body": [{"size": "sm", "label": "tenantId", "type": "input-text", "name": "tenantId", "onEvent": {"blur": {"actions": [{"actionType": "reload", "componentId": "dcIdSelect"}]}}}, {"id": "dcIdSelect", "name": "dcId", "label": "数据中心", "type": "select", "size": "lg", "labelField": "dataCenterName", "valueField": "id", "selectFirst": true, "clearable": true, "source": {"method": "get", "url": "../listDcInfos?tenantId=${tenantId}", "sendOn": "tenantId != null", "autoRefresh": false}, "searchable": true}, {"label": "对象", "type": "input-text", "name": "objApiName"}, {"label": "fieldType", "type": "input-text", "name": "fieldType"}, {"label": "searchStr", "type": "input-text", "name": "searchStr"}]}, "columns": [{"name": "id", "label": "id"}, {"name": "fieldApiName", "label": "fieldApiName"}, {"name": "fieldDefineType", "label": "fieldDefineType"}, {"name": "viewCode", "label": "viewCode"}, {"name": "viewExtend", "label": "viewExtend"}, {"name": "saveCode", "label": "saveCode"}, {"name": "saveExtend", "label": "saveExtend"}, {"name": "queryCode", "label": "queryCode"}, {"name": "erpFieldType", "label": "erpFieldType"}, {"name": "used<PERSON>uery", "label": "used<PERSON>uery", "type": "status"}, {"name": "priority", "label": "priority", "type": "input-number", "static": true}, {"name": "createTime", "label": "创建时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"name": "updateTime", "label": "修改时间", "type": "date", "format": "YYYY-MM-DD HH:mm:ss.SSS", "valueFormat": "unix"}, {"type": "operation", "label": "操作", "fixed": "right", "buttons": [{"label": "修改", "type": "button", "actionType": "drawer", "drawer": {"title": "修改", "width": "50%", "body": {"type": "form", "horizontal": {"leftFixed": "md"}, "api": "post:../upsertFieldExtend", "body": [{"type": "html", "html": "<p>id置空就是新增，tenantId拼接后缀相当于删除数据</p>"}, {"name": "id", "label": "id", "type": "input-text"}, {"name": "tenantId", "label": "tenantId", "type": "input-text"}, {"name": "dataCenterId", "label": "dataCenterId", "type": "static"}, {"name": "objApiName", "label": "objApiName", "type": "static"}, {"name": "fieldApiName", "label": "fieldApiName", "type": "input-text"}, {"name": "fieldDefineType", "label": "fieldDefineType", "type": "input-text", "value": "text"}, {"name": "viewCode", "label": "viewCode", "type": "input-text"}, {"name": "viewExtend", "label": "viewExtend", "type": "input-text"}, {"name": "saveCode", "label": "saveCode", "type": "input-text"}, {"name": "saveExtend", "label": "saveExtend", "type": "input-text"}, {"name": "queryCode", "label": "queryCode", "type": "input-text"}, {"name": "erpFieldType", "label": "erpFieldType", "type": "input-text"}, {"name": "used<PERSON>uery", "label": "used<PERSON>uery", "type": "switch", "value": "false"}, {"name": "priority", "label": "priority", "type": "input-number", "value": 65536}]}}}]}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "combineNum": 0}]}