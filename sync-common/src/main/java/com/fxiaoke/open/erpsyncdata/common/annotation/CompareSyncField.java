package com.fxiaoke.open.erpsyncdata.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2023/4/23 17:32
 * 比对下不同环境的字段同步结果
 * @Version 1.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface CompareSyncField {
    String syncType() default "" ;
}
