package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 20:03 2025/1/7
 * @Desc:
 */

@Component
@Slf4j
public class NeedSingleSyncManager {
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private ErpObjectDao erpObjectDao;

    public Boolean getNeedSingleSyncBySplitApiName(Integer tenantType, String tenantId, String sourceObjectApiName) {
        try{
            if (TenantType.CRM.equals(tenantType)) {
                List<String> result=tenantConfigurationManager.getCrmNeedSingleSync(tenantId);
                return result.contains(sourceObjectApiName);
            }
            ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, sourceObjectApiName);
            if (relation == null) {
                return false;
            }
            Map<String, List<String>> erpNeedSingleSync = tenantConfigurationManager.getErpNeedSingleSync(tenantId, relation.getDataCenterId());
            if (erpNeedSingleSync.containsKey(relation.getErpRealObjectApiname())) {
                ErpObjectEntity detailObj = erpObjectDao.getByObjApiName(tenantId, relation.getDataCenterId(), sourceObjectApiName);
                if (StringUtils.isNotBlank(detailObj.getErpObjectExtendValue())) {
                    return erpNeedSingleSync.get(relation.getErpRealObjectApiname()).contains(detailObj.getErpObjectExtendValue());
                }
            }
        }catch (Exception e){
            log.warn("getNeedSingleSyncBySplitApiName error={}", e);
        }
        return false;
    }

    public Boolean getErpNeedSingleSync(String tenantId, String dataCenterId, ErpObjectDescResult actualErpObject, ErpObjectDescResult erpObjectDescResult) {
        if (actualErpObject == null || StringUtils.isBlank(actualErpObject.getErpObjectApiName())
                || erpObjectDescResult == null || erpObjectDescResult.getErpObjectExtendValue() == null) {
            return false;
        }
        Map<String, List<String>> erpNeedSingleSync = tenantConfigurationManager.getErpNeedSingleSync(tenantId, dataCenterId);
        if (erpNeedSingleSync.containsKey(actualErpObject.getErpObjectApiName())
                && erpNeedSingleSync.get(actualErpObject.getErpObjectApiName()).contains(erpObjectDescResult.getErpObjectExtendValue().toString())) {
            return true;
        }
        return false;
    }
}
