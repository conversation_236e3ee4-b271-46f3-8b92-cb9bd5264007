package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class SyncPloyDetailListByPloyIdResult implements Serializable {
    @ApiModelProperty("同步策略明细列表")
    private List<SyncPloyDetailResult> syncPloyDetails;
    @ApiModelProperty("总条数")
    private int totalCount;
    @ApiModelProperty("页长")
    private int pageSize;
    @ApiModelProperty("页码")
    private int pageNumber;
}
