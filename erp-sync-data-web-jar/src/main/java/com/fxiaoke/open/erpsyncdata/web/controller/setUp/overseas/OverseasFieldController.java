package com.fxiaoke.open.erpsyncdata.web.controller.setUp.overseas;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erp.connertor.sdk.model.dto.FormMetaData;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SelectExtend;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/11 14:42:59
 */
@Slf4j
@Api(tags = "海外ERP相关接口")
public abstract class OverseasFieldController extends BaseController {

    @Autowired
    private ErpFieldManager erpFieldManager;

    public abstract ErpChannelEnum getChannel();

    protected abstract ErpFieldTypeEnum convert2ErpFieldTypeEnum(final String type);

    public void incrementalInsertErpObjectField(String tenantId, String dataCenterId, String objectApiName, List<FormMetaData> metaDataList, String idFieldApiName, String idFieldExtendValue) {
        final List<ErpObjectFieldEntity> formMeatData = convert2ErpObjectFieldEntity(tenantId, dataCenterId, objectApiName, metaDataList);
        final ErpObjectFieldEntity idField = idField(tenantId, dataCenterId, objectApiName, idFieldApiName, idFieldExtendValue);
        formMeatData.add(idField);

        erpFieldManager.incrementalInsertErpObjectField(tenantId, dataCenterId, objectApiName, formMeatData);
    }

    protected ErpObjectFieldEntity idField(final String tenantId, final String dataCenterId, final String objectName, final String idFieldApiName, final String idFieldExtendValue) {
        final ErpObjectFieldEntity objectField = new ErpObjectFieldEntity();
        objectField.setId(IdGenerator.get());
        objectField.setTenantId(tenantId);
        objectField.setDataCenterId(dataCenterId);
        objectField.setChannel(getChannel());
        objectField.setErpObjectApiName(objectName);
        objectField.setRequired(false);
        objectField.setCreateTime(System.currentTimeMillis());
        objectField.setUpdateTime(System.currentTimeMillis());
        objectField.setFieldApiName(idFieldApiName);
        objectField.setFieldLabel("id");
        objectField.setFieldDefineType(ErpFieldTypeEnum.id);
        objectField.setFieldExtendValue(idFieldExtendValue);
        return objectField;
    }

    protected List<ErpObjectFieldEntity> convert2ErpObjectFieldEntity(final String tenantId, final String dataCenterId, final String objectName, final List<FormMetaData> metaDataList) {
        return metaDataList.stream()
                .map(metaData -> {
                    final ErpObjectFieldEntity objectField = new ErpObjectFieldEntity();
                    objectField.setId(IdGenerator.get());
                    objectField.setTenantId(tenantId);
                    objectField.setDataCenterId(dataCenterId);
                    objectField.setChannel(getChannel());
                    objectField.setErpObjectApiName(objectName);
                    objectField.setRequired(false);
                    objectField.setCreateTime(System.currentTimeMillis());
                    objectField.setUpdateTime(System.currentTimeMillis());
                    objectField.setFieldApiName(metaData.getKey());
                    objectField.setFieldLabel(metaData.getLabel());
                    objectField.setFieldDefineType(convert2ErpFieldTypeEnum(metaData.getType()));
                    if (Objects.equals(objectField.getFieldDefineType(), ErpFieldTypeEnum.select_one)) {
                        final List<SelectExtend> selectExtends = metaData.getOptions().stream()
                                .map(options -> SelectExtend.of(options.getValue(), options.getKey()))
                                .collect(Collectors.toList());
                        objectField.setFieldExtendValue(JSON.toJSONString(selectExtends));
                    }
                    return objectField;
                }).collect(Collectors.toList());
    }

    protected static <T> List<T> convertList(final List<?> list, final Class<T> tClass) {
        return JSON.parseArray(JSON.toJSONString(list), tClass);
    }
}
