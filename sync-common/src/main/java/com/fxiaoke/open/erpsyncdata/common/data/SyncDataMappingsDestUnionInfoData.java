package com.fxiaoke.open.erpsyncdata.common.data;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class SyncDataMappingsDestUnionInfoData implements Serializable {
    private String sourceTenantId;
    private String sourceObjectApiName;
    private String destObjectApiName;
    private String destTenantId;
    private String destDataId;

    private SyncDataMappingsDestUnionInfoData() {
    }

    public SyncDataMappingsDestUnionInfoData(String sourceTenantId, String sourceObjectApiName, String destObjectApiName, String destTenantId, String destDataId) {
        this.sourceTenantId = sourceTenantId;
        this.sourceObjectApiName = sourceObjectApiName;
        this.destObjectApiName = destObjectApiName;
        this.destTenantId = destTenantId;
        this.destDataId = destDataId;
    }
}
