package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.specialBusinessImpl;

import cn.hutool.core.comparator.VersionComparator;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3Constant;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.CommonBusinessManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3CPQOrderManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.K3DataConverter;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.SpecialBusiness;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk.K3CloudApiClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.*;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.XOrderUtils;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldExtendEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component(K3CloudForm.SAL_XORDER)
public class XOrderSpecialBusinessImpl implements SpecialBusiness {
    @Autowired
    private K3CPQOrderManager k3XOrderManager;

    @Autowired
    private ConfigCenterConfig configCenterConfig;

    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private CommonBusinessManager commonBusinessManager;

    @Override
    public void beforeRunUpdate(SaveArg saveArg, StandardData standardData, K3DataConverter k3DataConverter, K3CloudApiClient apiClient, IdSaveExtend saveExtend) {
        k3XOrderManager.handleCpqSaleOrderProduct(standardData,apiClient,saveArg);
        log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,saveAgr={}",saveArg);
        String salesOrderId = saveArg.getModel().get("FId","FID").toString();
        saveArg.getModel().put("FPKIDX_H",salesOrderId);
        saveArg.getModel().delete("FId","FID");

        String[] items = standardData.getMasterFieldVal().getId().split("#");
        String billNo = null;
        if(items.length==2) {
            billNo = items[1];
        } else {
            //对订单id不带 # 的特殊处理逻辑
            QueryArg queryArg = new QueryArg();
            queryArg.setFormId(K3CloudForm.SAL_SaleOrder);
            queryArg.setFieldKeys("FID,FBillNo");
            queryArg.appendEqualFilter("FID",items[0]);
            commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
            Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
            if(!result.isSuccess()) {
                throw new ErpSyncDataException(result.getErrCode(),result.getErrMsg(),result.getI18nKey(),apiClient.getTenantId());
            }
            billNo = result.getData().get(0).getString("FBillNo");
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,billNo={}",billNo);
            standardData.getMasterFieldVal().putId(items[0]+"#"+billNo);
            standardData.getMasterFieldVal().put("ComId",standardData.getMasterFieldVal().getId());
        }
        if(billNo.length()<23){
            billNo+="_"+DateFormatUtils.format(new Date(),"HHmmss");
        }else{
            billNo=billNo.substring(billNo.length()-23)+DateFormatUtils.format(new Date(),"HHmmss");
        }

        saveArg.getModel().put("FBillNo",billNo);

        List<K3Model> entryList = saveArg.getModel().getDetails("FSaleOrderEntry");
        for(K3Model model : entryList) {
            String entryId = String.valueOf(model.get("FEntryID"));
            model.remove("FEntryID");
            try {
                Long.parseLong(entryId);
                //如果配置FChangeType字段并且这个字段值为T则该明细为终止状态，否则为修改
                if (!(model.containsKey("FChangeType")&&"T".equals(model.get("FChangeType")))){
                    model.put("FChangeType","B");
                }
                model.put("FPKIDX",entryId);
            } catch (Exception e) {
                //新加的订单明细，编辑时entryId=产品id，产品id无法转换成数字
                model.put("FChangeType","A");
            }
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,model={}",model);
        }

        log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,entryList={}",entryList);

        QueryArg queryArg = new QueryArg();
        queryArg.setFormId(K3CloudForm.SAL_SaleOrder);
        queryArg.setFieldKeys("FID,FBillNo,FSaleOrderEntry_FEntryID,FRowType,FMaterialId.FNumber,FTaxPrice,FQty");
        queryArg.setFilterString("FID='"+salesOrderId+"'");
        commonBusinessManager.fillQueryArgByViewExtend(apiClient.getTenantId(), apiClient.getDataCenterId(), queryArg);
        Result<List<K3Model>> result = apiClient.queryReturnMap(queryArg);
        log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,result={}",result);
        List<K3Model> removedEntryList = new ArrayList<>();
        if(result.isSuccess()) {
            //选出所有K3存在的套件子项
            Set<String> existK3SonEntryId = result.getData().stream()
            .filter(v -> K3Constant.SON.equals(v.getString("FRowType"))
                    || "Son".equalsIgnoreCase(v.getString("FRowType")))
            .map(v->v.getString("FSaleOrderEntry_FEntryID")).collect(Collectors.toSet());
            /*
             对于CRM无CPQ，k3有CPQ的，即订单仅同步套件父项
             需要增加配置，不然对多个订单 产品的会有问题。
             com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud.IdSaveExtend.isDifferentCPQ
             */
            boolean isDifferentCPQ = saveExtend.getIsDifferentCPQ();
            result.getData().forEach(k3Model -> {
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,k3Model={}",k3Model);
                String entryId = k3Model.getString("FSaleOrderEntry_FEntryID");
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,entryId={}",entryId);
                if (isDifferentCPQ&&existK3SonEntryId.contains(entryId)){
                    //CRM无CPQ，不删除套件子项
                    return;
                }
                if(!isInEntryList(entryList,entryId)) {
                    removedEntryList.add(k3Model);
                }
            });
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,removedEntryList={}",removedEntryList);
            if(removedEntryList.size()>0) {
                List<K3Model> modelList = XOrderUtils.querySaleOrderEntry(salesOrderId, entryList.get(0), apiClient);
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,modelList={}",modelList);

                // 旧版本的K3C且不在白名单中,需要补全删除字段
                boolean needFillDeleteInfo = isNeedFillDeleteInfo(k3DataConverter, apiClient);

                for(K3Model entry : removedEntryList) {
                    String orderEntryId = entry.getString("FSaleOrderEntry_FEntryID");

                    K3Model removedEntry=new K3Model();
                    if(needFillDeleteInfo){//新版本的删除明细，不需要传整个明细。整个明细参数会触发不一样的业务逻辑，导致删除失败：比如 第一行明细数量不能为0
                        removedEntry = XOrderUtils.getSaleOrderEntry(modelList,orderEntryId);
                        removedEntry = XOrderUtils.assembleDeleteSaleOrderEntry(removedEntry);
                    }
                    removedEntry.put("FChangeType","D");
                    removedEntry.put("FPKIDX",orderEntryId);//源销售订单明细
//                    HashMap<String,String> map = new HashMap<>();
//                    map.put("FNumber",entry.getString("FMaterialId.FNumber"));
//                    removedEntry.put("FMaterialId", map);
//                    removedEntry.put("FTaxPrice",entry.getString("FTaxPrice"));
//                    removedEntry.put("FQty",entry.getString("FQty"));
//                    removedEntry.put("FChangeType","D");
//                    removedEntry.put("FPKIDX",entry.getString("FSaleOrderEntry_FEntryID"));
                    log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,removedEntry={}",removedEntry);
                    entryList.add(removedEntry);
                }
            }
        }

        log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,entryList2={}",entryList);
        saveArg.getModel().put("FSaleOrderEntry",entryList);

        if(standardData.getDetailFieldVals().containsKey("SAL_SaleOrder.SaleOrderPlan")&&CollectionUtils.isNotEmpty(saveArg.getModel().getDetails("FSaleOrderPlan"))){
            List<K3Model> saleOrderPlanModelList = saveArg.getModel().getDetails("FSaleOrderPlan");
            for (K3Model model : saleOrderPlanModelList) {
                if(model.get("FEntryID")!=null){
                    String entryId = String.valueOf(model.get("FEntryID"));
                    model.put("FPKIDX",entryId);
                }
            }
            saveArg.getModel().put("FSaleOrderPlan",saleOrderPlanModelList);
        }

        boolean needUseNewXOrderInterface = needUseNewCreateXOrderInterface(apiClient);
        log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,needUseNewXOrderInterface={}",needUseNewXOrderInterface);
        if(needUseNewXOrderInterface){//先创建订单新变更单，再去更新它
            Result<ViewResult> xOrder = createXOrder(salesOrderId, apiClient);
            if(xOrder!=null&&xOrder.getData()!=null){
                String newFBillNo = xOrder.getData().getResult().getResult().getString("FBillNo");
                if(StringUtils.isEmpty(newFBillNo)) {
                    newFBillNo = xOrder.getData().getResult().getResult().getString("BillNo");
                }
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,newFBillNo={}",newFBillNo);
                saveArg.getModel().put("FBillNo",newFBillNo);
                Map<String, Map<Integer, Integer>> salesOrderId2XOrderId = XOrderUtils.getSalesOrderId2XOrderId(xOrder.getData());
                //缓存订单ID和变更单ID的映射关系，后面会用到
                standardData.getMasterFieldVal().put("salesOrderId2XOrderId",salesOrderId2XOrderId);
                saveArg.getModel().put("FID",salesOrderId2XOrderId.get("order").get(Integer.valueOf(salesOrderId)));
                for(K3Model entry : entryList) {
                    Integer salesOrderEntryId = entry.getInt("FPKIDX");
                    if(salesOrderEntryId!=null){
                        entry.put("FEntryID",salesOrderId2XOrderId.get("detail").get(salesOrderEntryId));
                    }
                }
                if(standardData.getDetailFieldVals().containsKey("SAL_SaleOrder.SaleOrderPlan")&&CollectionUtils.isNotEmpty(saveArg.getModel().getDetails("FSaleOrderPlan"))){
                    List<K3Model> saleOrderPlanModelList = saveArg.getModel().getDetails("FSaleOrderPlan");
                    for(K3Model entry :saleOrderPlanModelList ) {
                        if (entry.containsKey("FPKIDX")){
                            Integer FPKIDX = entry.getInt("FPKIDX");
                            if(FPKIDX!=null){
                                entry.remove("FEntryID");
                                entry.put("FEntryID",salesOrderId2XOrderId.get("SaleOrderPlan").get(FPKIDX));
                            }
                        }
                    }
                    saveArg.getModel().put("FSaleOrderPlan",saleOrderPlanModelList);
                }
            }
        }
        log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,saveAgr2={}",saveArg);
    }

    private boolean isNeedFillDeleteInfo(final K3DataConverter k3DataConverter, final K3CloudApiClient apiClient) {
        int compareVersion = VersionComparator.INSTANCE.compare(apiClient.getDisplayVersion(), "8.0.100.202202");
        boolean needFillDeleteInfo = compareVersion < 0;
        if (needFillDeleteInfo) {
            needFillDeleteInfo = k3DataConverter.getMasterFieldExtends().stream()
                    .map(ErpFieldExtendEntity::getTenantId)
                    .findFirst()
                    .map(tenantId -> !configCenterConfig.grayXorderDelete(tenantId))
                    .orElse(true);
        }
        return needFillDeleteInfo;
    }

    private boolean isInEntryList(List<K3Model> entryList, String entryId) {
        boolean exist = false;
        for(K3Model k3Model : entryList) {
            String entryId2 = k3Model.getString("FPKIDX");
            if(StringUtils.equalsIgnoreCase(entryId,entryId2)) {
                exist = true;
                break;
            }
        }
        return exist;
    }

    private Boolean needUseNewCreateXOrderInterface(K3CloudApiClient apiClient){
        String displayVersion = apiClient.getDisplayVersion();
        if(StringUtils.isNotBlank(displayVersion)){
            try{
                log.info("displayVersion={}",displayVersion);
                //大于等于7.6.2122.7版本才使用订单新变更单接口
                int compare = VersionComparator.INSTANCE.compare(displayVersion, "7.6.2122.7");
                return compare >= 0;
            }catch (Exception e){
                log.error("needUseNewCreateXOrderInterface",e);
            }
        }
        return false;
    }

    private Result<ViewResult> createXOrder(String k3SalesOrderId,K3CloudApiClient k3CloudApiClient){
        CreateXOrderBySalesOrderNoArg createXOrderBySalesOrderNoArg=new CreateXOrderBySalesOrderNoArg();
        createXOrderBySalesOrderNoArg.getSaveXSaleOrderArgs().setSaleOrderBillId(k3SalesOrderId);
        /**
         *  //大于8.0.277.10的返回值不一样：离谱的是金蝶成功与失败的返回格式还不一样:{"IsSuccess":true,"Message":"单据编号为“YFKJ202207S000376_V001”的销售订单新变更单，保存成功！","Datas":[{"FID":127504,"FBillNo":"YFKJ202207S000376_V001","SaleOrderEntry":[{"FEntryID":"133009","FSeq":"1","FChangeType":"B","FRowType":"Standard","FMaterialId":745045,"FMaterialName":"QingCloud桌面云系统","FQtyX":1,"FQty":1,"FPriceX":1327.433628,"FPrice":1327.433628,"FTaxPricex":1500,"FTaxPrice":1500,"FAmount":1061.95,"FAllAmount":1200,"FPKIDX":159516,"FRowId":"52549efb-f50b-80f2-11ed-098cdc64f64a","FParentRowId":" "},{"FEntryID":"133010","FSeq":"2","FChangeType":"B","FRowType":"Standard","FMaterialId":748826,"FMaterialName":"蔷薇灵动蜂巢自适应微隔离安全平台V2.0--基础软件平台（物理机、虚拟机）-单机版","FQtyX":1,"FQty":1,"FPriceX":884955.752212,"FPrice":884955.752212,"FTaxPricex":1000000,"FTaxPrice":1000000,"FAmount":884.96,"FAllAmount":1000,"FPKIDX":159517,"FRowId":"52549efb-f50b-80f2-11ed-098cdc64f64b","FParentRowId":" "}]}]}
         *         //失败的：{"Result":{"ResponseStatus":{"IsSuccess":false,"Errors":[{"Message":"订单已存在未生效的变更单信息，不能再次执行变更操作！"}]}}}
         *{"Result":{"ResponseStatus":{"IsSuccess":false,"Errors":[{"Message":"订单不是已审核状态，不能变更!"}]}}}
         * 还有失败：{"DataObject":null,"EntityListStartIndex":0,"MapSuccessDataEnityIndex":{"100055":0},"IsSuccess":false,"IsShowMessage":true,"ValidationErrors":[{"BillPKID":"100055","DisplayToFieldKey":"FBillNo","DataEntityIndex":0,"RowIndex":0,"Id":"E2","Message":"违反字段唯一性要求：编码唯一。[XSDD202302000012_V002]在当前系统中已经被使用。","Level":0,"Title":"单据编号","UserDefinedErrorInfos":null}],"OperateResult":[],"Rows":[],"CustomMessageFormId":null,"CustomMessageFormParameter":{"Context":null,"FormId":null,"FormIdExt":null,"LayoutId":null,"Height":0,"MaxHeight":0,"Width":0,"VOffset":0,"HOffset":0,"ParentPageId":null,"ConsoleDetailId":null,"PageId":null,"TraceId":null,"DynamicPlugins":[],"CustomParams":{},"CustomComplexParams":{},"PermissionItemId":null,"SubSystemId":null,"TopClassId":null,"ObjectTypeId":null,"SyncCallBackAction":true,"Caption":null,"CustomUIMetaData":null,"InitControlStates":[],"HiddenMinButton":false,"ShowMaxButton":true,"HiddenCloseButton":false,"Resizable":true,"TitleVisible":true,"IsGrandTitle":false,"NoBusy":false,"DefaultFormSizeStyle":0,"FluidSettings":null,"NetCtrlDisable":false,"SizeToContent":0,"CompatibilityMode":0,"OpenStyle":{"ShowType":0,"OpenType":0,"TagetKey":null,"CacheId":null,"InlineStyleCss":null,"FormTheme":null},"MultiSelect":true,"F8ListMinHeight":0,"F8ListMinWidth":0,"IsSaveFormSize":false},"CustomMessageModel":null,"FuncResult":null,"SuccessDataEnity":null,"InteractionContext":null,"FormTitle":null,"Sponsor":null}
         */
        int compareVersion=VersionComparator.INSTANCE.compare(k3CloudApiClient.getDisplayVersion(),"8.0.277.10");
        log.info("XOrderSpecialBusinessImpl.createXOrder,compareVersion={}",compareVersion);
        String xOrderNo=null;
        if(compareVersion>=0){
            Result<NewSaveResult> newSaveResultResult = k3CloudApiClient.newCreateXOrder(createXOrderBySalesOrderNoArg);
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,newSaveResultResult={}",JSONObject.toJSONString(newSaveResultResult));
            if(newSaveResultResult.isSuccess()&&newSaveResultResult.getData().getIsSuccess()){
                xOrderNo=newSaveResultResult.getData().getDatas().get(0).getFBillNo();
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,newSaveResultResult,xOrderNo={}",xOrderNo);
                if(StringUtils.isNotBlank(xOrderNo)){
                    if(ConfigCenter.XORDER_BILL_QUERY_TENANTS.contains(k3CloudApiClient.getTenantId())) {
                        return queryViewResult(k3CloudApiClient, xOrderNo);
                    } else {
                        ViewArg viewArg=new ViewArg();
                        viewArg.setNumber(xOrderNo);
                        return k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
                    }
                }
            }
            throw  new ErpSyncDataException(ResultCodeEnum.INVOKING_ERP_INTERFACE_FAIL, JSONObject.toJSONString(newSaveResultResult.getData()), null, null);
        }else {
            Result<SaveResult> xOrder = k3CloudApiClient.createXOrder(createXOrderBySalesOrderNoArg);
            log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,xOrder={}",JSONObject.toJSONString(xOrder));
            if (xOrder != null && xOrder.getData() != null && xOrder.getData().getResult() != null && xOrder.getData().getResult().getResponseStatus() != null
                    && xOrder.getData().getResult().getResponseStatus().getIsSuccess()
                    && xOrder.getData().getResult().getResponseStatus().getErrors() != null
                    && xOrder.getData().getResult().getResponseStatus().getErrors().get(0) != null
                    && xOrder.getData().getResult().getResponseStatus().getErrors().get(0).getMessage() != null) {
                String message = xOrder.getData().getResult().getResponseStatus().getMessage();
                xOrderNo = message.substring(message.indexOf("【") + 1, message.indexOf("】"));
                log.info("XOrderSpecialBusinessImpl.beforeRunUpdate,xOrder,xOrderNo={}",xOrderNo);
            }
            if(StringUtils.isNotBlank(xOrderNo)){
                if(ConfigCenter.XORDER_BILL_QUERY_TENANTS.contains(k3CloudApiClient.getTenantId())) {
                    return queryViewResult(k3CloudApiClient, xOrderNo);
                } else {
                    ViewArg viewArg=new ViewArg();
                    viewArg.setNumber(xOrderNo);
                    return k3CloudApiClient.view(K3CloudForm.SAL_XORDER, viewArg);
                }
            }
            throw  new ErpSyncDataException(ResultCodeEnum.INVOKING_ERP_INTERFACE_FAIL, JSONObject.toJSONString(xOrder.getData()),null,null);
        }
    }

    Result<ViewResult> queryViewResult(K3CloudApiClient k3CloudApiClient, String xOrderNo) {
        QueryArg queryArg = new QueryArg();
        queryArg.setFieldKeys("FID,FBillNo,FPKIDX_H,FSaleOrderEntry_FEntryID,FPKIDX,FSaleOrderPlan_FEntryID,FPKIDX_R");
        queryArg.setFormId(K3CloudForm.SAL_XORDER);
        queryArg.appendEqualFilter("FBillNo",xOrderNo);
        return k3CloudApiClient.queryViewResult(queryArg);
    }
}
