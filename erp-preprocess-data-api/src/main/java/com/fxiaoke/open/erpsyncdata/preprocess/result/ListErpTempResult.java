package com.fxiaoke.open.erpsyncdata.preprocess.result;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 轮询临时库结果封装
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class ListErpTempResult extends ListErpObjDataResult implements Serializable {
    private static final long serialVersionUID = -7995893623707201968L;

    /**
     * 已查询数据中最大的时间
     */
    private Long maxLastSyncTime;
    /**
     * 最大时间数据中已查询到的最大id
     */
    private String maxErpTempId;
}
