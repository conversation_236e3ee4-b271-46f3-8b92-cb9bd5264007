package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.fxiaoke.open.erpsyncdata.admin.arg.CopyBetweenDbArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.CopyLog2ClickHouseArg;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminBrushDataService;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 10:45 2021/11/14
 * @Desc:
 */
@RestController
@RequestMapping("erp/syncdata/superadmin/brushdata")
@Slf4j
public class SuperAdminBrushDataController extends BaseController {
    @Autowired
    private SuperAdminBrushDataService superAdminBrushDataService;



    @PostMapping("/copyBetweenDb")
    public Result<String> copyBetweenDb(@RequestBody CopyBetweenDbArg arg){
        return superAdminBrushDataService.copyBetweenDb(arg);
    }
    @PostMapping("/copyLog2ClickHouse")
    public Result<String> copyLog2ClickHouse(@RequestBody CopyLog2ClickHouseArg arg){
        return superAdminBrushDataService.copyLog2ClickHouse(arg);
    }

}
