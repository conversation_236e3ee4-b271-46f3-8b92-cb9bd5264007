package com.fxiaoke.open.erpsyncdata.preprocess.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData
import groovy.util.logging.Slf4j
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

import java.time.LocalDateTime
import java.time.ZoneOffset
/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/2
 */
@Ignore
@Slf4j
class ErpDataPreprocessServiceTest extends BaseTest {
    @Autowired
    private ErpDataPreprocessService erpDataPreprocessService
    @Autowired
    private ErpDataService erpDataService;
    Long syncTime

    @Before
    public void setUp() throws Exception {
        def dateTime = LocalDateTime.of(2021, 3, 2, 10, 2)
        syncTime = dateTime.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli()
    }

    @Test
    void listByTimeTest() {
        TimeFilterArg arg = new TimeFilterArg()
        arg.setTenantId("81772")
        arg.setObjAPIName("SAL_SaleOrder.BillHead")
        arg.setSnapshotId("7ff98b2603a34056b7ba4b9e44ec0ead")
        arg.setStartTime(1624948624885)
        arg.setEndTime(1624949765960)
        arg.setOperationType(2)
        def result = erpDataPreprocessService.listErpObjDataByTime(arg)
        assert result.success
    }
    @Test
    void listByTimeTest2() {
        TimeFilterArg arg = new TimeFilterArg()
        arg.setTenantId("84801")
        arg.setObjAPIName("BD_MATERIAL.BillHead")
        arg.setSnapshotId("3a728f41fd1b48858942223d4039aa5f")
        arg.setStartTime(1663051686991)
        arg.setEndTime(1663053848748)
        arg.setOperationType(2)
        def result = erpDataPreprocessService.listErpObjDataByTime(arg)
        assert result.success
    }


    @Test
    void createObjTest() {
        createStr="{\"destData\":{\"object_describe_api_name\":\"SAL_SaleOrder.BillHead\",\"tenant_id\":\"82777\",\"FChangeReason\":\"编辑了订单\",\"FSaleOrderFinance.FExchangeRate\":\"1\",\"FBillTypeID.FNumber\":\"XSDD01_SYS\",\"FCustId.FNumber\":\"20220314-000039\",\"FSalerId.FNumber\":[\"1000\"],\"FSaleOrgId.FNumber\":\"000\",\"FDate\":1.6497792E12,\"_id\":\"62567f864375b10001e6fa3a\"},\"destDataId\":\"62567f864375b10001e6fa3a\",\"destDetailSyncDataIdAndDestDataMap\":{\"d5dd3bf837734666974ff2955e347332\":{\"object_describe_api_name\":\"SAL_SaleOrder.SaleOrderEntry\",\"tenant_id\":\"82777\",\"fake_master_detail\":\"62567f864375b10001e6fa3a\",\"FUnitID.FNumber\":\"Pcs\",\"FDiscountRate\":\"100\",\"FMaterialId.FNumber\":\"CH888z\",\"FQty\":\"1.00\",\"FTaxPrice\":\"100.00\",\"FEntryNote\":\"222\",\"_id\":\"62567f864375b10001e6fa3b\",\"owner\":[],\"created_by\":[]}},\"destEventType\":1,\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"destTenantId\":\"82777\",\"destTenantType\":2,\"sourceTenantId\":\"82777\",\"syncDataId\":\"ca056679ffd944aea95a86f61dc1e1b3\",\"syncPloyDetailSnapshotId\":\"170108c2ebb941518cf0dc467831270f\",\"tenantId\":\"82777\"}";
        DoWriteMqData arg = JSON.parseObject(createStr, new TypeReference<DoWriteMqData>() {})
        def result = erpDataPreprocessService.createErpObjData(arg)
        println(result)
    }
    @Test
    void createLookUpObjTest() {
        String str = "{\"destData\":{\"tenant_id\":\"81138\",\"FDescription\":\"咕咕咕\",\"FCreateOrgId.FNumber\":\"000\",\"object_describe_api_name\":\"BD_Customer.BillHead\",\"name\":\"xjy测试客户114\",\"_id\":\"CUST3512\",\"FSELLER.FNumber\":[\"1001\"]},\"destDataId\":\"CUST3512\",\"destDetailSyncDataIdAndDestDataMap\":{\"eeac150e529141b498863a8571cd7a52\":{\"tenant_id\":\"81138\",\"owner\":[],\"FIsDefaultSettle\":true,\"FNUMBER1\":\"Addr.2021-01-14_002055\",\"FIsDefaultPayer\":true,\"fake_master_detail\":\"CUST3512\",\"object_describe_api_name\":\"BD_Customer.FT_BD_CUSTCONTACT\",\"FIsDefaultConsignee\":true,\"_id\":\"100976\",\"created_by\":[],\"FIsUsed\":true}},\"destEventType\":2,\"destObjectApiName\":\"BD_Customer.BillHead\",\"destTenantId\":\"81138\",\"destTenantType\":2,\"sourceTenantId\":\"81138\",\"syncDataId\":\"ea801b5bf90a489ba727230463e9b7fe\",\"syncPloyDetailSnapshotId\":\"429a940ae97b4570aee9d435d350d596\"}"
        DoWriteMqData arg = JSON.parseObject(str, new TypeReference<DoWriteMqData>() {})
        def result = erpDataPreprocessService.updateErpObjData(arg)
        println(result)
    }
    @Test
    void getReSyncObjDataById() {
        ErpIdArg arg=new ErpIdArg();
        arg.setTenantId("85279")
        arg.setDataId("2110")
        arg.setIncludeDetail(true)
        arg.setObjAPIName(K3CloudForm.SAL_SC_CUSTMAT)
        def result = erpDataPreprocessService.getReSyncObjDataById(arg)
        println(result)
    }

    @Test
    public void invaldDataTest() {
        DoWriteMqData doWriteMqData = JacksonUtil.fromJson("{\"destDataId\":\"D11\",\"destEventType\":3,\"destObjectApiName\":\"testObj_1f61vbnp1\",\"destTenantId\":\"82335\",\"sourceTenantId\":\"82335\",\"syncDataId\":\"ade40b7f07b045b485f71118df3d160c\"}",DoWriteMqData.class)
        erpDataPreprocessService.invalidErpObjData(doWriteMqData)
    }
    String createStr = "{\n" +
            "        \"destData\":{\n" +
            "            \"tenant_id\":\"81138\",\n" +
            "            \"FTransferBizType\":\"OverOrgSale\",\n" +
            "            \"FStockOutOrgId.Number\":\"000\",\n" +
            "            \"FOwnerTypeOutIdHead\":\"BD_Supplier\",\n" +
            "            \"FTransferDirect\":\"GENERAL\",\n" +
            "            \"object_describe_api_name\":\"STK_TransferDirect.BillHead\",\n" +
            "            \"FBillTypeID.Number\":\"124\",\n" +
            "            \"FOwnerTypeIdHead\":\"BD_OwnerOrg\",\n" +
            "            \"_id\":\"6017a929bcc525000101c467\",\n" +
            "            \"FStockOrgId.Number\":\"215315225\",\n" +
            "            \"FDate\":\"2020\"\n" +
            "        },\n" +
            "        \"destDataId\":\"6017a929bcc525000101c467\",\n" +
            "        \"destDetailSyncDataIdAndDestDataMap\":{\n" +
            "            \"0bb4430a8c104d0094e84fb8d914f721\":{\n" +
            "                \"tenant_id\":\"81138\",\n" +
            "                \"FOwnerTypeOutId\":\"BD_OwnerOrg\",\n" +
            "                \"FSrcStockLocId\":\"zsltest001||001.B1\",\n" +
            "                \"owner\":[\n" +
            "\n" +
            "                ],\n" +
            "                \"FOwnerTypeId\":\"BD_Supplier\",\n" +
            "                \"FBaseUnitId.Number\":\"214124\",\n" +
            "                \"FOwnerOutId.Number\":\"56457457\",\n" +
            "                \"FUnitID.Number\":\"1214\",\n" +
            "                \"created_by\":[\n" +
            "\n" +
            "                ],\n" +
            "                \"FMaterialId.Number\":\"51242113\",\n" +
            "                \"fake_master_detail\":\"6017a929bcc525000101c467\",\n" +
            "                \"object_describe_api_name\":\"STK_TransferDirect.TransferDirectEntry\",\n" +
            "                \"FKeeperOutId.Number\":\"12312\",\n" +
            "                \"FKeeperTypeId\":\"BD_Supplier\",\n" +
            "                \"_id\":\"6017a929bcc525000101c468\",\n" +
            "                \"FKeeperId.Number\":\"124124\",\n" +
            "                \"FKeeperTypeOutId\":\"BD_KeeperOrg\",\n" +
            "                \"FOwnerId.Number\":\"35436\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"destEventType\":1,\n" +
            "        \"destObjectApiName\":\"STK_TransferDirect.BillHead\",\n" +
            "        \"destTenantId\":\"81138\",\n" +
            "        \"destTenantType\":2,\n" +
            "        \"sourceTenantId\":\"81138\",\n" +
            "        \"syncDataId\":\"40fc95847d6a4a8a911c89204fbd8ddc\",\n" +
            "        \"syncPloyDetailSnapshotId\":\"ec10ad74361644f6bdf8cbfde50b3a49\"\n" +
            "    }"
}
