package com.fxiaoke.open.erpsyncdata.dbproxy.manager

import com.fxiaoke.open.erpsyncdata.BaseTest
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2021/5/12
 */
@Ignore
class ErpFieldDataMappingManagerTest extends BaseTest {
    @Autowired
    private ErpFieldDataMappingManager erpFieldDataMappingManager

    @Test
    void testListNoSearch() {
        for (i in 0..<10) {
            erpFieldDataMappingManager.listNoSearch("80775","663925035763105792",ErpFieldTypeEnum.employee,null,null)
        }

    }
}
