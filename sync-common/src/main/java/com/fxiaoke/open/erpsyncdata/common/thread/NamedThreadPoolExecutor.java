package com.fxiaoke.open.erpsyncdata.common.thread;

import com.github.trace.executor.MonitorTaskWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

@Slf4j
public class NamedThreadPoolExecutor extends ThreadPoolExecutor {
    private static final long DEFAULT_KEEP_ALIVE_TIME = 60;

    /**
     * @param name 会自动拼接-
     * @param corePoolSize
     * @param maximumPoolSize
     */
    public NamedThreadPoolExecutor(String name, int corePoolSize, int maximumPoolSize) {
        super(corePoolSize, maximumPoolSize, DEFAULT_KEEP_ALIVE_TIME, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new NamedThreadFactory(name));
    }


    public NamedThreadPoolExecutor(String name, int corePoolSize, int maximumPoolSize, int capacity) {
        super(corePoolSize, maximumPoolSize, DEFAULT_KEEP_ALIVE_TIME, TimeUnit.SECONDS, new LinkedBlockingQueue<>(capacity), new NamedThreadFactory(name));
    }

    public NamedThreadPoolExecutor(String name,
                                   int corePoolSize,
                                   int maximumPoolSize,
                                   int queueSize,
                                   RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, DEFAULT_KEEP_ALIVE_TIME, TimeUnit.SECONDS, new LinkedBlockingQueue<>(queueSize), new NamedThreadFactory(name), handler);
    }

    public NamedThreadPoolExecutor(String name,
                                   boolean daemon,
                                   int corePoolSize,
                              int maximumPoolSize,
                              long keepAliveTime,
                              TimeUnit unit,
                              BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue,new NamedThreadFactory(name,daemon));

    }

    @Override
    public void setCorePoolSize(int corePoolSize) {
        if (corePoolSize < 0) {
            corePoolSize = 0;
        }
        int maximumPoolSize = getMaximumPoolSize();
        if (corePoolSize > maximumPoolSize) {
            //如果输入的corePoolSize大于maximumPoolSize，强制修改maximumPoolSize为corePoolSize，防止jdk17下异常。
            setMaximumPoolSize(corePoolSize);
        }
        super.setCorePoolSize(corePoolSize);
    }

    @Override
    public void setMaximumPoolSize(int maximumPoolSize) {
        if (maximumPoolSize <= 0) {
            maximumPoolSize = 1;
        }
        int corePoolSize = getCorePoolSize();
        if (maximumPoolSize < corePoolSize) {
            //如果输入的maximumPoolSize小于corePoolSize，先将corePoolSize改小，防止jdk17下异常。
            setCorePoolSize(corePoolSize);
        }
        super.setMaximumPoolSize(maximumPoolSize);
    }

    @Override
    public void execute(Runnable command) {
        super.execute(MonitorTaskWrapper.wrap(command));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(MonitorTaskWrapper.wrap(task));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(MonitorTaskWrapper.wrap(task));
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        if (t != null) {
            log.warn("", t);
        }
    }
}
