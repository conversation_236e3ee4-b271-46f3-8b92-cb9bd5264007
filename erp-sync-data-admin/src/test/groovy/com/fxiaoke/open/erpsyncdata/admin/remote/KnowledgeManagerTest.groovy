package com.fxiaoke.open.erpsyncdata.admin.remote


import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR> 
 * @date 2023/5/19 15:53:45
 */
@ContextConfiguration(["classpath:test-knowledgeManager.xml"])
class KnowledgeManagerTest extends Specification {
    static {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data-web");
    }

    @Autowired
    private KnowledgeManager knowledgeManager;

    def "SearchKnowledge"() {
        //expect:
        //println  knowledgeManager.searchKnowledge("test", arg.getSize())
    }
}
