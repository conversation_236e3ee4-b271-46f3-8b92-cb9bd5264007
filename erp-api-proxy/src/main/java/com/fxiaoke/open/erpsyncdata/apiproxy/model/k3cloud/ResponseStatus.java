package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3cloud;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Joiner;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/11/11
 */
@Data
public class ResponseStatus {
    @SerializedName("ErrorCode")
    @JsonProperty("ErrorCode")
    private String errorCode;

    @SerializedName("IsSuccess")
    @JsonProperty("IsSuccess")
    private Boolean isSuccess;

    @SerializedName("Errors")
    @JsonProperty("Errors")
    private List<Messages> errors;

    @SerializedName("SuccessEntitys")
    @JsonProperty("SuccessEntitys")
    private List<SuccessEntitys> successEntitys;

    @SerializedName("SuccessMessages")
    @JsonProperty("SuccessMessages")
    private List<Messages> successMessages;

    @SerializedName("MsgCode")
    @JsonProperty("MsgCode")
    private String msgCode;

    @SerializedName("Message")
    @JsonProperty("Message")
    private String message;

    @Data
    public static class SuccessEntitys {
        @SerializedName("Id")
        @JsonProperty("Id")
        private String id;

        @SerializedName("Number")
        @JsonProperty("Number")
        private String number;

        @SerializedName("DIndex")
        @JsonProperty("DIndex")
        private Integer dindex;
    }

    @Data
    public static class Messages {
        @SerializedName("FieldName")
        @JsonProperty("FieldName")
        private String fieldName;

        @SerializedName("Message")
        @JsonProperty("Message")
        private String message;

        @SerializedName("DIndex")
        @JsonProperty("DIndex")
        private Integer dindex;
    }

    public String printErrors() {
        Joiner joiner = Joiner.on(",").skipNulls();
        String joinMessage = this.errors.stream()
                .map(v->joiner.join(v.getFieldName(),v.getMessage()))
                .collect(Collectors.joining(";"));
        return joinMessage;
    }
}
