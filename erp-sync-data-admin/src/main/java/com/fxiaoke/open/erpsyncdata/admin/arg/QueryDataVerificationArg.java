package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.alibaba.fastjson.annotation.JSONField;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.constant.IdSyncStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 15:18 2022/11/3
 * @Desc:
 */
@Data
@ApiModel
public class QueryDataVerificationArg implements Serializable {
    @ApiModelProperty(value = "数据核对任务id")
    @JSONField(serialize = false)
    private String dataVerificationTaskId;
    @ApiModelProperty(value = "集成流id")
    @JSONField(serialize = false)
    private String streamId;
    @ApiModelProperty(value = "历史数据任务id")
    @JSONField(serialize = false)
    private String taskId;
    @ApiModelProperty(value = "id同步状态")
    @JSONField(serialize = false)
    private IdSyncStatus idStatus;
    @ApiModelProperty(value = "查询数据id")
    @JSONField(serialize = false)
    private String dataId;
}
