package com.fxiaoke.open.oasyncdata.db.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;

import com.fxiaoke.open.oasyncdata.annotation.LogLevel;
import com.fxiaoke.open.oasyncdata.db.util.ConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 19:59 2022/5/12
 * @Desc:企业OA环境管理者
 */
@Component
@Slf4j
public class TenantEnvManager {

    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;

    /**
     * 获取web环境，前端应用
     * return <web环境，前端应用>
     */
    @Cached(cacheType = CacheType.LOCAL, expire = 60, localLimit = 1000)
    @LogLevel
    public Pair<String, String> getTenantWebEnvironment(String tenantId) {
        String env=getEnv(tenantId);

        String tenantWebModelEnv = ConfigCenter.ALL_ENV_TO_WEB_ENV.get(env);
        String tenantWebApp = ConfigCenter.WEB_ENV_TO_WEB_APP_ENV.get(tenantWebModelEnv);
        return Pair.of(tenantWebModelEnv, tenantWebApp);

    }

    public String getEnv(String tenantId){
        Set<String> grayEnvs = tenantConfigurationManager.getGrayEnvs();
        if(grayEnvs.contains(tenantId)){
            return "gray";
        }
        Set<String> norMalEnvs = tenantConfigurationManager.getNorMalEnvs();
        if(norMalEnvs.contains(tenantId)){
            return "normal";
        }
        return "normal";
    }



}
