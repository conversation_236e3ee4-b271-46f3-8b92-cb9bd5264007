package com.fxiaoke.open.oasyncdata.service;


import com.fxiaoke.open.oasyncdata.model.OASyncLogResultArg;
import com.fxiaoke.open.oasyncdata.model.OASyncLogVO;
import com.fxiaoke.open.oasyncdata.model.QueryOASyncLogArg;
import com.fxiaoke.open.oasyncdata.result.base.Result;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021/3/18
 */
public interface OASyncLogService {
    Result<List<OASyncLogVO>> queryOALog(QueryOASyncLogArg queryOASyncLogArg);

    Result<OASyncLogResultArg> queryOALogGroup(QueryOASyncLogArg queryOASyncLogArg,String lang);

    Result<String> reSyncData(String logId, String dataCenterId,String tenantId, String lang);

    Result<String> reSyncDataList(List<String> logId,String dataCenterId, String tenantId, String lang);

    Result<String> reSyncAllFailData(String tenantId);

    Result<String> resyncSettingCondition(String tenantId);

    Result<String> fixData(String tenantId, Long startTime, Long endTime);

    Result<String> transferData(String tenantId, Long startTime, Long endTime);
}
