package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.base;

import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.google.common.collect.ImmutableMap;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

/**
 * 日志Mongo baseDao，支持分片
 *
 * <AUTHOR> (^_−)☆
 * @date 2023/5/29
 */
public abstract class BaseExpireIndexLogMongoStore<T> extends BaseLogMongoStore<T> {

    @Autowired
    @Lazy
    private ConfigCenterConfig configCenterConfig;

    public BaseExpireIndexLogMongoStore() {
    }

    public BaseExpireIndexLogMongoStore(CodecRegistry codecRegistry) {
        super(codecRegistry);
    }

    public abstract Bson expireIndex();

    public Long getExpireDay(String tenantId) {
        return configCenterConfig.getSyncLogExpireDay(tenantId);
    }

    public void resetExpireTime(String tenantId) {
        resetExpireTime(tenantId, expireIndex(), getExpireDay(tenantId) * 24 * 3600);
    }

    public void resetExpireTime(String tenantId, Bson expireIndex, Long expireAfterSeconds) {
        final String collName = getCollName(tenantId);
        final MongoDatabase database = getDatabase(tenantId);
        final Document command = new Document();
//        db.runCommand({collMod: "sync_log_88104",index: { keyPattern: { createTime: -1 },expireAfterSeconds: 7776000}})
        command.append("collMod", collName)
                .append("index", new Document(ImmutableMap.of(
                        "keyPattern", expireIndex,
                        "expireAfterSeconds", expireAfterSeconds
                )));
        database.runCommand(command);
    }
}
