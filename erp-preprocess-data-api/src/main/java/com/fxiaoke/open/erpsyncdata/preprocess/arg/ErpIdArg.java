package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/26
 */
@Data
@ApiModel("id参数")
public class ErpIdArg implements Serializable {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty("企业ei，标准接口不传")
    private String tenantId;
    @ApiModelProperty("对象apiName")
    private String objAPIName;
    @ApiModelProperty("数据id")
    private String dataId;
    @ApiModelProperty("数据id是否是对象编码")
    private boolean dataIdIsNumber;
    @ApiModelProperty("是否包含明细数据")
    private boolean includeDetail = true;
    /** 快照id */
    @ApiModelProperty("快照id")
    private String syncPloyDetailSnapshotId;
    /** 源数据的数据事件类型 1、新增 2、修改 3、作废  */
    @ApiModelProperty("源数据的数据事件类型")
    private Integer sourceEventType;
}
