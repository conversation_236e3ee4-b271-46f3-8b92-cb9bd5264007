package com.fxiaoke.open.erpsyncdata.dbproxy.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class K3UltimateSignatureConfig implements Serializable {
    private Boolean needEncryptSecret;
    private String encryptSecretKey;
    private String encryptSecretType;
    private Integer encryptSecretKeySize;

    private Boolean needSignatureSecret;
    private String signatureSecretType;
    private String signatureSecretKey;

    public static class queryArg implements Serializable {
    }

}
