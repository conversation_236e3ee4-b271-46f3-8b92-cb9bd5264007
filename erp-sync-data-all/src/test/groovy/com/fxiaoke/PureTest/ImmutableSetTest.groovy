package com.fxiaoke.PureTest


import com.google.common.base.Splitter
import com.google.common.collect.ImmutableSet
import org.junit.Test

/**
 *
 * <AUTHOR> (^_−)☆
 */
class ImmutableSetTest {
    @Test
    void immutableSetTest() {
        def set1 = ImmutableSet.copyOf(Splitter.on(",").trimResults().split("setTenantId, checkStreamExist, getRealObjApiName, isCutDownField, sendOneEvent, getHandlerByName, getRelation, saveLog, queryIntegrationStreamSnaps, getMappingFirstBySource, initLogId, listNewestEnableSyncPloyDetailsSnapshots, existByTwoWay, executeCustomFunction, sendEventData2DispatcherMq, getSyncDataMapping, updateErpTempDataByIds, getReferName, getDataByMasterId, removeBatchCache, changeVariableFilter"))
        def set2 = ImmutableSet.of("setTenantId", "checkStreamExist", "getRealObjApiName", "isCutDownField",
                "sendOneEvent", "getHandlerByName", "getRelation", "saveLog", "queryIntegrationStreamSnaps", "getMappingFirstBySource",
                "initLogId", "listNewestEnableSyncPloyDetailsSnapshots", "existByTwoWay", "executeCustomFunction",
                "sendEventData2DispatcherMq", "getSyncDataMapping", "updateErpTempDataByIds", "getReferName",
                "getDataByMasterId", "removeBatchCache", "changeVariableFilter")
        print(set2)
        def next = set1.iterator().next()
        expect:
        set1 == set2
        next == next.trim()
    }
}
