package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023.4.23
 */
@Data
public class UserFieldDataMappingExcelVo implements Serializable {

    /**
     * 纷享员工名称
     */
    @ExcelProperty(value = "erpdss.global.global.s1037",index = 0)
    private String fsDataName;

    /**
     * 纷享员工id
     */
    @ExcelProperty(value = "erpdss.global.global.s1038",index = 1)
    private String fsDataId;

    /**
     * ERP用户名称
     */
    @ExcelProperty(value = "erpdss.global.global.s1039",index = 2)
    private String erpDataName;

    /**
     * erp用户id
     */
//    @ExcelProperty("erp用户id")
    @ExcelIgnore
    private String erpDataId;

}
