package com.fxiaoke.open.oasyncdata.db.util;

import com.fxiaoke.open.oasyncdata.annotation.LogLevelEnum;
import org.slf4j.Logger;

public class LogUtil {
    public static final String BREAK_KEY = "[.BREAK]";
    private static ThreadLocal<LogLevelEnum> logLevelEnumThreadLocal = new ThreadLocal<>();

    public static LogLevelEnum getCurrentLogLevel(LogLevelEnum defaultLevel) {
        LogLevelEnum logLevelEnum = logLevelEnumThreadLocal.get();
        if (logLevelEnum == null) {
            return defaultLevel;
        }
        return logLevelEnum;
    }

    public static void setCurrentLogLevel(LogLevelEnum logLevel) {
        logLevelEnumThreadLocal.set(logLevel);
    }

    public static void logDefaultInfo(Logger logger, String string, Object... args) {
        LogLevelEnum logLevelEnum = getCurrentLogLevel(LogLevelEnum.INFO);
        log(logger, logLevelEnum, string, args);
    }

    public static void logDefaultDebug(Logger logger, String string, Object... args) {
        LogLevelEnum logLevelEnum = getCurrentLogLevel(LogLevelEnum.DEBUG);
        log(logger, logLevelEnum, string, args);
    }

    public static void log(Logger logger, LogLevelEnum logLevel, String string, Object... args) {
        switch (logLevel) {
            case TRACE:
                logger.trace(string, args);
                break;
            case DEBUG:
                logger.debug(string, args);
                break;
            case INFO:
                logger.info(string, args);
                break;
            case WARN:
                logger.warn(string, args);
                break;
            case ERROR:
                logger.error(string, args);
                break;
        }
    }

    public static void log(Logger logger, LogLevelEnum logLevel, String string, Throwable e) {
        switch (logLevel) {
            case TRACE:
                logger.trace(string, e);
                break;
            case DEBUG:
                logger.debug(string, e);
                break;
            case INFO:
                logger.info(string, e);
                break;
            case WARN:
                logger.warn(string, e);
                break;
            case ERROR:
                logger.error(string, e);
                break;
        }
    }
}
