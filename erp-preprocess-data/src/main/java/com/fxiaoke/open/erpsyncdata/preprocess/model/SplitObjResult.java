package com.fxiaoke.open.erpsyncdata.preprocess.model;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.DoWriteMqData;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/21
 */
public class SplitObjResult {

    @Getter
    @Setter
    @ToString
    public static class Erp2Crm implements Serializable {
        private static final long serialVersionUID = 7432299176321762647L;

        private SyncDataContextEvent doWriteMqData;

        private ErpObjSplitTypeEnum masterObjSplitType;
    }
}
