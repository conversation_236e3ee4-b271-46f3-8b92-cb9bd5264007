package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao;

import cn.hutool.core.date.DateTime;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncFailedStatEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncFailedStatEntity.Fields;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.BeanUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.config.SyncFailedStat;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.UpdateResult;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.Date;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/2/3
 */
@Repository
@DependsOn("erpSyncDataLogMongoStore")
public class SyncFailedStatDao {

    @Qualifier("erpSyncDataLogMongoStore")
    @Autowired
    private DatastoreExt erpSyncDataLogMongoStore;
    private String DATABASE;

    @PostConstruct
    void init() {
        this.DATABASE = ConfigFactory.getInstance().getConfig("erp-sync-data-logmongo")
                .get("syncLogDbName", "fs-erp-sync-data");
//        createIndex();
    }

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry =
                CodecRegistries.fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
                        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build()));
    }

    private void createIndex() {
        //创建索引,如果已存在mongo不会执行操作
        MongoCollection<SyncFailedStatEntity> coll = getColl();
        IndexOptions indexOptions = new IndexOptions().unique(true);
        Bson idx = Indexes.ascending(Fields.tenantId, Fields.dataCenterId, Fields.streamId);
        coll.createIndex(idx, indexOptions);
    }


    private MongoCollection<SyncFailedStatEntity> getColl() {
        MongoCollection<SyncFailedStatEntity> coll = erpSyncDataLogMongoStore.getMongo().getDatabase(DATABASE)
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection("sync_failed_stat", SyncFailedStatEntity.class);
        return coll;
    }

    public SyncFailedStat getSyncFailedStat(String tenantId, String dcId, String streamId) {
        MongoCollection<SyncFailedStatEntity> coll = getColl();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.streamId, streamId));
        SyncFailedStatEntity first = coll.find(filters).first();
        return convert(first);
    }

    public void replace(String tenantId, String dcId, String streamId, SyncFailedStat syncFailedStat) {
        MongoCollection<SyncFailedStatEntity> coll = getColl();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.streamId, streamId));
        SyncFailedStatEntity entity = convert(tenantId, dcId, streamId, syncFailedStat);
        UpdateResult updateResult = coll.replaceOne(filters, entity, new ReplaceOptions().upsert(true));
    }


    public long updateLastBreakTime(String tenantId, String dcId, String streamId, Long lastBreakTime) {
        MongoCollection<SyncFailedStatEntity> coll = getColl();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.streamId, streamId));
        Bson updates = Updates.combine(
                Updates.set(Fields.lastBreakTime, lastBreakTime),
                Updates.currentDate(Fields.updateTime)
        );
        UpdateResult updateResult = coll.updateOne(filters, updates);
        return updateResult.getModifiedCount();
    }

    public long updateLastCount(String tenantId, String dcId, String streamId, int lastCount, long lastCountTime) {
        MongoCollection<SyncFailedStatEntity> coll = getColl();
        Bson filters = Filters.and(
                Filters.eq(Fields.tenantId, tenantId),
                Filters.eq(Fields.dataCenterId, dcId),
                Filters.eq(Fields.streamId, streamId));
        Bson updates = Updates.combine(
                Updates.set(Fields.lastCount, lastCount),
                Updates.set(Fields.lastCountTime, lastCountTime),
                Updates.currentDate(Fields.updateTime)
        );
        UpdateResult updateResult = coll.updateOne(filters, updates);
        return updateResult.getModifiedCount();
    }


    private static SyncFailedStatEntity convert(String tenantId, String dcId, String streamId, SyncFailedStat syncFailedStat) {
        SyncFailedStatEntity entity = new SyncFailedStatEntity();
        entity.setTenantId(tenantId);
        entity.setStreamId(streamId);
        entity.setDataCenterId(dcId);
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        BeanUtils.copyProperties(syncFailedStat, entity);
        return entity;
    }

    private SyncFailedStat convert(SyncFailedStatEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copy(entity, SyncFailedStat.class);
    }


}
