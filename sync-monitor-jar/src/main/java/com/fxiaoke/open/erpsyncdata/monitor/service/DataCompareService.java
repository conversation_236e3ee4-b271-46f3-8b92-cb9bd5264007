package com.fxiaoke.open.erpsyncdata.monitor.service;

import com.fxiaoke.open.erpsyncdata.monitor.model.arg.CompareArg;
import com.fxiaoke.open.erpsyncdata.monitor.model.result.CompareResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/12/29
 */
public interface DataCompareService {
    Result<Void> compareAndAlert(String tenantId,String compareName);
    Result<CompareResult> compareAndAlert(CompareArg arg);
    Result<CompareResult> doCompare(CompareArg arg);

    /**
     * 刷新任务
     * @return
     */
    Result<Void> refreshTask(String tenantId);
}
