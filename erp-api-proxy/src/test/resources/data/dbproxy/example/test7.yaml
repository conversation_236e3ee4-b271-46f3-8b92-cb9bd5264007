#YAML
# 插入和更新 sqlserver
insertSql: |
  insert into t7 (id, name, "date")
  values (${id}, ${name}, ${date__d});
updateSql: |
  MERGE INTO t7 AS target
  USING (VALUES
  (${id}, ${name}, ${date__d})
  ) AS source (id, name, date)
  ON target.id = source.id
  WHEN MATCHED THEN
  UPDATE SET
  target.name = source.name,
  target.date = source.date
  WHEN NOT MATCHED THEN
  INSERT (id, name, date)
  VALUES (source.id, source.name, source.date);
queryIdSql: |
  select ${id}