package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/3
 */
@Data
public class BatchSendErpObjDataArg implements Serializable {
    private static final long serialVersionUID = -845885387196836674L;
    private List<ErpEventData> erpEventDatas;

    @Data
    public static class ErpEventData implements Serializable {
        private static final long serialVersionUID = 5981021540715243029L;
        private Integer sourceEventType;
        private Integer sourceTenantType;
        private ObjectData sourceData;
        private String syncLogId;
        private Long DataVersion;
    }
}
