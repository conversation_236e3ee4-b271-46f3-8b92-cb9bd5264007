package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/24
 */
@Data
@ApiModel("获取header的脚本")
public class HeaderScript implements Serializable {
    private static final long serialVersionUID = 217939551318834972L;

    @ApiModelProperty("脚本code")
    private String scriptCode;
}
