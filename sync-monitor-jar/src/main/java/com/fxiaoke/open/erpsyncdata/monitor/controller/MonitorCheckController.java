package com.fxiaoke.open.erpsyncdata.monitor.controller;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/10
 */
@RestController
@Slf4j
@RequestMapping()
public class MonitorCheckController {

    @GetMapping()
    public Result<String> check(){
        return Result.newSuccess("hello,sync-monitor!");
    }

}
