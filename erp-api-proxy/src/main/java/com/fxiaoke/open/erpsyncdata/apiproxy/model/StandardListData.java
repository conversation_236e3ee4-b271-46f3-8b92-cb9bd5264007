package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import com.fxiaoke.open.erpsyncdata.common.util.RAMEstimable;
import com.fxiaoke.open.erpsyncdata.preprocess.util.RamUsageEstimateUtil;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
@Data
public class StandardListData implements RAMEstimable {

    /**
     * 总数，暂不使用
     */
    private Integer totalNum = 0;
    /**
     * 标准数据列表
     */
    private List<StandardData> dataList = new ArrayList<>(0);

    /**
     * 下次起始时间
     */
    private Long maxTime;

    /**
     * 下次起始ID
     */
    private String maxId;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
                .append("totalNum", totalNum)
                .append("dataList", dataList)
                .toString();
    }

    @Override
    public long ramBytesUsed(int depth) {
        return RamUsageEstimateUtil.sizeOfObject(dataList, depth);
    }
}
