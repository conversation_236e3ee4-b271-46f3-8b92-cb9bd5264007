package com.fxiaoke.open.erpsyncdata.web.controller.dev;

import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ConnectorHandlerType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SystemParams;
import com.fxiaoke.open.erpsyncdata.preprocess.model.connector.OuterConnector;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.AllConnectorUtil;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 开发者,同时支持cep和普通调用
 * <AUTHOR> (^_−)☆
 */
@RestController
@RequestMapping({"erp/syncdata/dev", "cep/setUp/dev"})
public class DevController extends BaseController {

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    /**
     * 注册连接器,,,cep不能传param
     */
    @PostMapping("changeConnector")
    public Result<Void> changeConnector(@RequestBody ConnectInfoResult connectInfoResult) {
        return changeConnector(connectInfoResult.getNumber());
    }

    /**
     * 注册连接器
     * @param number
     * @return
     */
    @GetMapping("changeConnector")
    public Result<Void> changeConnector(@RequestParam final Integer number) {
        String tenantId = getLoginUserTenantId();
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByNumber(tenantId, ErpChannelEnum.STANDARD_CHANNEL, number);
        if (connectInfo == null) {
            return Result.newSystemError(I18NStringEnum.s3677);
        }
        StandardConnectParam connectParam = ErpChannelEnum.STANDARD_CHANNEL.getConnectParam(connectInfo.getConnectParams());
        ConnectorHandlerType handlerType = connectParam.getConnectorHandlerType();
        if (handlerType != ConnectorHandlerType.APL_CLASS && handlerType != ConnectorHandlerType.HUB) {
            return Result.newSystemError(I18NStringEnum.s3678);
        }
        OuterConnector outerConnector = AllConnectorUtil.getOuterByApiName(connectParam.getApiName());
        if (outerConnector == null) {
            return Result.newSystemError(I18NStringEnum.s3679);
        }
        Integer connectorNum = outerConnector.getConnectorId() * 100;
        if (!Objects.equals(number, connectorNum)){
            ErpConnectInfoEntity exist = erpConnectInfoDao.getByNumber(tenantId, ErpChannelEnum.STANDARD_CHANNEL, connectorNum);
            if (exist != null) {
                return Result.newSystemError(I18NStringEnum.s3680);
            }
            erpConnectInfoDao.updateNumber(connectorNum, tenantId, connectInfo.getId());
        }
        //清空连接信息
        connectParam.setSystemParams(new SystemParams());
        connectParam.setIconUrl(outerConnector.getIconUrl());
        connectInfo.setConnectParams(GsonUtil.toJson(connectParam));
        erpConnectInfoDao.updateById(connectInfo);
        return Result.newSuccess();
    }
}
