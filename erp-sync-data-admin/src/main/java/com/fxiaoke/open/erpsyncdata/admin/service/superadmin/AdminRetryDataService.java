package com.fxiaoke.open.erpsyncdata.admin.service.superadmin;

import com.fxiaoke.open.erpsyncdata.admin.model.amis.Amis;
import com.fxiaoke.open.erpsyncdata.admin.result.PollingTempRecordResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ReTrySendMqResult;
import com.fxiaoke.open.erpsyncdata.dbproxy.arg.RetryConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/5/21 19:34
 * 提供管理页面重试失败的数据
 * @desc
 */
public interface AdminRetryDataService {

    Result<Integer> retryDataByType(RetryConfig retryConfig);

    Result<Amis.Crud<ReTrySendMqResult>> queryDataByType(String tenantId, String dataType, List<Integer> status, Long startTime, Long endTime, Integer limit, Integer offset);

    Result<Amis.Crud<PollingTempRecordResult>> queryDataByTempType(String tenantId, List<Integer> status, Long startTime, Long endTime, Integer limit, Integer offset);

    Result<Amis.Crud<PollingTempRecordResult>> retryDataByTempType(RetryConfig retryConfig);



}


