package com.fxiaoke.open.erpsyncdata.admin.service.impl.logEvent;

import com.fxiaoke.open.erpsyncdata.admin.result.LogDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncDataMappingGetDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncDataMappingService;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.BaseLinkWalkingService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.base.SyncLogManagerFactory;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogDetailArg;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StreamLogQueryArg;
import com.fxiaoke.open.erpsyncdata.common.constant.CustomFunctionConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjGroovyDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjGroovyEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdFieldConvertManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.FunctionQueryResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceFindArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.fxiaoke.otherrestapi.function.result.FunctionResult;
import com.fxiaoke.otherrestapi.function.result.FunctionServiceFindResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/6/17 14:57
 * @Version 1.0
 */
@Service
@Slf4j
public class WalkingManager {
    @Autowired
    private SyncLogManager syncLogManager;
    @Autowired
    private BaseLinkServiceImpl baseLinkService;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private ErpObjGroovyDao erpObjGroovyDao;
    @Autowired
    private IdFieldConvertManager idFieldConvertManager;
    @Autowired
    private CustomFunctionService customFunctionService;
    @Autowired
    private AdminSyncDataMappingService adminSyncDataMappingService;

    public Result getLogDetailByLogId(StreamLogDetailArg streamLogDetailArg,String lang) {
        String filterNode=streamLogDetailArg.getSyncNodeType();
        if (streamLogDetailArg.getLogEndTime() == null) {
            streamLogDetailArg.setLogEndTime(System.currentTimeMillis());
        } else {
            streamLogDetailArg.setLogEndTime(streamLogDetailArg.getLogEndTime() + 1000 * 60 * 10);//往前10分钟怕有误差
        }
        if (streamLogDetailArg.getLogStartTime() == null) {
            streamLogDetailArg.setLogStartTime(streamLogDetailArg.getLogEndTime() - 1000 * 60 * 60 * 24);//只查一天的
        }
        Long startLogTime=streamLogDetailArg.getLogStartTime(), endLogTime=streamLogDetailArg.getLogEndTime();
        //只获取自己以及父log
        List<SyncLog> syncLogs = syncLogManager.listParentByLogId(streamLogDetailArg.getTenantId(), streamLogDetailArg.getSyncLogId(),startLogTime,endLogTime);
        Collections.sort(syncLogs, (o1, o2) -> {//根据创建时间升序
           return  o1.getCreateTime().getTime()>o2.getCreateTime().getTime()?1:-1;
        });

        Map<String, List<Object>> syncLogMap = Maps.newHashMap();//节点内升序排序
        List<LogDetailResult> logDetailResults = Lists.newArrayList();
        if (CollectionUtils.isEmpty(syncLogs)) {
            return Result.newError(ResultCodeEnum.SYNC_DATA_NOT_FOUND_INTERFACE_LOG);
        }
        for (SyncLog syncLog : syncLogs) {
            if(StringUtils.isNotBlank(filterNode)&&!filterNode.equals(syncLog.getType().getType())){
                continue;
            }
            BaseLinkWalkingService instance = SyncLogManagerFactory.getInstance(syncLog.getType());
            StreamLogQueryArg.SyncLogDataArg syncLogDataArg = new StreamLogQueryArg.SyncLogDataArg();
            syncLogDataArg.setId(String.valueOf(syncLog.getId()));
            syncLogDataArg.setDcId(streamLogDetailArg.getDcId());
            syncLogDataArg.setNodeEnum(syncLog.getType().getType());
            Result result = instance.queryLogDataById(streamLogDetailArg.getTenantId(), syncLogDataArg,lang,syncLog);
            if(result.getData()!=null){
                syncLogMap.computeIfAbsent(syncLog.getType().getType(), k -> Lists.newArrayList()).add(result.getData());
            }
        }
        if(StringUtils.isBlank(filterNode)){//目前不会筛选这些节点
            //再查询该集成流是否有集成流函数
            Result<FunctionQueryResult> beforeFunction = baseLinkService.queryListLogByType(streamLogDetailArg.getTenantId(), streamLogDetailArg.getStreamId(), SyncLogTypeEnum.PRE_FUNCTION.getType(),lang);
            if (ObjectUtils.isNotEmpty(beforeFunction.getData())) {
                beforeFunction.getData().setTraceId(streamLogDetailArg.getSyncLogId());
                boolean runStats = baseLinkService.queryFunctionRunStats(streamLogDetailArg.getTenantId(), beforeFunction.getData().getApiName(), streamLogDetailArg.getSyncLogId());
                beforeFunction.getData().setRunStatus(runStats);
                syncLogMap.put(SyncLogTypeEnum.PRE_FUNCTION.getType(), Lists.newArrayList(beforeFunction.getData()));
            }
            Result<FunctionQueryResult> midFunction = baseLinkService.queryListLogByType(streamLogDetailArg.getTenantId(), streamLogDetailArg.getStreamId(), SyncLogTypeEnum.MID_FUNCTION.getType(),lang);
            if (ObjectUtils.isNotEmpty(midFunction.getData())) {
                midFunction.getData().setTraceId(streamLogDetailArg.getSyncLogId());
                boolean runStats = baseLinkService.queryFunctionRunStats(streamLogDetailArg.getTenantId(), midFunction.getData().getApiName(), streamLogDetailArg.getSyncLogId());
                midFunction.getData().setRunStatus(runStats);
                syncLogMap.put(SyncLogTypeEnum.MID_FUNCTION.getType(), Lists.newArrayList(midFunction.getData()));
            }
            Result<FunctionQueryResult> afterFunction = baseLinkService.queryListLogByType(streamLogDetailArg.getTenantId(), streamLogDetailArg.getStreamId(), SyncLogTypeEnum.AFTER_FUNCTION.getType(),lang);
            if (ObjectUtils.isNotEmpty(afterFunction.getData())) {
                afterFunction.getData().setTraceId(streamLogDetailArg.getSyncLogId());
                boolean runStats = baseLinkService.queryFunctionRunStats(streamLogDetailArg.getTenantId(), afterFunction.getData().getApiName(), streamLogDetailArg.getSyncLogId());
                afterFunction.getData().setRunStatus(runStats);
                syncLogMap.put(SyncLogTypeEnum.AFTER_FUNCTION.getType(), Lists.newArrayList(afterFunction.getData()));
            }
            //查询是否有函数接管了接口（批量查询/单条查询/创建/更新等等）
            this.queryAplFunction(streamLogDetailArg.getTenantId(), streamLogDetailArg.getStreamId(),streamLogDetailArg.getSyncLogId(),syncLogMap, lang);
        }
        //补充sync_data
        if (StringUtils.isBlank(filterNode) || SyncLogTypeEnum.SYNC_DATA.getType().equals(filterNode)) {
            this.querySyncDataLog(streamLogDetailArg.getTenantId(), streamLogDetailArg.getSyncDataId(), streamLogDetailArg.getDcId(), syncLogMap);
        }
        List<SyncLogTypeEnum> typeByOrder = SyncLogTypeEnum.getTypeByOrder();
        for (SyncLogTypeEnum syncLogTypeEnum : typeByOrder) {
            List<Object> objects = syncLogMap.get(syncLogTypeEnum.getType());
            if (ObjectUtils.isNotEmpty(objects)) {
                LogDetailResult logDetailResult = new LogDetailResult();
                logDetailResult.setSyncLogType(syncLogTypeEnum.getType());
                logDetailResult.setData(objects);
                logDetailResults.add(logDetailResult);
            }
        }
        return Result.newSuccess(logDetailResults);
    }

    private void querySyncDataLog(String tenantId, String syncDataId, String dcId, Map<String, List<Object>> syncLogMap) {
        if(StringUtils.isNotBlank(syncDataId)){
            Result<SyncDataMappingGetDetailResult> syncDataDetail = adminSyncDataMappingService.getSyncDataDetail(tenantId, dcId, null, syncDataId);
            if(syncDataDetail!=null&&syncDataDetail.isSuccess()&&syncDataDetail.getData()!=null&&syncDataDetail.getData().isExist()){
                syncLogMap.put(SyncLogTypeEnum.SYNC_DATA.getType(), Lists.newArrayList(syncDataDetail.getData()));
            }
        }
    }

    private void queryAplFunction(String tenantId, String streamId, String logId, Map<String, List<Object>> syncLogMap, String lang) {
        Result<SyncPloyDetailResult> byIdWithCache = adminSyncPloyDetailService.getByIdWithCache(tenantId, streamId,lang);
        if (ObjectUtils.isEmpty(byIdWithCache.getData())) {
            return ;
        }
        List<ErpObjInterfaceUrlEnum> interfaceUrlEnums;
        String erpDcId,erpObjApiName;
        SyncLogTypeEnum syncLogTypeEnum;
        SyncPloyDetailResult syncPloyDetailResult = byIdWithCache.getData();
        if(TenantType.ERP.equals(syncPloyDetailResult.getSourceTenantType())){//erp->crm，获取查询接口apl
            interfaceUrlEnums = Lists.newArrayList(ErpObjInterfaceUrlEnum.queryMasterBatch, ErpObjInterfaceUrlEnum.queryMasterById,ErpObjInterfaceUrlEnum.queryInvalid);
            erpDcId = syncPloyDetailResult.getSourceDataCenterId();
            erpObjApiName=syncPloyDetailResult.getSourceObjectApiName();
            syncLogTypeEnum=SyncLogTypeEnum.READ_APL;
            if(!syncLogMap.containsKey(SyncLogTypeEnum.READ.getType())){//没有包含读接口日志，则不查询apl
                return;
            }
        }else{
            interfaceUrlEnums = Lists.newArrayList(ErpObjInterfaceUrlEnum.create, ErpObjInterfaceUrlEnum.createDetail,ErpObjInterfaceUrlEnum.update,
                    ErpObjInterfaceUrlEnum.updateDetail, ErpObjInterfaceUrlEnum.invalid,ErpObjInterfaceUrlEnum.invalidDetail);
            erpDcId=syncPloyDetailResult.getDestDataCenterId();
            erpObjApiName=syncPloyDetailResult.getDestObjectApiName();
            syncLogTypeEnum=SyncLogTypeEnum.WRITE_APL;
            if(!syncLogMap.containsKey(SyncLogTypeEnum.WRITE.getType())){//没有包含写接口日志，则不查询apl
                return;
            }
        }
        String realApiName = idFieldConvertManager.getRealObjApiName(tenantId, erpObjApiName);
        HeaderObj headerObj = new HeaderObj(Integer.valueOf(tenantId), 10000);
        for(ErpObjInterfaceUrlEnum interfaceUrlEnum:interfaceUrlEnums){
            ErpObjGroovyEntity erpObjGroovyEntity = erpObjGroovyDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByTenantIdAndApiNameAndUrl(tenantId,erpDcId,
                    realApiName, interfaceUrlEnum.name());
            if(erpObjGroovyEntity!=null){
                FunctionServiceFindArg findArg = new FunctionServiceFindArg();
                findArg.setApiName(erpObjGroovyEntity.getFuncApiName());
                findArg.setBindingObjectApiName(CustomFunctionConstant.BINDING_OBJECT_API_NAME);
                Result<FunctionResult<FunctionServiceFindResult>> result = customFunctionService.find(headerObj, findArg);
                if (result.isSuccess()&&result.getData()!=null&&result.getData().isSuccess()&&result.getData().getResult()!=null
                        &&result.getData().getResult().getFunction()!=null) {
                    FunctionServiceFindData findData = result.getData().getResult().getFunction();
                    String functionName = findData.getFunctionName();
                    FunctionQueryResult functionQueryResult = new FunctionQueryResult();
                    functionQueryResult.setApiName(erpObjGroovyEntity.getFuncApiName());
                    functionQueryResult.setFunctionName(functionName);
                    functionQueryResult.setNameSpace(findData.getNameSpace());
                    functionQueryResult.setReturnType("Map");
                    boolean runStats = baseLinkService.queryFunctionRunStats(tenantId, erpObjGroovyEntity.getFuncApiName(), logId);
                    functionQueryResult.setRunStatus(runStats);
                    if(runStats) {
                        if(syncLogMap.containsKey(syncLogTypeEnum.getType())){
                            syncLogMap.get(syncLogTypeEnum.getType()).add(functionQueryResult);
                        }else{
                            syncLogMap.put(syncLogTypeEnum.getType(), Lists.newArrayList(functionQueryResult));
                        }
                    }
                }
            }
        }

    }


}
