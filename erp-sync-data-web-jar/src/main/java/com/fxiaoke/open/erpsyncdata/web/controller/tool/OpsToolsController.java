package com.fxiaoke.open.erpsyncdata.web.controller.tool;

import com.fxiaoke.open.erpsyncdata.admin.arg.MigrateProductCateArg;
import com.fxiaoke.open.erpsyncdata.admin.service.MigratePloyService;
import com.fxiaoke.open.erpsyncdata.admin.utils.DataCenterCookieUtils;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 运维工具,供实施使用
 *
 * <AUTHOR> (^_−)☆
 * @date 2022/4/24
 */
@Slf4j
@RestController("OpsToolsController")
@RequestMapping("erp/syncdata")
public class OpsToolsController extends AsyncSupportController {
    @Autowired
    private MigratePloyService migratePloyService;

    @ApiOperation(value = "迁移产品分类，get，post都可以使用")
    @RequestMapping("syncPloy/migrateProductCate/{erpProductCateObjApiName:.+}")
    public Result<String> migrateProductCate(@PathVariable(required = false) String erpProductCateObjApiName,
                                             @RequestBody(required = false) MigrateProductCateArg migrateProductCateArg,
                                             @RequestHeader(value = I18NStringManager.X_FS_LOCALE,required = false) String lang,
                                             HttpServletRequest request) {
        if (StringUtils.isNotBlank("erpProductCateObjApiName")) {
            //优先取path的
            migrateProductCateArg = new MigrateProductCateArg();
            migrateProductCateArg.setErpProductCateObjApiName(erpProductCateObjApiName);
        } else {
            if (migrateProductCateArg == null) {
                return Result.newErrorByI18N(I18NStringEnum.s807.getI18nValue(), I18NStringEnum.s807.getI18nKey(),null);
            }
        }
        String loginUserTenantId = getLoginUserTenantId();
        migrateProductCateArg.setTenantId(loginUserTenantId);
        migrateProductCateArg.setDcId(DataCenterCookieUtils.getDataCenterIdByCookie(loginUserTenantId, request.getCookies()));
        Result<String> stringResult = migratePloyService.migrateProductCate(migrateProductCateArg,lang);
        return stringResult;
    }
}
