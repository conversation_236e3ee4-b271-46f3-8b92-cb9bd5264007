package com.fxiaoke.open.oasyncdata.db.util;

/**
 * <AUTHOR>
 * @date 2019/11/6
 **/
public class NumUtils {

    /**
     * 过长的long类型转换成32进制字符串，缩短长度
     *
     * @param num
     * @return
     */
    public static String longTo32Str(Long num) {
        return Long.toUnsignedString(num, 32);
    }

    public static long recLongFrom32Str(String str) {
        return Long.valueOf(str, 32);
    }

    public static void main(String[] args) {
        System.out.println(longTo32Str(34562L));

        System.out.println(recLongFrom32Str("1ekglro9r"));
    }
}
