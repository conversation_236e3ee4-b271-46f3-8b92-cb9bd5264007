package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.ErpOrganizationObj;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * erp组织机构对象处理
 * <AUTHOR> (^_−)☆
 * @date 2020/11/20
 */
@Service
@Slf4j
public class ErpOrganizationObjManager {
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;


    /**
     * 获取数据中心所有对接ERP组织机构对象数据
     * @param tenantIdStr
     * @return
     */
    @Cached(expire = 120,cacheType = CacheType.LOCAL)
    public List<ErpOrganizationObj> queryDcErpOrganizationObj(String tenantIdStr,String dcId){
        Integer tenantId = Integer.valueOf(tenantIdStr);
        HeaderObj headerObj = HeaderObj.newInstance(tenantId,-10000);
        String erpOrgObj = ObjectApiNameEnum.FS_ERP_ORGANIZATION_OBJ.getObjApiName();
        ControllerListArg listArg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1000);
        listArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        if (!dataListRes.isSuccess()){
            log.warn("list erp org obj failed,tenantId:{},res:{}",tenantId,dataListRes);
            throw new ErpSyncDataException(ResultCodeEnum.LIST_ERP_ORGOBJ_ERROR,tenantIdStr);
        }
        Set<String> erpOrgFsId = getErpOrgFsId(tenantIdStr, dcId);
        List<ErpOrganizationObj> resultList = dataListRes.getData().getDataList().stream()
                .map(ErpOrganizationObj::convert).filter(Objects::nonNull)
                .filter(v->erpOrgFsId.contains(v.getId())).collect(Collectors.toList());
        return resultList;
    }


    private Set<String> getErpOrgFsId(String tenantId, String dataCenterId) {
        ErpObjectRelationshipEntity query = new ErpObjectRelationshipEntity();
        query.setTenantId(tenantId);
        query.setDataCenterId(dataCenterId);
        query.setErpRealObjectApiname(ObjectApiNameEnum.K3CLOUD_ORG_ORGANIZATIONS.getObjApiName());
        List<ErpObjectRelationshipEntity> erpObjectRelationshipEntities = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryList(query);
        String erpOrgFakeObjApiName = erpObjectRelationshipEntities.stream().map(ErpObjectRelationshipEntity::getErpSplitObjectApiname).findFirst().orElse(null);
        List<SyncDataMappingsEntity> syncDataMappingsEntities = syncDataMappingsDao.setTenantId(tenantId).listBySourceAndDestObjectApiName(tenantId, erpOrgFakeObjApiName, ObjectApiNameEnum.FS_ERP_ORGANIZATION_OBJ.getObjApiName());
        Set<String> erpOrgFsId = syncDataMappingsEntities.stream().map(SyncDataMappingsEntity::getDestDataId).collect(Collectors.toSet());
        return erpOrgFsId;
    }
}
