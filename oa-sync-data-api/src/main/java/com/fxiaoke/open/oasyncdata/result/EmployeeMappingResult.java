package com.fxiaoke.open.oasyncdata.result;


import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:35 2020/8/18
 * @Desc:
 */
@Data
@ApiModel
public class EmployeeMappingResult implements Serializable {
    @ApiModelProperty("数据id")
    public String id; //
    @ApiModelProperty("数据中心id")
    private String currentDcId;
    @ApiModelProperty("渠道，ERP_K3CLOUD,ERP_SAP,ERP_U8")
    public ErpChannelEnum channel; //渠道，k3,sap,u8，其他
    @ApiModelProperty("fs员工id")
    public Integer fsEmployeeId ;
    @ApiModelProperty("fs员工name")
    public String fsEmployeeName;
    @ApiModelProperty("erp员工id")
    public String erpEmployeeId ;
    @ApiModelProperty("erp员工name")
    public String erpEmployeeName;
    @ApiModelProperty("erp员工手机号")
    public String erpEmployeePhone ;
    @ApiModelProperty("erp员工账号")
    public String erpEmployeeAccount;
    @ApiModelProperty("fs职员状态")
    private Integer fsEmployeeStatus;
    @ApiModelProperty("fs职员电话")
    private String fsEmployeePhone;
    @ApiModelProperty("人员账号")
    private String erpUserAccount;
    @ApiModelProperty("人员id")
    private String erpUserId;
    @ApiModelProperty("人员帐号描述")
    private String erpUserAccountMsg;
}
