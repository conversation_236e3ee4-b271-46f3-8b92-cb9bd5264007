package com.fxiaoke.open.erpsyncdata.dbproxy.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailSnapshotDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailSnapshotEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/27
 */
@Component
public class SyncDataMappingsManager {
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private AdminSyncPloyDetailSnapshotDao adminSyncPloyDetailSnapshotDao;
    @Autowired
    private SyncDataFixDao adminSyncDataDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;


    /**
     * 是否存在映射，并已经创建
     * 当结果为true时缓存，缓存两分钟
     */
    @Cached(cacheType = CacheType.LOCAL,expire = 2,timeUnit = TimeUnit.MINUTES,postCondition = "#result")
    public boolean exitMappingAndCreated(String tenantId, String sourceObjectApiName, String sourceDataId, String destObjectApiName) {
        Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = getMapping2Way(tenantId, sourceObjectApiName, sourceDataId, destObjectApiName);
        if (mapping2Way.getLeft()!=null){
            return mapping2Way.getLeft().getIsCreated();
        }
        if (mapping2Way.getRight()!=null){
            return mapping2Way.getRight().getIsCreated();
        }
        return false;
    }

    /**
     * 获取双向的mapping
     *
     * @return pair 正向mapping，反向mapping,都可能为null
     */
    public Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> getMapping2Way(String tenantId, String sourceObjectApiName,
                                                                               String sourceDataId, String destObjectApiName) {
        SyncDataMappingsEntity sourceMapping = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, sourceObjectApiName, destObjectApiName, sourceDataId);
        SyncDataMappingsEntity destMapping = syncDataMappingsDao.setTenantId(tenantId).getByDestData(tenantId, destObjectApiName, sourceObjectApiName, sourceDataId);
        return Pair.of(sourceMapping, destMapping);
    }
    public SyncDataMappingsEntity findMappingBySyncDataId(String tenantId,String syncDataId) {
        SyncDataEntity syncDataEntity = adminSyncDataDao.setTenantId(tenantId).getSimple(tenantId,syncDataId);
        SyncDataMappingsEntity byUninKey = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId, syncDataEntity.getSourceObjectApiName(), syncDataEntity.getDestObjectApiName(), syncDataEntity.getSourceDataId());
        return byUninKey;
    }

    public Map<String, Boolean> listCreatedMapping(String tenantId, String sourceObjectApiName, List<String> sourceDataIds, String destObjectApiName) {
        List<List<String>> idsList = ListUtils.partition(sourceDataIds, 5);//调小，为了大概率能走索引
        Map<String, Boolean> result = Maps.newHashMap();
        for (List<String> ids : idsList) {
            partSetCreatedMapping(tenantId, sourceObjectApiName, ids, destObjectApiName, result);
        }
        return result;
    }

    private void partSetCreatedMapping(String tenantId, String sourceObjectApiName, List<String> sourceDataIds, String destObjectApiName, Map<String, Boolean> result) {
        List<SyncDataMappingsEntity> sourceMapping = syncDataMappingsDao.setTenantId(tenantId).listCreatedBySourceDataIds(tenantId, sourceObjectApiName, destObjectApiName, sourceDataIds);
        Map<String, Boolean> sourceResult = sourceMapping.stream()
                .collect(Collectors.toMap(SyncDataMappingsEntity::getSourceDataId, SyncDataMappingsEntity::getIsCreated, (v, u) -> v));
        List<SyncDataMappingsEntity> destMapping = syncDataMappingsDao.setTenantId(tenantId).listCreatedByDestDataIds(tenantId, destObjectApiName, sourceObjectApiName, sourceDataIds);
        Map<String, Boolean> destResult = destMapping.stream()
                .collect(Collectors.toMap(SyncDataMappingsEntity::getDestDataId, SyncDataMappingsEntity::getIsCreated, (v, u) -> v));
        for (String id : sourceDataIds) {
            if (!sourceResult.containsKey(id)
                    && !destResult.containsKey(id)) {
                result.put(id, false);
            } else if (sourceResult.containsKey(id)) {
                result.put(id, sourceResult.get(id));
            } else {
                result.put(id, destResult.get(id));
            }
        }
    }


    public void insertIfAbsentByDestApiName(String tenantId, String sourceId, String sourceName,String destObjApiName, String destId, String destName) {
        List<SyncPloyDetailSnapshotEntity> snapshots = adminSyncPloyDetailSnapshotDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .listNewestByDestTenantIdAndDestObjectApiName(tenantId, destObjApiName,
                        SyncPloyDetailStatusEnum.ENABLE.getStatus());
        if (CollectionUtils.isEmpty(snapshots)) {
            return;
        }
        SyncPloyDetailSnapshotEntity snapshot = snapshots.get(0);
        SyncDataMappingsEntity entity = syncDataMappingsDao.setTenantId(tenantId).getBySourceData(tenantId,
                snapshot.getSourceObjectApiName(), destObjApiName, sourceId);
        if (entity != null) {
            return;
        }
        Long now = System.currentTimeMillis();
        SyncDataMappingsEntity syncDataMappingsEntity = new SyncDataMappingsEntity();
        syncDataMappingsEntity.setId(idGenerator.get(tenantId,sourceId));
        syncDataMappingsEntity.setSourceTenantId(tenantId);
        syncDataMappingsEntity.setSourceObjectApiName(snapshot.getSourceObjectApiName());
        syncDataMappingsEntity.setSourceDataId(sourceId);
        syncDataMappingsEntity.setSourceDataName(sourceName);
        syncDataMappingsEntity.setDestObjectApiName(destObjApiName);
        syncDataMappingsEntity.setDestTenantId(tenantId);
        syncDataMappingsEntity.setDestDataId(destId);
        syncDataMappingsEntity.setDestDataName(destName);
        syncDataMappingsEntity.setIsCreated(true);
        syncDataMappingsEntity.setIsDeleted(false);
        syncDataMappingsEntity.setRemark(i18NStringManager.getByEi(I18NStringEnum.s1151,tenantId));
        syncDataMappingsEntity.setCreateTime(now);
        syncDataMappingsEntity.setUpdateTime(now);
        syncDataMappingsEntity.setLastSyncDataId("0");
        syncDataMappingsEntity.setLastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
        syncDataMappingsEntity.setLastSourceDataVserion(1L);
        syncDataMappingsEntity.setTenantId(tenantId);
        syncDataMappingsDao.setTenantId(tenantId).insertIgnore(syncDataMappingsEntity);
    }

    public List<SyncDataMappingsEntity> listByObjApiName(String tenantId,String erpObjApiName,int limit,int offset) {
        List<SyncDataMappingsEntity> entityList = syncDataMappingsDao.setTenantId(tenantId)
                .listByObjApiName(tenantId, erpObjApiName, limit, offset);
        return entityList;
    }

    public SyncDataMappingsEntity getByDestData2(String tenantId,
                                         String destObjectApiName,
                                         String destDataId) {
        return syncDataMappingsDao.getByDestData2(tenantId, destObjectApiName, destDataId);
    }
}
