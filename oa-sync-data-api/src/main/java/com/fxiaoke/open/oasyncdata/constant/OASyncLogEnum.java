package com.fxiaoke.open.oasyncdata.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Created by liuyc
 */
@Getter
@ToString
@AllArgsConstructor
public enum OASyncLogEnum {
    EXCEPTION("0","执行异常", I18NStringEnum.s1215.getI18nKey()),
    SUCCESS("1","同步成功", I18NStringEnum.s632.getI18nKey()),
    FAIL("2","同步失败", I18NStringEnum.s634.getI18nKey()),
    SYNC("3","同步中", I18NStringEnum.s667.getI18nKey()),
    NOT_SYNC("4","无需同步", I18NStringEnum.s1216.getI18nKey()),
    ;

    /**
     * 对象名称
     */
    private final String syncType;
    /**
     * 对象ApiName
     */
    private final String description;
    private final String i18nKey;

    public String getDescription(I18NStringManager i18NStringManager, String lang, String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,description);
    }
}
