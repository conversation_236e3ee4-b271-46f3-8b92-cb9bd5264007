package com.fxiaoke.open.erpsyncdata.web.controller.inner
//package com.fxiaoke.open.erpsyncdata.web.controller.inner
//
//import com.fxiaoke.open.erpsyncdata.BaseDbTest
//import com.fxiaoke.open.erpsyncdata.admin.model.superadmin.GrafanaAlert
//import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil
//import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService
//import groovy.util.logging.Slf4j
//import org.junit.Before
//import org.junit.Ignore
//import org.junit.Test
//import org.springframework.beans.factory.annotation.Autowired
//
///**
// *
// * <AUTHOR> (^_−)☆
// * @date 2022-9-29
// */
//@Ignore
//@Slf4j
//class WebhookControllerSpringTest extends BaseDbTest {
//    @Autowired
//    private NotificationService notificationService;
//
//    private WebhookController webhookController
//
//
//    def "测试异常告警"() {
//        when:
//        WebhookController controller = new WebhookController()
//        GrafanaAlert grafanaAlert = JacksonUtil.fromJson(alertMsg, GrafanaAlert.class)
//        def msg = controller.buildAdvanceTexts(grafanaAlert)
//        log.info("result:{}", msg)
//        then:
//        msg != null
//    }
//
//
//    @Test
//    void "测试限制发送时间段"() {
//        when:
//        GrafanaAlert grafanaAlert = JacksonUtil.fromJson(alertMsg, GrafanaAlert.class)
//        def result = webhookController.grafanaAlert(grafanaAlert)
//        log.info("result:{}", result)
//        then:
//        result.isSuccess()
//    }
//
//    @Test
//    public void testResolved() {
//        when:
//        GrafanaAlert grafanaAlert = JacksonUtil.fromJson(resolvedMsg, GrafanaAlert.class)
//        def result = webhookController.grafanaAlert(grafanaAlert)
//        log.info("result:{}", result)
//        then:
//        result.isSuccess()
//    }
//
//    @Before
//    void setup() {
//        webhookController = new WebhookController()
//        webhookController.setNotificationService(notificationService)
//    }
//
//    String resolvedMsg = """
//{
//    "receiver": "erp-sync-data-alert",
//    "status": "resolved",
//    "alerts": [
//        {
//            "status": "resolved",
//            "labels": {
//                "alertname": "pg负载高于警戒线",
//                "grafana_folder": "集成平台",
//                "module": "erp-sync-data"
//            },
//            "annotations": {},
//            "startsAt": 1676619180000,
//            "endsAt": 1676619240000,
//            "generatorURL": "http://grafana.foneshare.cn/alerting/grafana/WAKorI44z/view?orgId=1",
//            "fingerprint": "1bcf06f8609296e0",
//            "silenceURL": "http://grafana.foneshare.cn/alerting/silence/new?alertmanager=grafana&matcher=alertname%3Dpg%E8%B4%9F%E8%BD%BD%E9%AB%98%E4%BA%8E%E8%AD%A6%E6%88%92%E7%BA%BF&matcher=grafana_folder%3D%E9%9B%86%E6%88%90%E5%B9%B3%E5%8F%B0&matcher=module%3Derp-sync-data",
//            "dashboardURL": "http://grafana.foneshare.cn/d/-a-Qbk4Vk?orgId=1",
//            "panelURL": "http://grafana.foneshare.cn/d/-a-Qbk4Vk?orgId=1&viewPanel=123",
//            "valueString": ""
//        }
//    ],
//    "groupLabels": {},
//    "commonLabels": {
//        "alertname": "pg负载高于警戒线",
//        "grafana_folder": "集成平台",
//        "module": "erp-sync-data"
//    },
//    "commonAnnotations": {},
//    "externalURL": "http://grafana.foneshare.cn/",
//    "version": "1",
//    "groupKey": "{}/{module=\\"erp-sync-data\\"}:{}",
//    "truncatedAlerts": 0,
//    "orgId": 1,
//    "title": "[RESOLVED]  (pg负载高于警戒线 集成平台 erp-sync-data)",
//    "state": "ok",
//    "message": "**Resolved**\\n\\nValue: [no value]\\nLabels:\\n - alertname = pg负载高于警戒线\\n - grafana_folder = 集成平台\\n - module = erp-sync-data\\nAnnotations:\\nSource: http://grafana.foneshare.cn/alerting/grafana/WAKorI44z/view?orgId=1\\nSilence: http://grafana.foneshare.cn/alerting/silence/new?alertmanager=grafana&matcher=alertname%3Dpg%E8%B4%9F%E8%BD%BD%E9%AB%98%E4%BA%8E%E8%AD%A6%E6%88%92%E7%BA%BF&matcher=grafana_folder%3D%E9%9B%86%E6%88%90%E5%B9%B3%E5%8F%B0&matcher=module%3Derp-sync-data\\nDashboard: http://grafana.foneshare.cn/d/-a-Qbk4Vk?orgId=1\\nPanel: http://grafana.foneshare.cn/d/-a-Qbk4Vk?orgId=1&viewPanel=123\\n",
//    "statusEmoji": "[OK]【已恢复】"
//}
//"""
//
//    String alertMsg = "{\n" +
//            "    \"receiver\": \"xjyTest\",\n" +
//            "    \"status\": \"firing\",\n" +
//            "    \"alerts\": [\n" +
//            "        {\n" +
//            "            \"status\": \"firing\",\n" +
//            "            \"labels\": {\n" +
//            "                \"alertname\": \"同步条数低于正常值\",\n" +
//            "                \"grafana_folder\": \"alert\",\n" +
//            "                \"module\": \"erpSyncData\"\n" +
//            "            },\n" +
//            "            \"annotations\": {},\n" +
//            "            \"startsAt\": 1664434380000,\n" +
//            "            \"endsAt\": -62135769600000,\n" +
//            "            \"fingerprint\": \"7684300251e84da5\",\n" +
//            "            \"valueString\": \"[ var='B0' metric='value' labels={appName=fs-erp-sync-data-gray} value=0 ], [ var='B1' metric='value' labels={appName=fs-erp-sync-data} value=0 ]\",\n" +
//            "            \"generatorURL\": \"http://grafana.firstshare.cn/alerting/grafana/nsJXY444k/view\",\n" +
//            "            \"silenceURL\": \"http://grafana.firstshare.cn/alerting/silence/new?alertmanager=grafana&matcher=alertname%3D%E5%90%8C%E6%AD%A5%E6%9D%A1%E6%95%B0%E4%BD%8E%E4%BA%8E%E6%AD%A3%E5%B8%B8%E5%80%BC&matcher=grafana_folder%3Dalert&matcher=module%3DerpSyncData\",\n" +
//            "            \"dashboardURL\": \"http://grafana.firstshare.cn/d/-XsuCfI7z\",\n" +
//            "            \"panelURL\": \"http://grafana.firstshare.cn/d/-XsuCfI7z?viewPanel=4\"\n" +
//            "        }\n" +
//            "    ],\n" +
//            "    \"groupLabels\": {\n" +
//            "        \"alertname\": \"同步条数低于正常值\"\n" +
//            "    },\n" +
//            "    \"commonLabels\": {\n" +
//            "        \"alertname\": \"同步条数低于正常值\",\n" +
//            "        \"grafana_folder\": \"alert\",\n" +
//            "        \"module\": \"erpSyncData\"\n" +
//            "    },\n" +
//            "    \"commonAnnotations\": {\n" +
//            "        \"limitTime\": \"09:30-20:30\"\n" +
//            "    },\n" +
//            "    \"version\": \"1\",\n" +
//            "    \"groupKey\": \"{}/{module=\\\"erpSyncData\\\"}:{alertname=\\\"同步条数低于正常值\\\"}\",\n" +
//            "    \"truncatedAlerts\": 0,\n" +
//            "    \"orgId\": 1,\n" +
//            "    \"title\": \"[FIRING:1] 同步条数低于正常值 (alert erpSyncData)\",\n" +
//            "    \"state\": \"alerting\",\n" +
//            "    \"message\": \"**Firing**\\n\\nValue: [ var='B0' metric='value' labels={appName=fs-erp-sync-data-gray} value=0 ], [ var='B1' metric='value' labels={appName=fs-erp-sync-data} value=0 ]\\nLabels:\\n - alertname = 同步条数低于正常值\\n - grafana_folder = alert\\n - module = erpSyncData\\nAnnotations:\\nSource: http://grafana.firstshare.cn/alerting/grafana/nsJXY444k/view\\nSilence: http://grafana.firstshare.cn/alerting/silence/new?alertmanager=grafana&matcher=alertname%3D%E5%90%8C%E6%AD%A5%E6%9D%A1%E6%95%B0%E4%BD%8E%E4%BA%8E%E6%AD%A3%E5%B8%B8%E5%80%BC&matcher=grafana_folder%3Dalert&matcher=module%3DerpSyncData\\nDashboard: http://grafana.firstshare.cn/d/-XsuCfI7z\\nPanel: http://grafana.firstshare.cn/d/-XsuCfI7z?viewPanel=4\\n\",\n" +
//            "    \"statusEmoji\": \"[骷髅]\",\n" +
//            "    \"externalURL\": \"http://grafana.firstshare.cn/\"\n" +
//            "}"
//}
