package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import cn.hutool.core.lang.Opt;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.GetConfigValueByKeyArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.result.GetConfigValueByKeyResult;
import com.fxiaoke.crmrestapi.service.SkuSpuService;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetAllErpObjApiType;
import com.fxiaoke.open.erpsyncdata.admin.arg.GetApiFormatArg;
import com.fxiaoke.open.erpsyncdata.admin.manager.AplConfigManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.ErpObjDataPushManager;
import com.fxiaoke.open.erpsyncdata.admin.manager.SyncPloyDetailAdminManager;
import com.fxiaoke.open.erpsyncdata.admin.model.ErpObjApiNameModel;
import com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate.K3UltimateSubscribeEventModel;
import com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate.K3UltimateTokenModel;
import com.fxiaoke.open.erpsyncdata.admin.result.ApiFormatResult2;
import com.fxiaoke.open.erpsyncdata.admin.service.*;
import com.fxiaoke.open.erpsyncdata.admin.utils.ChannelUrlUtils;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.SfaApiManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.model.StandardData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.impl.ErpCustomInterfaceServiceImpl;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.K3UltimateEventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TokenVersionEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpCustomInterfaceDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpK3UltimateTokenDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.*;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.K3UltimateEventConfigModel;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NHeaderObj;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SapConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.StandardConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date: 18:03 2020/9/12
 * @Desc:
 */
@Service
@Slf4j
public class InterfaceFormatServiceImpl implements InterfaceFormatService {

    @Autowired
    private ErpObjectFieldsService erpObjectFieldsService;
    @Autowired
    private SyncPloyDetailAdminManager syncPloyDetailAdminManager;
    @Autowired
    private SkuSpuService skuSpuService;
    @Autowired
    private SfaApiManager sfaApiManager;
    @Autowired
    private ErpCustomInterfaceDao erpCustomInterfaceDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private ConnectInfoService connectInfoService;
    @Autowired
    private ErpObjCustomFunctionService erpObjCustomFunctionService;
    @Autowired
    private ErpObjDataPushManager erpObjDataPushManager;
    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;
    @Autowired
    private ErpObjManager erpObjManager;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private ErpObjectDao erpObjectDao;
    @Autowired
    private ErpObjInterfaceCheckedManager erpObjInterfaceCheckedManager;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private K3UltimateApiTemplateManager k3UltimateApiTemplateManager;
    @Autowired
    private ErpK3UltimateTokenDao erpK3UltimateTokenDao;
    @Autowired
    private K3UltimateEventSubscribeService k3UltimateEventSubscribeService;
    @Autowired
    private PushIdentifyManager pushIdentifyManager;
    @Autowired
    private AplConfigManager aplConfigManager;

    private static String postAPIPrefix = "POST";
    private static String getAPIPrefix = "GET";

    /**
     * 格式化输出JSON字符串
     *
     * @return 格式化后的JSON字符串
     */
    private static String toPrettyFormat(String json) {
        JsonParser jsonParser = new JsonParser();
        JsonObject jsonObject = jsonParser.parse(json).getAsJsonObject();
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        return gson.toJson(jsonObject);
    }


    @Override
    public Result<ErpInterfaceFormatResult> getAddDataInterfaceFormat(String tenantId,
                                                                      Integer userId,
                                                                      ErpObjectDescResult arg,
                                                                      String dataCenterId,
                                                                      StandardData standardData,
                                                                      ConnectInfoResult connInfoResult,
                                                                      String lang,
                                                                      Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy) {
        ErpInterfaceFormatResult erpInterfaceFormatResult = new ErpInterfaceFormatResult();
        StandardData addArg =standardData;
        Map<String,Object> addResult = getErrorCode(connInfoResult,lang,tenantId);
        List<ApiFormatResult.ApiFieldMessage> defaultArgs=Lists.newArrayList();
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("objAPIName").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s277,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("masterFieldVal").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s278,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("detailFieldVals").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s279,lang,tenantId)).build());
        erpInterfaceFormatResult.setRequestField(defaultArgs);

        List<ApiFormatResult.ApiFieldMessage> defaultResults=Lists.newArrayList();
        for(String field:addResult.keySet()){
            defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName(field).fieldType("String").fieldDesc(String.valueOf(addResult.get(field))).build());
        }
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s280,lang,tenantId)).build());
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.masterDataId").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s281,lang,tenantId)).build());
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.detailDataIds").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s282,lang,tenantId)).build());
        if (connInfoResult != null && url2Groovy != null) {
            Boolean supportOtherFields = ErpChannelEnum.STANDARD_CHANNEL.equals(connInfoResult.getChannel())
                    || ErpChannelEnum.ERP_SAP.equals(connInfoResult.getChannel())
                    || url2Groovy.containsKey(ErpObjInterfaceUrlEnum.create)
                    || url2Groovy.containsKey(ErpObjInterfaceUrlEnum.update);
            if (supportOtherFields) {
                defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.masterReturnData").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s5101, lang, tenantId)).build());
                defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.detailReturnData").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s5102, lang, tenantId)).build());

            }
        }
        erpInterfaceFormatResult.setResponseField(defaultResults);

        addResult.put("data", getAddDataInterfaceResult(tenantId, userId, addArg,lang,connInfoResult,url2Groovy));
        erpInterfaceFormatResult.setRequest(new Gson().toJson(addArg));
        erpInterfaceFormatResult.setResponse(new Gson().toJson(addResult));

        log.info("trace getAddDataInterfaceFormat, result: {}", erpInterfaceFormatResult);

        erpInterfaceFormatResult.setRequest(toPrettyFormat(erpInterfaceFormatResult.getRequest()));
        erpInterfaceFormatResult.setResponse(toPrettyFormat(erpInterfaceFormatResult.getResponse()));
        log.info("trace getAddDataInterfaceFormat, format pretty result: {}", erpInterfaceFormatResult);
        return Result.newSuccess(erpInterfaceFormatResult);
    }

    private Map<String, Object> getErrorCode(ConnectInfoResult connInfoResult,String lang,String tenantId) {
        Map<String,Object> result = Maps.newLinkedHashMap();
        switch (connInfoResult.getChannel()) {
            case ERP_SAP:
                SapConnectParam sapConnectParam = connInfoResult.getConnectParams().getSap();
                result.put(sapConnectParam.getResultFormat().getCodeName(), i18NStringManager.get(I18NStringEnum.s283,lang,tenantId));
                result.put(sapConnectParam.getResultFormat().getMsgName(), i18NStringManager.get(I18NStringEnum.s284,lang,tenantId));
                break;
            case STANDARD_CHANNEL:
                StandardConnectParam standardConnectParam = connInfoResult.getConnectParams().getStandard();
                StandardConnectParam.ResultFormat resultFormat = standardConnectParam.getResultFormatOrDefault();
                result.put(resultFormat.getCodeName(), i18NStringManager.get(I18NStringEnum.s283,lang,tenantId));
                result.put(resultFormat.getMsgName(), i18NStringManager.get(I18NStringEnum.s284,lang,tenantId));
                break;

            default:
                result.put("code", i18NStringManager.get(I18NStringEnum.s283,lang,tenantId));
                result.put("message", i18NStringManager.get(I18NStringEnum.s284,lang,tenantId));
        }
        return result;
    }

    public ErpIdResult getAddDataInterfaceResult(String tenantId, Integer userId, StandardData arg, String lang,ConnectInfoResult connInfoResult,
                                                 Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy) {
        ErpIdResult erpIdResult = new ErpIdResult();
        erpIdResult.setMasterDataId(i18NStringManager.get(I18NStringEnum.s285,lang,tenantId));
        erpIdResult.setDetailDataIds(Maps.newLinkedHashMap());
        Map<String, List<ObjectData>> detailData = arg.getDetailFieldVals();
        if (detailData != null) {
            for (String detailApiName : detailData.keySet()) {
                erpIdResult.getDetailDataIds().put(detailApiName, Lists.newArrayList(i18NStringManager.get(I18NStringEnum.s286,lang,tenantId)));
            }
        }
        if (connInfoResult != null && url2Groovy != null) {
            Boolean supportOtherFields = ErpChannelEnum.STANDARD_CHANNEL.equals(connInfoResult.getChannel())
                    || ErpChannelEnum.ERP_SAP.equals(connInfoResult.getChannel())
                    || url2Groovy.containsKey(ErpObjInterfaceUrlEnum.create)
                    || url2Groovy.containsKey(ErpObjInterfaceUrlEnum.update);
            if (supportOtherFields) {
                erpIdResult.setMasterReturnData(Maps.newHashMap());
                erpIdResult.setDetailReturnData(Maps.newHashMap());
                Map<String, Map<String, Object>> map = Maps.newHashMap();
                map.put("detailId", Maps.newHashMap());
                for (String detailApiName : detailData.keySet()) {
                    erpIdResult.getDetailReturnData().put(detailApiName, map);
                }
            }
        }
        return erpIdResult;
    }

    @Override
    public Result<ErpInterfaceFormatResult> getQueryDataListInterfaceFormat(String tenantId,
                                                                            Integer userId,
                                                                            ErpObjectDescResult arg,
                                                                            String dataCenterId,
                                                                            StandardData standardData,
                                                                            ConnectInfoResult connectInfoResult,
                                                                            String lang) {
        ErpInterfaceFormatResult erpInterfaceFormatResult = new ErpInterfaceFormatResult();
        List<ApiFormatResult.ApiFieldMessage> defaultArgs=Lists.newArrayList();
        String queryArg = getQueryDataListInterfaceArg(arg,defaultArgs,lang,tenantId);
        erpInterfaceFormatResult.setRequestField(defaultArgs);
        List<ApiFormatResult.ApiFieldMessage> defaultResults=Lists.newArrayList();
        Map queryResult = getQueryDataListInterfaceResult(standardData,connectInfoResult,defaultResults,lang,tenantId);
        erpInterfaceFormatResult.setResponseField(defaultResults);
        erpInterfaceFormatResult.setRequest(queryArg);
        erpInterfaceFormatResult.setResponse(new Gson().toJson(queryResult));
        erpInterfaceFormatResult.setResponse(toPrettyFormat(erpInterfaceFormatResult.getResponse()));
        log.info("trace getAddDataInterfaceFormat, format pretty result: {}", erpInterfaceFormatResult);

        return Result.newSuccess(erpInterfaceFormatResult);
    }

    public String getQueryDataListInterfaceArg(ErpObjectDescResult arg, List<ApiFormatResult.ApiFieldMessage> defaultArgs,String lang,String tenantId) {
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder()
                .fieldApiName("objAPIName")
                .fieldType("String")
                .fieldDesc(i18NStringManager.get2(I18NStringEnum.s291,
                lang,
                tenantId,
                arg.getErpObjectApiName()))
                .build());
        String i18nStartTime = i18NStringManager.get(I18NStringEnum.s292,lang,tenantId);
        String i18nEndTime = i18NStringManager.get(I18NStringEnum.s293,lang,tenantId);
        String i18nOffset = i18NStringManager.get(I18NStringEnum.s295,lang,tenantId);
        String i18nLimit = i18NStringManager.get(I18NStringEnum.s296,lang,tenantId);
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("startTime").fieldType("Long").fieldDesc(i18nStartTime).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("endTime").fieldType("Long").fieldDesc(i18nEndTime).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("includeDetail").fieldType("Boolean").fieldDesc(i18NStringManager.get(I18NStringEnum.s294,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("offset").fieldType("Integer").fieldDesc(i18nOffset).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("limit").fieldType("Integer").fieldDesc(i18nLimit).build());
        String queryArgs = "objAPIName=" + arg.getErpObjectApiName() + "&startTime="+i18nStartTime+"&endTime="+i18nEndTime+"&includeDetail=true&offset="+i18nOffset+"&limit="+i18nLimit;
        return queryArgs;
    }

    public Map<String, Object> getQueryDataListInterfaceResult(StandardData standardData,
                                                               ConnectInfoResult connectInfoResult,
                                                               List<ApiFormatResult.ApiFieldMessage> defaultResults,
                                                               String lang,
                                                               String tenantId) {
        Map<String, Object> queryListDatalistMap = Maps.newLinkedHashMap();
        queryListDatalistMap.put("totalNum", i18NStringManager.get(I18NStringEnum.s287,lang,tenantId));
        queryListDatalistMap.put("dataList", Lists.newArrayList(standardData));

        Map<String, Object> queryListResultMap = getErrorCode(connectInfoResult,lang,tenantId);
        for(String field:queryListResultMap.keySet()){
            defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName(field).fieldType("String").fieldDesc(String.valueOf(queryListResultMap.get(field))).build());
        }
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s288,lang,tenantId)).build());
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.totalNum").fieldType("Integer").fieldDesc(i18NStringManager.get(I18NStringEnum.s289,lang,tenantId)).build());
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.dataList").fieldType("List").fieldDesc(i18NStringManager.get(I18NStringEnum.s290,lang,tenantId)).build());

        queryListResultMap.put("data", queryListDatalistMap);
        return queryListResultMap;
    }

    @Override
    public Result<ErpInterfaceFormatResult> getQueryDataDetailByIdInterfaceFormat(String tenantId,
                                                                                  Integer userId,
                                                                                  ErpObjectDescResult arg,
                                                                                  String dataCenterId,
                                                                                  StandardData standardData,
                                                                                  ConnectInfoResult connectInfoResult,
                                                                                  String lang) {
        ErpInterfaceFormatResult erpInterfaceFormatResult = new ErpInterfaceFormatResult();
        List<ApiFormatResult.ApiFieldMessage> defaultArgs=Lists.newArrayList();
        String queryDetailByIdArg = getQueryDataDetailByIdInterfaceArg(tenantId, arg,defaultArgs,lang);
        erpInterfaceFormatResult.setRequestField(defaultArgs);

        List<ApiFormatResult.ApiFieldMessage> defaultResults=Lists.newArrayList();
        Map queryDetailByIdResult = getQueryDataDetailByIdInterfaceResult(standardData, connectInfoResult,defaultResults,lang,tenantId);
        erpInterfaceFormatResult.setResponseField(defaultResults);

        erpInterfaceFormatResult.setRequest(queryDetailByIdArg);
        erpInterfaceFormatResult.setResponse(new Gson().toJson(queryDetailByIdResult));
        erpInterfaceFormatResult.setResponse(toPrettyFormat(erpInterfaceFormatResult.getResponse()));
        log.info("trace getAddDataInterfaceFormat, format pretty result: {}", erpInterfaceFormatResult);

        return Result.newSuccess(erpInterfaceFormatResult);
    }

    public String getQueryDataDetailByIdInterfaceArg(String tenantId,
                                                     ErpObjectDescResult arg,
                                                     List<ApiFormatResult.ApiFieldMessage> defaultArgs,
                                                     String lang) {
        //这里一定查不到，直接干掉
//        ErpObjectFieldEntity idField = erpObjectFieldDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findIdField(tenantId, arg.getErpObjectApiName());
        String id = i18NStringManager.get(I18NStringEnum.s280,lang,tenantId);
//        if (idField != null) {
//            id = idField.getFieldLabel();
//        }
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("objAPIName").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s297,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("dataId").fieldType("String").fieldDesc(id).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("includeDetail").fieldType("Boolean").fieldDesc(i18NStringManager.get(I18NStringEnum.s294,lang,tenantId)).build());

        String queryArgs = "objAPIName=" + arg.getErpObjectApiName() + "&dataId=" + id + "&includeDetail=true";
        return queryArgs;
    }

    public Map getQueryDataDetailByIdInterfaceResult(StandardData standardData,
                                                     ConnectInfoResult connectInfoResult,
                                                     List<ApiFormatResult.ApiFieldMessage> defaultResults,
                                                     String lang,
                                                     String tenantId) {
        Map<String, Object> queryListResultMap = getErrorCode(connectInfoResult,lang,tenantId);
        for(String field:queryListResultMap.keySet()){
            defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName(field).fieldType("String").fieldDesc(String.valueOf(queryListResultMap.get(field))).build());
        }
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s288,lang,tenantId)).build());
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.masterFieldVal").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s298,lang,tenantId)).build());
        defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data.detailFieldVals").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s299,lang,tenantId)).build());

        queryListResultMap.put("data", standardData);
        return queryListResultMap;
    }

    /**
     * 获取ERP模板数据
     *
     * @param tenantId
     * @param userId
     * @param arg
     * @param dataCenterId
     * @return
     */
    private StandardData getErpObjStandardData(String tenantId, Integer userId, ErpObjectDescResult arg, String dataCenterId,String lang) {
        StandardData standardData = new StandardData();
        Result<ErpObjectRelationshipResult> realObjectResult = erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId(tenantId, userId, arg, dataCenterId,lang);
        Map<String, ErpObjectDescResult> erpFakeObjMap = realObjectResult.safeData().getFakeErpObject().stream()
                .collect(Collectors.toMap(ErpObjectDescResult::getErpObjectApiName, obj -> obj));
        String erpMasterObjectApiName = realObjectResult.getData().getMasterObjectApiName();
        ErpObjectDescResult erpMasterObj = erpFakeObjMap.get(erpMasterObjectApiName);
        ObjectData master = new ObjectData();
        for (ErpObjectFieldResult field : erpMasterObj.getErpObjectFields()) {
            if (ErpFieldTypeEnum.master_detail.name().equals(field.getFieldDefineType().name())) {//主从关联字段去掉
                continue;
            }
            master.put(field.getFieldApiName(), field.getFieldLabel());
        }

        standardData.setObjAPIName(arg.getErpObjectApiName());
        standardData.setMasterFieldVal(master);
        standardData.setDetailFieldVals(Maps.newHashMap());
        erpFakeObjMap.remove(erpMasterObjectApiName);
        for (ErpObjectDescResult detail : erpFakeObjMap.values()) {
            ObjectData detailObj = new ObjectData();
            for (ErpObjectFieldResult field : detail.getErpObjectFields()) {
                if (ErpFieldTypeEnum.master_detail.name().equals(field.getFieldDefineType().name())) {//主从关联字段去掉
                    continue;
                }
                detailObj.put(field.getFieldApiName(), field.getFieldLabel());
            }
            if (detail.getErpObjectExtendValue() == null || StringUtils.isEmpty(detail.getErpObjectExtendValue().toString())) {
                standardData.getDetailFieldVals().put(detail.getErpObjectApiName(), Lists.newArrayList(detailObj));
            } else {
                standardData.getDetailFieldVals().put(detail.getErpObjectExtendValue().toString(), Lists.newArrayList(detailObj));
            }

        }
        String erpRealProductObjApiName = syncPloyDetailAdminManager.getErpRealObjApiNameByCrmObjApiName(tenantId, ObjectApiNameEnum.FS_PRODUCTOBJ.getObjApiName());
        if (arg.getErpObjectApiName().equals(erpRealProductObjApiName)) {//产品特殊
            HeaderObj headerObj = I18NHeaderObj.getHeader(tenantId,i18NStringManager);
            GetConfigValueByKeyArg configArg = new GetConfigValueByKeyArg();
            configArg.setKey(CrmConfigKeyConstants.SPU);
//            GetConfigValueByKeyResult isOpenSpu = skuSpuService.getConfigValueByKey(headerObj, configArg);
            String isOpenSpu =sfaApiManager.querySpuOpenStatus(tenantId,false) ;

            configArg.setKey(CrmConfigKeyConstants.CPQ);
            GetConfigValueByKeyResult isOpenCpq = skuSpuService.getConfigValueByKey(headerObj, configArg);
            if (isOpenCpq != null && IsOpenCpqEnum.IsOpen.getValue().equals(isOpenCpq.getValue())) {//开启cpq,cpq
                dealWithStandardCpq(standardData,lang,tenantId);
            } else if (isOpenSpu != null && IsOpenSpuEnum.IsOpen.getValue().equals(isOpenSpu)) {//开启商品，多规格
                dealWithStandardMultiSpecification(standardData,lang,tenantId);
            }
        }
        return standardData;
    }

    private void dealWithStandardCpq(StandardData standardData,String lang,String tenantId) {
        ObjectData bomObj = new ObjectData();
        ObjectData featuresObj = new ObjectData();
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s309,lang,tenantId), "xxx");
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s310,lang,tenantId), "xxx");
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s311,lang,tenantId), 1);
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s312,lang,tenantId), true);
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s313,lang,tenantId), 1);
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s314,lang,tenantId), 1);
        featuresObj.put(i18NStringManager.get(I18NStringEnum.s315,lang,tenantId), true);
        ObjectData featuresValueObj = new ObjectData();
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s316,lang,tenantId), "xxx");
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s317,lang,tenantId), "xxx");
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s318,lang,tenantId), "xxx");
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s319,lang,tenantId), "xxx");
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s311,lang,tenantId), 1);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s320,lang,tenantId), 1);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s321,lang,tenantId), 1);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s315,lang,tenantId), true);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s322,lang,tenantId), true);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s323,lang,tenantId), true);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s324,lang,tenantId), true);
        featuresValueObj.put(i18NStringManager.get(I18NStringEnum.s325,lang,tenantId), true);
        bomObj.put("FeaturesObj", featuresObj);
        bomObj.put("FeaturesValueObj", featuresValueObj);
        standardData.getDetailFieldVals().put("BOMObj", Lists.newArrayList(bomObj));

    }

    private void dealWithStandardMultiSpecification(StandardData standardData,String lang,String tenantId) {
        ObjectData specification = new ObjectData();
        specification.put(i18NStringManager.get(I18NStringEnum.s307,lang,tenantId), "xxx");
        specification.put(i18NStringManager.get(I18NStringEnum.s308,lang,tenantId), "xxx");
        standardData.getDetailFieldVals().put("SpecificationObj", Lists.newArrayList(specification));
    }


    @Override
    public Result<ErpInterfaceFormatResult> getInvalidDataByIdInterfaceFormat(String tenantId,
                                                                              Integer userId,
                                                                              ErpObjectDescResult arg,
                                                                              String dataCenterId,
                                                                              StandardData standardData,
                                                                              ConnectInfoResult connectInfoResult,
                                                                              String lang) {
        ErpInterfaceFormatResult erpInterfaceFormatResult = new ErpInterfaceFormatResult();
        List<ApiFormatResult.ApiFieldMessage> defaultArgs=Lists.newArrayList();
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("objAPIName").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s297,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("masterFieldVal").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s278,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("masterFieldVal._id").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s281,lang,tenantId)).build());
        Map<String,Object> requestArg=Maps.newHashMap();
        Map<String,Object> objData=Maps.newHashMap();
        objData.put("_id",i18NStringManager.get(I18NStringEnum.s1101,lang,tenantId));
        requestArg.put("objAPIName",i18NStringManager.get(I18NStringEnum.s1102,lang,tenantId));
        requestArg.put("masterFieldVal",objData);
        erpInterfaceFormatResult.setRequest(new Gson().toJson(requestArg));
        erpInterfaceFormatResult.setRequest(toPrettyFormat(erpInterfaceFormatResult.getRequest()));
        erpInterfaceFormatResult.setRequestField(defaultArgs);

        Map<String,Object> invalidDataByIdResult = getErrorCode(connectInfoResult,lang,tenantId);
        List<ApiFormatResult.ApiFieldMessage> defaultResults=Lists.newArrayList();
        for(String field:invalidDataByIdResult.keySet()){
            defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName(field).fieldType("String").fieldDesc(String.valueOf(invalidDataByIdResult.get(field))).build());
        }
        erpInterfaceFormatResult.setResponseField(defaultResults);
        erpInterfaceFormatResult.setResponse(new Gson().toJson(invalidDataByIdResult));
        erpInterfaceFormatResult.setResponse(toPrettyFormat(erpInterfaceFormatResult.getResponse()));
        log.info("trace getInvalidDataByIdInterfaceFormat, format pretty result: {}", erpInterfaceFormatResult);

        return Result.newSuccess(erpInterfaceFormatResult);
    }


    @Override
    public Result<ErpInterfaceFormatResult> getInvalidDetailDataByIdInterfaceFormat(String tenantId,
                                                                                    Integer userId,
                                                                                    ErpObjectDescResult arg,
                                                                                    String dataCenterId,
                                                                                    StandardData erpStandardObj,
                                                                                    ConnectInfoResult connectInfo,
                                                                                    String lang) {
        ErpInterfaceFormatResult erpInterfaceFormatResult = new ErpInterfaceFormatResult();
        List<ApiFormatResult.ApiFieldMessage> defaultArgs=Lists.newArrayList();
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("objAPIName").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s297,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("masterFieldVal").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s278,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("masterFieldVal._id").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s281,lang,tenantId)).build());
        defaultArgs.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("detailFieldVal").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s300,lang,tenantId)).build());
        Map<String,Object> requestArg=Maps.newHashMap();
        Map<String,Object> objData=Maps.newHashMap();
        Map<String,Object> detailObjId=Maps.newHashMap();
        objData.put("_id",i18NStringManager.get(I18NStringEnum.s1101,lang,tenantId));
        detailObjId.put(i18NStringManager.get(I18NStringEnum.s1133,lang,tenantId),i18NStringManager.get(I18NStringEnum.s1134,lang,tenantId));
        requestArg.put("objAPIName",i18NStringManager.get(I18NStringEnum.s1102,lang,tenantId));
        requestArg.put("masterFieldVal",objData);
        requestArg.put("detailFieldVal",detailObjId);
        erpInterfaceFormatResult.setRequest(new Gson().toJson(requestArg));
        erpInterfaceFormatResult.setRequest(toPrettyFormat(erpInterfaceFormatResult.getRequest()));
        erpInterfaceFormatResult.setRequestField(defaultArgs);

        Map<String,Object> invalidDataByIdResult = getErrorCode(connectInfo,lang,tenantId);
        List<ApiFormatResult.ApiFieldMessage> defaultResults=Lists.newArrayList();
        for(String field:invalidDataByIdResult.keySet()){
            defaultResults.add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName(field).fieldType("String").fieldDesc(String.valueOf(invalidDataByIdResult.get(field))).build());
        }
        erpInterfaceFormatResult.setResponseField(defaultResults);
        erpInterfaceFormatResult.setResponse(new Gson().toJson(invalidDataByIdResult));
        erpInterfaceFormatResult.setResponse(toPrettyFormat(erpInterfaceFormatResult.getResponse()));
        log.info("trace getInvalidDetailDataByIdInterfaceFormat, format pretty result: {}", erpInterfaceFormatResult);

        return Result.newSuccess(erpInterfaceFormatResult);
    }

    @Override
    public Result<StandardData> getErpStandardObj(String tenantId,
                                                  Integer userId,
                                                  ErpObjectDescResult arg,
                                                  String dataCenterId,
                                                  String lang) {
        return Result.newSuccess(getErpObjStandardData(tenantId, userId, arg, dataCenterId,lang));
    }

    @Override
    public Result<List<ApiFormatResult2>> getExportInterfaceFormat(String tenantId, Integer userId, String dataCenterId, ErpChannelEnum channel, String splitObjectApiName, Set<String> apiTypes, String lang) {
        GetApiFormatArg apiFormatArg = new GetApiFormatArg();
        apiFormatArg.setChannel(channel);
        apiFormatArg.setSplitObjectApiName(splitObjectApiName);
        apiFormatArg.setLang(lang);
        final Result<List<ApiFormatResult2>> interfaceFormat = getInterfaceFormat(tenantId, dataCenterId, userId, apiFormatArg);
        if (!interfaceFormat.isSuccess()) {
            return Result.copy(interfaceFormat);
        }

        final List<ApiFormatResult2> collect = interfaceFormat.getData().stream()
                .filter(apiFormatResult2 -> CollectionUtils.isEmpty(apiTypes) || apiTypes.contains(apiFormatResult2.getApiType().name()))
                .collect(Collectors.toList());
        // 实施要求推送接口的api中,将对象的字段信息也添加上
        addPushField(tenantId, dataCenterId, splitObjectApiName, collect, lang);
        return Result.newSuccess(collect);
    }

    private void addPushField(String tenantId, String dataCenterId, String splitObjectApiName, List<ApiFormatResult2> collect, String lang) {
        collect.stream()
                .filter(apiFormatResult2 -> Objects.equals(apiFormatResult2.getApiType(), ErpObjInterfaceUrlEnum.push))
                .findFirst()
                .ifPresent(apiFormatResult2 -> addPushField(tenantId, dataCenterId, splitObjectApiName, apiFormatResult2, lang));
    }

    private void addPushField(String tenantId, String dataCenterId, String splitObjectApiName, ApiFormatResult2 apiFormatResult2, String lang) {
        // 添加对象字段
        final List<ApiFormatResult.ApiFieldMessage> argFieldList = apiFormatResult2.getStandardApi().getArgFieldList();
        if (CollectionUtils.isEmpty(argFieldList)) {
            return;
        }
        final Integer masterIndex = getFieldIndex(argFieldList, "masterFieldVal");
        if (Objects.isNull(masterIndex)) {
            return;
        }

        final ErpObjectRelationshipEntity relation = erpObjManager.getRelation(tenantId, splitObjectApiName);
        Result<ErpObjectRelationshipResult> realObjectResult = erpObjectFieldsService.queryErpObjectAndFieldsByActualObjAndDcId(tenantId, dataCenterId, relation.getErpRealObjectApiname(), relation.getSplitSeq(), lang);
        Map<String, ErpObjectDescResult> erpFakeObjMap = realObjectResult.safeData().getFakeErpObject().stream()
                .collect(Collectors.toMap(ErpObjectDescResult::getErpObjectApiName, obj -> obj));
        String erpMasterObjectApiName = realObjectResult.getData().getMasterObjectApiName();

        // 主对象
        final List<ErpObjectFieldResult> erpObjectFields = erpFakeObjMap.get(erpMasterObjectApiName).getErpObjectFields();
        final List<ApiFormatResult.ApiFieldMessage> masterFields = erpObjectFields.stream()
                .filter(field -> !ErpFieldTypeEnum.master_detail.name().equals(field.getFieldDefineType().name()))
                .map(field -> getApiFieldMessage(field, ""))
                .collect(Collectors.toList());
        argFieldList.addAll(masterIndex + 1, masterFields);

        // 从对象
        final Integer detailIndex = getFieldIndex(argFieldList, "detailFieldVals");
        if (Objects.isNull(detailIndex)) {
            return;
        }
        erpFakeObjMap.remove(erpMasterObjectApiName);
        final List<ApiFormatResult.ApiFieldMessage> detailFields = erpFakeObjMap.entrySet().stream()
                .flatMap(entry -> {
                    final String apiName = entry.getKey();
                    final ApiFormatResult.ApiFieldMessage detailField = new ApiFormatResult.ApiFieldMessage("" + apiName, "List", "detail");
                    final Stream<ApiFormatResult.ApiFieldMessage> detailFieldsStream = entry.getValue().getErpObjectFields().stream()
                            .filter(field -> !ErpFieldTypeEnum.master_detail.name().equals(field.getFieldDefineType().name()))
                            .map(field -> getApiFieldMessage(field, ""));
                    return Stream.concat(
                            Stream.of(detailField),
                            detailFieldsStream
                    );
                }).collect(Collectors.toList());

        argFieldList.addAll(detailIndex + 1, detailFields);
    }

    private static ApiFormatResult.ApiFieldMessage getApiFieldMessage(ErpObjectFieldResult field, String prefix) {
        String fieldLabel = field.getFieldLabel();
        if (ErpFieldTypeEnum.isSelectType(field.getFieldDefineType())) {
            fieldLabel += "(" + field.getFieldExtendValue() + ")";
        }
        return new ApiFormatResult.ApiFieldMessage(prefix + field.getFieldApiName(), field.getFieldDefineType().name(), fieldLabel);
    }

    @Nullable
    private static Integer getFieldIndex(List<ApiFormatResult.ApiFieldMessage> argFieldList, String fieldName) {
        Integer masterIndex = null;
        for (int i = 0; i < argFieldList.size(); i++) {
            if (argFieldList.get(i).getFieldApiName().equals(fieldName)) {
                masterIndex = i;
                break;
            }
        }
        return masterIndex;
    }

    @Override
    public Result<List<ApiFormatResult2>> getInterfaceFormat(String tenantId,
                                                             String dataCenterId,
                                                             Integer userId,
                                                             GetApiFormatArg getApiFormatArg) {
        String lang = getApiFormatArg.getLang();
        ErpObjectDescResult arg = new ErpObjectDescResult();
        String splitObjectApiName = getApiFormatArg.getSplitObjectApiName();
        if (StringUtils.isNotBlank(getApiFormatArg.getErpObjectApiName()) && getApiFormatArg.getSplitSeq() != null) {
            arg.setErpObjectApiName(getApiFormatArg.getErpObjectApiName());
            arg.setSplitSeq(getApiFormatArg.getSplitSeq());
            arg.setErpObjectName(getApiFormatArg.getErpObjectName());
            // 获取中间对象
            splitObjectApiName = erpObjManager.getMasterSplitObjApiName(tenantId, dataCenterId, getApiFormatArg.getErpObjectApiName());
        } else if (StringUtils.isNotBlank(getApiFormatArg.getSplitObjectApiName())) {//虚拟apiName
            ErpObjectRelationshipEntity split = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findBySplit(tenantId, getApiFormatArg.getSplitObjectApiName());
            if (split != null) {
                arg.setErpObjectApiName(split.getErpRealObjectApiname());
            }
        }
        final String erpObjectApiName = arg.getErpObjectApiName();
        if (StringUtils.isBlank(erpObjectApiName)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        final String erpObjectName = arg.getErpObjectName();
        if (StringUtils.isBlank(erpObjectName)) {
            ErpObjectEntity byObjApiName = erpObjManager.getErpObj(tenantId, erpObjectApiName);
            if (byObjApiName != null) {
                arg.setErpObjectName(byObjApiName.getErpObjectName());
            }
        }
        List<ApiFormatResult> apiFormatResultList = Lists.newArrayList();
        ConnectInfoResult connectInfo = connectInfoService.getConnectInfoByDataCenterId(tenantId, userId, dataCenterId).safeData();
        Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy = aplConfigManager.queryUrl2Apl(tenantId,dataCenterId,erpObjectApiName);
        StandardData erpStandardObj = getErpStandardObj(tenantId, userId, arg, dataCenterId,lang).safeData();
        Result<ErpInterfaceFormatResult> addDataInterfaceFormat = getAddDataInterfaceFormat(tenantId, userId, arg, dataCenterId, erpStandardObj, connectInfo,lang,url2Groovy);
        Result<ErpInterfaceFormatResult> queryDataListInterfaceFormat = getQueryDataListInterfaceFormat(tenantId, userId, arg, dataCenterId, erpStandardObj, connectInfo,lang);
        Result<ErpInterfaceFormatResult> queryDataDetailByIdInterfaceFormat = getQueryDataDetailByIdInterfaceFormat(tenantId, userId, arg, dataCenterId, erpStandardObj, connectInfo,lang);
        Result<ErpInterfaceFormatResult> invalidDataInterfaceFormat = getInvalidDataByIdInterfaceFormat(tenantId, userId, arg, dataCenterId, erpStandardObj, connectInfo,lang);
        Result<ErpInterfaceFormatResult> invalidDetailDataInterfaceFormat = getInvalidDetailDataByIdInterfaceFormat(tenantId, userId, arg, dataCenterId, erpStandardObj, connectInfo,lang);

        List<ApiFormatResult.ApiHeaderMessage> defaultHeaders = Lists.newArrayList();
        defaultHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("Content-Type").fieldValue("application/json").fieldDesc(i18NStringManager.get(I18NStringEnum.s275,lang,tenantId)).build());
        defaultHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("xxx").fieldValue("xxx").fieldDesc(i18NStringManager.get(I18NStringEnum.s276,lang,tenantId)).build());


        if (addDataInterfaceFormat.isSuccess() && addDataInterfaceFormat.getData() != null) {
            //create
            ApiFormatResult createApiFormatResult = postApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    addDataInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.create,
                    lang);

            apiFormatResultList.add(createApiFormatResult);

            //update
            ApiFormatResult updateApiFormatResult = postApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    addDataInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.update,
                    lang);
            apiFormatResultList.add(updateApiFormatResult);

            //push
            ApiFormatResult pushApiFormatResult = getPushApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    addDataInterfaceFormat.getData(),
                    defaultHeaders,
                    lang);
            apiFormatResultList.add(pushApiFormatResult);
        }
        if ((queryDataListInterfaceFormat.isSuccess() && queryDataListInterfaceFormat.getData() != null)) {
            //queryMasterBatch,批量查询
            ApiFormatResult queryMasterBatchApiFormatResult = getApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    queryDataListInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.queryMasterBatch,
                    lang);
            apiFormatResultList.add(queryMasterBatchApiFormatResult);

            //查询作废数据,使用和新增更新一样的结构体只是url变了
            ApiFormatResult queryInvalidApiFormatResult = getApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    queryDataListInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.queryInvalid,
                    lang);
            apiFormatResultList.add(queryInvalidApiFormatResult);
        }
        if ((queryDataDetailByIdInterfaceFormat.isSuccess() && queryDataDetailByIdInterfaceFormat.getData() != null)) {
            //queryMasterById,通过id查询
            ApiFormatResult queryMasterByIdApiFormatResult = getApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    queryDataDetailByIdInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.queryMasterById,
                    lang);
            apiFormatResultList.add(queryMasterByIdApiFormatResult);
        }

        if ((invalidDataInterfaceFormat.isSuccess() && invalidDataInterfaceFormat.getData() != null)) {
            /**
             * POST http://test.fxiaoke.com/invalid
             * --header 'Content-Type: application/json'
             * --header '通过groovy脚本获取的header参数'
             * --data-raw { "objAPIName": "productObj", "masterFieldVal": { "_id": " id" }}
             * http 请求response
             * { "errCode": "错误返回码", "errMsg": "错误提示语"}   */
            //invalid,通过id作废明细
            ApiFormatResult invalidDataByIdApiFormatResult = postApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    invalidDataInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.invalid,
                    lang);
            apiFormatResultList.add(invalidDataByIdApiFormatResult);
            //delete,删除
            ApiFormatResult deleteDataByIdApiFormatResult = postApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    invalidDataInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.delete,
                    lang);
            apiFormatResultList.add(deleteDataByIdApiFormatResult);
        }

        if ((invalidDetailDataInterfaceFormat.isSuccess() && invalidDetailDataInterfaceFormat.getData() != null)) {
            /**
             * POST www.test.com/invalidDetail
             *
             * --header 'Content-Type: application/json'
             *
             * --header '通过groovy脚本获取的header参数'
             *
             * --data-raw {
             *     "objAPIName": "erpAccount ",
             *     "masterFieldVal": {
             *         "_id": "id"
             *     },
             *     "detailFieldVal": {
             *         "detailObj1": "id"
             *     }
             * }
             *
             * http 请求response
             *
             * { "errCode": "错误返回码", "errMsg": "错误提示语"}
             */
            //invalidDetail,作废明细
            ApiFormatResult invalidDetailDataByIdApiFormatResult = postApiFormatResult(tenantId,
                    dataCenterId,
                    splitObjectApiName,
                    erpObjectApiName,
                    erpObjectName,
                    connectInfo,
                    url2Groovy,
                    invalidDetailDataInterfaceFormat.getData(),
                    defaultHeaders,
                    ErpObjInterfaceUrlEnum.invalidDetail,
                    lang);
            apiFormatResultList.add(invalidDetailDataByIdApiFormatResult);
        }
        if (ErpChannelEnum.ERP_K3CLOUD.equals(connectInfo.getChannel())
                || ErpChannelEnum.ERP_U8.equals(connectInfo.getChannel())
                || ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.equals(connectInfo.getChannel())) {//覆盖realUrl
            for (ApiFormatResult apiFormatResult : apiFormatResultList) {
                apiFormatResult.setRealUrl(ChannelUrlUtils.getApiUrl(tenantId,
                        dataCenterId,
                        apiFormatResult.getApiType(),
                        connectInfo,
                        apiFormatResult.getObjApiName(),
                        k3UltimateApiTemplateManager));
                if(ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.equals(connectInfo.getChannel())) {
                    apiFormatResult.setK3ultimate_url(apiFormatResult.getRealUrl());
                }
            }
        }

        List<ApiFormatResult2> apiFormatResult2List = new ArrayList<>();

        apiFormatResultList.forEach(result -> {
            ApiFormatResult2 apiFormatResult2 = new ApiFormatResult2();
            apiFormatResult2.setApiType(result.getApiType());
            apiFormatResult2.setApiTypeName(result.getApiTypeName());
            apiFormatResult2.setObjApiName(result.getObjApiName());
            ErpObjInterfaceCheckedEntity checkedEntity = erpObjInterfaceCheckedManager.findOne(tenantId,
                    dataCenterId,
                    erpObjectApiName,
                    result.getApiType());
            if(checkedEntity!=null) {
                apiFormatResult2.setSelectedInterfaceType(checkedEntity.getCheckedInterfaceType());
                if(StringUtils.isNotEmpty(result.getFuncApiName())) {
                    apiFormatResult2.setCustomFunc(new ApiFormatResult2.CustomFunc(result.getFuncApiName()));
                }
                if(StringUtils.isNotEmpty(result.getCustomInterfaceId())) {
                    apiFormatResult2.setCustomApi(erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .findByTenantIdAndId(tenantId, result.getCustomInterfaceId()));
                }
                if(checkedEntity.getCheckedInterfaceType()==ErpObjInterfaceTypeEnum.K3C) {
                    if(connectInfo.getChannel()==ErpChannelEnum.ERP_K3CLOUD && ErpObjInterfaceUrlEnum.k3cInterfaceList.contains(result.getApiType())) {
                        apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.K3C);
                    } else {
                        if(connectInfo.getChannel()==ErpChannelEnum.ERP_K3CLOUD_ULTIMATE) {
                            apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.K3ULTIMATE_API);
                        } else {
                            apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.STANDARD_API);
                        }
                    }
                } else {
                    log.info("InterfaceFormatServiceImpl.getInterfaceFormat,interface type not support");
                }
            } else {
                if(StringUtils.isNotEmpty(result.getFuncApiName())) {
                    apiFormatResult2.setCustomFunc(new ApiFormatResult2.CustomFunc(result.getFuncApiName()));
                    apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.CUSTOM_FUNC);
                }
                if(StringUtils.isNotEmpty(result.getCustomInterfaceId())) {
                    apiFormatResult2.setCustomApi(erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                            .findByTenantIdAndId(tenantId, result.getCustomInterfaceId()));
                    if(apiFormatResult2.getSelectedInterfaceType() != ErpObjInterfaceTypeEnum.CUSTOM_FUNC) {
                        //自定义函数的优先级高于自定义API，自定义函数和自定义API互斥
                        apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.CUSTOM_API);
                    }
                }

                if(apiFormatResult2.getSelectedInterfaceType()==null) {
                    if(connectInfo.getChannel()==ErpChannelEnum.ERP_K3CLOUD && ErpObjInterfaceUrlEnum.k3cInterfaceList.contains(result.getApiType())) {
                        apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.K3C);
                    } else {
                        if(connectInfo.getChannel()==ErpChannelEnum.ERP_K3CLOUD_ULTIMATE) {
                            apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.K3ULTIMATE_API);
                        } else {
                            apiFormatResult2.setSelectedInterfaceType(ErpObjInterfaceTypeEnum.STANDARD_API);
                        }
                    }
                }
            }

            apiFormatResult2.setStandardApi(result);
            apiFormatResult2List.add(apiFormatResult2);
        });
        //增加事件订阅配置
        addWebhookApiFormat(tenantId, dataCenterId, url2Groovy, apiFormatResult2List);
        //重排,push放最后
        apiFormatResult2List.sort(Comparator.comparingInt(v -> Objects.equals(v.getApiType(), ErpObjInterfaceUrlEnum.push) ? 1 : 0));
        //旧K3订阅
//        if(connectInfo.getChannel()==ErpChannelEnum.ERP_K3CLOUD_ULTIMATE) {
//            apiFormatResult2List.add(getSubscribeEventApiFormat(tenantId,dataCenterId,erpObjectApiName,connectInfo,lang));
//        }
        return Result.newSuccess(apiFormatResult2List);
    }

    private void addWebhookApiFormat(String tenantId, String dataCenterId, Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy, List<ApiFormatResult2> apiFormatResult2List) {
        ApiFormatResult2 webhookResult2 = new ApiFormatResult2();
        webhookResult2.setApiType(ErpObjInterfaceUrlEnum.webhook);
        webhookResult2.setApiTypeName(ErpObjInterfaceUrlEnum.webhook.getText());
        webhookResult2.setObjApiName("ALL");
        ApiFormatResult2.WebhookApi webhookApi = ApiFormatResult2.WebhookApi.builder()
                .webhookUrl(pushIdentifyManager.buildWebhookUrl(tenantId, dataCenterId))
                .datacenterFuncApiName(Opt.ofNullable(url2Groovy.get(ErpObjInterfaceUrlEnum.webhook))
                        .map(u -> u.getFuncApiName()).get()).build();
        Opt.ofNullable(url2Groovy.get(ErpObjInterfaceUrlEnum.webhook)).ifPresent(v->{
            webhookApi.setFuncId(v.getId());
            webhookApi.setDatacenterFuncApiName(v.getFuncApiName());
        });
        webhookResult2.setWebhookApi(webhookApi);
        apiFormatResult2List.add(webhookResult2);
    }

    @Deprecated
    private ApiFormatResult2 getSubscribeEventApiFormat(String tenantId,
                                                        String dataCenterId,
                                                        String erpObjectApiName,
                                                        ConnectInfoResult connectInfo,
                                                        String lang) {
        K3UltimateSubscribeEventModel subscribeEventModel = new K3UltimateSubscribeEventModel();
        subscribeEventModel.setUrl(ChannelUrlUtils.getApiUrl(tenantId,
                dataCenterId,
                ErpObjInterfaceUrlEnum.subscribeEvent,
                connectInfo,
                erpObjectApiName,
                k3UltimateApiTemplateManager));

        ErpK3UltimateTokenEntity entity = erpK3UltimateTokenDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findData(tenantId,dataCenterId,erpObjectApiName, "1.0");
        if(entity==null) {
            K3UltimateTokenModel model = new K3UltimateTokenModel();
            model.setTenantId(tenantId);
            model.setDataCenterId(dataCenterId);
            model.setErpObjApiName(erpObjectApiName);
            model.setVersion("1.0");
            model.setTimestamps(System.currentTimeMillis()+"");
            Result<String> genToken = k3UltimateEventSubscribeService.genToken(model);
            if(genToken.isSuccess()) {
                entity = erpK3UltimateTokenDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                        .findData(tenantId,dataCenterId,erpObjectApiName, "1.0");
            } else {
                subscribeEventModel.setToken(i18NStringManager.get2(genToken.getI18nKey(),lang,tenantId,genToken.getErrMsg(),genToken.getI18nExtra()));
            }
        }
        if(entity!=null) {
            subscribeEventModel.setToken(entity.getToken());

            List<K3UltimateEventConfigModel> supportSubscribeEventList = Lists.newArrayList(
                    new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.SAVE, "",
                            K3UltimateEventTypeEnum.SAVE.getName(i18NStringManager, lang, tenantId)),
                    new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.SUBMIT, "",
                            K3UltimateEventTypeEnum.SUBMIT.getName(i18NStringManager, lang, tenantId)),
                    new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.UN_SUBMIT, "",
                            K3UltimateEventTypeEnum.UN_SUBMIT.getName(i18NStringManager, lang, tenantId)),
                    new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.AUDIT,"",
                            K3UltimateEventTypeEnum.AUDIT.getName(i18NStringManager, lang, tenantId)),
                    new K3UltimateEventConfigModel(K3UltimateEventTypeEnum.UN_AUDIT,"",
                            K3UltimateEventTypeEnum.UN_AUDIT.getName(i18NStringManager, lang, tenantId))
            );
            List<K3UltimateEventConfigModel> eventConfigModelList = null;
            if(StringUtils.isEmpty(entity.getEventConfig())) {
                eventConfigModelList = new ArrayList<>();
            } else {
                eventConfigModelList = JSONObject.parseArray(entity.getEventConfig(), K3UltimateEventConfigModel.class);
            }

            if(CollectionUtils.isNotEmpty(eventConfigModelList)) {
                for(K3UltimateEventConfigModel model : supportSubscribeEventList) {
                    for(K3UltimateEventConfigModel item : eventConfigModelList) {
                        if(item.getEventType()==model.getEventType()) {
                            model.setEventCode(item.getEventCode());
                            break;
                        }
                    }
                }
            }
            subscribeEventModel.setEventConfigList(supportSubscribeEventList);
        }

        ErpObjInterfaceUrlEnum urlEnum = ErpObjInterfaceUrlEnum.subscribeEvent;

        ApiFormatResult2 apiFormatResult2 = new ApiFormatResult2();
        apiFormatResult2.setApiType(urlEnum);
        apiFormatResult2.setApiTypeName(i18NStringManager.get(urlEnum.getI18nKey(),lang,tenantId,urlEnum.getNameDesc()));
        apiFormatResult2.setObjApiName(erpObjectApiName);
        apiFormatResult2.setSubscribeEventModel(subscribeEventModel);

        return apiFormatResult2;
    }

    @Override
    public Result<String> updateCustomInterfaceData(ErpCustomInterfaceEntity entity) {
        if (StringUtils.isNotEmpty(entity.getId())) {
            ErpCustomInterfaceEntity customInterfaceEntity = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                    .findByTenantIdAndId(entity.getTenantId(), entity.getId());
            if (customInterfaceEntity != null) {
                entity.setCreateTime(customInterfaceEntity.getCreateTime());
                entity.setUpdateTime(System.currentTimeMillis());
                int count = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                        .updateById(entity);
                log.info("InterfaceFormatServiceImpl.updateCustomInterfaceData,updateById,count={}", count);
                return count == 1 ? Result.newSuccess(entity.getId()) : Result.newSystemError(I18NStringEnum.s268);
            }
        }

        entity.setId(idGenerator.get());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        int count = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                .insert(entity);
        log.info("InterfaceFormatServiceImpl.updateCustomInterfaceData,insert,count={}", count);
        return count == 1 ? Result.newSuccess(entity.getId()) : Result.newSystemError(I18NStringEnum.s269);
    }

    @Override
    public Result<ErpCustomInterfaceEntity> queryCustomInterfaceData(String tenantId,
                                                                     String dataCenterId,
                                                                     String objApiName,
                                                                     ErpObjInterfaceUrlEnum interfaceType) {
        ErpCustomInterfaceEntity entity = ErpCustomInterfaceEntity.builder()
                .tenantId(tenantId)
                .dataCenterId(dataCenterId)
                .objApiName(objApiName)
                .interfaceType(interfaceType)
                .build();
        List<ErpCustomInterfaceEntity> entityList = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .queryList(entity);
        if(CollectionUtils.isNotEmpty(entityList)) {
            return Result.newSuccess(entityList.get(0));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Map<String,List<ErpObjApiNameModel>>> queryErpObjectApiNames(String tenantId,String dataCenterId,String erpObjApiName, String lang) {
        List<ErpObjectRelationshipEntity> objectRelationshipEntityList = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .findByRealObjectApiName(tenantId, dataCenterId, erpObjApiName);

        Map<String,List<ErpObjApiNameModel>> allMap = new LinkedHashMap<>();
        List<ErpObjApiNameModel> publicList = Lists.newArrayList(new ErpObjApiNameModel(i18NStringManager.get(I18NStringEnum.s271,lang,tenantId),"code"),
                new ErpObjApiNameModel(i18NStringManager.get(I18NStringEnum.s272,lang,tenantId),"msg"),
                new ErpObjApiNameModel(i18NStringManager.get(I18NStringEnum.s273,lang,tenantId),"success"));


        if(CollectionUtils.isNotEmpty(objectRelationshipEntityList)) {
            String masterSplitObjApiName = objectRelationshipEntityList.stream()
                    .filter((entity -> entity.getSplitType()==ErpObjSplitTypeEnum.NOT_SPLIT))
                    .map(ErpObjectRelationshipEntity::getErpSplitObjectApiname)
                    .findFirst()
                    .get();

            List<String> erpObjApiNameList = objectRelationshipEntityList.stream()
                    .map(v -> v.getErpSplitObjectApiname())
                    .collect(Collectors.toList());

            List<ErpObjectEntity> erpObjectList = erpObjectDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                    .queryByApiNames2(tenantId, dataCenterId, erpObjApiNameList);

            //新增
            List<ErpObjApiNameModel> createList = new ArrayList<>(publicList);
            allMap.put(ErpObjInterfaceUrlEnum.create.name(),createList);

            erpObjectList.stream().forEach((entity -> {
                ErpObjApiNameModel idModel = null;
                ErpObjApiNameModel nameModel = null;
                if(StringUtils.equalsIgnoreCase(masterSplitObjApiName,entity.getErpObjectApiName())) {
                    idModel = new ErpObjApiNameModel(entity.getErpObjectName()+"ID",entity.getErpObjectApiName());
                    nameModel = new ErpObjApiNameModel(entity.getErpObjectName()+i18NStringManager.get(I18NStringEnum.s274,lang,tenantId),
                            entity.getErpObjectApiName() + ErpCustomInterfaceServiceImpl.OBJ_NAME_SUFFIX);
                } else {
                    idModel = new ErpObjApiNameModel(entity.getErpObjectName()+"ID",
                            entity.getErpObjectApiName() + ErpCustomInterfaceServiceImpl.DETAIL_OBJ_API_NAME_SUFFIX);
                }
                createList.add(idModel);
                if(nameModel!=null) {
                    createList.add(nameModel);
                }
            }));

            //更新
            allMap.put(ErpObjInterfaceUrlEnum.update.name(),createList);


            //批量查询
            List<ErpObjApiNameModel> queryMasterBatchList = new ArrayList<>(publicList);
            allMap.put(ErpObjInterfaceUrlEnum.queryMasterBatch.name(),queryMasterBatchList);

            erpObjectList.stream().forEach((entity -> {
                if(StringUtils.equalsIgnoreCase(masterSplitObjApiName,entity.getErpObjectApiName())) {
                    queryMasterBatchList.add(new ErpObjApiNameModel(entity.getErpObjectName(),entity.getErpObjectApiName()));
                } else {
                    queryMasterBatchList.add(new ErpObjApiNameModel(entity.getErpObjectName() + i18NStringManager.get(I18NStringEnum.s3793, lang, tenantId),
                            entity.getErpObjectApiName()+ ErpCustomInterfaceServiceImpl.DETAIL_PARENT_XPATH_SUFFIX));
                    queryMasterBatchList.add(new ErpObjApiNameModel(entity.getErpObjectName() + i18NStringManager.get(I18NStringEnum.s3794, lang, tenantId),
                            entity.getErpObjectApiName()+ ErpCustomInterfaceServiceImpl.DETAIL_CHILD_XPATH_SUFFIX));
                    queryMasterBatchList.add(new ErpObjApiNameModel(entity.getErpObjectName() + i18NStringManager.get(I18NStringEnum.s3792, lang, tenantId),
                            entity.getErpObjectApiName()+ ErpCustomInterfaceServiceImpl.DETAIL_MAPPING_XPATH_SUFFIX));
                }
            }));

            //批量查询作废
            allMap.put(ErpObjInterfaceUrlEnum.queryInvalid.name(),queryMasterBatchList);


            //通过Id查询
            List<ErpObjApiNameModel> queryMasterByIdList = new ArrayList<>(publicList);
            allMap.put(ErpObjInterfaceUrlEnum.queryMasterById.name(),queryMasterByIdList);

            erpObjectList.stream().forEach((entity -> {
                if(StringUtils.equalsIgnoreCase(masterSplitObjApiName,entity.getErpObjectApiName())) {
                    queryMasterByIdList.add(new ErpObjApiNameModel(entity.getErpObjectName(),entity.getErpObjectApiName()));
                } else {
                    queryMasterByIdList.add(new ErpObjApiNameModel(entity.getErpObjectName(),
                            entity.getErpObjectApiName() + ErpCustomInterfaceServiceImpl.DETAIL_OBJ_API_NAME_SUFFIX));
                }
            }));


            //作废主对象
            List<ErpObjApiNameModel> invalidList = new ArrayList<>(publicList);
            allMap.put(ErpObjInterfaceUrlEnum.invalid.name(),invalidList);

            //作废明细对象
            List<ErpObjApiNameModel> invalidDetailList = new ArrayList<>(publicList);
            allMap.put(ErpObjInterfaceUrlEnum.invalidDetail.name(),invalidDetailList);

            return Result.newSuccess(allMap);
        }
        return Result.newError(ResultCodeEnum.NO_ERP_OBJECT);
    }

    @Override
    public Result<ErpCustomInterfaceEntity> queryCustomInterfaceDataById(String tenantId,String id) {
        return Result.newSuccess(erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findByTenantIdAndId(tenantId, id));
    }

    @Override
    public Result<Void> deleteCustomInterfaceDataById(String tenantId,String id) {
        int count = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId))
                .deleteByEiAndId(tenantId,id);
        log.info("InterfaceFormatServiceImpl.deleteCustomInterfaceDataById,count={}", count);
        return count == 1 ? Result.newSuccess() : Result.newSystemError(I18NStringEnum.s270);
    }

    @Override
    public Result<Integer> updateCheckedInterfaceType(ErpObjInterfaceCheckedEntity entity) {
        if(entity.getCheckedInterfaceType()==ErpObjInterfaceTypeEnum.CUSTOM_FUNC) {
            Map<ErpObjInterfaceUrlEnum, ErpObjCustomFunctionResult> url2Groovy = aplConfigManager.queryUrl2Apl(entity.getTenantId(),
                    entity.getDataCenterId(),
                    entity.getObjApiName());
            ErpObjCustomFunctionResult functionResult = url2Groovy.get(entity.getInterfaceUrl());
            if(functionResult==null || StringUtils.isEmpty(functionResult.getFuncApiName())) {
                return Result.newError(ResultCodeEnum.CUSTOM_FUNC_NOT_EXIST);
            }
        } else if(entity.getCheckedInterfaceType()==ErpObjInterfaceTypeEnum.CUSTOM_API) {
            ErpCustomInterfaceEntity data = erpCustomInterfaceDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(entity.getTenantId()))
                    .findData(entity.getTenantId(),
                            entity.getDataCenterId(),
                            entity.getObjApiName(),
                            entity.getInterfaceUrl());
            if(data==null) {
                return Result.newError(ResultCodeEnum.CUSTOM_API_CONFIG_NOT_EXIST);
            }
        }
        int count = erpObjInterfaceCheckedManager.insertOrUpdate(entity);
        if(count==1) {
            return Result.newSuccess(count);
        }
        return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
    }

    private ApiFormatResult getPushApiFormatResult(String tenantId,
                                                   String dataCenterId,
                                                   String splitObjectApiName,
                                                   String erpObjectApiName,
                                                   String erpObjectName,
                                                   ConnectInfoResult connectInfo,
                                                   Map<ErpObjInterfaceUrlEnum,
                                                   ErpObjCustomFunctionResult> url2Groovy,
                                                   ErpInterfaceFormatResult interfaceFormatResult,
                                                   List<ApiFormatResult.ApiHeaderMessage> defaultHeaders,
                                                   String lang) {
        ApiFormatResult pushApiFormatResult = postApiFormatResult(tenantId,
                dataCenterId,
                splitObjectApiName,
                erpObjectApiName,
                erpObjectName,
                connectInfo,
                url2Groovy,
                interfaceFormatResult,
                defaultHeaders,
                ErpObjInterfaceUrlEnum.push,
                lang);
        pushApiFormatResult.getResultFieldList().clear();//清空
        pushApiFormatResult.getResultFieldList().add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("errCode").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s283,lang,tenantId)).build());
        pushApiFormatResult.getResultFieldList().add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("errMsg").fieldType("String").fieldDesc(i18NStringManager.get(I18NStringEnum.s284,lang,tenantId)).build());
        pushApiFormatResult.getResultFieldList().add(ApiFormatResult.ApiFieldMessage.builder().fieldApiName("data").fieldType("Map").fieldDesc(i18NStringManager.get(I18NStringEnum.s288,lang,tenantId)).build());

        Map<String,String> responseMap = new HashMap<>();
        responseMap.put("errCode",Result.newSuccess().getErrCode());
        responseMap.put("errMsg",i18NStringManager.get(Result.newSuccess().getI18nKey(),lang,tenantId,Result.newSuccess().getErrMsg()));
        StandardData standardData=new StandardData();
        ObjectData masterFieldVal=new ObjectData();
        masterFieldVal.put("id","value");
        standardData.setMasterFieldVal(masterFieldVal);
        standardData.setDetailFieldVals(Maps.newHashMap());
        standardData.getDetailFieldVals().put("detailApiName",Lists.newArrayList(masterFieldVal));
        responseMap.put("data", JacksonUtil.toJson(standardData));

        pushApiFormatResult.setResultExample(new Gson().toJson(responseMap));
        String token = erpObjDataPushManager.getTokenByTenantId(tenantId, TokenVersionEnum.MD5.getValue(),lang);
        pushApiFormatResult.setUrl(String.format("%s/erp/syncdata/open/objdata/push", ConfigCenter.ERP_OPEN_DOMAIN_URL));
        pushApiFormatResult.setRealUrl(pushApiFormatResult.getUrl());
        List<ApiFormatResult.ApiHeaderMessage> pushHeaders = Lists.newArrayList();
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("token").fieldValue(token).fieldDesc(i18NStringManager.get(I18NStringEnum.s301,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("tenantId").fieldValue(tenantId).fieldDesc(i18NStringManager.get(I18NStringEnum.s302,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("objectApiName").fieldValue(erpObjectApiName).fieldDesc(i18NStringManager.get(I18NStringEnum.s297,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("id").fieldValue("xxxx").fieldDesc(i18NStringManager.get(I18NStringEnum.s303,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("operationType").fieldValue("3").fieldDesc(i18NStringManager.get(I18NStringEnum.s304,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("version").fieldValue("v1").fieldDesc(i18NStringManager.get(I18NStringEnum.s305,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("dataCenterId").fieldValue(dataCenterId).fieldDesc(i18NStringManager.get(I18NStringEnum.s306,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("Content-Type").fieldValue("application/json").fieldDesc(i18NStringManager.get(I18NStringEnum.s275,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("directSync").fieldValue("false").fieldDesc(i18NStringManager.get(I18NStringEnum.s2000,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("destObjectApiName").fieldValue("").fieldDesc(i18NStringManager.get(I18NStringEnum.s2001,lang,tenantId)).build());
        pushHeaders.add(ApiFormatResult.ApiHeaderMessage.builder().fieldApiName("accept-language").fieldValue("").fieldDesc(i18NStringManager.get(I18NStringEnum.s4501,lang,tenantId)).build());
        pushApiFormatResult.setHeaderList(pushHeaders);
        return pushApiFormatResult;
    }

    private ApiFormatResult getApiFormatResult(String tenantId,
                                               String dataCenterId,
                                               String splitObjectApiName,
                                               String erpObjectApiName,
                                               String erpObjectName,
                                               ConnectInfoResult connectInfo,
                                               Map<ErpObjInterfaceUrlEnum,
                                               ErpObjCustomFunctionResult> url2Groovy,
                                               ErpInterfaceFormatResult interfaceFormatResult,
                                               List<ApiFormatResult.ApiHeaderMessage> defaultHeaders,
                                               ErpObjInterfaceUrlEnum urlEnum,
                                               String lang) {
        final String url = getApiUrl(urlEnum, connectInfo) + "?" + interfaceFormatResult.getRequest();

        return generateApiFormatResult(tenantId,
                dataCenterId,
                splitObjectApiName,
                erpObjectApiName,
                erpObjectName,
                connectInfo,
                url2Groovy,
                interfaceFormatResult,
                defaultHeaders,
                urlEnum,
                url,
                url,
                getAPIPrefix,
                lang);
    }

    private ApiFormatResult generateApiFormatResult(String tenantId,
                                                    String dataCenterId,
                                                    String splitObjectApiName,
                                                    String erpObjectApiName,
                                                    String erpObjectName,
                                                    ConnectInfoResult connectInfo,
                                                    Map<ErpObjInterfaceUrlEnum,
                                                    ErpObjCustomFunctionResult> url2Groovy,
                                                    ErpInterfaceFormatResult interfaceFormatResult,
                                                    List<ApiFormatResult.ApiHeaderMessage> defaultHeaders,
                                                    ErpObjInterfaceUrlEnum urlEnum,
                                                    String url,
                                                    String argExample,
                                                    String requestMethod,
                                                    String lang) {
        ApiFormatResult result = new ApiFormatResult();
        result.setObjApiName(erpObjectApiName);
        result.setSplitObjectApiName(splitObjectApiName);
        result.setObjName(erpObjectName);
        result.setApiType(urlEnum);
        result.setApiTypeName(i18NStringManager.get(urlEnum.getI18nKey(),lang,tenantId,urlEnum.getNameDesc()));
        result.setRequestMethod(requestMethod);
        result.setUrl(url);
        result.setRealUrl(getApiUrl(urlEnum, connectInfo));
        result.setHeaderList(defaultHeaders);

        result.setArgFieldList(new ArrayList<>());
        result.getArgFieldList().addAll(interfaceFormatResult.getRequestField());

        result.setArgExample(argExample);

        result.setResultFieldList(new ArrayList<>());
        result.getResultFieldList().addAll(interfaceFormatResult.getResponseField());
        result.setResultExample(interfaceFormatResult.getResponse());

        if (url2Groovy.containsKey(urlEnum)) {
            result.setFuncApiName(url2Groovy.get(urlEnum).getFuncApiName());
            result.setId(url2Groovy.get(urlEnum).getId());
        }

        Result<ErpCustomInterfaceEntity> customInterfaceEntityResult = queryCustomInterfaceData(tenantId,
                dataCenterId,
                erpObjectApiName,
                urlEnum);
        if (customInterfaceEntityResult.getData() != null) {
            result.setCustomInterfaceId(customInterfaceEntityResult.getData().getId());
        }
        return result;
    }

    private ApiFormatResult postApiFormatResult(String tenantId,
                                                String dataCenterId,
                                                String splitObjectApiName,
                                                String erpObjectApiName,
                                                String erpObjectName,
                                                ConnectInfoResult connectInfo,
                                                Map<ErpObjInterfaceUrlEnum,
                                                ErpObjCustomFunctionResult> url2Groovy,
                                                ErpInterfaceFormatResult interfaceFormatResult,
                                                List<ApiFormatResult.ApiHeaderMessage> defaultHeaders,
                                                ErpObjInterfaceUrlEnum urlEnum,
                                                String lang) {
        final String url = getApiUrl(urlEnum, connectInfo);
        final String argExample = interfaceFormatResult.getRequest();

        return generateApiFormatResult(tenantId,
                dataCenterId,
                splitObjectApiName,
                erpObjectApiName,
                erpObjectName,
                connectInfo,
                url2Groovy,
                interfaceFormatResult,
                defaultHeaders,
                urlEnum,
                url,
                argExample,
                postAPIPrefix,
                lang);
    }

    private String getApiUrl(ErpObjInterfaceUrlEnum urlEnum,
                             ConnectInfoResult connectInfo) {
        String baseUrl = ChannelUrlUtils.getApiBaseUrl(connectInfo);
        String urlPrefix = urlEnum.name();
        return baseUrl + urlPrefix;
    }
}
