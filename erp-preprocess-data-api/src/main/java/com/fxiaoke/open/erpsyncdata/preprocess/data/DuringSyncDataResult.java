package com.fxiaoke.open.erpsyncdata.preprocess.data;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/3 14:10:19
 */
@Data
public class DuringSyncDataResult implements Serializable {
    /**
     * 替换主对象信息
     */
    private DuringFuncObjectData objectData;

    /**
     * 替换从对象信息,为null不做替换 key:从对象apiName value:从对象信息列表
     */
    private Map<String, List<DuringFuncObjectData>> details;

    /**
     * 覆盖从对象数据
     */
    private Boolean detailCover = Boolean.FALSE;

    @Data
    public static class DuringFuncObjectData extends ObjectData {
        public static final String erpExtendDataKey = "erpExtendData";
        public static final String erpExtendDataKey2 = "fsShenZhenErpdssExtendData";
        public ErpExtendData getErpExtendData() {
            Object erpExtendData = this.get(erpExtendDataKey2);
            if (Objects.isNull(erpExtendData)) {
                erpExtendData = this.get(erpExtendDataKey);
                if (Objects.isNull(erpExtendData)) {
                    return null;
                }
            }
            return JSON.parseObject(JSON.toJSONString(erpExtendData), ErpExtendData.class);
        }
    }

    @Data
    public static class ErpExtendData implements Serializable {
        /**
         * 删除的字段
         */
        private List<String> removeFields;
        /**
         * 新增字段
         */
        private Map<String, Object> addData;
    }
}
