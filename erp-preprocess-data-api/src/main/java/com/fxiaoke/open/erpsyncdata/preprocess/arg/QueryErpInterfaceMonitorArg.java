package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:06 2021/8/11
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("获取erp接口参数")
public class QueryErpInterfaceMonitorArg extends PageArg{
    /**
     * 对象apiName
     */
    @ApiModelProperty("对象apiName")
    private String erpObjectApiName;
    /**
     * 接口类型
     */
    @ApiModelProperty("接口类型")
    private ErpObjInterfaceUrlEnum interfaceType;
    /**
     * 类型列表
     */
    @ApiModelProperty("接口类型")
    private List<String> interfaceTypes;
    /**
     * 查询时间点
     */
    @ApiModelProperty("查询时间点")
    private Long queryTime;
}
