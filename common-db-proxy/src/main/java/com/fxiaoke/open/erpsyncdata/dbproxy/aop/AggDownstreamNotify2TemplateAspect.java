package com.fxiaoke.open.erpsyncdata.dbproxy.aop;

import com.fxiaoke.open.erpsyncdata.dbproxy.model.RelationErpShardDto;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UpstreamAlertAggregationDao;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/15 12:02:07
 * <p>
 * 下游报警通知聚合后通知到上游企业
 * 这里只负责聚合,通知 {@link com.fxiaoke.open.erpsyncdata.admin.task.AggDownstreamNotify2TemplateTask}
 */
@Component
@Aspect
@Slf4j
public class AggDownstreamNotify2TemplateAspect extends AbstractReplaceEnterpriseAspect {

    @Autowired
    private UpstreamAlertAggregationDao upstreamAlertAggregationDao;

    public static Integer levelIndex;
    public static Integer dcIndex;

    public static Integer PloyDetailIndex;

    @After("execution(* com.fxiaoke.open.erpsyncdata.dbproxy.manager.DataIntegrationNotificationManager.insert(..))")
    public void incUpstreamAgg(JoinPoint jp) {
        try {
            checkAndIncAgg(jp);
        } catch (Exception e) {
            log.error("AggDownstreamNotify2TemplateAspect error", e);
        }
    }

    private void checkAndIncAgg(JoinPoint jp) {
        final String tenantId = getTenantIdByParameter(jp);
        if (Objects.isNull(tenantId) || !managedEnterprise(tenantId)) {
            return;
        }

        final RelationErpShardDto relationErpShardDto = relationErpShardDao.queryFirstNormalByDownstreamId(tenantId);
        if (Objects.isNull(relationErpShardDto)) {
            return;
        }


        final AlarmLevel alarmLevel = getAlarmLevel(jp);
        final String dcId = getDcId(jp);
        List<String> ployDetailIdList = getPloyDetailIdList(jp);
        if (Objects.isNull(alarmLevel) || StringUtils.isBlank(dcId)) {
            return;
        }

        incLevel(relationErpShardDto.getTenantId(), tenantId, dcId, ployDetailIdList, alarmLevel);
    }

    private void incLevel(String upstreamId, String tenantId, String dcId, List<String> ployDetailIdList, AlarmLevel alarmLevel) {
        try {
            upstreamAlertAggregationDao.incAlarmLevel(upstreamId, tenantId, dcId, ployDetailIdList, alarmLevel);
        } catch (Exception e) {
            // 多个下游可能并发导致报错,在重试一次,还失败先忽略
            try {
                upstreamAlertAggregationDao.incAlarmLevel(upstreamId, tenantId, dcId, ployDetailIdList, alarmLevel);
            } catch (Exception ex) {
                log.error("incAlarmLevel error upstreamId:{} downstreamId:{} level:{} dcId:{}", upstreamId, tenantId, alarmLevel, ex);
            }
        }
    }

    private List<String> getPloyDetailIdList(JoinPoint jp) {
        if (Objects.isNull(PloyDetailIndex)) {
            init(jp);
        }

        Object arg = jp.getArgs()[PloyDetailIndex];
        return Objects.nonNull(arg) && arg instanceof List ? (List<String>) arg : new ArrayList<>();
    }

    private String getDcId(JoinPoint jp) {
        if (Objects.isNull(dcIndex)) {
            init(jp);
        }

        final Object arg = jp.getArgs()[dcIndex];
        return Objects.isNull(arg) ? "" : (String) arg;
    }

    protected AlarmLevel getAlarmLevel(JoinPoint jp) {
        if (Objects.isNull(levelIndex)) {
            init(jp);
        }

        final Object arg = jp.getArgs()[levelIndex];
        return Objects.isNull(arg) ? null : (AlarmLevel) arg;
    }

    private void init(JoinPoint jp) {
        synchronized (this) {
            if (Objects.isNull(PloyDetailIndex)) {
                MethodSignature methodSignature = (MethodSignature) jp.getSignature();
                final String[] parameterNames = getParameterNames(methodSignature);
                final Method method = methodSignature.getMethod();
                final Class<?>[] parameterTypes = method.getParameterTypes();
                for (int i = 0; i < parameterNames.length; i++) {
                    if (Objects.equals(parameterNames[i], "ployDetailIdList") && Objects.equals(parameterTypes[i], List.class)) {
                        PloyDetailIndex = i;
                        continue;
                    }
                    if (Objects.equals(parameterNames[i], "dataCenterId") && Objects.equals(parameterTypes[i], String.class)) {
                        dcIndex = i;
                        continue;
                    }
                    if (Objects.equals(parameterTypes[i], AlarmLevel.class)) {
                        levelIndex = i;
                    }
                }
            }
        }
    }
}
