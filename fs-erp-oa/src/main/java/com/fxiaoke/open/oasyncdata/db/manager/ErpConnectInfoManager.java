package com.fxiaoke.open.oasyncdata.db.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.oasyncdata.constant.ErpChannelEnum;
import com.fxiaoke.open.oasyncdata.db.dao.ErpConnectInfoDao;
import com.fxiaoke.open.oasyncdata.db.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.oasyncdata.db.util.DataBaseBatchIndexUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/4/6
 */
@Component
public class ErpConnectInfoManager {

    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    /**
     * 获取企业所有数据中心连接信息
     *
     * @param tenantId
     * @return
     */
    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public List<ErpConnectInfoEntity> listByTenantId(String tenantId) {
        return erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listErpDcByTenantId(tenantId);
    }

    @Cached(expire = 30, cacheType = CacheType.LOCAL)
    public ErpConnectInfoEntity getCrmDcByTenantId(String tenantId) {
        List<ErpConnectInfoEntity> erpConnectInfoEntities = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByTenantId(tenantId);
        erpConnectInfoEntities.removeIf(v->!v.getChannel().equals(ErpChannelEnum.CRM));
        return erpConnectInfoEntities.get(0);
    }

    @Cached(expire = 30,cacheType = CacheType.LOCAL)
    public ErpConnectInfoEntity getByIdAndTenantId(String tenantId, String id) {
        return erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getByIdAndTenantId(tenantId,id);
    }

}
