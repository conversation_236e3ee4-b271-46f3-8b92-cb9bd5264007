package com.fxiaoke.open.erpsyncdata.custom.service.impl;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.open.erpsyncdata.custom.service.FeatureSyncService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpDataFeatureDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFeatureSyncDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpDataFeatureEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFeatureSyncEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.model.FeatureCalculation;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.*;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpDataPreprocessService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ErpSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/5/22
 */
@Slf4j
@Service
public class FeatureSyncServiceImpl implements FeatureSyncService {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private ErpFeatureSyncDao erpFeatureSyncDao;
    @Autowired
    private ErpDataFeatureDao erpDataFeatureDao;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Resource(name = "allErpDataPreprocessService")
    private ErpDataPreprocessService erpDataPreprocessService;
    @Resource(name = "allErpSyncService")
    private ErpSyncService erpSyncService;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private RedisDataSource redisDataSource;

    private static final ForkJoinPool JOIN_POOL = new ForkJoinPool(20);

    @Override
    public Result<String> executeTenantFeatureSync(String tenantId, String executor) {
        TraceUtil.initTraceWithFormat(tenantId);
        log.info("executeTenantFeatureSync begin,tenantId:{},executor:{}", tenantId, executor);
        List<ErpFeatureSyncEntity> erpFeatureSyncEntities = erpFeatureSyncDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).queryAllByTenantId(tenantId);
        StringBuilder result = new StringBuilder();
        for (ErpFeatureSyncEntity erpFeatureSyncEntity : erpFeatureSyncEntities) {
            result.append(erpFeatureSyncEntity.getSyncPloyDetailId()).append(":");
            String lockKey = String.format("ERPSYNCDATA:FEATURE:%s:%s", tenantId, erpFeatureSyncEntity.getId());
            //加锁，36小时超时
            String setex = redisDataSource.get(this.getClass().getSimpleName()).set(lockKey, LocalDateTime.now().toString(), "NX", "EX", 60 * 60 * 36);
            if (!"OK".equals(setex)) {
                result.append(i18NStringManager.getByEi(I18NStringEnum.s3759, tenantId));
                continue;
            }
            try {
                String s = fieldFeatureSync(erpFeatureSyncEntity);
                result.append(s);
            } catch (Exception e) {
                log.error("field feature sync execute error,feature:{}", erpFeatureSyncEntity, e);
                result.append(i18NStringManager.getByEi(I18NStringEnum.s3760, tenantId)).append(e.getClass().getName()).append(e.getMessage());
            } finally {
                redisDataSource.get(this.getClass().getSimpleName()).del(lockKey);
                result.append(";");
            }
        }
        return Result.newSuccess(result.toString());
    }


    /**
     * 选取字段特征同步
     *
     * @param erpFeatureSyncEntity
     * @return
     */
    private String fieldFeatureSync(ErpFeatureSyncEntity erpFeatureSyncEntity) {
        String tenantId = erpFeatureSyncEntity.getTenantId();
        Integer ei = Integer.parseInt(tenantId);
        String condition = erpFeatureSyncEntity.getCondition();
        String calculationStr = erpFeatureSyncEntity.getCalculation();
        FeatureCalculation calculation = JacksonUtil.fromJson(calculationStr, FeatureCalculation.class);
        //ERP往CRM方向的策略明细
        SyncPloyDetailEntity syncPloyDetailEntity = adminSyncPloyDetailDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getById(tenantId, erpFeatureSyncEntity.getSyncPloyDetailId());
        if (!syncPloyDetailEntity.getSourceTenantType().equals(TenantType.ERP)) {
            return i18NStringManager.getByEi(I18NStringEnum.s3653,tenantId);
        }
        String sourceErpObjApiName = syncPloyDetailEntity.getSourceObjectApiName();
        String destCrmObjectApiName = syncPloyDetailEntity.getDestObjectApiName();
        ControllerListArg controllerListArg = GsonUtil.fromJson(condition, ControllerListArg.class);
        log.info("list arg:{}", GsonUtil.toJson(controllerListArg));
        String searchQueryInfo = controllerListArg.getSearchQueryInfo();
        SearchQuery searchQuery = GsonUtil.fromJson(searchQueryInfo, SearchQuery.class);
        searchQuery.setLimit(200);
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        //先查出所有符合条件的数据，再进行同步，降低循环拉取分页出问题的情况。
        int needSyncErpDataIdCount = 0;
        //由于操作有网络请求，提高并行数
        for (int i = 0; i < 1000; i++) {
            //最多循环一千次，20万数据
            searchQuery.setOffset(i * 200);
            controllerListArg.setSearchQuery(searchQuery);
            Page<ObjectData> data = metadataControllerService.list(headerObj, destCrmObjectApiName, controllerListArg).getData();
            if (data.getDataList().isEmpty()) {
                break;
            }
            List<String> crmIds = data.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
            //通过目标查数据映射
            Set<String> erpIds = syncDataMappingsDao.setTenantId(tenantId).queryByDestDataIdList(tenantId, sourceErpObjApiName, crmIds, destCrmObjectApiName).stream().filter(SyncDataMappingsEntity::getIsCreated).map(SyncDataMappingsEntity::getSourceDataId).collect(Collectors.toSet());
            //反向通过sourceids查
            Set<String> erpIds2 = syncDataMappingsDao.setTenantId(tenantId).queryBySourceDataIdList(tenantId, destCrmObjectApiName, crmIds, sourceErpObjApiName).stream().filter(SyncDataMappingsEntity::getIsCreated).map(SyncDataMappingsEntity::getDestDataId).collect(Collectors.toSet());
            erpIds.addAll(erpIds2);
            Set<String> collect = new HashSet<>();
            if (calculation.getEnableParallel()) {
                try {
                    collect = JOIN_POOL.submit(() -> erpIds.parallelStream().filter(erpId ->
                            checkFeatureAndSync(tenantId, sourceErpObjApiName, destCrmObjectApiName, erpId, calculation))
                            .collect(Collectors.toSet())).get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("parallelStream check feature failed", e);
                }
            } else {
                //默认情况下不用多线程获取
                collect = erpIds.stream().filter(erpId ->
                        checkFeatureAndSync(tenantId, sourceErpObjApiName, destCrmObjectApiName, erpId, calculation))
                        .collect(Collectors.toSet());
            }
            log.info("check need sync {}_{},index:{}, Ids:{}", tenantId, sourceErpObjApiName, i, collect);
            needSyncErpDataIdCount += collect.size();
        }
        return Integer.toString(needSyncErpDataIdCount);
    }

    private boolean checkFeatureAndSync(String tenantId, String erpObjectApiName, String destCrmObjectApiName, String erpId, FeatureCalculation featureCalculation) {
        try {
            boolean b = checkFeature(tenantId, erpObjectApiName, erpId, featureCalculation);
            if (b) {
                ErpIdArg erpIdArg = new ErpIdArg();
                erpIdArg.setTenantId(tenantId);
                erpIdArg.setObjAPIName(erpObjectApiName);
                erpIdArg.setDataId(erpId);
                erpSyncService. syncSingletonData(erpIdArg, destCrmObjectApiName);
            }
            return b;
        } catch (Exception e) {
            log.error("checkFeatureAndSync get error,tenantID:{},erpObjApiName:{},id:{}", tenantId, erpObjectApiName, erpId, e);
            return false;
        }
    }

    /**
     * 检查特征值是否变化
     *
     * @param tenantId
     * @param erpObjectApiName
     * @param erpId
     * @param featureCalculation
     * @return
     */
    private boolean checkFeature(String tenantId, String erpObjectApiName, String erpId, FeatureCalculation featureCalculation) {
        ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setObjAPIName(erpObjectApiName);
        erpIdArg.setTenantId(tenantId);
        erpIdArg.setDataId(erpId);
        Result<SyncDataContextEvent> erpObjDataById = erpDataPreprocessService.getErpObjDataById(erpIdArg);
        if (!erpObjDataById.isSuccess()) {
            log.warn("get data by id error,erpIdArg:{}", erpIdArg);
            return false;
        }
        String featureValue = calFeature(erpObjDataById.getData(), featureCalculation);
        ErpDataFeatureEntity featureEntity = erpDataFeatureDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).getOne(tenantId, erpId);
        log.debug("featureValue:{},featureCalculation:{},featureEntity:{}", erpObjDataById, featureValue, featureEntity);
        if (featureEntity == null) {
            //原来未存在特征值
            if (featureValue.startsWith("0")) {
                //新特征值为空，不落库
                return false;
            } else {
                //新增特征值数据,不触发同步
                featureEntity = ErpDataFeatureEntity.builder()
                        .id(idGenerator.get())
                        .tenantId(tenantId)
                        .objectApiName(erpObjectApiName)
                        .dataId(erpId)
                        .feature(featureValue).build();
                int insert = erpDataFeatureDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).insert(featureEntity);
                return true;
            }
        } else {
            if (featureValue.equals(featureEntity.getFeature())) {
                //有特征值但没变化，该数据为不健康调用，后续考虑在这做出限制方法
                return false;
            } else {
                //更新特征值数据,不触发同步
                featureEntity.setFeature(featureValue);
                featureEntity.setUpdateTime(new Date());
                erpDataFeatureDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).updateById(featureEntity);
                return true;
            }
        }
    }

    private String calFeature(SyncDataContextEvent erpObjDataResult, FeatureCalculation calculation) {
        com.fxiaoke.open.erpsyncdata.common.data.ObjectData masterData = erpObjDataResult.getSourceData();
        //找出特征字段
        List<String> curFieldValues = new ArrayList<>();
        calculation.getFieldConfig().getMasterObjFields().forEach(f -> curFieldValues.add(masterData.getString(f)));
        calculation.getFieldConfig().getDetailObjFields().forEach((detailObjApiName, detailFields) -> {
            List<com.fxiaoke.open.erpsyncdata.common.data.ObjectData> detailList = erpObjDataResult.getDetailData().getOrDefault(detailObjApiName, new ArrayList<>());
            detailList.forEach(
                    v -> detailFields.forEach(
                            f -> curFieldValues.add(v.getString(f))));
        });
        //有值字段计数
        int validCount = 0;
        StringBuilder fieldJoin = new StringBuilder();
        for (String curFieldValue : curFieldValues) {
            fieldJoin.append(curFieldValue);
            if (StringUtils.isNotBlank(curFieldValue)) {
                validCount++;
            }
        }
        //计算md5
        String s = Md5Util.md5(fieldJoin.toString());
        return validCount + s;
    }
}
