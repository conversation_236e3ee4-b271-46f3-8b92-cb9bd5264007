package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailCreateArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.SyncPloyDetailUpdateFieldMappingsArg;
import com.fxiaoke.open.erpsyncdata.preprocess.result.DataCenterInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.EmployeeMappingResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Maps;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface DingCrmConnectorService {
    String CRM_CUSTOMER = "crm_customer";
    String CRM_CUSTOMER_PERSONAL = "crm_customer_personal";
    String CRM_CONTACT = "crm_contact";

    Map<String,String> crmObjectMapping = new HashMap<>();

    Result<DataCenterInfoResult> queryDataCenterInfo(String tenantId);

    Result<Boolean> enableVisitPrivilege(String tenantId);

    Result<Boolean> updateConnectInfo(String tenantId);

    Result<List<EmployeeMappingResult>> batchBindEmployeeMapping(String tenantId,List<EmployeeMappingResult> employeeMappingList);

    Result<String> bindEmployeeMapping(String tenantId,EmployeeMappingResult employeeMappingResult);

    Result<String> updateErpObjectList(String tenantId,String dataCenterId, List<ErpObjectRelationshipResult> erpObjectRelationshipList);

    Result<String> presetErpObjectAndFieldList(String tenantId,String dataCenterId);

    Result<Boolean> presetPloyAndPloyDetail(String tenantId,
                               String dataCenterId,
                               List<SyncPloyDetailCreateArg> ployDetailCreateArgList,
                               List<SyncPloyDetailUpdateFieldMappingsArg> fieldMappingsArgList);

    Result<Boolean> batchPresetPloyAndPloyDetail(String tenantId, String dataCenterId);

    Result<String> dingCrmObjectId2FsCrmObjectId(String tenantId, String dingObjectApiName, String dingObjectId);

    Result<String> fsCrmObjectId2DingCrmObjectId(String tenantId, String crmObjectApiName, String crmObjectId);

    Result<String> addCustomer2CRM(String tenantId, String customerId, String customerName);

    Result<String> addContact2CRM(String tenantId, String crmAccountId,String contactId, String contactName, String phone, String email);

    Result<Boolean> updateSyncConditions(String tenantId, String ployDetailId,String objectApiName);
}
