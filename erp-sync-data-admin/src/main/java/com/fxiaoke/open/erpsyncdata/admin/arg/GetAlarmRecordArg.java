package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import lombok.Data;

import javax.validation.constraints.Max;
import java.util.List;

@Data
public class GetAlarmRecordArg extends CepArg {
    private String tenantId; //可不传
    private String dataCenterId; //可不传
    private List<String> ployDetailIdList; //可不传
    private AlarmType alarmType; //可不传
    private AlarmLevel alarmLevel;//可不传
    private Long startTime; //可不传，如果传了，startTime和endTime必须同时传
    private Long endTime; //可不传，如果传了，startTime和endTime必须同时传
    private int pageNumber; //必传  1代表第一页
    @Max(100)
    private int pageSize; //必传
}
