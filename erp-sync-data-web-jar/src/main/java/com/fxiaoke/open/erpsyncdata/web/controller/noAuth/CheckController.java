package com.fxiaoke.open.erpsyncdata.web.controller.noAuth;

import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.web.controller.AsyncSupportController;
import com.fxiaoke.open.erpsyncdata.web.interceptor.SuperAdminTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.concurrent.TimeUnit;

import static com.fxiaoke.open.erpsyncdata.web.interceptor.SuperAdminTokenUtil.erpDssToken;

/**
 * 检查接口
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/8/18
 */
@RestController
@RequestMapping("erp/syncdata/noAuth")
@Slf4j
public class CheckController extends AsyncSupportController {

    @RequestMapping("/check")
    public String check(HttpServletResponse response, HttpServletRequest request) {
        log.info("header:{}", request.getHeaderNames());
        log.info("param:{}", request.getQueryString());
        Cookie fsAuthXC = new Cookie("FSAuthXC", "0G60n4qzVu40000RldOapMWHs8FePHqAskud3esbtmDgbCVMboJVmrwClDA7iPJS0wfoIzwfgp9yAruCyWChAOFF9RKTYKXGdMhzZVyNvKDtwh4ka30xr8PwnF39ORc0Qs3sHzJT2CmeiYyxUSyvWeTzBtJf0u8cZZsMi5KFhN4V3ZbyD361yy7WMdp09krhXQZrJGcjLGHYwUUfZayH32VoXpa59pXoHcx9OfH1Cypo9vocmN3yece8y1Xk1");
        fsAuthXC.setMaxAge(36000);
        fsAuthXC.setPath("/");
        fsAuthXC.setSecure(true);
        fsAuthXC.setHttpOnly(true);
        response.addCookie(fsAuthXC);
        response.setHeader("Set-Cookie", response.getHeader("Set-Cookie") + ";SameSite=None");
        return "hello world";
    }

    /**
     * 鉴权并设置cookie
     * @param token
     * @param response
     * @return
     */
    @GetMapping("authToken")
    public Result<Boolean> authToken(@RequestParam String token, HttpServletResponse response){
        boolean auth = SuperAdminTokenUtil.checkToken(token);
        if (auth){
            //鉴权成功，设置到cookie里面。
            Cookie cookie = new Cookie(erpDssToken, token);
            cookie.setMaxAge((int) TimeUnit.MINUTES.toSeconds(10L));
            cookie.setPath("/erp/syncdata/superadmin");
            response.addCookie(cookie);
        }
        return Result.newSuccess(auth);
    }
}
