package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.AlarmSettingData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.google.common.base.Splitter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;

/**
 * 告警规则表
 * <AUTHOR>
 * @date 2023-11-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "erp_alarm_rule")
public class ErpAlarmRuleEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 企业ei
     */
    @TenantID
    private String tenantId;

    /**
     * 数据中心id
     */
    private String dataCenterId;

    /**
     * 集成流ID，多个集成流ID以分号分隔，all 代表应用当前连接器下所有集成流
     * 比如： id1;id2
     *
     */
    private String ployDetailIds;

    /**
     * 告警规则类型
     */
    private AlarmRuleType alarmRuleType;

    /**
     * 告警规则类型
     */
    private String alarmRuleName;

    /**
     * 告警类型
     */
    private AlarmType alarmType;

    /**
     * 告警等级
     */
    private AlarmLevel alarmLevel;

    /**
     * 告警阀值
     */
    private Integer threshold;

    /**
     * 通知类型:com.fxiaoke.open.erpsyncdata.preprocess.constant.NotifyType
     *
     *
     */
    private ListStringData notifyType;

    /**
     * 通知设置
     */
    private AlarmSettingData alarmSetting;

    /**
     * 通知人员ID，多个人员以分号分隔
     * 比如：userId1;userId2
     *
     */
    private String userIds;

    /**
     * 通知角色ID，多个角色以分号分隔
     * 比如：roleId1;roleId2
     *
     */
    private String roleIds;

   /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    public List<String> getUserIdList() {
        if(StringUtils.isEmpty(userIds)) return null;
        List<String> userIdList = Splitter.on(";").splitToList(userIds);
        return userIdList;
    }

    public List<String> getRoleIdList() {
        if(StringUtils.isEmpty(roleIds)) return null;
        List<String> roleIdList = Splitter.on(";").splitToList(roleIds);
        return roleIdList;
    }

    public List<Integer> getUserIdList2() {
        List<String> userIdList = getUserIdList();
        if(CollectionUtils.isEmpty(userIdList)) return null;

        List<Integer> userList = new ArrayList<>();
        for(String userId : userIdList) {
            userList.add(Integer.valueOf(userId));
        }
        return userList;
    }
}