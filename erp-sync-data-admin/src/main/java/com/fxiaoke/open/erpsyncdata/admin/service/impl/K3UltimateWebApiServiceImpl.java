package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.model.k3ultimate.K3UltimateGetApiTemplateModel;
import com.fxiaoke.open.erpsyncdata.admin.service.K3UltimateWebApiService;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.ErpK3UltimateApiTemplateManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.K3UltimateApiTemplateManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateBaseApiTemplate;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3ultimate.template.K3UltimateExecuteStepEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpK3UltimateApiTemplateEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class K3UltimateWebApiServiceImpl implements K3UltimateWebApiService {
    @Autowired
    private K3UltimateApiTemplateManager k3UltimateApiTemplateManager;
    @Autowired
    private ErpK3UltimateApiTemplateManager erpK3UltimateApiTemplateManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;

    @Override
    public Result<K3UltimateGetApiTemplateModel> getApiTemplate(String tenantId,
                                                                String dataCenterId,
                                                                String erpObjApiName,
                                                                String lang) {
        K3UltimateBaseApiTemplate apiTemplate = k3UltimateApiTemplateManager.getApiTemplate(tenantId,
                dataCenterId,
                erpObjApiName);
        if(apiTemplate==null) {
            apiTemplate = new K3UltimateBaseApiTemplate();
        }

        K3UltimateGetApiTemplateModel getApiTemplateModel = new K3UltimateGetApiTemplateModel();
        getApiTemplateModel.setApiList(K3UltimateGetApiTemplateModel.getApiList(i18NStringManager,lang,tenantId,apiTemplate));

        K3UltimateGetApiTemplateModel.StepModel addStepModel = new K3UltimateGetApiTemplateModel.StepModel();
        addStepModel.setStepList(Lists.newArrayList(
                new K3UltimateGetApiTemplateModel.StepItem(i18NStringManager.get(I18NStringEnum.s652, lang, tenantId),//新增
                        K3UltimateExecuteStepEnum.ADD,
                        Lists.newArrayList(
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2202, lang, tenantId),"batchAddApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2203, lang, tenantId),"batchUpdateApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2204, lang, tenantId),"batchSubmitApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2205, lang, tenantId), "batchUnSubmitApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2206, lang, tenantId), "batchAuditApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2207, lang, tenantId), "batchUnAuditApi", null, false)
                        ),false),
                new K3UltimateGetApiTemplateModel.StepItem(i18NStringManager.get(I18NStringEnum.s2212, lang, tenantId),//审核
                        K3UltimateExecuteStepEnum.AUDIT,
                        Lists.newArrayList(
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2202, lang, tenantId),"batchAddApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2203, lang, tenantId),"batchUpdateApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2204, lang, tenantId),"batchSubmitApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2205, lang, tenantId), "batchUnSubmitApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2206, lang, tenantId), "batchAuditApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2207, lang, tenantId), "batchUnAuditApi", null, true)
                        ),false)
        ));


        K3UltimateGetApiTemplateModel.StepModel updateStepModel = new K3UltimateGetApiTemplateModel.StepModel();
        updateStepModel.setStepList(Lists.newArrayList(
                new K3UltimateGetApiTemplateModel.StepItem(i18NStringManager.get(I18NStringEnum.s364, lang, tenantId),//更新
                        K3UltimateExecuteStepEnum.UPDATE,
                        Lists.newArrayList(
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2202, lang, tenantId),"batchAddApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2203, lang, tenantId),"batchUpdateApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2204, lang, tenantId),"batchSubmitApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2205, lang, tenantId), "batchUnSubmitApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2206, lang, tenantId), "batchAuditApi", null, false),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2207, lang, tenantId), "batchUnAuditApi", null, false)
                        ),false),
                new K3UltimateGetApiTemplateModel.StepItem(i18NStringManager.get(I18NStringEnum.s2212, lang, tenantId),//审核
                        K3UltimateExecuteStepEnum.AUDIT,
                        Lists.newArrayList(
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2202, lang, tenantId),"batchAddApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2203, lang, tenantId),"batchUpdateApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2204, lang, tenantId),"batchSubmitApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2205, lang, tenantId), "batchUnSubmitApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2206, lang, tenantId), "batchAuditApi", null, true),
                                new K3UltimateGetApiTemplateModel.ApiItem(i18NStringManager.get(I18NStringEnum.s2207, lang, tenantId), "batchUnAuditApi", null, true)
                        ),false)
        ));

        for(K3UltimateGetApiTemplateModel.StepItem stepItem : addStepModel.getStepList()) {
            if(stepItem.getExecuteStepEnum()==apiTemplate.getAddExecuteStep()) {
                stepItem.setChecked(true);
            }
        }
        for(K3UltimateGetApiTemplateModel.StepItem stepItem : updateStepModel.getStepList()) {
            if(stepItem.getExecuteStepEnum()==apiTemplate.getUpdateExecuteStep()) {
                stepItem.setChecked(true);
            }
        }
        getApiTemplateModel.setAdd(addStepModel);
        getApiTemplateModel.setUpdate(updateStepModel);
        return Result.newSuccess(getApiTemplateModel);
    }

    @Override
    public Result<Void> updateApiTemplate(String tenantId,
                                          String dataCenterId,
                                          String erpObjApiName,
                                          K3UltimateBaseApiTemplate apiTemplate) {
        if (apiTemplate.getUpdateExecuteStep() == null && apiTemplate.getUpdateExecuteStep() == null) {
            if (StringUtils.isEmpty(apiTemplate.getBatchQueryApi())) {
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
            }
        }
        if (apiTemplate.getAddExecuteStep() != null) {
            if (apiTemplate.getAddExecuteStep() == K3UltimateExecuteStepEnum.ADD) {
                if (StringUtils.isEmpty(apiTemplate.getBatchAddApi())) {
                    return Result.newError(ResultCodeEnum.PARAM_ERROR);
                }
            } else if (apiTemplate.getAddExecuteStep() == K3UltimateExecuteStepEnum.AUDIT) {
                if (StringUtils.isEmpty(apiTemplate.getBatchAddApi())
                        || StringUtils.isEmpty(apiTemplate.getBatchSubmitApi())
                        || StringUtils.isEmpty(apiTemplate.getBatchAuditApi())) {
                    return Result.newError(ResultCodeEnum.PARAM_ERROR);
                }
            }
        }
        if (apiTemplate.getUpdateExecuteStep() != null) {
            if (apiTemplate.getUpdateExecuteStep() == K3UltimateExecuteStepEnum.UPDATE) {
                if (StringUtils.isEmpty(apiTemplate.getBatchUpdateApi())) {
                    return Result.newError(ResultCodeEnum.PARAM_ERROR);
                }
            } else if (apiTemplate.getUpdateExecuteStep() == K3UltimateExecuteStepEnum.AUDIT) {
                if (StringUtils.isEmpty(apiTemplate.getBatchUpdateApi())
                        || StringUtils.isEmpty(apiTemplate.getBatchSubmitApi())
                        || StringUtils.isEmpty(apiTemplate.getBatchUnSubmitApi())
                        || StringUtils.isEmpty(apiTemplate.getBatchAuditApi())
                        || StringUtils.isEmpty(apiTemplate.getBatchUnAuditApi())) {
                    return Result.newError(ResultCodeEnum.PARAM_ERROR);
                }
            }
        }

        int count = 0;

        ErpK3UltimateApiTemplateEntity entity = erpK3UltimateApiTemplateManager.findData(tenantId, dataCenterId, erpObjApiName);
        if(entity==null) {
            entity = new ErpK3UltimateApiTemplateEntity();
            entity.setId(idGenerator.get());
            entity.setTenantId(tenantId);
            entity.setDataCenterId(dataCenterId);
            entity.setErpObjApiName(erpObjApiName);
            entity.setApiTemplate(JSONObject.toJSONString(apiTemplate));
            entity.setCreateTime(System.currentTimeMillis());
            entity.setUpdateTime(System.currentTimeMillis());
            count = erpK3UltimateApiTemplateManager.insert(entity);
        } else {
            entity.setApiTemplate(JSONObject.toJSONString(apiTemplate));
            entity.setUpdateTime(System.currentTimeMillis());
            count = erpK3UltimateApiTemplateManager.update(entity);
        }
        return count==1 ? Result.newSuccess() : Result.newError(ResultCodeEnum.UPDATE_K3_ULTIMATE_API_TEMPLATE_FAILED);
    }
}
