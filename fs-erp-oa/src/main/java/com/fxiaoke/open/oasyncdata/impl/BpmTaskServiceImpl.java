package com.fxiaoke.open.oasyncdata.impl;

import com.fxiaoke.crmrestapi.common.data.FieldDescribe;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.open.oasyncdata.manager.UserManager;
import com.fxiaoke.open.oasyncdata.model.OAObjectFieldVO;
import com.fxiaoke.open.oasyncdata.service.ObjectServiceFiledConvert;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/25 11:35 业务流的审批
 * @Version 1.0
 */
@Service("bpmTaskServiceImpl")
public class BpmTaskServiceImpl implements ObjectServiceFiledConvert {

    @Autowired
    private UserManager userManager;
    @ReloadableProperty("oa.author.app.url")
    private String oaAuthorAppUrl;

    @ReloadableProperty("oa.author.web.url")
    private String oaAuthorWebUrl;

    @Override
    public void dealSpecialField(ControllerGetDescribeResult objectData, String tenantId, String dataCenterId) {
        //人员的描述，全部替换成对应的erp人员
        HashMap<String, FieldDescribe> fields = objectData.getDescribe().getFields();
        fields.keySet().forEach(item -> {
            switch (fields.get(item).getType()) {
                case "employee":
                    Object employeeValue = objectData.getData().get(item);
                    if (ObjectUtils.isEmpty(employeeValue)) {
                        break;
                    }
                    List<String> empIds = (List<String>) employeeValue;
                    if(!item.equals("candidateIds")){
                        List<String> erpSubmitterCodeList = userManager.getUserCodeList(empIds, tenantId,dataCenterId);
                        if(CollectionUtils.isNotEmpty(erpSubmitterCodeList)){
                            objectData.getData().put(item, erpSubmitterCodeList.get(0));
                        }
                    }

                    break;
                case "date_time":
                    Object dataTime = objectData.getData().get(item);
                    if (ObjectUtils.isEmpty(dataTime)) {
                        break;
                    }
                    if (item.equals("create_time")) {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = new Date(((Double) dataTime).longValue());
                        String createTime = simpleDateFormat.format(date);
                        objectData.getData().put(item, createTime);
                    }
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public String specialHand(OAObjectFieldVO oaObjectFieldVO, ObjectData data, String tenantId, String resultJson,String dataCenterId) {
        String apiName = data.get("objectApiName").toString();
        String dataId = data.get("objectDataId").toString();
        //webUrl和appUrl两个占位符暂时无法通用，复星OA专用免登录
        if ("#webUrl#".equals(oaObjectFieldVO.getReplaceName())) {
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), URLEncoder.encode(oaAuthorWebUrl + apiName + "/" + dataId +
                    "/" + tenantId));
        }

        if ("#appUrl#".equals(oaObjectFieldVO.getReplaceName())) {
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), URLEncoder.encode(oaAuthorAppUrl + apiName + "/" + dataId +
                    "/" + tenantId));
        }
        if ("#currentTime#".equals(oaObjectFieldVO.getReplaceName())) {
            long time = System.currentTimeMillis();
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), String.valueOf(time));
        }
        // 特殊的时间格式，用来做鉴权
        if ("#currentTime2#".equals(oaObjectFieldVO.getReplaceName())) {
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), df.format(new Date()));
        }
        // 取nickname，特殊加的字段
        if ("#G051".equals(oaObjectFieldVO.getReplaceName())) {
            Map<String, String> submitterMap = (Map) data.get("owner__r");
            if (ObjectUtils.isEmpty(submitterMap) || ObjectUtils.isEmpty(submitterMap.get("nickname"))) {
                return StringUtils.EMPTY;
            }
            //避免为空导致的异常
            String submitterName = submitterMap.get("nickname");
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), submitterName);
        }
        if ("#G037".equals(oaObjectFieldVO.getReplaceName())) {
            Long time = new Double((Double) data.get("last_modified_time")).longValue();
            return resultJson.replaceAll(oaObjectFieldVO.getReplaceName(), String.valueOf(time));
        }
        if (data.get(oaObjectFieldVO.getFieldApiName()) == null || "#F001".equals(oaObjectFieldVO.getReplaceName())) {
            return resultJson;
        }
        return null;
    }

//    @Override
//    public List<String> getCRMReceivers(ObjectData objectData, String tenantId) {
//        List<String> userCodeList = (List) objectData.get("candidateIds");
//        return userCodeList;
//    }

    @Override
    public Map<String,String> builderTitle(ObjectData objectData,String bizType, String lang,String dataCenterId) {

        HashMap<String, String> objectNameMap = Maps.newHashMap();
        objectNameMap.put("object_name",String.valueOf(objectData.get("objectApiName__r")));
        objectNameMap.put("data_name",String.valueOf(objectData.get("objectDataId__r")));
        return objectNameMap;
    }
}
