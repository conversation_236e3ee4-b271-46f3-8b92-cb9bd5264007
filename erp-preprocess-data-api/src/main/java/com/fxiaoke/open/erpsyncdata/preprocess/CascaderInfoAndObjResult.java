package com.fxiaoke.open.erpsyncdata.preprocess;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjNameDescResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/22 17:26
 * 级联连接器对象信息
 * @desc
 */
@Data
@ApiModel
public class CascaderInfoAndObjResult implements Serializable {
    @ApiModelProperty("数据中心id")
    public String dataCenterId;
    @ApiModelProperty("连接信息")
    public String dataCenterName;
    @ApiModelProperty("渠道")
    public ErpChannelEnum channelEnum;
    @ApiModelProperty("真实渠道")
    public String connectorKey;
    @ApiModelProperty("iconUrl")
    public String iconUrl;
    @ApiModelProperty("对象信息")
    public List<ErpObjNameDescResult> objectDescResults;
}
