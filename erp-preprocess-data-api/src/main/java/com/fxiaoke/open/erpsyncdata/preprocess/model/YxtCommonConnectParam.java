package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class YxtCommonConnectParam extends BaseLangConnectParam {

    @ApiModelProperty("push数据的对象名称")
    private List<String> pushDataApiNames=new ArrayList<>();

}
