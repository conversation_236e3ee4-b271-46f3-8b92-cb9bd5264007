package com.fxiaoke.open.erpsyncdata.common.util;

import com.fxiaoke.open.erpsyncdata.common.thread.NamedThreadPoolExecutor;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by huangzhw on 2017/7/26.
 */
@Slf4j
public class CommonPoolUtil {
    private static final int DEFAULT_TIMEOUT_SECOND = 60;

    public CommonPoolUtil() {
    }

    public static <R, T> List<R> execute(List<T> srcDatas, int cutline, Function<List<T>, List<R>> function) {
        return executeNewExecutor("CommonPoolUtil.execute", srcDatas, cutline, function);
    }

    /**
     * 异步分批处理数据
     *
     * @param srcDatas 待处理数据
     * @param function 处理函数
     * @param cutline 切割线，分批处理时按照多少条切割
     * @param <R> 返回值类型
     * @param <T> 入参类型
     */
    public static <R, T> List<R> executeNewExecutor(String name, List<T> srcDatas, int cutline, Function<List<T>, List<R>> function) {
        int coreSize = srcDatas.size() / cutline;
        coreSize = getThreadCoreSize(coreSize);
        ThreadPoolExecutor executor = new NamedThreadPoolExecutor(name, coreSize, coreSize * 2);
        int maxPageNumber = (int) Math.ceil(srcDatas.size() * 1.0 / cutline);
        int fromIndex = 0;
        int toIndex = cutline;
        List<Future<List<R>>> futures = new CopyOnWriteArrayList<>();
        for (int i = 0; i < maxPageNumber; i++) {
            if (toIndex > srcDatas.size()) {
                toIndex = srcDatas.size();
            }
            List<T> onePage = srcDatas.subList(fromIndex, toIndex);
            Future<List<R>> future = executor.submit(MonitorTaskWrapper.wrap(() -> function.apply(onePage)));
            futures.add(future);
            fromIndex += cutline;
            toIndex += cutline;
        }

        List<R> result = new ArrayList<>();
        for (int i = 0; i < futures.size(); i++) {
            Future<List<R>> future = futures.get(i);
            try {
                if (future.get() != null) {
                    result.addAll(future.get(DEFAULT_TIMEOUT_SECOND, TimeUnit.SECONDS));
                } else {
                    log.warn("the future get is null, line index : {}", i);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        executor.shutdown();
        return result;
    }

    private static int getThreadCoreSize(int taskSize) {
        int coreSize = (int) (taskSize * 0.6);
        if (coreSize == 0) {
            coreSize = 1;
        }
        if (coreSize > 50) {
            coreSize = 50;
        }
        return coreSize;
    }
}
