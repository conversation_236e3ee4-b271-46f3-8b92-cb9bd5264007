package com.fxiaoke.open.oasyncdata.constant;

import com.google.common.collect.ImmutableSet;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 13:36 2020/8/19
 * @Desc:
 */
public enum ErpObjInterfaceUrlEnum {
    create,//创建
    createDetail,//创建明细
    update,//更新
    updateDetail,//更新明细
    queryMasterBatch,//批量查询
    queryMasterById,//通过id查询
    push,//推送
    invalid,
    invalidDetail,//作废明细
    crmCreate,
    crmUpdate,
    crmInvalid,
    ;

    public static final Set<ErpObjInterfaceUrlEnum> crmTypes = ImmutableSet.of(crmCreate,crmUpdate,crmInvalid);
}
