package com.fxiaoke.open.erpsyncdata.probe.controller;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.arg.RetryConfig;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.TriggerPollingData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ProbeErpDataService;
import com.fxiaoke.open.erpsyncdata.probe.service.ProbeDataTaskService;
import com.fxiaoke.open.erpsyncdata.probe.service.RetryFailDataService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/9
 */
@RestController
@RequestMapping("erp/syncdata/probedata")
@Slf4j
public class ProbeDataController {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private ProbeDataTaskService probeDataTaskService;
    @Autowired
    private RetryFailDataService retryFailDataService;
    @Autowired
    private ProbeErpDataService probeErpDataService;

    @RequestMapping("/check.io")
    public String check(){
        return "hello,erp sync data task!";
    }

    /**
     * 执行策略，会进入队列，并尝试出队触发轮询。
     * 无论定时任务还是主动触发的轮询，轮询结束后会重新检查队列。
     *
     * @param tenantId
     * @param objApiName
     * @return
     */
    @PostMapping("/executePloys")
    public Result<Void> executePloys(@RequestParam("tenantId") String tenantId,
                                     @RequestParam(value = "objApiName") String objApiName) {
        return probeDataTaskService.enqueueAndTryExecute(tenantId, objApiName);
    }
    /**
     * 手动触发（轮询）同步
     * 不会进入队列，获取锁失败就直接返回
     * @param tenantId
     * @param objApiName
     * @return
     */
    @Deprecated
    @PostMapping("/manualExecutePloys")
    public Result<Void> manualExecutePloys(@RequestParam("tenantId") String tenantId,
                                     @RequestParam(value = "objApiName") String objApiName) {
        log.info("this controller will be remove");
        return probeDataTaskService.executeProbeErpDataJob(tenantId, objApiName);
    }
    /**
     * 触发轮询临时库，会进入队列，并尝试出队触发轮询临时库。
     * 轮询结束后会重新检查队列。
     *
     * @param triggerPollingData
     * @return
     */
    @PostMapping("/executePollingTempData")
    public Result<Void> executePloys(@RequestBody TriggerPollingData triggerPollingData) {
        if(triggerPollingData==null|| StringUtils.isBlank(triggerPollingData.getTenantId())){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return probeDataTaskService.enqueueAndTryExecutePollingTempData(triggerPollingData);
    }

    /**
     * 触发失败的数据重试
     */
    @PostMapping("/executeFailDataRetry")
    public Result<Integer> executeFailDataRetry(@RequestBody RetryConfig retryConfig) {
        log.info("exceute data retry config:{}", JSONObject.toJSONString(retryConfig));
        if(CollectionUtils.isNotEmpty(retryConfig.getDataIds())){
            List<ObjectId> objectIds = retryConfig.getDataIds().stream().map(ObjectId::new).collect(Collectors.toList());
            return retryFailDataService.retryDataByIds(objectIds);
        }
       return retryFailDataService.retryDataService(retryConfig.getTenantId(),retryConfig.getRetryDataEnum(),retryConfig.getStartTime(),retryConfig.getEndTime(),retryConfig.getStatus(),retryConfig.getLimit(),retryConfig.getOffset());
    }

    /**
     * 临时库触发
     */
    @PostMapping("/executeTempData")
    public Result<Integer> executeTempData(@RequestBody RetryConfig retryConfig) {
        log.info("exceute data retry config:{}", JSONObject.toJSONString(retryConfig));
        if(CollectionUtils.isEmpty(retryConfig.getDataIds())){
            return Result.newSystemError(I18NStringEnum.s3681);
        }

        if(CollectionUtils.isNotEmpty(retryConfig.getDataIds())){
         //   List<ObjectId> objectIds = retryConfig.getDataIds().stream().map(ObjectId::new).collect(Collectors.toList());
            if(retryConfig.getDataIds().size()>1){
                return Result.newSystemError(I18NStringEnum.s3682);
            }
            String id=retryConfig.getDataIds().get(0);
            probeErpDataService.retryPollingTempFailExcludeStatus(id);

        }
        return Result.newSystemError(I18NStringEnum.s3683);
    }
}
