package com.fxiaoke.open.erpsyncdata.common.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum SyncStatusEnum {
    SUCCESS(1, "成功", I18NStringEnum.s6.getI18nKey(),
        Lists.newArrayList(SyncDataStatusEnum.WRITE_SUCCESS.getStatus())),
    FAILED(2, "失败", I18NStringEnum.s7.getI18nKey(),
        Lists.newArrayList(SyncDataStatusEnum.TRIGGER_FAILED.getStatus(), SyncDataStatusEnum.PROCESS_FAILED.getStatus(), SyncDataStatusEnum.WRITE_FAILED.getStatus())),
    RUNNING(3, "同步中", I18NStringEnum.s667.getI18nKey(),
        Lists.newArrayList(SyncDataStatusEnum.BE_PROCESS.getStatus(), SyncDataStatusEnum.BE_WRITE.getStatus(), SyncDataStatusEnum.WAITTING.getStatus())),
    INGORE(4, "忽略", I18NStringEnum.s693.getI18nKey(),
        Lists.newArrayList(SyncDataStatusEnum.IGNORE.getStatus())),
    ;
    private int status;
    private String name;
    private String i18nKey;
    private List<Integer> syncDataStatuses;

    public String getNameByLang(I18NStringManager i18NStringManager,String lang,String tenantId) {
        return i18NStringManager.get(i18nKey,lang,tenantId,name);
    }

    public String getNameByEi(I18NStringManager i18NStringManager,String tenantId) {
        return i18NStringManager.getByEi(i18nKey,tenantId,name);
    }

    public static String getNameByStatus(int status) {
        for (SyncStatusEnum syncStatusEnum : SyncStatusEnum.values()) {
            if (syncStatusEnum.getStatus() == status) {
                return syncStatusEnum.getName();
            }
        }
        return null;
    }

    public static SyncStatusEnum getBySyncDataStatus(Integer status){
        for (SyncStatusEnum syncStatusEnum : SyncStatusEnum.values()) {
            if (syncStatusEnum.getSyncDataStatuses().contains(status)) {
                return syncStatusEnum;
            }
        }
        return SyncStatusEnum.FAILED;
    }
}
