package com.fxiaoke.open.oasyncdata.db.util;


import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.open.oasyncdata.model.HttpResponse;
import com.fxiaoke.open.oasyncdata.model.HttpResponseMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
@Slf4j
@Service
public class OkHttpUtils {

    private static OkHttpSupport okHttpSupport;
    private static SyncCallback syncCallback = new SyncCallback() {
        @Override
        public Object response(Response response) throws Exception {
            if (Objects.nonNull(response)) {
                long tx = response.sentRequestAtMillis();
                long rx = response.receivedResponseAtMillis();
                long spendTime =rx - tx ;
                HttpResponse httpResponse = new HttpResponse(response);
                Request request = response.request();
                return httpResponse;
            } else {
                log.warn("the response is null!");
                return null;
            }
        }
    };
    @Autowired
    public void setOkHttpSupport(OkHttpSupport okHttpSupport) {
        OkHttpUtils.okHttpSupport = okHttpSupport;
    }

    public static HttpResponse post(String url, Map<String, String> headerMap, String body) {
        Request request = buildPostRequest(url, headerMap, body);
        return (HttpResponse)okHttpSupport.syncExecute(request, syncCallback);
    }

    public static HttpResponseMessage sendHttpPost(String url, Map<String, String> headerMap, String body) {
        HttpResponse httpResponse = post(url, headerMap, body);
        HttpResponseMessage response = new HttpResponseMessage();
        response.setHttpcode(String.valueOf(httpResponse.getCode()));
        response.setMessage(httpResponse.getMessage());
        response.setContent(httpResponse.getBody());
        return response;
    }

    private static Request buildPostRequest(String url, Map<String, String> headerMap, String body) {
        Builder requestBuilder = new Builder();
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), body);
        headerMap.forEach(requestBuilder::addHeader);
        return requestBuilder.url(url).post(requestBody).build();
    }

}
