package com.fxiaoke.open.erpsyncdata.web.transfer.handler;

import com.facishare.transfer.handler.page.PageTransferArg;
import com.facishare.transfer.handler.page.PageTransferResult;
import com.facishare.transfer.handler.page.TargetDataSource;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminSyncPloyDetailService;
import com.fxiaoke.open.erpsyncdata.admin.service.InitLastSyncTimeService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpSyncTimeDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpSyncTimeEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncRulesData;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 删除多出的数据
 *
 * <AUTHOR>
 * @date 2023/2/23 16:52:39
 */
@Component
public class CleanSyncTimeHandler extends ErpRateLimitPageTransferHandler<String, String, Integer> {
    @Autowired
    private InitLastSyncTimeService initLastSyncTimeService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private AdminSyncPloyDetailService adminSyncPloyDetailService;
    @Autowired
    private ErpSyncTimeDao erpSyncTimeDao;


    @Override
    protected TargetDataSource<String, Integer> getTargetDataSource(int enterpriseId) throws Throwable {
        return new TargetDataSource<String, Integer>(enterpriseId) {
            @Override
            protected Integer targetData(String key) throws Throwable {
                return null;
            }
        };
    }

    public static final Set<Integer> deleteType = Sets.newHashSet(1, 2, 3);

    @Override
    protected void update(int enterpriseId, String key, String apiName, Integer targetData) throws Throwable {
        final String tenantId = String.valueOf(enterpriseId);
        final List<ErpSyncTimeEntity> erpSyncTimeEntities = erpSyncTimeDao.listByTenantIdAndObjectApiName(tenantId, apiName);
        final Set<Integer> collect = erpSyncTimeEntities.stream().map(ErpSyncTimeEntity::getOperationType).collect(Collectors.toSet());
        erpSyncTimeEntities.stream()
                .filter(entity -> deleteType.contains(entity.getOperationType()))
                .forEach(entity -> {
                    Integer newType = entity.getOperationType() + 200;
                    if (collect.contains(newType)) {
                        erpSyncTimeDao.deleteByTenantIdAndId(tenantId, entity.getId());
                    } else {
                        erpSyncTimeDao.updateType(tenantId, entity.getId(), newType);
                    }
                });
    }

    @Override
    protected PageTransferResult<String, String> getSourceDataPage(PageTransferArg<String> arg) {
        final String tenantId = String.valueOf(arg.getEnterpriseId());
        List<SyncPloyDetailEntity> syncPloyDetailEntities = adminSyncPloyDetailDao.setGlobalTenant(tenantId).listBySourceType(tenantId, TenantType.ERP);
        final Map<String, List<SyncRulesData>> objRules = syncPloyDetailEntities.stream()
                .collect(Collectors.groupingBy(SyncPloyDetailEntity::getSourceObjectApiName,
                        Collectors.mapping(SyncPloyDetailEntity::getSyncRules, Collectors.toList())));

        final ArrayList<String> push = Lists.newArrayList("push");
        final Map<String, String> collect = syncPloyDetailEntities.stream()
                .filter(entity -> Objects.equals(entity.getSyncRules().getSyncType(), "push"))
                .map(SyncPloyDetailEntity::getSourceObjectApiName)
                .distinct()
//                全是push的才需要处理
                .filter(apiName -> objRules.get(apiName).stream().allMatch(rules -> Objects.equals(rules.getSyncType(), "push") || Objects.equals(rules.getSyncTypeList(), push)))
                .collect(Collectors.toMap(Function.identity(), Function.identity()));

        return new PageTransferResult<>(collect, null, false);
    }
}
