package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 20:06 2021/9/29
 * @Desc:
 */
@Getter
@Setter
@ToString
@ApiModel("获取erp对象接口参数")
public class QueryErpObjInterfaceMonitorArg extends QueryErpInterfaceMonitorArg{
    /**
     * traceId
     */
    @ApiModelProperty("traceId")
    private String traceId;

    @ApiModelProperty("traceIds")
    private List<String> traceIds;
    @ApiModelProperty("ids")
    private List<String> objectIds;
    /**
     * 状态 1 成功 2 失败
     */
    @ApiModelProperty("状态")
    private Integer status;
    /**
     * 开始调用时间
     */
    @ApiModelProperty("开始调用时间")
    private Long startTime;
    /**
     * 结束调用时间
     */
    @ApiModelProperty("结束调用时间")
    private Long endTime;
    /**
     * result
     */
    @ApiModelProperty("result")
    private String result;

    /**
     * result 结果数据是否有数据
     */
    @ApiModelProperty("resultDataPresent")
    private String resultDataPresent;
}
