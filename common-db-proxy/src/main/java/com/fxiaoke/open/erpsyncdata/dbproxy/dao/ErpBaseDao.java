package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.github.mybatis.annotation.AutoResultMap;
import com.github.mybatis.annotation.FillEntityType;
import org.apache.ibatis.annotations.*;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by fengyh on 2020/8/26.
 */
public interface ErpBaseDao<T> {
    /**
     * 插入一条记录
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = CrudProvider.class, method = "insert")
    @Result(javaType = int.class)
    int insert(T record);


    /**
     * 插入一条记录, 唯一键重复时忽略
     * insert 增加 ON CONFLICT DO NOTHING
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = CrudProvider.class, method = "insertIgnore")
    @Result(javaType = int.class)
    int insertIgnore(T record);

    /**
     * 批量插入记录
     * 谨慎使用， postgres对传的参数数量有限制。
     * @param record the record
     * @return insert count
     */
    @InsertProvider(type = BatchProvider.class, method = "batchInsert")
    @Result(javaType = int.class)
    int batchInsert(@Param(BatchProvider.KEY)List<T> record);

    /**
     * 批量更新记录
     * @param record the record
     * @return batchUpdate count
     */
/*    @InsertProvider(type = BatchProvider.class, method = "batchUpdate")
    @Result(javaType = int.class)
    int batchUpdate(@Param(BatchProvider.KEY)List<T> record);*/

    /**
     * 查询列表
     * record中非空的字段，会拼装为 where条件
     *
     * 这里用findByEntity， 用findBySelective会有问题，原因没有定位到。
     * */
    @SelectProvider(type = CrudProvider.class, method = "findByEntity")
    @AutoResultMap
    List<T> queryList(T record);

    /**
     * 根据参数record中的id定位到数据库中记录，然后用其它非空字段都会被新值覆盖。
     * 如果没有id字段为空，那么更新不会成功，返回值为0.
     * */
    @UpdateProvider(type = CrudProvider.class, method = "update")
    @Result(javaType = int.class)
    int updateById(T record);

    /**根据Id删除数据库中的记录。
     * 如果没有记录被删除，返回0. 大于0表示有记录被删除。a
     * */
    @DeleteProvider(type = CrudProvider.class, method = "deleteById")
    @FillEntityType
    @Result(javaType = int.class)
    int deleteById(String Id);


    /**根据Id删除数据库中的记录。
     * 如果没有记录被删除，返回0. 大于0表示有记录被删除。a
     * */
    @DeleteProvider(type = CrudProvider.class, method = "deleteByEiAndId")
    @FillEntityType
    @Result(javaType = int.class)
    int deleteByEiAndId(String ei, String Id);

    /**根据Id查找数据库中的记录。
     *
     * @param id
     * @return
     * */
    @SelectProvider(type = CrudProvider.class, method = "findById")
    @AutoResultMap
    T findById(String id);
}
