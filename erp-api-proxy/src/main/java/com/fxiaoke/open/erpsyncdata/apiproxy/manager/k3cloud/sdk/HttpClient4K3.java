package com.fxiaoke.open.erpsyncdata.apiproxy.manager.k3cloud.sdk;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/6/17
 */
@Slf4j
public class HttpClient4K3 {

    private static final MediaType CONTENT_TYPE = MediaType.parse("text/plain; charset=UTF-8");
    private static final MediaType JSON_CONTENT_TYPE = MediaType.parse("application/json; charset=UTF-8");

    private static final String SESSION_KEY = "kdservice-sessionid";
    private static final String ASP_SESSION_KEY = "ASP.NET_SessionId";
    private static final String CHARSET = "UTF-8";
    /**
     * 获取客户端和服务器建立连接的timeout
     */
    public static Integer CONNECT_TIME_OUT = 20000;
    /**
     * 获取从连接池获取连接的timeout
     */
    public static Integer CONNECT_REQUEST_TIME_OUT = 20000;
    /**
     * 客户端从服务器读取数据的timeout
     */
    public static Integer SOCKET_TIME_OUT = 20000;

    @Getter
    private CloseableHttpClient client;

    private HttpClient4K3() {
        client = HttpClients.createDefault();
    }

    public static HttpClient4K3 defaultClient() {
        return HttpClient4K3.LazyHolder.INSTANCE;
    }
    private static class LazyHolder {
        private static final HttpClient4K3 INSTANCE = new HttpClient4K3();
    }

    public String postUseCookie(String url, String body, Map<String, String> cookies) throws Exception {
        HttpPost httppost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIME_OUT).setConnectionRequestTimeout(CONNECT_REQUEST_TIME_OUT)
                .setSocketTimeout(SOCKET_TIME_OUT).build();
        httppost.setConfig(requestConfig);

        HttpEntity httpEntity = new StringEntity(body, CHARSET);
        httppost.setEntity(httpEntity);
        httppost.setHeader("Content-Type", "application/json");
        for(String key:cookies.keySet()){
            httppost.addHeader(key,cookies.get(key));
        }
        log.info("request url:{},request headers:{},request body:{}", url, Lists.newArrayList(httppost.getAllHeaders()).toString(), body);
        CloseableHttpResponse response = null;
        try {
            response = client.execute(httppost);
            List<Header> headerList=Lists.newArrayList(response.getHeaders("Set-Cookie"));
            if(CollectionUtils.isNotEmpty(headerList)){
                List<String> headers1 = headerList.stream().map(Header::getValue).collect(Collectors.toList());
                for (String s : headers1) {
                    if (s.startsWith(SESSION_KEY)){
                        String s1 = StringUtils.substringBefore(s, ";");
                        String sessionId = StringUtils.substringAfter(s1, "=");
                        if (StringUtils.isNotBlank(sessionId)){
                            cookies.put(SESSION_KEY,sessionId);
                        }
                    }
                    if (s.startsWith(ASP_SESSION_KEY)){
                        String s1 = StringUtils.substringBefore(s, ";");
                        String sessionId = StringUtils.substringAfter(s1, "=");
                        if (StringUtils.isNotBlank(sessionId)){
                            cookies.put(ASP_SESSION_KEY,sessionId);
                        }
                    }
                }
            }
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return EntityUtils.toString(response.getEntity(), CHARSET);
            } else {
                if (null != response) {
                    response.close();
                }
                log.warn("request failed,url:{},arg:{},return:{}", url, body, response.getStatusLine().toString());
                throw new ErpSyncDataException(String.format(ResultCodeEnum.RESULT_ERROR.getErrMsg(),response.getStatusLine().toString()),null,null);
            }
        } catch (IOException e) {
            log.warn("response stream exception,{}", e.toString());
            if (null != response) {
                try {
                    response.close();
                } catch (IOException e1) {
                    log.error("when response exception close response exception,{}", e1.toString());
                }
            }
            throw e;
        }
    }


}
