package com.fxiaoke.open.erpsyncdata.dbproxy.util;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

/**
 * <AUTHOR> (^_−)☆
 * @date 2023/1/3
 */
@Slf4j
@UtilityClass
public class MonitorUtil{
    private final AutoConfMQProducer producer = new AutoConfMQProducer("erp-sync-data-monitor", "mq-producer");

    public void sendMultiType(Object data, String key, MonitorType... types) {
        if (types == null) {
            return;
        }
        for (MonitorType type : types) {
            send(data, key, type);
        }
    }

    public void send(Object data, MonitorType type) {
        send(data, null, type);
    }

    public void send(Object data, String key, MonitorType type) {
        if (data == null || type == null) {
            return;
        }
        try {
            SendResult result = producer.send(new Message(producer.getDefaultTopic(), type.name(), key, JSON.toJSONBytes(data)), (mqs, msg, arg) -> {
                // 设置traceContext
                MessageHelper.fillPropertiesFromContext(msg, TraceContext.get());
                int size = mqs.size();
                String keys = msg.getKeys();
                if (keys != null) {
                    return mqs.get(Math.abs(keys.hashCode()) % size);
                } else {
                    return mqs.get(RandomUtil.randomInt(size));
                }
            }, null);
            log.debug("MonitorMqProducer send mq,{},{},{}", type, data, result);
        } catch (Throwable e) {
            log.error("MonitorMqProducer send mq error", e);
        }
    }

}
