package com.facishare.open.erp.connertor.sdk.controller.linkedin;

import com.facishare.open.erp.connertor.sdk.model.linkedin.GetAllForm;
import com.facishare.open.erp.connertor.sdk.model.linkedin.GetFormMetaData;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/5/10 19:53:38
 */
public interface ILinkedinController {

    @RequestMapping("/getAllForm")
    GetAllForm.Result getAllForm(@RequestBody GetAllForm.Arg arg);

    @RequestMapping("/getFormMetaData")
    GetFormMetaData.Result getFormMetaData(@RequestBody GetFormMetaData.Arg arg);
}
