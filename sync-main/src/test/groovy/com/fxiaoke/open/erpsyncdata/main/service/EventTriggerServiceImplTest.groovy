package com.fxiaoke.open.erpsyncdata.main.service

import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager
import com.fxiaoke.open.erpsyncdata.preprocess.arg.BatchSendEventDataArg
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ObjectApiNameEnum
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory
import com.fxiaoke.open.erpsyncdata.preprocess.result.Result2
import com.fxiaoke.open.erpsyncdata.preprocess.service.OuterService
import com.fxiaoke.open.erpsyncdata.preprocess.service.SyncMainService
import spock.lang.Specification

class EventTriggerServiceImplTest extends Specification {

    EventTriggerServiceImpl impl
    def tenantId = "123456"


    def setup() {
        def syncMainService = Mock(SyncMainService) {
            sendEventData2DispatcherMq(*_) >> Result2.newSuccess("test")
        }
        def syncLogManager = Mock(SyncLogManager) {
            getInitLogId(*_) >> "test"
        }
        def outerServiceFactory = Mock(OuterServiceFactory) {
            get(*_) >> {
                def s = Mock(OuterService) {
                    getObjectData(_,_,_,_) >> { args ->
                        def obj = new ObjectData()
                        obj.putTenantId(tenantId)
                        obj.putId("test_id")
                        obj.put("bom_instance_id", "test")
                        obj.put("tree_id", "test")
                        obj.put("bom_tree_id", "test")
                        obj.put("product_id", "test")
                        if (args[1] != null) {
                            obj.put("root_id", "test_root_id")
                            obj.put("bom_id", "test_bom_id")
                        } else {
                            obj.put("root_id", "test_id")
                            obj.put("bom_id", "test_id")
                        }
                        return Result2.newSuccess(obj)
                    }
                }
                return s
            }
        }
        impl = new EventTriggerServiceImpl(syncLogManager: syncLogManager, syncMainService: syncMainService, outerServiceFactory: outerServiceFactory)
    }

    def "test batchSendEventData2DispatcherMq"() {
        given:
        def obj = new ObjectData()
        obj.putTenantId(tenantId)
        obj.putApiName(ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName())
        def data = new BatchSendEventDataArg.EventData(syncLogId: "test", sourceData: obj)
        def data1 = new BatchSendEventDataArg.EventData(syncLogId: "", sourceData: obj, sourceTenantType: 0)
        def arg = new BatchSendEventDataArg([data, data1])
        expect:
        impl.batchSendEventData2DispatcherMq(arg).isSuccess()
    }

    def "test batchSendEventData2DispatcherMqByContext"() {
        given:
        def data = new ObjectData()
        data.putTenantId(tenantId)
        data.putApiName(ObjectApiNameEnum.FS_BOM_INSTANCE_OBJ.getObjApiName())
        def event = new SyncDataContextEvent(syncLogId: "", sourceData: data, dataReceiveType: null)
        def event1 = new SyncDataContextEvent(syncLogId: "test", sourceData: data, sourceTenantType: 0)
        expect:
        impl.batchSendEventData2DispatcherMqByContext([event, event1]).isSuccess()
    }
}
