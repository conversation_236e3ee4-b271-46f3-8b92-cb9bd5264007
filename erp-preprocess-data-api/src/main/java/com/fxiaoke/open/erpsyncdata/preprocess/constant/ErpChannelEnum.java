package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import com.fxiaoke.open.erpsyncdata.common.util.GsonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18nBase;
import com.fxiaoke.open.erpsyncdata.preprocess.model.*;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Sets;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fengyh on 2020/8/22.
 */
@Getter
public enum ErpChannelEnum implements I18nBase {
    ERP_K3CLOUD("金蝶云·星空",
            0,
            "k3_data_sync_app",
            ConnectorTypeEnum.ERP,
            K3CloudConnectParam.class,
            "k3Cloud",
            false,
            I18NStringEnum.s409.getI18nKey()),
    ERP_SAP("SAP",
            1,
            "sap_data_sync_app",
            ConnectorTypeEnum.ERP,
            SapConnectParam.class,
            "sap",
            true,
            I18NStringEnum.s410.getI18nKey()),
    ERP_U8("用友U8Cloud",
            2,
            "u8_data_sync_app",
            ConnectorTypeEnum.ERP,
            U8ConnectParam.class,
            "u8",
            true,
            I18NStringEnum.s411.getI18nKey()),
    STANDARD_CHANNEL("自定义",
            3,
            "standard_data_sync_app",
            ConnectorTypeEnum.ERP,
            StandardConnectParam.class,
            "standard",
            true,
            I18NStringEnum.s3312.getI18nKey()),
    ERP_U8_EAI("用友U8",
            4,
            "u8_eai_data_sync_app",
            ConnectorTypeEnum.ERP,
            U8EaiConnectParam.class,
            "u8Eai",
            false,
            I18NStringEnum.s413.getI18nKey()),
    OA("CRM待办OA",
            5,
            "oa_data_sync_app",
            ConnectorTypeEnum.OA,
            I18NStringEnum.s414.getI18nKey()),
    DING_DING_CRM("钉钉官方CRM",
            6,
            "ding_ding_crm_data_sync_app",
            ConnectorTypeEnum.MARKETING,
            I18NStringEnum.s415.getI18nKey()),
    ALL("所有渠道",
            7,
            I18NStringEnum.s416.getI18nKey()),
    CRM("纷享销客",
            8,
            I18NStringEnum.s417.getI18nKey()),
    DING_TALK_DOCK_APP("钉钉",
            9,
            "ding_talk_dock_app",
            ConnectorTypeEnum.OA,
            I18NStringEnum.s418.getI18nKey()),
    YXT_MARKETING_ZHIHU("知乎营销画报",
            10,
            "zhihu_huabao_data_sync_app",
            ConnectorTypeEnum.MARKETING,
            ZhiHuConnectParam.class,
            "zhiHu",
            true,
            I18NStringEnum.s419.getI18nKey()),
    YXT_UC_SHENMA("UC神马建站工具",
            11,
            "uc_shenma_data_sync_app",
            ConnectorTypeEnum.MARKETING,
            YxtCommonConnectParam.class,
            "yxtCommon",
            false,
            I18NStringEnum.s420.getI18nKey()),
    YXT_SOGO_XIANSUO("搜狗线索通",
            12,
            "sogo_xiansuocrm_data_sync_app",
            ConnectorTypeEnum.MARKETING,
            YxtCommonConnectParam.class,
            "yxtCommon",
            false,
            I18NStringEnum.s421.getI18nKey()),
    YXT_KUAISHOU_XIANSU("快手线索CRM",
            13,
            "kuaishou_xiansuocrm_data_sync_app",
            ConnectorTypeEnum.MARKETING,
            YxtCommonConnectParam.class,
            "yxtCommon",
            false,
            I18NStringEnum.s422.getI18nKey()),
    ERP_DB_PROXY("DB",
            14,
            "db_data_sync_app",
            ConnectorTypeEnum.DB,
            DBProxyConnectParam.class,
            "dbProxy",
            false,
            I18NStringEnum.s423.getI18nKey()),
    CONNECTOR_FEISHU("飞书",
            15,
            "feishu_data_sync_app",
            ConnectorTypeEnum.OA,
            FeiShuConnectParam.class,
            "feiShu",
            false,
            I18NStringEnum.s424.getI18nKey()),
    CONNECTOR_JDY("金蝶云·星辰",
            16,
            "jdy_app",
            ConnectorTypeEnum.ERP,
            I18NStringEnum.s425.getI18nKey()),
    CONNECTOR_QYWX("企业微信",
            17,
            "wecom_data_sync_app",
            ConnectorTypeEnum.OA,
            QYWXConnectParam.class,
            "qywx",
            false,
            I18NStringEnum.s426.getI18nKey()),

    ERP_FACEBOOK("facebook连接器",
            18,
            "facebook_data_sync_app",
            ConnectorTypeEnum.ERP,
            Oauth2ConnectParam.class,
            "oauthInfo",
            false,
            I18NStringEnum.s427.getI18nKey()),
    ERP_LINKEDIN("linkedin连接器",
            19,
            "linkedin_data_sync_app",
            ConnectorTypeEnum.ERP,
            Oauth2ConnectParam.class,
            "oauthInfo",
            false,
            I18NStringEnum.s428.getI18nKey()),
    ERP_JDY("云星辰连接器",
            20,
            "jdy_data_sync_app",
            ConnectorTypeEnum.ERP,
            JdyConnectParam.class,
            "jdy",
            false,
            I18NStringEnum.s1076.getI18nKey()),
    //     ERP_GOOGLE("google连接器", 20, "google_data_sync_app", ConnectorTypeEnum.ERP, Oauth2ConnectParam.class, "oauthInfo"),
    ERP_K3CLOUD_ULTIMATE("金蝶云·星空旗舰版",
            21,
            "k3_ultimate_data_sync_app",
            ConnectorTypeEnum.ERP,
            K3UltimateConnectParam.class,
            "k3CloudUltimate",
            false,
            I18NStringEnum.s430.getI18nKey()),
    CONNECTOR_LARK("LARK",
            22,
            "lark_data_sync_app",
            ConnectorTypeEnum.OA,
            FeiShuConnectParam.class,
            "feiShu",
            false,
            I18NStringEnum.s5011.getI18nKey()),
    ;

    private final String defaultName;
    /**
     * 渠道序号，与erp_connect_info的number相关
     */
    private final int channelId;

    /**
     * license moduleCode
     */
    private final String moduleCode;

    private final ConnectorTypeEnum connectorType;

//    /**
//     * 企业微信支持的纷享企业版本列表
//     */
//    private static List<String> qywxSupportVersionList = new ArrayList<>();
//
//    /**
//     * 企业微信支持的模块列表
//     */
//    private static List<String> qywxSupportModuleList = new ArrayList<>();

    private final Class<? extends BaseConnectParam> className;

    private final Function<ConnectInfoResult.ConnectParams, ? extends BaseConnectParam> getConnectParam;

    private final BiConsumer<ConnectInfoResult.ConnectParams, BaseConnectParam> setConnectParam;

    private final boolean hasHeaderFunctionName;

    private final String i18nKey;
    /**
     * 连接参数转换的类型
     */
    private final String fieldName;

    @SneakyThrows
    ErpChannelEnum(String defaultName, int channelId, String moduleCode, ConnectorTypeEnum connectorType, Class<? extends BaseConnectParam> className, String fieldName, boolean hasHeaderFunctionName, String i18nKey) {
        this.defaultName = defaultName;
        this.channelId = channelId;
        this.connectorType = connectorType;
        this.moduleCode = moduleCode;
        this.className = className;
        this.hasHeaderFunctionName = hasHeaderFunctionName;
        this.i18nKey = i18nKey;
        this.fieldName=fieldName;
        if (StringUtils.isNotBlank(fieldName)) {
            Field field = ConnectInfoResult.ConnectParams.class.getField(fieldName);
            getConnectParam = (connectParams) -> {
                try {
                    if (connectParams == null){
                        connectParams = new ConnectInfoResult.ConnectParams();
                    }
                    return (BaseConnectParam) field.get(connectParams);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            };
            setConnectParam = (connectParams, connectParam) -> {
                try {
                    field.set(connectParams, connectParam);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            };
        } else {
            getConnectParam = null;
            setConnectParam = null;
        }
    }

    ErpChannelEnum(String defaultName, int channelId, String moduleCode, ConnectorTypeEnum connectorType,String i18nKey) {
        this(defaultName, channelId, moduleCode, connectorType, null, null, false,i18nKey);
    }

    ErpChannelEnum(String defaultName, int channelId, String i18nKey) {
        this(defaultName, channelId, null, null,i18nKey);
    }

    /**
     * 对于企微，由于历史原因，特定的CRM版本也会有企业对接入口，比如企微版CRM，还有独立版CRM等
     *
     * @return
     */
    public static Map<String, ErpChannelEnum> getExtraProductVersionMap() {
        Map<String, ErpChannelEnum> map = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(qywxSupportVersionList)) {
//            for (String version : qywxSupportVersionList) {
//                map.put(version, ErpChannelEnum.CONNECTOR_QYWX);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(qywxSupportModuleList)) {
//            for (String version : qywxSupportModuleList) {
//                map.put(version, ErpChannelEnum.CONNECTOR_QYWX);
//            }
//        }
        map.put("jdy_edition", ErpChannelEnum.CONNECTOR_JDY);
        return map;
    }

    /**
     * 渠道license编码,渠道
     * <a href="http://wiki.firstshare.cn/pages/viewpage.action?pageId=90509409">版本和module定义</a>
     */
    public static final Map<String, ErpChannelEnum> CHANNEL_CODE_MAP = Arrays.stream(ErpChannelEnum.values())
            .filter(v -> v.moduleCode != null)
            .collect(Collectors.toMap(ErpChannelEnum::getModuleCode, u -> u, (u, v) -> u));

    /**
     * 不需要补充链接渠道到ERP_CONNECT_INFO
     */
    public static final Set<ErpChannelEnum> UN_START_CONNECT_CHANNEL = Sets.newHashSet(
            ErpChannelEnum.CRM,
            ErpChannelEnum.DING_TALK_DOCK_APP,
            ErpChannelEnum.OA,
            //ErpChannelEnum.CONNECTOR_FEISHU,
            //ErpChannelEnum.CONNECTOR_QYWX,
            ErpChannelEnum.CONNECTOR_JDY);

    /**
     * 需要初始化crm连接器的license编码
     */
    public static final Set<String> InitCrmConnectInfoLicenseModule = CHANNEL_CODE_MAP.entrySet().stream()
            .filter(entry -> !UN_START_CONNECT_CHANNEL.contains(entry.getValue()))
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());

    /**
     * 可以新增的渠道
     */
    public static final Set<ErpChannelEnum> AddableChannelSet = Sets.immutableEnumSet(
            ERP_K3CLOUD, ERP_K3CLOUD_ULTIMATE, ERP_U8_EAI, ERP_DB_PROXY, STANDARD_CHANNEL ,ERP_JDY,ERP_SAP
    );

    /**
     * 可以新增的OA渠道
     */
    public static final Set<ErpChannelEnum> AddableOAChannelSet = Sets.immutableEnumSet(
            OA,
            CONNECTOR_QYWX,
            CONNECTOR_FEISHU
    );


    public String getHeaderFunctionName(String json) {
        if (!isHasHeaderFunctionName()) {
            return null;
        }

        final DocumentContext context = JsonPath.parse(json);
        try {
            return context.read("$.headerFunctionName");
        } catch (Exception e) {
            return null;
        }
    }

    public List<String> getPushDataApiNames(String json) {
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }

        return getConnectParam(json).getPushDataApiNames();
    }

    public ConnectInfoResult.ConnectParams getNewConnectParam(String json) {
        if (Objects.isNull(setConnectParam)) {
            throw new IllegalStateException("channel not support: " + name());
        }
        final BaseConnectParam connectParam = getConnectParam(json);

        return getNewConnectParam(connectParam);
    }

    @NotNull
    public ConnectInfoResult.ConnectParams getNewConnectParam(BaseConnectParam accept) {
        if (Objects.isNull(setConnectParam)) {
            throw new IllegalStateException("channel not support: " + name());
        }

        ConnectInfoResult.ConnectParams params = new ConnectInfoResult.ConnectParams();
        setConnectParam.accept(params, accept);

        return params;
    }

    public <T extends BaseConnectParam> T safeGetConnectParam(String json) {
        try {
            return getConnectParam(json);
        } catch (Exception e) {
            return null;
        }
    }

    public <T extends BaseConnectParam> T getConnectParam(String json) {
        if (Objects.isNull(className)) {
            throw new IllegalStateException("channel not support: " + name());
        }
        return GsonUtil.fromJson(json, className);
    }

    public BaseConnectParam getConnectParam(ConnectInfoResult.ConnectParams connectParams) {
        if (Objects.isNull(getConnectParam)) {
            throw new IllegalStateException("channel not support: " + name());
        }
        return getConnectParam.apply(connectParams);
    }

    public <T extends BaseConnectParam> T getAndCheckConnectParam(String tenantId,String connectParams) {
        final T connectParam = getConnectParam(connectParams);
        if (connectParam==null){
            throw new ErpSyncDataException(I18NStringEnum.s152,tenantId);
        }

        return connectParam;
    }

    public String getConnectorHandlerKey(BaseConnectParam baseConnectParam){
        if (ErpChannelEnum.STANDARD_CHANNEL.equals(this)) {
            //通用连接器通过handlerType路由执行bean
            return Optional.ofNullable(baseConnectParam).map(v -> v.getConnectorHandlerType()).orElse(ConnectorHandlerType.REST_API).name();
        } else {
            return this.name();
        }
    }

    @Override
    public String getI18nValue() {
        return getDefaultName();
    }
}
