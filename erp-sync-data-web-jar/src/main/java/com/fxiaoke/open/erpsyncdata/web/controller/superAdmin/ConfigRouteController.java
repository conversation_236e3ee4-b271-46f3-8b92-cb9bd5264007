package com.fxiaoke.open.erpsyncdata.web.controller.superAdmin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.pod.client.DbRouterClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fxiaoke.open.erpsyncdata.admin.result.SyncPloyDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.service.superadmin.SuperAdminBrushDataService;
import com.fxiaoke.open.erpsyncdata.dbproxy.config.ConfigCenterConfig;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SpeedLimitTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.TenantConfigurationTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpTenantConfigurationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ConfigRouteManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.service.CustomFunctionService;
import com.fxiaoke.open.erpsyncdata.web.controller.BaseController;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceFindData;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @Date: 14:14 2022/4/28
 * @Desc:
 */
@Slf4j
@Api(tags = "企业连接信息设置相关接口")
@RestController("ConfigRouteController")
@RequestMapping("erp/syncdata/superadmin/configRoute")
//IgnoreI18nFile
public class ConfigRouteController extends BaseController {
    @Autowired
    private ConfigRouteManager configRouteManager;
    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;
    @Autowired
    private TenantConfigurationManager tenantConfigurationManager;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private SuperAdminBrushDataService superAdminBrushDataService;
    @Autowired
    private DbRouterClient dbRouterClient;
    @Autowired
    private ConfigCenterConfig configCenterConfig;
    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private UserCenterService userCenterService;
    @Autowired
    private CustomFunctionService customFunctionService;

    @ApiOperation(value = "新增或者修改企业路由")
    @RequestMapping(value = "/addOrUpdateTenantRoute", method = RequestMethod.GET)
    public Result<Boolean> addOrUpdateTenantRoute(@RequestParam String tenantId, @RequestParam String resourceId) {
        if (StringUtils.isBlank(tenantId) && StringUtils.isBlank(resourceId)) {
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        return Result.newSuccess(configRouteManager.configRoute(tenantId, resourceId));
    }

    @ApiOperation(value = "批量新增或者修改企业路由")
    @RequestMapping(value = "/batchAddOrUpdateTenantRoute", method = RequestMethod.POST)
    public Result<Boolean> batchAddOrUpdateTenantRoute(@RequestParam Boolean allTenant, @RequestParam String ignoreTenantIds, @RequestParam String tenantIds, @RequestParam String resourceId) {
        Set<String> tenantIdList = Sets.newHashSet();
        if (allTenant != null && allTenant) {
            Set<String> ignoreTenantIdList = Sets.newHashSet();
            if (StringUtils.isNotBlank(ignoreTenantIds)) {
                ignoreTenantIdList = ImmutableSet.copyOf(Splitter.on(",").split(ignoreTenantIds));
            }
            List<String> allTenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
            allTenantIds.removeAll(ignoreTenantIdList);
            tenantIdList.addAll(allTenantIds);
        } else {
            if (StringUtils.isBlank(tenantIds) && StringUtils.isBlank(resourceId)) {
                return Result.newError(ResultCodeEnum.PARAM_ERROR);
            }
            tenantIdList = ImmutableSet.copyOf(Splitter.on(",").split(tenantIds));
        }
        log.info("batchAddOrUpdateTenantRoute tenantIdList={} resourceId={}", tenantIdList, resourceId);
        List<String> list = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(tenantIdList)) {
            for (String ei : tenantIdList) {
                Boolean success = configRouteManager.configRoute(ei, resourceId);
                list.add(ei + "->" + success);
            }
        }
        log.info("batchAddOrUpdateTenantRoute result={} ", list);
        return Result.newSuccess();
    }

    @ApiOperation(value = "初始化路由配置")
    @RequestMapping(value = "/initRoute", method = RequestMethod.GET)
    public Result<Integer> initRoute(@RequestParam String resourceId) {
        List<String> allTenantIds = erpConnectInfoDao.setTenantId(DataBaseBatchIndexUtil.notTenantId).listTenantId();
        Map<String, List<String>> configMap = Maps.newHashMap();
        configMap.put(resourceId, allTenantIds);
        ErpTenantConfigurationEntity config = new ErpTenantConfigurationEntity();
        config.setChannel(ErpChannelEnum.ALL.name());
        config.setDataCenterId("0");
        config.setTenantId("all");
        config.setType(TenantConfigurationTypeEnum.CONFIG_ROUTE_TENANT.name());
        config.setId(idGenerator.get());
        config.setConfiguration(JSONObject.toJSONString(configMap));
        config.setCreateTime(System.currentTimeMillis());
        config.setUpdateTime(System.currentTimeMillis());
        int insert = tenantConfigurationManager.insert(config.getTenantId(), config);
        return Result.newSuccess(insert);
    }

    @ApiOperation(value = "刷sync_data,sync_data_mappings,erp_processed_data数据")
    @RequestMapping(value = "/brushTableData", method = RequestMethod.POST)
    public Result<String> brushTableData(@RequestBody List<String> sourceTenantId, @RequestHeader(value = "destDataBaseTenantId") String destDataBaseTenantId) {//destTenantId是能指向目标数据库的虚拟企业id
        return superAdminBrushDataService.brushTableData(sourceTenantId, destDataBaseTenantId);
    }

    @ApiOperation(value = "刷企业对象分发时间配置")
    @RequestMapping(value = "/brushDispatcherTime", method = RequestMethod.POST)
    public Result<String> brushDispatcherTime(@RequestBody List<String> sourceTenantIds, @RequestHeader(value = "time") Integer time, @RequestHeader(value = "cancel") Boolean cancel) {
        return superAdminBrushDataService.brushDispatcherTime(sourceTenantIds, time, cancel);
    }

    @ApiOperation(value = "获取企业路由信息")
    @RequestMapping(value = "/getTenantIdRoute", method = RequestMethod.GET)
    public Result<Map<String, Set<String>>> getTenantIdRoute(@RequestParam Boolean onlySandBox, @RequestParam String destRoute) {
        return superAdminBrushDataService.getTenantIdRoute(onlySandBox, destRoute);
    }


    /**
     * (1)pg是否独立库，(2)是否开了批量接口
     */
    @ApiOperation(value = "获取企业对象批量写crm相关信息")
    @GetMapping(value = "/getObjectBatchWriteInfo")
    public Result<ObjectBatchWriteInfo> getObjectBatchWriteInfo(@RequestParam(value = "objectApiName", required = false) String objectApiName, @RequestParam("tenantId") String tenantId) {
        final ObjectBatchWriteInfo objectBatchWriteInfo = new ObjectBatchWriteInfo();
        objectBatchWriteInfo.setTenantId(tenantId);

        final String enterpriseName = userCenterService.getEnterpriseName(tenantId);
        objectBatchWriteInfo.setEnterpriseName(enterpriseName);

        // 独立库
        final boolean independent = checkTenantIndependentDB(tenantId);
        objectBatchWriteInfo.setIndependent(independent);

        // 写crm速度
        Map<String, Integer> mapConfig = configCenterConfig.getMapConfig(TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2CRM_BATCHWRITE.name(), Collections.emptyMap());
        final Integer batchLimit = mapConfig.getOrDefault(tenantId, SpeedLimitTypeEnum.TO_CRM_BATCHWRITE.getDefaultTps().intValue());
        objectBatchWriteInfo.setBatchLimit(batchLimit);

        mapConfig = configCenterConfig.getMapConfig(TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2CRM.name(), Collections.emptyMap());
        final Integer limit = mapConfig.getOrDefault(tenantId, SpeedLimitTypeEnum.TO_CRM.getDefaultTps().intValue());
        objectBatchWriteInfo.setLimit(limit);

        // 是否批量写crm接口
        List<String> batchWrite2CRMObjAPiNameList = configCenterConfig.getBatchWrite2CrmByTenant(tenantId);
        objectBatchWriteInfo.setBatchWriteApiNameList(JSON.toJSONString(batchWrite2CRMObjAPiNameList));

        if (StringUtils.isNotBlank(objectApiName)) {
            final boolean contains = batchWrite2CRMObjAPiNameList.contains(objectApiName);
            objectBatchWriteInfo.setBatch(contains);
        }

        return Result.newSuccess(objectBatchWriteInfo);
    }

    /**
     * 集成流是否挂了函数，拉出函数review, 是否对CRM对象做新建和修改操作。
     */
    @ApiOperation(value = "获取企业对象批量写crm相关信息")
    @GetMapping(value = "/getPloyDetailsByObjectApiName")
    public Result<List<SyncPloyDetailResult>> getPloyDetailsByObjectApiName(@RequestParam(required = false) String objectApiName, @RequestParam String tenantId) {
        // 相关的集成流
        final List<SyncPloyDetailResult> allPloyDetailByDestObjectApiName = getAllPloyDetailByDestObjectApiName(tenantId, StringUtils.isNotBlank(objectApiName) ? objectApiName : null);

        return Result.newSuccess(allPloyDetailByDestObjectApiName);
    }

    @ApiOperation(value = "修改批量写crm速度")
    @GetMapping(value = "/updateBatchWriteCrmLimit")
    public Result<Void> updateBatchWriteCrmLimit(@RequestParam("tenantId") String tenantId, @RequestParam(value = "checkBatchWriteObject", required = false) Boolean checkBatchWriteObject, @RequestParam("limit") int limit, @RequestParam(value = "force", required = false) Boolean force) {
        if (StringUtils.isBlank(tenantId) || limit <= 0) {
            return Result.newError("参数错误");
        }

        // 强制修改/小于1800/min,直接修改
        if (BooleanUtils.isTrue(force) || limit <= 30) {
            updateBatchWriteCrmLimit(tenantId, limit);
            return Result.newSuccess();
        }

        final boolean independent = checkTenantIndependentDB(tenantId);
        if (!independent) {
            return Result.newError(tenantId + "企业不是独立库,不能超过1800/min");
        }

        // 检查是否有函数修改crm对象
        final String funcApiName = getUpdateCrmFuncName(tenantId, checkBatchWriteObject);
        if (StringUtils.isNotBlank(funcApiName)) {
            return Result.newError("函数" + funcApiName + "中有涉及 创建/修改 crm对象,请检查后判断能否修改");
        }

        updateBatchWriteCrmLimit(tenantId, limit);
        return Result.newSuccess();
    }

    @ApiOperation(value = "修改单个写crm速度")
    @GetMapping(value = "/updateActionWriteCrmLimit")
    public Result<Void> updateActionWriteCrmLimit(@RequestParam("tenantId") String tenantId, @RequestParam("limit") int limit) {
        if (StringUtils.isBlank(tenantId) || limit <= 0) {
            return Result.newError("参数错误");
        }

        Map<String, Integer> mapConfig = configCenterConfig.getMapConfig(TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2CRM.name(), Collections.emptyMap());
        mapConfig.put(tenantId, limit);
        tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2CRM.name(), JSON.toJSONString(mapConfig));
        return Result.newSuccess();
    }

    @ApiOperation(value = "修改批量写crm对象列表")
    @GetMapping(value = "/updateBatchWriteCrmApiName")
    public Result<Void> updateActionWriteCrmApiName(@RequestParam("tenantId") String tenantId, @RequestParam("batchWriteApiNameList") String batchWriteApiNameList) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isEmpty(batchWriteApiNameList)) {
            return Result.newError("参数错误");
        }

        ErpTenantConfigurationEntity configuration = tenantConfigurationManager.findOne("*", "*", "*", TenantConfigurationTypeEnum.BATCH_WRITE_TO_CRM_TENANT_OBJECT_API_NAME.name());
        final Map mapConfig = Optional.ofNullable(configuration)
                .map(ErpTenantConfigurationEntity::getConfiguration)
                .map(conf -> JacksonUtil.fromJson(conf, Map.class))
                .orElseGet(HashMap::new);
        mapConfig.put(tenantId, JSON.parseArray(batchWriteApiNameList));
        tenantConfigurationManager.updateConfig("*", "*", "*", TenantConfigurationTypeEnum.BATCH_WRITE_TO_CRM_TENANT_OBJECT_API_NAME.name(), JSON.toJSONString(mapConfig));

        return Result.newSuccess();
    }

    /**
     * 检查是否有函数修改crm对象
     */
    @NotNull
    private String getUpdateCrmFuncName(String tenantId, Boolean checkBatchWriteObject) {
        final List<SyncPloyDetailResult> allPloyDetailByDestObjectApiName;
        if (BooleanUtils.isTrue(checkBatchWriteObject)) {
            List<String> batchWrite2CRMObjAPiNameList = configCenterConfig.getBatchWrite2CrmByTenant(tenantId);
            allPloyDetailByDestObjectApiName = batchWrite2CRMObjAPiNameList.stream()
                    .map(apiName -> getAllPloyDetailByDestObjectApiName(tenantId, apiName))
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            allPloyDetailByDestObjectApiName = getAllPloyDetailByDestObjectApiName(tenantId, null);
        }

        Pattern pattern = Pattern.compile("Fx\\.object\\.\\w*([cC]reate|[uU]pdate|[rR]emove|[aA]dd|[cC]hange|[rR]ecover|[dD]elete).*");
        final String funcApiName = allPloyDetailByDestObjectApiName.stream()
                .flatMap(syncPloyDetailResult -> Stream.of(syncPloyDetailResult.getBeforeFuncApiName(), syncPloyDetailResult.getDuringFuncApiName(), syncPloyDetailResult.getAfterFuncApiName()))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .filter(funcName -> {
                    final Result<FunctionServiceFindData> function = customFunctionService.getFunction(tenantId, funcName);
                    return Optional.ofNullable(function.getData())
                            .map(FunctionServiceFindData::getBody)
                            .map(body -> {
                                final Matcher matcher = pattern.matcher(body);
                                while (matcher.find()) {
                                    String matchedLine = matcher.group();
                                    if (matchedLine.trim().startsWith("//")) {
                                        continue;
                                    }
                                    return true;
                                }
                                return false;
                            }).orElse(false);
                }).collect(Collectors.joining(";"));
        return funcApiName;
    }

    private void updateBatchWriteCrmLimit(String tenantId, int limit) {
        Map<String, Integer> mapConfig = configCenterConfig.getMapConfig(TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2CRM_BATCHWRITE.name(), Collections.emptyMap());
        mapConfig.put(tenantId, limit);
        tenantConfigurationManager.updateGlobalConfig(TenantConfigurationTypeEnum.TENANT_LIMIT_PER_COUNT_SECOND_2CRM_BATCHWRITE.name(), JSON.toJSONString(mapConfig));
    }

    @Data
    public static class ObjectBatchWriteInfo {
        private String tenantId;
        private String enterpriseName;
        private boolean independent;
        private Integer batchLimit;
        private Integer limit;
        private String batchWriteApiNameList;
        private Boolean batch;
    }

    private List<SyncPloyDetailResult> getAllPloyDetailByDestObjectApiName(String tenantId, String objectApiName) {
        final List<SyncPloyDetailEntity> entityList = adminSyncPloyDetailDao.listByDestTenantId(tenantId, null, null, objectApiName, null, 0, 500);
        return SyncPloyDetailResult.buildSyncPloyDetailResultsByEntities(i18NStringManager, null, "1", entityList);
    }

    /**
     * 检查企业是否为专属DB
     */
    public boolean checkTenantIndependentDB(String tenantId) {
        final String url = dbRouterClient.queryJdbcUrl(tenantId, "CRM", "postgresql");
        final String address = transfer2RealAddress(url);
        return isIndependentLibrary(StringUtils.substringBefore(address, ":"));
    }

    /**
     * 序号==ip地址第四位
     * 系==ip地址第三位
     *
     * ************  系(classNo): 52,序号: 30
     * 序号: 020-029 或 040-059 或 080-99
     *
     * 参考： https://wiki.firstshare.cn/pages/viewpage.action?pageId=139301953
     *
     */
    private boolean isIndependentLibrary(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        int classNo = Integer.parseInt(parts[2].split("/")[0]);
        int seqNo = Integer.parseInt(parts[3]);

        if ((seqNo >= 20 && seqNo <= 29) ||
                (seqNo >= 40 && seqNo <= 59) ||
                (seqNo >= 80 && seqNo <= 99)) {
            return true;
        }

        return false;
    }


    private String transfer2RealAddress(String metaDataPG) {
        if (MapUtils.isEmpty(source2target)) {
            source2target = new HashMap<String, String>() {
                {
                    put("172.17.56", "172.17.52");
                    put("172.17.156", "172.17.12");
                    put("172.17.76", "172.17.72");
                    put("172.17.57", "172.17.54");
                    put("172.17.157", "172.17.14");
                    put("172.17.77", "172.17.74");
                }
            };
        }
        for (Map.Entry<String, String> entry : source2target.entrySet()) {
            String source = entry.getKey();
            String target = entry.getValue();
            if (metaDataPG.contains(source)) {
                metaDataPG = metaDataPG.replaceAll(source, target);
            }
        }
        return metaDataPG.split("/")[0];
    }

    private Map<String, String> source2target;

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-flow-session-switch", config -> {
            final String text = new String(config.getContent(), UTF_8);
            source2target = JSON.parseObject(text).getObject("pGRealAddressMap", new TypeReference<Map<String, String>>() {
            }.getType());
            log.info("session switch {}", text);
        });
    }
}
