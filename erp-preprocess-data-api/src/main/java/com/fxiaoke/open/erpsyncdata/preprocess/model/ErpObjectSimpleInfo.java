package com.fxiaoke.open.erpsyncdata.preprocess.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2021/12/22
 */
@Data
@ApiModel(value = "Erp对象信息")
@Builder
public class ErpObjectSimpleInfo implements Serializable {
    private static final long serialVersionUID = -2154344410354123192L;

    @ApiModelProperty("对象apiName")
    private String objApiName;

    @ApiModelProperty("对象名称")
    private String objName;

    @ApiModelProperty("主键字段apiName")
    private String idFieldApiName;

    @ApiModelProperty("主键字段名称")
    private String idFieldName;

}
