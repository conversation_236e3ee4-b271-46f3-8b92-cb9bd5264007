package com.fxiaoke.open.erpsyncdata.admin.arg;


import com.fxiaoke.open.erpsyncdata.admin.data.SyncRulesWebData;
import com.fxiaoke.open.erpsyncdata.admin.result.QueryIntegrationDetailResult;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ObjectMappingVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class UpdateIntegrationStreamArg implements Serializable {
    @ApiModelProperty("集成流id")
    private String id;
    @ApiModelProperty("集成流名称")
    private String integrationStreamName;
    @ApiModelProperty("源系统节点")
    private QueryIntegrationDetailResult.SourceSystemNode sourceSystemNode;
    @ApiModelProperty("目标系统节点")
    private QueryIntegrationDetailResult.DestSystemNode destSystemNode;
    @ApiModelProperty("数据范围")
    private QueryIntegrationDetailResult.SyncConditionsNode syncConditionsNode;
    @ApiModelProperty("检查中间表节点")
    private QueryIntegrationDetailResult.CheckSyncDataMappingNode checkSyncDataMappingNode;
    @ApiModelProperty("通过源数据查询crm节点")
    private QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeBySource;
    @ApiModelProperty("字段映射")
    private QueryIntegrationDetailResult.FieldMappingNode fieldMappingNode;
    @ApiModelProperty("通过转换后数据查询crm节点")
    private QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeByDest;
    @ApiModelProperty("同步前自定义函数APIName")
    public QueryIntegrationDetailResult.BeforeFunctionNode beforeFunctionNode;
    @ApiModelProperty("同步中自定义函数APIName")
    public QueryIntegrationDetailResult.DurationFunctionApiNode durationFunctionApiNode;
    @ApiModelProperty("同步前自定义函数APIName")
    public QueryIntegrationDetailResult.AfterFunctionNode afterFunctionNode;
    @ApiModelProperty("同步规则")
    private SyncRulesWebData syncRules;
    @ApiModelProperty("回写Crm节点")
    private QueryIntegrationDetailResult.ReverseWriteNode reverseWriteNode;
    @ApiModelProperty("通知组件")
    private QueryIntegrationDetailResult.NotifyComplementNode notifyComplementNode;
    @ApiModelProperty("重试组件")
    private QueryIntegrationDetailResult.ReSyncErrorDataNode reSyncErrorDataNode;
    @ApiModelProperty("启用状态，1启用 2停用")
    private Integer status;
    @ApiModelProperty("状态名称，启用，停用")
    private String statusName;
    @ApiModelProperty("异常状态显示")
    private Boolean isValid;


    @Data
    @ApiModel
    public static class ObjectMappingArg implements Serializable {
        @ApiModelProperty("源企业对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("目标企业对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("字段映射列表，每条数据对应一个字段的映射关系")
        private List<SyncPloyDetailUpdateFieldMappingsArg.FieldMappingArg> fieldMappings;
    }

    @Data
    @ApiModel
    public static class FieldMappingArg implements Serializable {
        @ApiModelProperty("源企业对象字段的apiName")
        private String sourceApiName;
        @ApiModelProperty("源企业对象字段的类型")
        private String sourceType;
        @ApiModelProperty("源对象关联字段所对应对象的apiName")
        private String sourceTargetApiName;
        @ApiModelProperty("源对象引用类型，所应用的字段对应的类型")
        private String sourceQuoteFieldType;

        private String sourceQuoteRealField;
        private String sourceQuoteFieldTargetObjectApiName;
        private String sourceQuoteFieldTargetObjectField;
        @ApiModelProperty("目标对象字段apiName")
        private String destApiName;
        @ApiModelProperty("目标对象字段类型")
        private String destType;
        @ApiModelProperty("关联字段所对应对象的apiName")
        private String destTargetApiName;
        @ApiModelProperty("目标对象引用类型，所应用的字段对应的类型\"")
        private String destQuoteFieldType;
        //        @ApiModelProperty("选项")
//        private Map<Object, Object> optionMapping;
        @ApiModelProperty("选项")
        private List<SyncPloyDetailUpdateFieldMappingsArg.OptionMappingArg> optionMappings;
        @ApiModelProperty("映射类型 ： 1、原对象取值，2、原对象引用取值，3、固定值，4、循环分配")
        private Integer mappingType;
        @ApiModelProperty("自定义函数")
        private String function;
        @ApiModelProperty("固定值和循环分配时的值, json字符串")
        private String value;
        @ApiModelProperty("默认值")
        private String defaultValue;
        @ApiModelProperty("值类型，固定值1，默认值2")
        private Integer valueType;//值类型，固定值1，默认值2
    }

    @Data
    @ApiModel
    public static class OptionMappingArg implements Serializable {
        @ApiModelProperty("源选项")
        private Object sourceOption;
        @ApiModelProperty("目标选项")
        private Object destOption;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class UpdateStreamNameArg implements Serializable{
        @ApiModelProperty("集成流id")
        private String id;
        @ApiModelProperty("集成流名称")
        private String integrationStreamName;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CheckAndUpdateDetailObjMappingArg implements Serializable{
        @ApiModelProperty("集成流id")
        private String id;
        @ApiModelProperty("租户ID")
        private String tenantId;
        @ApiModelProperty("数据中心ID")
        private String dcId;
        @ApiModelProperty("主对象映射")
        private ObjectMappingVo masterObjMapping;
        @ApiModelProperty("明细对象映射")
        private List<ObjectMappingModel> detailObjMappingList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ObjectMappingModel implements Serializable {
        @ApiModelProperty("源对象apiName")
        private String sourceObjectApiName;
        @ApiModelProperty("新源对象apiName")
        private String newSourceObjectApiName;

        @ApiModelProperty("目标对象apiName")
        private String destObjectApiName;
        @ApiModelProperty("新目标对象apiName")
        private String newDestObjectApiName;

        @ApiModelProperty("是否删除明细")
        private boolean delete;
    }

}