package com.fxiaoke.open.erpsyncdata.admin.service.overseas;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.open.erpsyncdata.admin.constant.LinkedinAdErpObjectEnum;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpConnectInfoDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpObjManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SelectExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.UpdateErpObjectResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class LinkedinFieldService {
    @Autowired
    private I18NStringManager i18NStringManager;


    @Autowired
    private ErpObjManager erpObjManager;

    @Autowired
    private ErpFieldManager erpFieldManager;

    @Autowired
    private ErpObjectService erpObjectService;

    @Autowired
    private ErpObjectRelationshipDao erpObjectRelationshipDao;


    @Autowired
    private ErpConnectInfoDao erpConnectInfoDao;

    public void initObject(String tenantId, String dataCenterId) {
        log.info("init linkedin field, tenantId: {} dataCenterId: {}", tenantId, dataCenterId);
        ErpConnectInfoEntity connectInfo = erpConnectInfoDao.getByIdAndTenantId(tenantId, dataCenterId);
        if (!checkObjectExist(tenantId, dataCenterId, LinkedinAdErpObjectEnum.CAMPAIGN.getApiName())) {
            log.info("开始初始化领英广告计划对象，tenantId: {}, dataCenterId: {}", tenantId, dataCenterId);
            final String campaignObjectApiName = createErpObject(connectInfo, LinkedinAdErpObjectEnum.CAMPAIGN.getName(), LinkedinAdErpObjectEnum.CAMPAIGN.getApiName());
            initCampaignObject(tenantId, dataCenterId, campaignObjectApiName);
        }
        if (!checkObjectExist(tenantId, dataCenterId, LinkedinAdErpObjectEnum.AD.getApiName())) {
            log.info("开始初始化领英广告对象，tenantId: {}, dataCenterId: {}", tenantId, dataCenterId);
            final String adObjectApiName = createErpObject(connectInfo, LinkedinAdErpObjectEnum.AD.getName(), LinkedinAdErpObjectEnum.AD.getApiName());
            String campaignObjectApiName = erpObjManager.getMasterSplitObjApiName(connectInfo.getTenantId(), connectInfo.getId(), LinkedinAdErpObjectEnum.CAMPAIGN.getApiName());
            initAdGroupObject(tenantId, dataCenterId, adObjectApiName, campaignObjectApiName);
        }

        if (!checkObjectExist(tenantId, dataCenterId, LinkedinAdErpObjectEnum.AD_LAUNCH_DETAIL.getApiName())) {
            log.info("开始初始化领英广告投放明细对象，tenantId: {}, dataCenterId: {}", tenantId, dataCenterId);
            String adObjectApiName = erpObjManager.getMasterSplitObjApiName(connectInfo.getTenantId(), connectInfo.getId(), LinkedinAdErpObjectEnum.AD.getApiName());
            final String adLaunchDetailApiName = createErpObject(connectInfo, LinkedinAdErpObjectEnum.AD_LAUNCH_DETAIL.getName(), LinkedinAdErpObjectEnum.AD_LAUNCH_DETAIL.getApiName());
            initAdLaunchDetailObject(tenantId, dataCenterId, adLaunchDetailApiName, adObjectApiName);
        }
    }

    private void initCampaignObject(String tenantId, String dataCenterId, String splitObjApiName) {
        final List<ErpObjectFieldEntity> campaignErpObjectFieldEntityList = createCampaignErpObjectFieldEntity(tenantId, dataCenterId, splitObjApiName);
        erpFieldManager.incrementalInsertErpObjectField(tenantId, dataCenterId, splitObjApiName, campaignErpObjectFieldEntityList);
    }

    private void initAdGroupObject(String tenantId, String dataCenterId, String splitObjApiName, String campaignObjApiName) {
        final List<ErpObjectFieldEntity> adGroupErpObjectFieldEntityList = createCampaignErpObjectFieldEntity(tenantId, dataCenterId, splitObjApiName);

        final ErpObjectFieldEntity campaignField = new ErpObjectFieldEntity();
        campaignField.setId(IdGenerator.get());
        campaignField.setTenantId(tenantId);
        campaignField.setDataCenterId(dataCenterId);
        campaignField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        campaignField.setErpObjectApiName(splitObjApiName);
        campaignField.setRequired(false);
        campaignField.setCreateTime(System.currentTimeMillis());
        campaignField.setUpdateTime(System.currentTimeMillis());
        campaignField.setFieldApiName("linkedin_campaign");
        campaignField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3766, tenantId));
        campaignField.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        campaignField.setFieldExtendValue(campaignObjApiName);
        adGroupErpObjectFieldEntityList.add(campaignField);

        erpFieldManager.incrementalInsertErpObjectField(tenantId, dataCenterId, splitObjApiName, adGroupErpObjectFieldEntityList);
    }

    private void initAdLaunchDetailObject(String tenantId, String dataCenterId, String objectApiName, String adGroupObjectApiName) {
        List<ErpObjectFieldEntity> result = createLaunchErpObjectField(tenantId, dataCenterId, objectApiName, adGroupObjectApiName);
        String idFieldExtendValue = JSON.toJSONString(ImmutableMap.of("showFormField", false));
        final ErpObjectFieldEntity idField = idField(tenantId, dataCenterId, objectApiName, "id", idFieldExtendValue);
        result.add(idField);

        erpFieldManager.incrementalInsertErpObjectField(tenantId, dataCenterId, objectApiName, result);
    }

    private List<ErpObjectFieldEntity> createLaunchErpObjectField(String tenantId, String dataCenterId, String objectApiName, String adGroupObjectApiName) {
        List<ErpObjectFieldEntity> result = Lists.newArrayList();
        final ErpObjectFieldEntity adField = new ErpObjectFieldEntity();
        adField.setId(IdGenerator.get());
        adField.setTenantId(tenantId);
        adField.setDataCenterId(dataCenterId);
        adField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        adField.setErpObjectApiName(objectApiName);
        adField.setRequired(false);
        adField.setCreateTime(System.currentTimeMillis());
        adField.setUpdateTime(System.currentTimeMillis());
        adField.setFieldApiName("linkedin_ad");
        adField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3767, tenantId));
        adField.setFieldDefineType(ErpFieldTypeEnum.object_reference);
        adField.setFieldExtendValue(adGroupObjectApiName);
        result.add(adField);

        final ErpObjectFieldEntity clickField = new ErpObjectFieldEntity();
        clickField.setId(IdGenerator.get());
        clickField.setTenantId(tenantId);
        clickField.setDataCenterId(dataCenterId);
        clickField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        clickField.setErpObjectApiName(objectApiName);
        clickField.setRequired(false);
        clickField.setCreateTime(System.currentTimeMillis());
        clickField.setUpdateTime(System.currentTimeMillis());
        clickField.setFieldApiName("click");
        clickField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3768, tenantId));
        clickField.setFieldDefineType(ErpFieldTypeEnum.number);
        result.add(clickField);

        final ErpObjectFieldEntity showField = new ErpObjectFieldEntity();
        showField.setId(IdGenerator.get());
        showField.setTenantId(tenantId);
        showField.setDataCenterId(dataCenterId);
        showField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        showField.setErpObjectApiName(objectApiName);
        showField.setRequired(false);
        showField.setCreateTime(System.currentTimeMillis());
        showField.setUpdateTime(System.currentTimeMillis());
        showField.setFieldApiName("show");
        showField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3762, tenantId));
        showField.setFieldDefineType(ErpFieldTypeEnum.number);
        result.add(showField);

        final ErpObjectFieldEntity costField = new ErpObjectFieldEntity();
        costField.setId(IdGenerator.get());
        costField.setTenantId(tenantId);
        costField.setDataCenterId(dataCenterId);
        costField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        costField.setErpObjectApiName(objectApiName);
        costField.setRequired(false);
        costField.setCreateTime(System.currentTimeMillis());
        costField.setUpdateTime(System.currentTimeMillis());
        costField.setFieldApiName("cost");
        costField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3761, tenantId));
        costField.setFieldDefineType(ErpFieldTypeEnum.currency);
        result.add(costField);

        final ErpObjectFieldEntity launchDateField = new ErpObjectFieldEntity();
        launchDateField.setId(IdGenerator.get());
        launchDateField.setTenantId(tenantId);
        launchDateField.setDataCenterId(dataCenterId);
        launchDateField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        launchDateField.setErpObjectApiName(objectApiName);
        launchDateField.setRequired(false);
        launchDateField.setCreateTime(System.currentTimeMillis());
        launchDateField.setUpdateTime(System.currentTimeMillis());
        launchDateField.setFieldApiName("launch_date");
        launchDateField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3728, tenantId));
        launchDateField.setFieldDefineType(ErpFieldTypeEnum.date);
        result.add(launchDateField);
        return result;
    }

    protected String createErpObject(ErpConnectInfoEntity connectInfo, String objectName, String objectApiName) {
        ErpObjectRelationshipResult erpObjectRelationshipResult = new ErpObjectRelationshipResult();
        ErpObjectDescResult actualErpObject = new ErpObjectDescResult();
        actualErpObject.setChannel(connectInfo.getChannel());
        actualErpObject.setErpObjectApiName(objectApiName);
        actualErpObject.setErpObjectExtendValue("FIELD_SPLIT");
        actualErpObject.setErpObjectName(objectName);
        erpObjectRelationshipResult.setActualErpObject(actualErpObject);

        ErpObjectDescResult fakeErpObject = new ErpObjectDescResult();
        fakeErpObject.setChannel(connectInfo.getChannel());
        fakeErpObject.setChildren(new ArrayList<>());
        fakeErpObject.setErpObjectExtendValue("");
        fakeErpObject.setErpObjectName(objectName);
        fakeErpObject.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
        erpObjectRelationshipResult.setFakeErpObject(Lists.newArrayList(fakeErpObject));

        Result<UpdateErpObjectResult> updateResult = erpObjectService.updateErpObjects(connectInfo.getTenantId(), connectInfo.getId(), SuperUserConstants.USER_ID, erpObjectRelationshipResult, null);
        String tenantId = connectInfo.getTenantId();
        List<ErpObjectRelationshipEntity> list = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findMasterByRealObjectApiName(tenantId, connectInfo.getId(), objectApiName);
        log.info("createErpObject, tenantId:{}, objectApiName: {} result: {} queryList: {}", connectInfo.getTenantId(), objectApiName, updateResult, list);
        return list.get(0).getErpSplitObjectApiname();

    }

    private List<ErpObjectFieldEntity> createCampaignErpObjectFieldEntity(String tenantId, String dataCenterId, String objectApiName) {
        List<ErpObjectFieldEntity> result = Lists.newArrayList();
        final ErpObjectFieldEntity nameField = new ErpObjectFieldEntity();
        nameField.setId(IdGenerator.get());
        nameField.setTenantId(tenantId);
        nameField.setDataCenterId(dataCenterId);
        nameField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        nameField.setErpObjectApiName(objectApiName);
        nameField.setRequired(false);
        nameField.setCreateTime(System.currentTimeMillis());
        nameField.setUpdateTime(System.currentTimeMillis());
        nameField.setFieldApiName("name");
        nameField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s274, tenantId));
        nameField.setFieldDefineType(ErpFieldTypeEnum.string);
        result.add(nameField);

        final ErpObjectFieldEntity statusField = new ErpObjectFieldEntity();
        statusField.setId(IdGenerator.get());
        statusField.setTenantId(tenantId);
        statusField.setDataCenterId(dataCenterId);
        statusField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        statusField.setErpObjectApiName(objectApiName);
        statusField.setRequired(false);
        statusField.setCreateTime(System.currentTimeMillis());
        statusField.setUpdateTime(System.currentTimeMillis());
        statusField.setFieldApiName("status");
        statusField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3738, tenantId));
        statusField.setFieldDefineType(ErpFieldTypeEnum.select_one);
        List<SelectExtend> selectExtends = Lists.newArrayList();
        selectExtends.add(SelectExtend.of("ongoing", i18NStringManager.getByEi(I18NStringEnum.s3743, tenantId)));
        selectExtends.add(SelectExtend.of("complete", i18NStringManager.getByEi(I18NStringEnum.s3752, tenantId)));
        statusField.setFieldExtendValue(JSON.toJSONString(selectExtends));
        result.add(statusField);

        final ErpObjectFieldEntity startDateField = new ErpObjectFieldEntity();
        startDateField.setId(IdGenerator.get());
        startDateField.setTenantId(tenantId);
        startDateField.setDataCenterId(dataCenterId);
        startDateField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        startDateField.setErpObjectApiName(objectApiName);
        startDateField.setRequired(false);
        startDateField.setCreateTime(System.currentTimeMillis());
        startDateField.setUpdateTime(System.currentTimeMillis());
        startDateField.setFieldApiName("startDate");
        startDateField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3726, tenantId));
        startDateField.setFieldDefineType(ErpFieldTypeEnum.date);
        JSONObject dateFormat = new JSONObject();
        dateFormat.put("format", "yyyy-MM-dd");
        startDateField.setFieldExtendValue(dateFormat.toJSONString());
        result.add(startDateField);

        final ErpObjectFieldEntity endDateField = new ErpObjectFieldEntity();
        endDateField.setId(IdGenerator.get());
        endDateField.setTenantId(tenantId);
        endDateField.setDataCenterId(dataCenterId);
        endDateField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        endDateField.setErpObjectApiName(objectApiName);
        endDateField.setRequired(false);
        endDateField.setCreateTime(System.currentTimeMillis());
        endDateField.setUpdateTime(System.currentTimeMillis());
        endDateField.setFieldApiName("endDate");
        endDateField.setFieldLabel(i18NStringManager.getByEi(I18NStringEnum.s3727, tenantId));
        endDateField.setFieldDefineType(ErpFieldTypeEnum.date);
        endDateField.setFieldExtendValue(dateFormat.toJSONString());
        result.add(endDateField);
        String idFieldExtendValue = JSON.toJSONString(ImmutableMap.of("showFormField", false));
        final ErpObjectFieldEntity idField = idField(tenantId, dataCenterId, objectApiName, "id", idFieldExtendValue);
        result.add(idField);
        return result;
    }

    protected ErpObjectFieldEntity idField(final String tenantId, final String dataCenterId, final String objectName, final String idFieldApiName, final String idFieldExtendValue) {
        final ErpObjectFieldEntity objectField = new ErpObjectFieldEntity();
        objectField.setId(IdGenerator.get());
        objectField.setTenantId(tenantId);
        objectField.setDataCenterId(dataCenterId);
        objectField.setChannel(ErpChannelEnum.ERP_LINKEDIN);
        objectField.setErpObjectApiName(objectName);
        objectField.setRequired(false);
        objectField.setCreateTime(System.currentTimeMillis());
        objectField.setUpdateTime(System.currentTimeMillis());
        objectField.setFieldApiName(idFieldApiName);
        objectField.setFieldLabel("id");
        objectField.setFieldDefineType(ErpFieldTypeEnum.id);
        objectField.setFieldExtendValue(idFieldExtendValue);
        return objectField;
    }

    private boolean checkObjectExist(String tenantId, String dataCenterId, String objectName) {
        List<ErpObjectRelationshipEntity> list = erpObjectRelationshipDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).findMasterByRealObjectApiName(tenantId, dataCenterId, objectName);
        log.info("checkObjectExist, tenantId: {}, dataCenterId: {}, objectName: {}, result: {}", tenantId, dataCenterId, objectName, list);
        return CollectionUtils.isNotEmpty(list);
    }
}
