package com.fxiaoke.open.erpsyncdata.monitor.mq.processor;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.monitor.manager.AlertAndBreakManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.TimeFilterArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.MonitorType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.PollingNotFirstErpAlarmData;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.BaseResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date: 16:02 2024/11/21
 * @Desc:
 */
@Service
@Slf4j
public class PollingNotFirstErpProcessor extends AbstractMonitorMqProcessor<PollingNotFirstErpAlarmData> {
    @Autowired
    private AlertAndBreakManager alertAndBreakManager;

    public PollingNotFirstErpProcessor() {
        super(MonitorType.POLLING_NOT_FIRST_ERP, new TypeReference<PollingNotFirstErpAlarmData>() {
        });
    }

    @Override
    void process(PollingNotFirstErpAlarmData data) {
        alertAndBreakManager.handelPollingNotFirstErp(data);
    }
}
