{"type": "page", "title": "写crm对象限速", "body": [{"type": "form", "actions": [{"type": "submit", "label": "查看集成流"}, {"type": "button", "actionType": "ajax", "label": "查询限速信息", "reload": "checkLimit", "api": "get:../configRoute/getObjectBatchWriteInfo?tenantId=${tenantId}&objectApiName=${objectApiName}"}, {"type": "button", "actionType": "ajax", "label": "修改企业批量写crm限速", "api": "get:../configRoute/updateBatchWriteCrmLimit?tenantId=${tenantId}&limit=${batchLimit}&force=${force}"}, {"type": "button", "actionType": "ajax", "label": "修改企业批量写crm对象", "api": "get:../configRoute/updateBatchWriteCrmApiName?tenantId=${tenantId}&batchWriteApiNameList=${batchWriteApiNameList}"}, {"type": "button", "actionType": "ajax", "label": "修改企业action写crm限速", "api": "get:../configRoute/updateActionWriteCrmLimit?tenantId=${tenantId}&limit=${limit}"}], "title": "检查对象限速信息", "name": "checkLimit", "mode": "inline", "target": "getPloyDetailsByObjectApiName", "body": [{"type": "input-text", "name": "tenantId", "label": "tenantId"}, {"type": "input-text", "name": "objectApiName", "clearValueOnEmpty": true, "trimContents": true, "placeholder": "为空查所有", "label": "crm对象apiName"}, {"type": "static", "name": "enterpriseName", "label": "企业名称"}, {"type": "static", "name": "independent", "label": "专属DB"}, {"type": "static", "name": "batch", "label": "是否批量写"}, {"type": "input-text", "name": "batchLimit", "label": "批量写crm速度(企业级)"}, {"type": "input-text", "name": "limit", "label": "action写crm速度(企业级)"}, {"type": "textarea", "maxRows": 3, "name": "batchWriteApiNameList", "label": "批量写对象ApiName列表"}, {"type": "checkbox", "name": "force", "id": "force", "option": "强制更新批量写crm限速", "label": "勾选"}]}, {"type": "crud", "name": "getPloyDetailsByObjectApiName", "debug": true, "initFetch": false, "api": "../configRoute/getPloyDetailsByObjectApiName?tenantId=${tenantId}&objectApiName=${objectApiName}", "defaultParams": {"perPage": 100}, "columns": [{"name": "id", "label": "集成流id"}, {"name": "ployName", "label": "集成流名称"}, {"name": "sourceObjectApiName", "label": "源对象"}, {"name": "destObjectApiName", "label": "目标对象"}, {"name": "syncRulesResult.events", "label": "目标事件"}, {"name": "status", "type": "status", "label": "是否启用", "sortable": "${status} == 1 ? true : false"}, {"name": "beforeFuncApiName", "label": "同步前函数", "onEvent": {"click": {"actions": [{"expression": "${beforeFuncApiName != null && beforeFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${beforeFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}, {"name": "duringFuncApiName", "label": "同步中函数", "onEvent": {"click": {"actions": [{"expression": "${duringFuncApiName != null && duringFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${duringFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}, {"name": "afterFuncApiName", "label": "同步后函数", "onEvent": {"click": {"actions": [{"expression": "${afterFuncApiName != null && afterFuncApiName != ''}", "type": "button", "actionType": "drawer", "drawer": {"resizable": true, "closeOnEsc": true, "overlay": false, "actions": [], "size": "xl", "position": "left", "title": "函数(按 Esc 关闭)", "body": {"type": "page", "initFetch": true, "initApi": {"method": "post", "url": "../integrationstream/findFunctionBody", "data": {"tenantId": "${ployTenantId}", "function": "${afterFuncApiName}"}}, "body": [{"type": "code", "language": "java", "name": "body"}]}}}]}}}], "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "combineNum": 0}]}