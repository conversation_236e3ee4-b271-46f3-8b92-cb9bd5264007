<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!--redisson-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="erp-sync-data-all"/>
    </bean>
    <!--okHttp 这里的配置，foneshare会不走代理。-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="erp-sync-data-all"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!--使用cglib代理，不使用代码会报错-->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>