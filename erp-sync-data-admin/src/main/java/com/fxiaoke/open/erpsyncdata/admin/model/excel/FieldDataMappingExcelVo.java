package com.fxiaoke.open.erpsyncdata.admin.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldDataMappingExcelVo implements Serializable {

    /**
     * 纷享数据name
     */
    @ExcelProperty(value = "erpdss.global.global.s1043",index = 0)
    private String fsDataName;

    /**
     * 纷享数据id
     */
    @ExcelProperty(value = "erpdss.global.global.s1044",index = 1)
    private String fsDataId;

    /**
     * erp数据name
     */
    @ExcelProperty(value = "erpdss.global.global.s1045",index = 2)
    private String erpDataName;

    /**
     * erp数据id
     */
    @ExcelProperty(value = "erpdss.global.global.s1046",index = 3)
    private String erpDataId;

    public static FieldDataMappingExcelVo getTempData(I18NStringManager i18NStringManager, String tenantId, String lang) {
        FieldDataMappingExcelVo vo = new FieldDataMappingExcelVo();
        vo.setFsDataName(i18NStringManager.get(I18NStringEnum.s3780, lang, tenantId));
        vo.setErpDataName(i18NStringManager.get(I18NStringEnum.s3781, lang, tenantId));
        vo.setFsDataId("248");
        vo.setErpDataId("248");
        return vo;
    }

}
