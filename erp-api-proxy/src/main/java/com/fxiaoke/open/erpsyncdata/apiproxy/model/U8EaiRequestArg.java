package com.fxiaoke.open.erpsyncdata.apiproxy.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class U8EaiRequestArg implements Serializable {

    private static final long serialVersionUID = -8302434255771514879L;
    private String strategy;

    private String objApiName;

    private String sender;

    private String receiver="u8";

    private String requestEaiBody;//EAI报文

    private String masterDataIdField;//主数据字段

    private String masterDataSql;//主数据执行sql

    private Map<String,List<String>> detailDataSql;//明细数据执行sql

    private String masterIdQuerySql;//反查主数据id

    private Map<String,List<String>> detailIdQuerySql;//反查明细数据id

    private List<String>excuteMasterSqlList;//主数据可能执行多个表。第一个sql需要是主对象。

    private  Map<String,List<String>>excuteDetailSqlList;//明细执行sql

    private Boolean queryDataFromCache=false;//缓存里面拉取

    private Integer offset;

    private Integer limit;

    private Long startTime;

    private Long endTime;

    private Boolean useCombine=false;

}
