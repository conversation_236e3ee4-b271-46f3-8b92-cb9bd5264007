package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by fengyh on 2020/12/15.
 */

@Slf4j
public class ObjToMapUtil {
    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    public static Map<String, Object> objectToMap(Object obj)  {
        ObjectMapper oMapper = new ObjectMapper();
        Map<String, Object> map = oMapper.convertValue(obj, Map.class);
        return map;
    }

    public static void main(String args []) {
        ErpIdArg arg = new ErpIdArg();
        arg.setDataId("id1");
        arg.setIncludeDetail(false);
        arg.setObjAPIName("testObjAPI");
        arg.setTenantId("tid");

        try {
            Map<String, Object> map = objectToMap(arg);
            System.out.println(map);
        }catch (Exception e) {}
    }
}
