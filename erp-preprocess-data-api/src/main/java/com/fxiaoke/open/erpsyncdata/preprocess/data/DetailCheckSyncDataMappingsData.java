package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @Date: 10:40 2023/5/19
 * @Desc:
 */
@Data
public class DetailCheckSyncDataMappingsData extends ArrayList<DetailCheckSyncDataMappingsData.DetailCheckSyncDataMappingData> implements Serializable {
    @Data
    public static class DetailCheckSyncDataMappingData implements Serializable {
        private QueryObjectMappingData queryObjectMappingData; //查询crm
        private DetailObjectIdFieldMappingsData source2SyncDataMapping;//集成流源对象字段映射到中间表、查出来的目标对象字段映射到中间表

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("{");
            sb.append("\"queryObjectMappingData\":\"")
                    .append(queryObjectMappingData).append('\"');
            sb.append(",\"source2SyncDataMapping\":\"")
                    .append(source2SyncDataMapping).append('\"');
            sb.append('}');
            return sb.toString();
        }
    }
}

