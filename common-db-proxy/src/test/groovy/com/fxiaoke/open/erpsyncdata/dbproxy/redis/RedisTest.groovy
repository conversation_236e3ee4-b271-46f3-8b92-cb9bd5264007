package com.fxiaoke.open.erpsyncdata.dbproxy.redis


import com.github.jedis.support.JedisFactoryBean
import com.google.gson.GsonBuilder
import com.google.gson.ToNumberPolicy
import spock.lang.Ignore
import spock.lang.Specification
import spock.lang.Unroll

import java.util.function.Function
import java.util.stream.Collectors

/**
 * <AUTHOR> 
 * @date 2022/10/20 14:07:00
 */
// TODO: 外部配置
class RedisTest extends Specification {

    @Ignore
    def "测试redis的zset"() {
        setup:
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-erp-sync-data");

        def bean = new JedisFactoryBean()
        bean.setConfigName("erp-sync-data-all")
        bean.afterPropertiesSet()
        def jedisCmd = bean.getObject()


        def key = "testRedisZset"
        when:
        jedisCmd.del(key)

        def collect = ["test": 123D, "test2": 123D, "test3": 123D]
//        def zadd = jedisCmd.zadd(key, collect)
        def s
        collect.put("test4", 456D)
        jedisCmd.pipeline({ pipelineCmd ->
            s = collect.keySet().stream()
                    .collect(Collectors.toMap(Function.identity(), { member -> pipelineCmd.zscore(key, (String) member) }))
        });
        def collect1 = s.values().stream().map({ it.get() }).collect(Collectors.toList())
        s.entrySet().stream()
                .filter({ entry -> Objects.nonNull(entry.getValue().get()) })
                .map({ it.getKey() })
                .forEach({ collect.remove(it) });

        def zadd1 = jedisCmd.zadd(key, collect)
        def tuples = jedisCmd.zrangeWithScores(key, 0, -1)

        jedisCmd.del(key)

        then:
//        println zadd
        println zadd1
        println tuples
        println collect

    }

    @Unroll
    def "测试gson-#id"() {
        when:
//        String json = "{\"number\":1234567890,\"number2\":1234567809}";

        def gson = new GsonBuilder().setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE).create()
//        def gson = new GsonBuilder().setObjectToNumberStrategy(ToNumberPolicy.BIG_DECIMAL).create()

        def json1 = gson.fromJson(json, Map.class)
        println json1

        def json2 = gson.toJson(json1)

        then:
        json == json2

        where:
        id       | json
        "int"    | "{\"number\":168,\"number2\":621}"
        "long"   | "{\"number\":1681352267170,\"number2\":1681352267621}"
        "double" | "{\"number\":1.68135226717,\"number2\":1.681352267621}"

        "I+L+D"    | "{\"number\":168,\"number2\":1681352260000,\"number3\":1.68135226}"

        "D+S"    | "{\"number\":1.68135226717,\"number2\":\"af\"}"
    }
}
