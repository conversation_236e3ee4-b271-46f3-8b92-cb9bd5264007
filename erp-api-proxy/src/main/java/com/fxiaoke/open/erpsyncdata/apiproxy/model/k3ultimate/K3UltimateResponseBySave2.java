package com.fxiaoke.open.erpsyncdata.apiproxy.model.k3ultimate;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class K3UltimateResponseBySave2 implements Serializable {
    private DataModel data;
    private String errorCode;
    private String message;
    //发现有些接口返回这样的key，先这样兼容一下
    private String messagecn;
    private boolean status;

    @Data
    public static class DataModel implements Serializable {
        private int failCount;
        private int successCount;
        private int totalCount;
        private List<ResultModel> result;
    }

    @Data
    public static class ResultModel implements Serializable {
        /**
         * 单据索引，适用于批量新增或更新，这个是和入参的顺序保持一致
         */
        private int billIndex;
        /**
         * 单据状态
         */
        private boolean billStatus;
        /**
         * 如果当前单据新增或更新失败，返回对应的失败信息
         */
        private List<Object> errors;
        /**
         * 新增或更新成功，返回单据id
         */
        private String id;
        /**
         * 新增或更新成功，返回明细单据id,预置的接口没有返回，需要查询一次数据，如果客开接口返回了，那就不需要调用查询了
         */
        private Map<String, List<String>> detailDataIds = new HashMap<>(0);
        /**
         * 候选键字段apiName列表
         */
        private Map<String, String> keys;
        /**
         * 新增或更新成功，返回单据编码
         */
        private String number;
        /**
         * 代表新增(Add)或更新(Update)动作
         */
        private String type;

        @Data
        public static class Error implements Serializable {
            /**
             * 旗舰版对象apiName，比如bd_customer
             */
            private String entityKey;
            /**
             * 明细行索引，可为null
             */
            private Integer entryRowIndex;
            /**
             * 候选键字段apiName列表,可为null
             */
            private Map<String, String> keys;
            /**
             * 错误信息
             */
            private List<String> rowMsg;
            /**
             * 子明细行索引，可为null
             */
            private Integer subEntryRowIndex;
        }
    }

    public boolean isSuccess() {
        return "0".equals(errorCode) && status;
    }

    public String getMessageOrMessageCn() {
        if (StringUtils.isNotBlank(message)) {
            return message;
        } else {
            return messagecn;
        }
    }
}