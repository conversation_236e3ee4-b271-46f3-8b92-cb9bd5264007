package com.fxiaoke.open.erpsyncdata.converter.manager;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncDataStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.SyncDataMappingFieldKey;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.SyncDataMappingsDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingsEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdGenerator;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.TenantType;
import com.fxiaoke.open.erpsyncdata.preprocess.data.*;
import com.fxiaoke.open.erpsyncdata.preprocess.factory.OuterServiceFactory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.ErpSyncDataException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 同步前，校验中间表，
 * 前端区分了只能erp->crm，都是查crm的数据
 */
@Slf4j
@Component
public class CheckSyncDataMappingManager {

    @Autowired
    private OuterServiceFactory outerServiceFactory;
    @Autowired
    private SyncDataMappingManager syncDataMappingManager;
    @Autowired
    private SyncDataMappingsDao syncDataMappingsDao;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private I18NStringManager i18NStringManager;


    public void processMessage(SyncDataContextEvent eventData, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        //检查中间表节点不为空
        if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes() == null
                || syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getCheckSyncDataMappingNode() == null) {
            return;
        }
        Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
        ObjectData sourceData = eventData.getSourceData();
        String sourceObjectApiName = sourceData.getApiName();
        Integer sourceEventType = eventData.getSourceEventType();
        String tenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        eventData.setTenantId(tenantId);
        eventData.setSourceTenantId(tenantId);
        boolean isMasterObject = syncPloyDetailSnapshotData.getSourceObjectApiName().equals(sourceObjectApiName);
        //新增/更新才执行检查中间表节点逻辑
        if (sourceEventType != EventTypeEnum.ADD.getType() && sourceEventType != EventTypeEnum.UPDATE.getType()) {
            log.info("checkSyncDataMapping destEventType={} is not add and update ", sourceEventType);
            return;
        }

        try {
            String crmDcId;
            if (TenantType.CRM.equals(sourceTenantType)) {
                crmDcId = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceDataCenterId();
            } else {
                crmDcId = syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestDataCenterId();
            }
            //检查中间表节点
            IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getCheckSyncDataMappingNode();
            //查询crm数据节点
            QueryObjectMappingData queryObjectMappingData = checkSyncDataMappingNode.getQueryObjectMappingData();
            if (isMasterObject) {//主对象
                //主对象
                doCheckSyncDataMapping(eventData, crmDcId, sourceTenantType, tenantId, sourceData, queryObjectMappingData, checkSyncDataMappingNode.getSource2SyncDataMapping(), true);
            } else {//单独从对象
                DetailCheckSyncDataMappingsData detailCheckSyncDataMappingsData = checkSyncDataMappingNode.getDetailCheckSyncDataMappingData();
                if (CollectionUtils.isNotEmpty(detailCheckSyncDataMappingsData)) {
                    for (DetailCheckSyncDataMappingsData.DetailCheckSyncDataMappingData checkSyncDataMappingData : detailCheckSyncDataMappingsData) {
                        QueryObjectMappingData detailQueryObjectMappingData = checkSyncDataMappingData.getQueryObjectMappingData();
                        if (sourceObjectApiName.equals(detailQueryObjectMappingData.getDestObjectApiName())) {//需要处理的从对象
                            doCheckSyncDataMapping(eventData, crmDcId, sourceTenantType, tenantId, sourceData, detailQueryObjectMappingData, checkSyncDataMappingData.getSource2SyncDataMapping(), false);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查询中间表节点异常：" + e);
        }
    }

    public void processDetailMessage(SyncDataContextEvent eventData, SyncPloyDetailSnapshotData2 syncPloyDetailSnapshotData) {
        String tenantId = syncPloyDetailSnapshotData.getSourceTenantId();
        Map<String, List<ObjectData>> detailObjectDatasMap = eventData.getDetailObjectDatasMap();
        try {
            Integer sourceTenantType = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceTenantType();
            String crmDcId;
            if (TenantType.CRM.equals(sourceTenantType)) {
                crmDcId = syncPloyDetailSnapshotData.getSyncPloyDetailData().getSourceDataCenterId();
            } else {
                crmDcId = syncPloyDetailSnapshotData.getSyncPloyDetailData().getDestDataCenterId();
            }
            if (syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes() != null
                    && syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getCheckSyncDataMappingNode() != null) {
                //检查中间表节点
                IntegrationStreamNodesData.CheckSyncDataMappingNode checkSyncDataMappingNode = syncPloyDetailSnapshotData.getSyncPloyDetailData().getIntegrationStreamNodes().getCheckSyncDataMappingNode();
                DetailCheckSyncDataMappingsData detailCheckSyncDataMappingsData = checkSyncDataMappingNode.getDetailCheckSyncDataMappingData();
                if (CollectionUtils.isNotEmpty(detailCheckSyncDataMappingsData) && detailObjectDatasMap != null) {
                    for (DetailCheckSyncDataMappingsData.DetailCheckSyncDataMappingData checkSyncDataMappingData : detailCheckSyncDataMappingsData) {
                        QueryObjectMappingData detailQueryObjectMappingData = checkSyncDataMappingData.getQueryObjectMappingData();
                        List<ObjectData> detailObjectDataList = detailObjectDatasMap.get(detailQueryObjectMappingData.getDestObjectApiName());
                        if (CollectionUtils.isNotEmpty(detailObjectDataList)) {
                            for (ObjectData detailObjectData : detailObjectDataList) {
                                doCheckSyncDataMapping(eventData, crmDcId, sourceTenantType, tenantId, detailObjectData, detailQueryObjectMappingData, checkSyncDataMappingData.getSource2SyncDataMapping(), false);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("从对象-查询中间表节点异常：" + e);
        }
    }

    private void doCheckSyncDataMapping(SyncDataContextEvent eventData, String crmDcId, Integer sourceTenantType, String tenantId, ObjectData sourceData,
                                        QueryObjectMappingData queryObjectMappingData, DetailObjectIdFieldMappingsData source2SyncDataMapping, Boolean isMaster) {
        String crmObjApiName = queryObjectMappingData.getSourceObjectApiName();
        String mappingSourceObjApiName = queryObjectMappingData.getDestObjectApiName();
        List<List<FilterData>> orFilters = Lists.newArrayList();
        for (QueryObjectOrFilterFieldMappingsData.QueryObjectAndFilterFieldMappingsData fieldMappingData : queryObjectMappingData.getQueryFieldMappings()) {
            List<FilterData> andFilters = Lists.newArrayList();
            for (FilterData filterData : fieldMappingData) {
                if (CollectionUtils.isNotEmpty(filterData.getFieldValue())) {
                    FilterData filter = JacksonUtil.fromJson(JacksonUtil.toJson(filterData), FilterData.class);
                    String sourceApiName = filterData.getFieldValue().get(0).toString();
                    Object sourceValue = sourceData.get(sourceApiName);
                    if (sourceValue == null) {
                        filter.setFieldValue(Lists.newArrayList());
                    } else {
                        filter.setFieldValue(Lists.newArrayList(sourceValue));
                    }
                    andFilters.add(filter);
                }
            }
            orFilters.add(andFilters);
        }
        ObjectData crmObjectData = outerServiceFactory.get(TenantType.CRM).queryObjectDataByEqFilter(crmDcId, tenantId, sourceTenantType, crmObjApiName, orFilters).getData();
        if (crmObjectData != null) {
            Map<String, Object> mapping = Maps.newHashMap();
            mapping.put(SyncDataMappingFieldKey.sourceObjectApiName, mappingSourceObjApiName);
            mapping.put(SyncDataMappingFieldKey.destObjectApiName, crmObjApiName);
            for (DetailObjectIdFieldMappingsData.DetailObjectIdFieldMappingData objectMappingData : source2SyncDataMapping) {
                if (crmObjApiName.equals(objectMappingData.getSourceObjectApiName())) {
                    mapping.put(objectMappingData.getDestApiName(), crmObjectData.get(objectMappingData.getSourceApiName()));
                } else if (mappingSourceObjApiName.equals(objectMappingData.getSourceObjectApiName())) {
                    mapping.put(objectMappingData.getDestApiName(), sourceData.get(objectMappingData.getSourceApiName()));
                }
            }
            SyncDataMappingsEntity mappingsEntity = JacksonUtil.fromJson(JacksonUtil.toJson(mapping), SyncDataMappingsEntity.class);
            if (mappingsEntity == null || StringUtils.isBlank(mappingsEntity.getSourceObjectApiName()) || StringUtils.isBlank(mappingsEntity.getSourceDataId())
                    || StringUtils.isBlank(mappingsEntity.getDestObjectApiName()) || StringUtils.isBlank(mappingsEntity.getDestDataId())
                    || StringUtils.isBlank(mappingsEntity.getSourceDataName())) {
                log.info("doCheckSyncDataMapping SyncDataMappingsEntity field need not null mapping={}", mapping);
                return;
            }
            if (!isMaster && StringUtils.isBlank(mappingsEntity.getMasterDataId())) {
                log.info("doCheckSyncDataMapping SyncDataMappingsEntity masterDataId field need not null mapping={}", mapping);
                return;
            }
            if (StringUtils.isBlank(mappingsEntity.getRemark())) {
                mappingsEntity.setRemark(i18NStringManager.getByEi(I18NStringEnum.s928,tenantId));
            }
            //此处调用的方法，如果left不为空，right就不查询了一定为空
            Pair<SyncDataMappingsEntity, SyncDataMappingsEntity> mapping2Way = syncDataMappingManager.getMappingFirstBySource(tenantId, mappingsEntity.getSourceObjectApiName(),
                    mappingsEntity.getSourceDataId(), mappingsEntity.getDestObjectApiName());
            try {
            if (mapping2Way.getLeft() == null && mapping2Way.getRight() == null) {//插入
                log.info("doCheckSyncDataMapping insert SyncDataMappingsEntity mapping={} old mapping2Way={}", mapping, mapping2Way);
                mappingsEntity.setId(idGenerator.get(tenantId, mappingsEntity.getSourceDataId()));
                mappingsEntity.setTenantId(tenantId);
                mappingsEntity.setSourceTenantId(tenantId);
                mappingsEntity.setDestTenantId(tenantId);
                mappingsEntity.setIsCreated(true);
                mappingsEntity.setIsDeleted(false);
                mappingsEntity.setLastSyncStatus(SyncDataStatusEnum.WRITE_SUCCESS.getStatus());
                mappingsEntity.setCreateTime(System.currentTimeMillis());
                mappingsEntity.setUpdateTime(mappingsEntity.getUpdateTime());
                syncDataMappingsDao.setTenantId(tenantId).insertIgnore(mappingsEntity);
                // id映射补充中间表后,需要修改EventType,防止后面369stop
                eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
            } else {
                if (mapping2Way.getLeft() != null && !mapping2Way.getLeft().getDestDataId().equals(mappingsEntity.getDestDataId())) {//更新目标id
                    log.info("doCheckSyncDataMapping update destDataId SyncDataMappingsEntity mapping={} old mapping2Way={}", mapping, mapping2Way);
                    int i = syncDataMappingsDao.setTenantId(tenantId).updateMappingByIdIgnore(tenantId, mapping2Way.getLeft().getId(), null, mappingsEntity.getDestDataId(),
                            mappingsEntity.getSourceDataName(), mappingsEntity.getDestDataName(), mappingsEntity.getMasterDataId(),
                            SyncDataStatusEnum.WRITE_SUCCESS.getStatus(), true, System.currentTimeMillis(), mappingsEntity.getRemark());
                    if (i != 1) {
                        log.info("doCheckSyncDataMapping update destDataId SyncDataMappingsEntity failed mapping={} old mapping2Way={}", mapping, mapping2Way);
                        throw new ErpSyncDataException("doCheckSyncDataMapping update destDataId SyncDataMappingsEntity failed");
                    }
                    if (BooleanUtils.isFalse(mapping2Way.getLeft().getIsCreated())) {
                        // id映射补充中间表后,需要修改EventType,防止后面369stop
                        eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                    }
                } else if (mapping2Way.getRight() != null && !mapping2Way.getRight().getSourceDataId().equals(mappingsEntity.getDestDataId())) {//更新源id
                    log.info("doCheckSyncDataMapping update sourceDataId SyncDataMappingsEntity mapping={} old mapping2Way={}", mapping, mapping2Way);
                    int i = syncDataMappingsDao.setTenantId(tenantId).updateMappingByIdIgnore(tenantId, mapping2Way.getRight().getId(), mappingsEntity.getDestDataId(), null,
                            mappingsEntity.getSourceDataName(), mappingsEntity.getDestDataName(), mappingsEntity.getMasterDataId(),
                            SyncDataStatusEnum.WRITE_SUCCESS.getStatus(), true, System.currentTimeMillis(), mappingsEntity.getRemark());
                    if (i != 1) {
                        log.info("doCheckSyncDataMapping update sourceDataId SyncDataMappingsEntity failed mapping={} old mapping2Way={}", mapping, mapping2Way);
                        throw new ErpSyncDataException("doCheckSyncDataMapping update sourceDataId SyncDataMappingsEntity failed");
                    }
                    if (BooleanUtils.isFalse(mapping2Way.getRight().getIsCreated())) {
                        // id映射补充中间表后,需要修改EventType,防止后面369stop
                        eventData.setSourceEventType(EventTypeEnum.UPDATE.getType());
                    }
                } else {
                    log.info("doCheckSyncDataMapping SyncDataMappingsEntity mapping={} old mapping2Way={}", mapping, mapping2Way);
                }
            }
            } catch (Exception e) {
                log.warn("doCheckSyncDataMapping insert SyncDataMappingsEntity error mapping={} old mapping2Way={}", mapping, mapping2Way);
            }
        }

    }


}
