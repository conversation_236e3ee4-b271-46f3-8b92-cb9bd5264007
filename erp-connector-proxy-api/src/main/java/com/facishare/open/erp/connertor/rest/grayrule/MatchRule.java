package com.facishare.open.erp.connertor.rest.grayrule;

import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/21 15:16:02
 */
@EqualsAndHashCode
@ToString
public class MatchRule {
    private MatchRule subRule;
    private String key;
    private boolean isMathAll;

    public MatchRule(String rule) {
        if (StringUtils.isBlank(rule) || Objects.equals(rule, "*")) {
            isMathAll = true;
            return;
        }

        key = StringUtils.substringBefore(rule, ".");
        subRule = new MatchRule(StringUtils.substringAfter(rule, "."));
    }

    boolean matches(String[] keys, int index) {
        if (isMathAll) {
            return true;
        }

        if (keys.length <= index) {
            return false;
        }

        return keyMath(keys[index]) && subRule.matches(keys, ++index);
    }

    boolean keyMath(String value) {
        return Objects.equals(key, "*") || Objects.equals(key, value);
    }
}
