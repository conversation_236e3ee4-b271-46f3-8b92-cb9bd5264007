package com.fxiaoke.open.erpsyncdata.support

import com.alibaba.fastjson.JSON
import org.apache.http.HttpEntity
import org.apache.http.HttpResponse
import org.apache.http.client.config.RequestConfig
import org.apache.http.client.methods.CloseableHttpResponse
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.BasicHttpEntity
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils

/**
 *
 * <AUTHOR> (^_−)☆
 * @date 2020/9/25
 */
abstract class BaseHttpTest {

    private static CloseableHttpClient httpclient = HttpClients.createDefault()

    static String post(String url, Map header, Object body) {
        String strBody = JSON.toJSONString(body)
        println("request body:" + body)
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(10000).setConnectionRequestTimeout(10000)
                .setSocketTimeout(10000).build();
        httpPost.setConfig(requestConfig);
        httpPost.setEntity(buildEntity(strBody))
        if (header != null) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        httpPost.setHeader("Content-Type", "application/json");
        CloseableHttpResponse response = httpclient.execute(httpPost)
        String entity = EntityUtils.toString(response.getEntity(), "UTF-8")
        println("response entity:" + entity)
        return entity
    }


    private static HttpEntity buildEntity(String body) {
        BasicHttpEntity entity = new BasicHttpEntity();
        entity.setContentEncoding("UTF-8")
        byte[] bytes = body.getBytes("UTF-8")
        entity.setContentLength(bytes.length)
        entity.setContent(new ByteArrayInputStream(bytes))
        return entity;
    }
}
