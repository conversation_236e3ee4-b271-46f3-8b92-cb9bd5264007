{"type": "page", "title": "企业列表", "remark": null, "name": "tenantList", "initApi": "../config/initPageData?page=tenantList", "body": [{"type": "tpl", "tpl": "${reminder}", "className": "text-danger font-bold"}, {"type": "crud", "name": "tenants", "api": "../tenantInfoQuery", "loadDataOnce": false, "autoFillHeight": true, "tableLayout": "fixed", "defaultParams": {"perPage": 50}, "primaryField": "tenantId", "headerToolbar": [{"type": "export-excel", "label": "全量导出", "api": "../tenantInfoQuery?all=true"}, {"type": "button", "label": "收集最新数据", "actionType": "ajax", "api": "get:../invalidCache?name=AllAdminTenantInfoList.", "reload": "tenants"}, "reload", "bulkActions"], "bulkActions": [{"label": "修改环境", "actionType": "dialog", "dialog": {"title": "批量修改环境", "body": {"type": "form", "api": "../config/batchChangeTenantInfo", "body": [{"type": "input-text", "name": "ids", "label": "tenantId列表(逗号分隔)"}, {"type": "select", "name": "env", "options": ["VIP", "JACOCO", "GRAY", "NORMAL", "HAOLIYOU"], "label": "环境", "desc": "112环境，NORMAL企业路由到fstest，GRAY企业路由到fstest-gray"}]}}}, {"type": "button", "label": "快捷跳转", "actionType": "dialog", "level": "light", "dialog": {"type": "dialog", "title": "跳转到", "actions": [], "body": [{"type": "tpl", "tpl": "单企业查询"}, {"type": "button-toolbar", "buttons": [{"type": "button", "label": "集成流配置", "actionType": "link", "link": "./streamList?perPage=100&page=1&tenantId=${ids}"}, {"type": "button", "label": "对象列表", "actionType": "link", "link": "./objList?perPage=100&page=1&tenantId=${ids}"}, {"type": "button", "label": "待定", "actionType": "toast", "toast": {"items": [{"body": "开发中，敬请期待"}]}}]}, {"type": "tpl", "tpl": "多企业查询"}, {"type": "button-toolbar", "buttons": [{"type": "button", "label": "企业灰度操作日志", "actionType": "link", "link": "./operationList?perPage=100&page=1&ids=${ids}"}, {"type": "button", "label": "待定", "actionType": "toast", "toast": {"items": [{"body": "开发中，敬请期待"}]}}]}, {"type": "tpl", "tpl": "多企业操作"}, {"type": "button-toolbar", "buttons": [{"type": "button", "label": "重置全链路日志过期时间", "actionType": "link", "link": "./resetSyncLogExpireTime?tenantIds=${ids}"}, {"type": "button", "label": "按时间补刷数据", "actionType": "link", "link": "./processChangeDataByTime?tenantIds=${ids}"}]}]}}], "filter": {"mode": "horizontal", "body": [{"label": "关键字", "type": "input-text", "name": "keywords", "placeholder": "搜索企业ei，ea，名称，逗号分隔多个，ea和名称模糊搜索"}]}, "affixHeader": true, "columnsTogglable": "auto", "placeholder": "暂无数据", "combineNum": 0}]}