package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.common.constant.SyncPloyDetailStatusEnum;
import com.fxiaoke.open.erpsyncdata.common.constant.TenantTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel
public interface FindObjFuncUsageDetail {

    @Data
    class Arg implements Serializable {
        /**
         * 租户Id
         */
        private List<String> tenantIds;

        /**
         * 查询的对象ApiName
         */
        private String objApiName;

        /**
         * @see TenantTypeEnum#getType
         */
        private Integer type;

        /**
         * 是否为从对象
         */
        private boolean detail;

        /**
         * 状态
         * @see SyncPloyDetailStatusEnum#getStatus
         */
        private Integer status;

        private Integer page;
    }



}
