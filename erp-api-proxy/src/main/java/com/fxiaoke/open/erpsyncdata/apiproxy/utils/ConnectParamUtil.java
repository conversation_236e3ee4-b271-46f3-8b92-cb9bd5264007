package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.model.K3UltimateConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.SapConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ZhiHuConnectParam;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ConnectInfoResult;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/9/2
 */
public class ConnectParamUtil {

    public static SapConnectParam parseSap(String paramJson)  {
        return ErpChannelEnum.ERP_SAP.getConnectParam(paramJson);
    }
    public static ZhiHuConnectParam parseZhiHu(String tenantId,String paramJson) {
        return ErpChannelEnum.YXT_MARKETING_ZHIHU.getAndCheckConnectParam(tenantId, paramJson);
    }

    public static K3UltimateConnectParam parseK3Ultimate(String paramJson)  {
        return ErpChannelEnum.ERP_K3CLOUD_ULTIMATE.getConnectParam(paramJson);
    }

    public static ConnectInfoResult.ConnectParams json2ConnectParam(ErpConnectInfoEntity connectInfoEntity) {
        return connectInfoEntity.getChannel().getNewConnectParam(connectInfoEntity.getConnectParams());
    }

    public static String getHeaderFunctionApiName(ErpConnectInfoEntity connectInfoEntity) {
        ConnectInfoResult.ConnectParams params = new ConnectInfoResult.ConnectParams();
        final ErpChannelEnum channel = connectInfoEntity.getChannel();
        if (Objects.isNull(channel)) {
            throw new IllegalStateException("channel not support: " + channel);
        }

        String json = connectInfoEntity.getConnectParams();
        return channel.getHeaderFunctionName(json);
    }

    public static List<String> getPushDataObjApiNameList(ErpConnectInfoEntity connectInfo) {
        return connectInfo.getChannel().getPushDataApiNames(connectInfo.getConnectParams());
    }
}
