package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:
 */
@Data
@ApiModel
public class QueryMessageRetryIdArg implements Serializable {
    @ApiModelProperty("企业id")
    private String tenantId;
    @ApiModelProperty("数据中心id")
    private String dataCenterId;

    @ApiModelProperty("数据id")
    private String dataId;

    @ApiModelProperty("接受人")
    private String receiverId;

    @ApiModelProperty("事件类型")
    private  String eventType;

}
