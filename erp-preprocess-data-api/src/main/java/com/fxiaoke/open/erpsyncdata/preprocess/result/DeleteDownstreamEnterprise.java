package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4 16:20:19
 */
public interface DeleteDownstreamEnterprise {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel
    class Arg implements Serializable {
        @ApiModelProperty("分组id")
        private String id;
        @ApiModelProperty("删除的下游企业Id")
        private List<String> downstreamIds;
        @ApiModelProperty("是否全部删除")
        private Boolean all;
    }

    @Data
    class Result implements Serializable {
    }
}
