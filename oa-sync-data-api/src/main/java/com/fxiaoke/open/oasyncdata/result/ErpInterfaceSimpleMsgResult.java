package com.fxiaoke.open.oasyncdata.result;


import com.fxiaoke.open.oasyncdata.constant.ErpObjInterfaceUrlEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 16:35 2021/8/11
 * @Desc:
 */
@Data
@ApiModel
public class ErpInterfaceSimpleMsgResult implements Serializable {
    @ApiModelProperty("对象名称")
    public String objName;
    @ApiModelProperty("对象编码")
    public String objApiName;
    @ApiModelProperty("接口类型")
    public ErpObjInterfaceUrlEnum interfaceType;
    @ApiModelProperty("最近请求时间")
    public Long lastRequestTime;
    @ApiModelProperty("最新状态描述")
    public String lastStatus;
    @ApiModelProperty("最新备注")
    public String lastRemark;
}
