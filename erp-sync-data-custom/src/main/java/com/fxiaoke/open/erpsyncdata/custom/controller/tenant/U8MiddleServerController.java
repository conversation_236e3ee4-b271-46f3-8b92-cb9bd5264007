package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.apiproxy.manager.ProxyHttpClient;
import com.fxiaoke.open.erpsyncdata.apiproxy.utils.HttpRspLimitLenUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;


/**
 *
 *用友融联银企联云服务,
 * 对接过程需要引入几个客户的jar包计算签名。
 */

@Slf4j
@Api(tags = "U8客开处理相关接口")
@RestController("U8MiddleServerController")
@RequestMapping(path = {"inner/erp/syncdata/customfunction/u8"})
public class U8MiddleServerController {
    @Autowired
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("dss.u8.middle.url")
    private String u8MiddleServerUrl;

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @ApiOperation(value = "手动拉取时间段内的数据")
    @RequestMapping(value = "/syncObjByManual",method = RequestMethod.POST)
    public Result<String> syncObjByManual(@RequestHeader(value="x-fs-ei") Integer tenantId,
                                    HttpServletRequest request,@RequestBody Map<String, String> bodyMap) {
        if (bodyMap==null||bodyMap.isEmpty()){
            return Result.newError("fail", "request params not allow empty");
        }
        String objApiName=bodyMap.get("objApiName");
        String startTime=bodyMap.get("startTime");
        String endTime=bodyMap.get("endTime");
        String dsSequence=bodyMap.get("dsSequence");
        if (objApiName==null||startTime==null||dsSequence==null){
            return Result.newError("fail", "request params invalid!");
        }
        if (endTime==null){
            endTime=System.currentTimeMillis()+"";
            bodyMap.put("endTime",endTime);
        }
        if (Long.valueOf(endTime)-Long.valueOf(startTime)>1000*60*60*24*31){
            return Result.newError("fail", I18NStringEnum.s3657);
        }
        bodyMap.put("tenantId",tenantId+"");
        bodyMap.put("dsSequence",dsSequence);
        HttpRspLimitLenUtil.ResponseBodyModel rs = proxyHttpClient.postUrl(
                u8MiddleServerUrl + "/yongyou/sync/obj/syncObjByManual", bodyMap, Collections.emptyMap(),(Long)null);
        log.info("执行结果：{}",rs);
        return Result.newSuccess("success");

    }

}
