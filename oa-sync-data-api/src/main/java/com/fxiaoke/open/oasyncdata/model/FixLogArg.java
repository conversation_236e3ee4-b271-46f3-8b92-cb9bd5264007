package com.fxiaoke.open.oasyncdata.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2021/3/18
 * @Desc:
 */
@Data
@ApiModel
public class FixLogArg implements Serializable {
    @ApiModelProperty("对象名")
    private Long startTime;

    @ApiModelProperty("同步状态")
    private Long  endTime;

    @ApiModelProperty("同步状态")
    private String  tenantId;
}
