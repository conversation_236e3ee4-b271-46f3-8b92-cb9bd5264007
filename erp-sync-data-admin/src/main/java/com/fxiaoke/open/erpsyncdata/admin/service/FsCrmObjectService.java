package com.fxiaoke.open.erpsyncdata.admin.service;

import com.fxiaoke.open.erpsyncdata.admin.result.ListObjectFieldsResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;

import java.util.List;
import java.util.Map;

public interface FsCrmObjectService {
    Result<ListObjectFieldsResult> listObjectFieldsWithFilterBlackList(String tenantId, String objectApiName,String lang);
    Result<Map<String,ListObjectFieldsResult>> listObjectsFieldsWithFilterBlackList(String tenantId, List<String> objectApiNames, String lang);
}
