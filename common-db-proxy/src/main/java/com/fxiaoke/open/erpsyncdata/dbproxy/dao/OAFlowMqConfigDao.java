package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.OAFlowMqConfigEntity;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OAFlowMqConfigDao extends ErpBaseDao<OAFlowMqConfigEntity>, ITenant<OAFlowMqConfigDao> {

    /**
     * 获取配置信息
     *
     * @param tenantId
     * @return
     */
    @Cached(expire = 30,cacheType = CacheType.LOCAL)
    List<OAFlowMqConfigEntity> queryListByType(@Param("tenantId")String tenantId, @Param("eventType") String eventType, @Param("objApiName")String objApiName);
}
