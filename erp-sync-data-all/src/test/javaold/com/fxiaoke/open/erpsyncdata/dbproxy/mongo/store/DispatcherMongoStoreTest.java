package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.store;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import org.assertj.core.util.Sets;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date: 18:36 2021/12/14
 * @Desc:
 */
@Ignore
public class DispatcherMongoStoreTest extends BaseTest {

    @Autowired
    private DispatcherMongoStore dispatcherMongoStore;

    @Test
    public void getStoreCollectionSize() {
        Long buf_81243 = dispatcherMongoStore.getStoreCollectionSize("81243");
        System.out.println("");
    }


    @Test
    public void getStoreCollectionSizeLimit() {
        Long buf_81243 = dispatcherMongoStore.getStoreCollectionCountLimit("buf_81243");
        System.out.println("");
    }

    @Test
    public void getTenantBufCollectionCount() {
        Long allCollectionCount = dispatcherMongoStore.getStoreCollectionCount("81243");
        System.out.println(allCollectionCount);
    }
}