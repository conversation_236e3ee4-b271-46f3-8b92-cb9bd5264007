package com.fxiaoke.open.erpsyncdata.web.service.customFunction;

import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.open.erpsyncdata.admin.arg.CustomFunctionCommonArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.QueryFieldDataMappingArg;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpFieldDataMappingDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("queryFieldMapping")
public class QueryFieldMappingServiceImpl implements CustomFunctionCommonService {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @Override
    public Result<String> executeLogic(CustomFunctionCommonArg commonArg) {
        String tenantId=commonArg.getTenantId();
        QueryFieldDataMappingArg arg = JsonUtil.fromJson(commonArg.getParams(), QueryFieldDataMappingArg.class);
        if (arg == null || arg.getDataType()==null ||arg.getDataCenterId()==null|| (CollectionUtils.isEmpty(arg.getFsDataId()) && CollectionUtils.isEmpty(arg.getErpDataId()))) {
            log.info("executeLogic params error commonArg={} arg={}", commonArg, arg);
            return Result.newError(ResultCodeEnum.PARAM_ERROR);
        }
        List<ErpFieldDataMappingEntity> dataList=null;
        if(CollectionUtils.isNotEmpty(arg.getFsDataId())){//fs数据ids
            dataList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByFsIds(tenantId, arg.getDataCenterId(), arg.getDataType(), arg.getFsDataId());
        }else if(CollectionUtils.isNotEmpty(arg.getErpDataId())){//erp数据ids
            dataList = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex(tenantId)).listByErpIds(tenantId, arg.getDataCenterId(), arg.getDataType(), arg.getErpDataId());
        }
        if(CollectionUtils.isNotEmpty(dataList)){
            return Result.newSuccess(JsonUtil.toJson(dataList));
        }
        return Result.newSuccess();
    }

}
