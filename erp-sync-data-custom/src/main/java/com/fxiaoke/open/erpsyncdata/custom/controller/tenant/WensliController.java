package com.fxiaoke.open.erpsyncdata.custom.controller.tenant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.admin.model.excel.CategoryFieldDataMappingExcelVo;
import com.fxiaoke.open.erpsyncdata.admin.service.SpecialSyncService;
import com.fxiaoke.open.erpsyncdata.custom.utils.tenant.Wensli.WensliUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ErpProductCategory;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.asm.$AnnotationVisitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用于【万事利】自定义函数调用，实现产品分类对象化对接，token获取
 */
@Slf4j
@Api(tags = "万事利相关接口")
@RestController("WensliController")
// 80771：112纷享专用 zsl测试企业020       729731：万事利沙盒       723736：万事利正式
@RequestMapping(path = { "inner/erp/syncdata/customfunction/729731", "inner/erp/syncdata/customfunction/723736"})
public class WensliController {
    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private WensliUtil wensliUtil;

    @ApiOperation(value = "通用方法")
    @RequestMapping(value = "/dowork",method = RequestMethod.POST)
    public Result<String> dowork(@RequestHeader(value="x-fs-ei") String tenantId,
                                 @RequestHeader Map<String, String> headerMap,
                                 @RequestBody Map<String, String> bodyMap) {

        log.info("trace WensliController dowork tenantId:{}, headerMap:{}, bodymap: {} ", tenantId, headerMap, bodyMap);

        String param = bodyMap.get("param");

        if(StringUtil.isEmpty(param)){
            return Result.newError("fail", I18NStringEnum.s3659 + param);
        }

        try{
            Map<String,Object> params = JacksonUtil.fromJson(param,Map.class);

            String method = (String)params.get("method");
            /**
             *userToken：获取与人员相关tokentoken
             *messageToken：获取与消息相关tokentoken
             *category：存储产品分类，可更新，不支持删除
             */
            switch (method){
                case "userToken": return wensliUtil.getToken(tenantId,WensliUtil.TokenType.user);
                case "messageToken":return wensliUtil.getToken(tenantId,WensliUtil.TokenType.message);
                case "category": return wensliUtil.saveCategory(param,tenantId);
                default: return Result.newSuccess(i18NStringManager.getByEi2(I18NStringEnum.s3710, tenantId));
            }

        }catch (Exception e){
            log.info("WensliController exception: ", e);

            return Result.newError("fail", e.getMessage());
        }
    }
}
