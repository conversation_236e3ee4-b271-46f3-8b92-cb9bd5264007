package com.fxiaoke.open.erpsyncdata.admin.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

public interface GetOauthLoginDialogUrl {

    @Data
    @ApiModel
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        /**
         *  暂时只有facebook有值,1-普通登录,2-企业登录
         */
        private Integer type;
    }

    @Data
    @ApiModel
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {
        @ApiModelProperty(value = "跳转链接")
        private String url;
    }
}
