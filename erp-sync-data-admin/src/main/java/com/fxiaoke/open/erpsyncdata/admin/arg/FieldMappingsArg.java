package com.fxiaoke.open.erpsyncdata.admin.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;



@ApiModel
@Data
public class FieldMappingsArg implements Serializable {

    @ApiModelProperty("数据中心id")
    private String dataCenterId;
    @ApiModelProperty("基础数据类型")
    private ErpFieldTypeEnum dataType;
    @ApiModelProperty("fs数据ids")
    private String fsDataId;
    @ApiModelProperty("erp数据ids")
    private String erpDataId;
}
