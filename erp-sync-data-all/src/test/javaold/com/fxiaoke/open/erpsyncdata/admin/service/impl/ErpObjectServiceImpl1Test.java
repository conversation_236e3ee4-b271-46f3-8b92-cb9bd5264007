package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.service.ErpObjectService;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.DeleteErpObjectArg;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.PageArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjSplitTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpObjectTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectDescResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpObjectRelationshipResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 19:19 2020/8/20
 * @Desc:
 */
@Ignore
@Data
@Slf4j
public class ErpObjectServiceImpl1Test extends BaseTest {
    @Autowired
    private ErpObjectService erpObjectService;

    @Test
    public void updateErpObject() {
        ErpObjectDescResult erpObjectDescResult=new ErpObjectDescResult();
        erpObjectDescResult.setErpObjectApiName("SaleOrderObj");
        erpObjectDescResult.setErpObjectName("销售订单");
        erpObjectDescResult.setErpObjectType(ErpObjectTypeEnum.REAL_OBJECT);
        erpObjectDescResult.setSplitType(ErpObjSplitTypeEnum.NOT_SPLIT);
        erpObjectDescResult.setChannel(ErpChannelEnum.ERP_SAP);
        erpObjectDescResult.setDeleteStatus(false);
        erpObjectDescResult.setErpObjectExtendValue("123");
        erpObjectDescResult.setId("596948744751677440");
        Result<String> listResult = erpObjectService.updateErpObject("123","", 1001, erpObjectDescResult);
        log.info("result={}",listResult);
    }

    @Test
    public void deleteErpObject() {
        DeleteErpObjectArg deleteArg=new DeleteErpObjectArg();
        deleteArg.setId("596948744751677440");
        Result<String> listResult = erpObjectService.deleteErpObject("123", 1001, deleteArg);
        log.info("result={}",listResult);
    }
    @Test
    public void queryRealErpObject() {
        PageArg pageArg=new PageArg();
        pageArg.setQueryStr("");
        Result<List<ErpObjectRelationshipResult>> listResult = erpObjectService.queryRealErpObjectByTenantIdAndDcId("85228", 1000, null,"a9ef25bd8e4e4899a0680b9ad9787bcb");
        System.out.println("");
    }

    @Test
    public void queryErpObjectByObjApiName() {
        Result<ErpObjectDescResult> listResult = erpObjectService.queryErpObjectByObjApiName("88466",
                "6436278b3dcc6b0001e76652",
                1000,
                "SAL_SaleOrder.SaleOrderEntry");
        System.out.println(listResult);
    }
}