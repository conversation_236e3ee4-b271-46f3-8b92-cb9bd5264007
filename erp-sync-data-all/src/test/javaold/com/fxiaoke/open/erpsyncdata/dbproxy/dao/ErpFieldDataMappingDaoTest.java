package com.fxiaoke.open.erpsyncdata.dbproxy.dao;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpFieldDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.DataBaseBatchIndexUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpFieldTypeEnum;
import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 15:50 2020/10/10
 * @Desc:
 */
@Ignore
public class ErpFieldDataMappingDaoTest extends BaseTest {
    @Autowired
    private ErpFieldDataMappingDao erpFieldDataMappingDao;

    @Test
    public void countByTenantIdAndDataTypeAndQueryStr() {
        int count = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675"))
                .countByTenantIdAndDataTypeAndQueryStr("84801","780777150699143168", ErpFieldTypeEnum.employee,null);
        System.out.println(count);
    }

    @Test
    public void listByTenantIdAndDataType() {
        List<ErpFieldDataMappingEntity> erpFieldDataMappingEntities = erpFieldDataMappingDao.setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("79675")).listByTenantIdAndDataType("79675", ErpFieldTypeEnum.employee, 2, 0,null);
        System.out.println(erpFieldDataMappingEntities);
    }

    @Test
    public void listByIdList() {
        List<ErpFieldDataMappingEntity> mappingEntityList = erpFieldDataMappingDao
                .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .listByIdList("84801","780777150699143168", null);

        mappingEntityList = erpFieldDataMappingDao
                .setTenantId(DataBaseBatchIndexUtil.getDataBaseBatchIndex("84801"))
                .listByIdList("84801","780777150699143168",
                        Lists.newArrayList("796786756082106368","63b689fa8c24cf000188af69"));
        System.out.println(mappingEntityList);
    }
}