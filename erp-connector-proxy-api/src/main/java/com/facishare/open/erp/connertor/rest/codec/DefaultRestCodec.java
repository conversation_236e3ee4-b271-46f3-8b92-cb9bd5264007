package com.facishare.open.erp.connertor.rest.codec;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @date 2023/3/21 19:51:05
 */
@Slf4j
public enum DefaultRestCodec implements RestCodeC {

    fastJson;

    @Override
    public <T> byte[] encodeArg(T obj) {

        if (Objects.isNull(obj)) {
            return null;
        }

        if (obj instanceof String) {
            return ((String) obj).getBytes(UTF_8);
        }
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue).getBytes(UTF_8);
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        String bodyString = new String(bytes, UTF_8);
        if (statusCode >= HttpStatus.MULTIPLE_CHOICES.value()) {
            throw new RuntimeException("返回值状态码为:" + statusCode + "  " + bodyString);
        }
        if (clazz == String.class) {
            return (T) bodyString;
        }
        if (clazz == void.class) {
            return null;
        }
        return JSON.parseObject(new String(bytes, UTF_8), clazz);
    }


}
