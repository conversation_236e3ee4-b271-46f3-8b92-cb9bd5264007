<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:beans="http://www.springframework.org/schema/beans"
       xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context-3.2.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
	                       http://www.springframework.org/schema/mvc
	                       http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">


    <context:component-scan base-package="com.fxiaoke.open.erpsyncdata.*">
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.ControllerAdvice"/>
    </context:component-scan>

    <aop:aspectj-autoproxy/>

    <mvc:default-servlet-handler/>

    <mvc:annotation-driven>
        <mvc:message-converters register-defaults="true">
            <!-- 将StringHttpMessageConverter的默认编码设为UTF-8 -->
            <beans:bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <beans:constructor-arg value="UTF-8"/>
            </beans:bean>
            <beans:bean class="org.springframework.http.converter.json.GsonHttpMessageConverter">
                <beans:property name="supportedMediaTypes">
                    <beans:list>
                        <beans:value>application/json</beans:value>
                    </beans:list>
                </beans:property>
            </beans:bean>
        </mvc:message-converters>
        <!--异步请求超时配置-->
        <mvc:async-support default-timeout="15000" task-executor="taskExecutor"/>
    </mvc:annotation-driven>
    <!-- 拦截器配置 -->
<!--    <mvc:interceptors>-->
<!--        <mvc:interceptor>-->
<!--            <mvc:mapping path="/**"/>-->
<!--            <mvc:exclude-mapping path="/**/anonymousQuery" />-->
<!--            <mvc:exclude-mapping path="/api/v1/**" />-->
<!--            <mvc:exclude-mapping path="/monitor/**" />-->
<!--            <bean class="com.fxiaoke.open.erpsyncdata.admin.interceptor.UserInterceptors"/>-->
<!--        </mvc:interceptor>-->
<!--    </mvc:interceptors>-->

    <beans:import resource="classpath*:spring/all-applicationContext.xml" />
</beans>
