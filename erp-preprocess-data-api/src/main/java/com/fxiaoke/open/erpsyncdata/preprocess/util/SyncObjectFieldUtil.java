package com.fxiaoke.open.erpsyncdata.preprocess.util;

import com.fxiaoke.open.erpsyncdata.common.constant.FieldType;

public class SyncObjectFieldUtil {
    public static boolean isMasterDetailFieldType(String type) {
        if (FieldType.getMasterDetailTypeList().contains(type)) {
            return true;
        }
        return false;
    }

    public static boolean isObjectReferenceFieldType(String type) {
        if (FieldType.getObjectReferenceFieldTypeList().contains(type)) {
            return true;
        }
        return false;
    }
    public static boolean isObjectsReferenceFieldType(String type) {
        if (FieldType.getObjectsReferenceFieldTypeList().contains(type)) {
            return true;
        }
        return false;
    }

    public static boolean isFileFieldType(String type) {
        if (FieldType.getFileFieldTypeList().contains(type)) {
            return true;
        }
        return false;
    }
}
