package com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao

import com.fxiaoke.open.erpsyncdata.BaseSpockTest
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.SyncDataFailedEntity
import org.junit.Ignore
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired

@Ignore
class SyncDataFailedDaoTest extends BaseSpockTest {
    @Autowired
    private SyncDataFailedDao syncFailedDataDao
    def tenantId = "81243"
    def dcId = "dcid100"
    def ployDetailId = "ployDetailId100"
    def sourceDataId = "100"

    @Test
    void replace() {


        for(int i=0;i<100;i++) {
            syncFailedDataDao.replace(tenantId,
                    dcId,
                    ployDetailId,
                    sourceDataId + i)
        }

        def data = syncFailedDataDao.getData(tenantId, dcId, ployDetailId, sourceDataId)
        println(data)

        def delete = syncFailedDataDao.delete(tenantId, dcId, ployDetailId, "100")
        delete = syncFailedDataDao.delete(tenantId, dcId, ployDetailId, "101")

        def dataList2 = syncFailedDataDao.getDataList(tenantId, dcId, ployDetailId, 100)
        println(dataList2)
    }

    @Test
    void getTenantDataList() {
        def tenantDataList = syncFailedDataDao.getTenantDcPloyDetailDataMap()
        println(tenantDataList)

        tenantDataList.each {
            def count = syncFailedDataDao.deleteMany(it.value.tenantId)
            println(count)
        }
    }

    @Test
    void insertMany() {
        List<SyncDataFailedEntity> dataList = new ArrayList<>()
        for(int i=0;i<10;i++) {
            SyncDataFailedEntity entity = new SyncDataFailedEntity();
            entity.setTenantId(tenantId)
            entity.setDataCenterId(dcId)
            entity.setPloyDetailId(ployDetailId)
            entity.setSourceDataId((100+i).toString())
            entity.setCreateTime(new Date())
            entity.setUpdateTime(new Date())
            dataList.add(entity)
        }
        def count = syncFailedDataDao.insertMany(dataList)
        println(count)
    }
}
