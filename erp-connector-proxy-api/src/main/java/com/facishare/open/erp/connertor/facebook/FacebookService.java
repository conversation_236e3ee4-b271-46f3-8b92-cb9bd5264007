package com.facishare.open.erp.connertor.facebook;

import com.facishare.open.erp.connertor.codec.AesCodec;
import com.facishare.open.erp.connertor.rest.annotation.RestResource;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetAllForm;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetAllPage;
import com.facishare.open.erp.connertor.sdk.model.facebook.GetFormMetaData;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @date 2023/3/20 19:22:18
 */
@RestResource(value = "ErpConnectorProxy", desc = "调用国外的erp", codec = AesCodec.class)
public interface FacebookService {

    @RequestMapping("/facebook/getAllPage")
    GetAllPage.Result getAllPage(@RequestBody GetAllPage.Arg arg);

    @RequestMapping("/facebook/getAllForm")
    GetAllForm.Result getAllForm(@RequestBody GetAllForm.Arg arg);

    @RequestMapping("/facebook/getFormMetaData")
    GetFormMetaData.Result getFormMetaData(@RequestBody GetFormMetaData.Arg arg);
}