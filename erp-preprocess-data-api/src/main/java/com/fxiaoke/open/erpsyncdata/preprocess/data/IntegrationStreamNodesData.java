package com.fxiaoke.open.erpsyncdata.preprocess.data;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class IntegrationStreamNodesData implements Serializable{

    private ReverseWriteNode reverseWriteNode;
    /**
     * 检查中间表节点
     */
    private CheckSyncDataMappingNode checkSyncDataMappingNode;
    /**
     * 通过源数据查询crm节点
     */
    private QueryCrmObject2DestNode queryCrmObject2DestNodeBySource;
    /**
     * 通过转换后数据查询crm节点
     */
    private QueryCrmObject2DestNode queryCrmObject2DestNodeByDest;
    /**
     * 数据范围-查询crm
     */
    private SyncConditionsQueryDataNode syncConditionsQueryDataNode;
    /**
     * 不更新字段ApiName,
     */
    private Map<String,List<String>> objApiName2NotUpdateFieldApiName;
    /**
     * 需要返回字段ApiName，
     */
    private Map<String,List<String>> objApiName2NeedReturnFieldApiName;
    /**
     * 错误数据重试
     */
    private ReSyncErrorDataNode reSyncErrorDataNode;

    @Data
    public static class SyncConditionsQueryDataNode implements Serializable{
        private List<QueryObjectMappingData> queryObjectMappingData; //查询crm
        //不支持明细
        //同步条件
        private Integer syncCondition; //同步条件，1：查到同步，2：查不到同步
    }

    @Data
    public static class QueryCrmObject2DestNode implements Serializable{
        private List<QueryObjectToDestObjectData> queryObjectToDestObject;
        private DetailQueryObjectMappingsData detailQueryData2DestDataMapping;//明细
    }
    @Data
    public static class CheckSyncDataMappingNode implements Serializable{
        private QueryObjectMappingData queryObjectMappingData; //查询crm
        private DetailObjectIdFieldMappingsData source2SyncDataMapping;//集成流源对象字段映射到中间表、查出来的目标对象字段映射到中间表
        private DetailCheckSyncDataMappingsData detailCheckSyncDataMappingData;//明细
    }


    @Data
    public static class ReverseWriteNode implements Serializable{
        private FieldMappingsData fieldMappings;
        private DetailObjectMappingsData detailObjectMappings = new DetailObjectMappingsData();
    }
    @Data
    public static class ReSyncErrorDataNode implements Serializable{
        private String reSyncCondition;//未使用
        private Integer reSyncTimeInterval;//单位固定分钟
        private Boolean reSyncRightNow;
        private Integer reSyncTopLimit;

    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"reverseWriteNode\":")
                .append(reverseWriteNode);
        sb.append("\"checkSyncDataMappingNode\":")
                .append(checkSyncDataMappingNode);
        sb.append("\"queryCrmObject2DestNodeBySource\":")
                .append(queryCrmObject2DestNodeBySource);
        sb.append("\"queryCrmObject2DestNodeByDest\":")
                .append(queryCrmObject2DestNodeByDest);
        sb.append("\"syncConditionsQueryDataNode\":")
                .append(syncConditionsQueryDataNode);
        sb.append("\"objApiName2NotUpdateFieldApiName\":")
                .append(objApiName2NotUpdateFieldApiName);
        sb.append("\"objApiName2NeedReturnFieldApiName\":")
                .append(objApiName2NeedReturnFieldApiName);
        sb.append("\"reSyncErrorDataNode\":")
                .append(reSyncErrorDataNode);
        sb.append('}');
        return sb.toString();
    }
}
