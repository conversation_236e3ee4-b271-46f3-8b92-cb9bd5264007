package com.fxiaoke.open.erpsyncdata.preprocess.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/5/22 15:31:26
 * <p>
 * 注意:这里的多语是审计日志使用,标签需要选择server
 * 需要在配置中心中配置对应的i18nKey,并在多语平台中配置对应的多语
 * 配置文件: fs-paas-auditlog-config
 */
public interface AuditLogI18nConstant {

    /**
     * 集成平台，对应审计日志->操作模块->集成平台
     * 该字段是预定义字段，不允许修改
     * 配置项: log_module_group
     */
    String MODULE = "erpdss";

    /**
     * 审计日志->子模块
     * 配置项: log_operation_object
     */
    enum SubModuleEnum {
        ERPDSS_INTEGRATION_STREAM("erpdss_integration_stream", "数据集成流"),   // ignoreI18n  审计日志多语
        SYSTEM_FIELD_MANAGEMENT("systemFieldManagement", "系统字段管理"),   // ignoreI18n  审计日志多语
        ERPDSS_CONNECTION("erpdss_connection", "连接器"),   // ignoreI18n  审计日志多语
        ;
        @Getter
        private String i18nKey;
        private String i18nValue;

        SubModuleEnum(String i18nKey, String i18nValue) {
            this.i18nKey = i18nKey;
            this.i18nValue = i18nValue;
        }
    }

    /**
     * 对应审计日志->操作行为
     * 配置项: log_action_type
     */
    enum BizOperationEnum {
        // 操作行为
        DELETE_SYSTEM_FIELD("deleteSystemField", "删除系统字段"),   // ignoreI18n  审计日志多语
        EXPORT_SYSTEM_FIELD("exportSystemField", "导出系统字段"),   // ignoreI18n  审计日志多语
        ERPDSS_DELETE_MAPPING("erpdss_delete_mapping", "删除中间表映射"),   // ignoreI18n  审计日志多语
        ERPDSS_DELETE_CONNECTION("erpdss_delete_connection", "删除连接器"),   // ignoreI18n  审计日志多语
        ;
        @Getter
        private String i18nKey;
        private String i18nValue;

        BizOperationEnum(String i18nKey, String i18nValue) {
            this.i18nKey = i18nKey;
            this.i18nValue = i18nValue;
        }
    }
}
