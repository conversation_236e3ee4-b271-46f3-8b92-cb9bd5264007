package com.fxiaoke.open.erpsyncdata.preprocess.data

import cn.hutool.core.util.IdUtil
import cn.hutool.core.util.RandomUtil
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2
import spock.lang.Specification

/**
 *
 * <AUTHOR> (^_−)☆
 */
class SyncDataContextEventTest extends Specification {
    def "deepCopyIgnoreBigDataTest"() {
        def event = new SyncDataContextEvent()
        event.setDataId("dataId666")
        event.setSourceData(r())
        event.setSyncDataData(r1())
        event.setDetailData(r2())
        event.setDetailSyncDataDataMap(r3())
        def newEvent = event.deepCopyIgnoreBigData()
        SyncDataContextEvent newEvent2 = BeanUtil2.deepCopy(event, SyncDataContextEvent)
        expect:
        event.getSourceData() != null
        newEvent.getSourceData() == null
        event.getDetailObjectDatasMap() != null
        newEvent.getDetailObjectDatasMap().isEmpty()
        event.getSourceData() == newEvent2.getSourceData()
    }

    private static ObjectData r() {
        def data = new ObjectData()
        //随机10到100个字段
        for (i in 0..<RandomUtil.randomInt(10, 100)) {
            def v
            switch (i % 6) {
                case 0: v = RandomUtil.randomInt()
                    break
                case 1: v = RandomUtil.randomBigDecimal()
                    break
                case 2: v = RandomUtil.randomLong()
                    break
                case 3: v = RandomUtil.randomString(RandomUtil.randomInt(10, 100))
                    break
                case 4: v = RandomUtil.randomBoolean()
                    break
                default: v = ""
            }
            data.put(IdUtil.nanoId(), v)
        }
        return data
    }


    private static SyncDataData r1() {
        def syncDataData = new SyncDataData()
        syncDataData.setSourceData(r())
        syncDataData.setDestData(r())
        return syncDataData
    }

    private static Map<String, List<ObjectData>> r2() {
        Map<String, List<ObjectData>> data = [:]
        for (i in 0..<RandomUtil.randomInt(5)) {
            def list = []
            for (j in 0..<RandomUtil.randomInt(1, 1000)) {
                list.add(r())
            }
            data.put(IdUtil.nanoId(), list)
        }
        return data
    }


    private static Map<String, SyncDataData> r3() {
        Map<String, SyncDataData> data = [:]
        for (i in 0..<RandomUtil.randomInt(200)) {
            data.put(IdUtil.nanoId(), r1())
        }
        return data
    }
}
