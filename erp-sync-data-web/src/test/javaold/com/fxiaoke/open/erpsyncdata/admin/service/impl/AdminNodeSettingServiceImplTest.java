package com.fxiaoke.open.erpsyncdata.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.admin.arg.IdArg;
import com.fxiaoke.open.erpsyncdata.admin.arg.UpdateIntegrationStreamArg;
import com.fxiaoke.open.erpsyncdata.admin.data.SyncConditionsData;
import com.fxiaoke.open.erpsyncdata.admin.result.IntegrationFieldVerifyResult;
import com.fxiaoke.open.erpsyncdata.admin.result.ObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.result.QueryIntegrationDetailResult;
import com.fxiaoke.open.erpsyncdata.admin.result.QueryObjectMappingResult;
import com.fxiaoke.open.erpsyncdata.admin.service.AdminNodeSettingService;
import com.fxiaoke.open.erpsyncdata.admin.service.IntegrationStreamService;
import com.fxiaoke.open.erpsyncdata.common.constant.SyncForceConstant;
import com.fxiaoke.open.erpsyncdata.common.rule.FilterData;
import com.fxiaoke.open.erpsyncdata.common.util.BeanUtil2;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.admin.AdminSyncPloyDetailDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.admin.SyncPloyDetailEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.DetailObjectMappingsData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.FieldMappingData;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.SyncPloyDetailData;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 14:36 2022/7/27
 * @Desc:
 */
@Ignore
public class AdminNodeSettingServiceImplTest extends BaseTest {
    @Autowired
    private AdminNodeSettingService adminNodeSettingService;
    @Autowired
    private AdminSyncPloyDetailDao adminSyncPloyDetailDao;
    @Autowired
    private IntegrationStreamService integrationStreamService;

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void updateSyncCondition() {
    }

    @Test
    public void updateFunction() {
    }

    @Test
    public void updateFieldMapping() {
    }

    @Test
    public void updatePollingRules() {
    }

    @Test
    public void updateIntegrationStreamNodes() {
        String str = "{\"checkSyncDataMappingNode\":{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SalesOrderObj\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FBillNo\"],\"operate\":\"EQ\"}]]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"sourceObjectApiName\":\"SalesOrderObj\",\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"}],\"detailCheckSyncDataMappingData\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FID\"],\"operate\":\"EQ\"}]]},\"source2SyncDataMapping\":[{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"destDataId\",\"sourceApiName\":\"_id\"},{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"destDataName\",\"sourceApiName\":\"name\"},{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"sourceDataId\",\"sourceApiName\":\"erp_id\"},{\"sourceObjectApiName\":\"SalesOrderProductObj\",\"destApiName\":\"sourceDataName\",\"sourceApiName\":\"name\"},{\"sourceObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"destApiName\":\"masterDataId\",\"sourceApiName\":\"master_id\"}]}]},\"queryCrmObject2DestNodeBySource\":{\"queryObjectToDestObject\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SAL_SaleOrder.BillHead\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FCustId.FNumber\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]}}],\"detailQueryData2DestDataMapping\":[[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SAL_SaleOrder.SaleOrderEntry\",\"queryFieldMappings\":[[{\"fieldApiName\":\"name\",\"fieldValue\":[\"FCustId.FNumber\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]}}]]},\"queryCrmObject2DestNodeByDest\":{\"queryObjectToDestObject\":[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"queryFieldMappings\":[[{\"fieldApiName\":\"_id\",\"fieldValue\":[\"account_id\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]}}],\"detailQueryData2DestDataMapping\":[[{\"queryObjectMappingData\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"queryFieldMappings\":[[{\"fieldApiName\":\"_id\",\"fieldValue\":[\"account_id\"],\"operate\":\"EQ\"}]]},\"queryData2DestDataMapping\":{\"sourceObjectApiName\":\"AccountObj\",\"destObjectApiName\":\"SalesOrderProductObj\",\"fieldMappings\":[{\"destApiName\":\"owner\",\"sourceApiName\":\"owner\"}]}}]]}}";
        JSONObject jsonObject = JSONObject.parseObject(str);
        String check = jsonObject.getJSONObject("checkSyncDataMappingNode").toJSONString();
        String source = jsonObject.getJSONObject("queryCrmObject2DestNodeBySource").toJSONString();
        String dest = jsonObject.getJSONObject("queryCrmObject2DestNodeByDest").toJSONString();
        UpdateIntegrationStreamArg arg = new UpdateIntegrationStreamArg();
        arg.setFieldMappingNode(new QueryIntegrationDetailResult.FieldMappingNode());
        arg.getFieldMappingNode().setFieldMappings(new ObjectMappingResult());
        arg.getFieldMappingNode().getFieldMappings().setDestObjectApiName("SalesOrderObj");
        arg.getFieldMappingNode().getFieldMappings().setNotUpdateFieldApiNameList(Lists.newArrayList("name"));
        QueryIntegrationDetailResult.CheckSyncDataMappingNode checkSyncDataMappingNode = JacksonUtil.fromJson(check, QueryIntegrationDetailResult.CheckSyncDataMappingNode.class);
        QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeBySource = JacksonUtil.fromJson(source, QueryIntegrationDetailResult.QueryCrmObject2DestNode.class);
        QueryIntegrationDetailResult.QueryCrmObject2DestNode queryCrmObject2DestNodeByDest = JacksonUtil.fromJson(dest, QueryIntegrationDetailResult.QueryCrmObject2DestNode.class);

        arg.setCheckSyncDataMappingNode(checkSyncDataMappingNode);
        arg.setQueryCrmObject2DestNodeBySource(queryCrmObject2DestNodeBySource);
        arg.setQueryCrmObject2DestNodeByDest(queryCrmObject2DestNodeByDest);
        arg.setId("936dbf22e1294752a80a4e272df9b916");
        //数据范围查询crm节点
        QueryIntegrationDetailResult.SyncConditionsQueryDataNode syncConditionsQueryDataNode = new QueryIntegrationDetailResult.SyncConditionsQueryDataNode();
        QueryObjectMappingResult queryObjectMappingResult = new QueryObjectMappingResult();
        queryObjectMappingResult.setSourceObjectApiName("AccountObj");
        queryObjectMappingResult.setDestObjectApiName("SalesOrderObj");
        FilterData filterData = new FilterData();
        filterData.setFieldApiName("account_no");
        filterData.setFieldValue(Lists.newArrayList("FCustId.FNumber"));
        FilterData filterData1 = new FilterData();
        filterData1.setFieldApiName("remark");
        filterData1.setFieldValue(Lists.newArrayList("FSaleOrgId.FNumber"));
        List<List<FilterData>> queryFieldMappings = Lists.newArrayList();
        queryFieldMappings.add(Lists.newArrayList(filterData, filterData1));
        queryObjectMappingResult.setQueryFieldMappings(queryFieldMappings);
        syncConditionsQueryDataNode.setQueryObjectMappingData(Lists.newArrayList(queryObjectMappingResult));
        syncConditionsQueryDataNode.setSyncCondition(1);
        arg.setSyncConditionsNode(new QueryIntegrationDetailResult.SyncConditionsNode());
        arg.getSyncConditionsNode().setSyncConditions(new SyncConditionsData("SAL_SaleOrder.BillHead", Lists.newArrayList(), SyncForceConstant.CLOSE,new QueryIntegrationDetailResult.SyncConditionsQueryDataNode()));
        arg.getSyncConditionsNode().getSyncConditions().setSyncConditionsQueryDataNode(syncConditionsQueryDataNode);
        Result<String> result = adminNodeSettingService.updateIntegrationStreamNodes("84801", arg);
        SyncPloyDetailEntity entity = adminSyncPloyDetailDao.setTenantId("-10001").getById("84801", "936dbf22e1294752a80a4e272df9b916");
        SyncPloyDetailData syncPloyDetailData = BeanUtil2.deepCopy(entity, SyncPloyDetailData.class);
        IdArg idarg = new IdArg();
        idarg.setId("936dbf22e1294752a80a4e272df9b916");
        Result<QueryIntegrationDetailResult> resultResult = integrationStreamService.getIntegrationStreamDetail("84801", idarg,null);
//        List<IntegrationFieldVerifyResult> integrationFieldVerifyResults = verifyReverseWrite("81243", entity);
//        QueryIntegrationDetailResult.ReverseWriteNode node=BeanUtil.copy(entity.getIntegrationStreamNodes().getReverseWriteNode(), QueryIntegrationDetailResult.ReverseWriteNode.class);
        System.out.println("");
    }

    private List<IntegrationFieldVerifyResult> verifyReverseWrite(String tenantId, SyncPloyDetailEntity entity) {
        List<IntegrationFieldVerifyResult> fieldResult = Lists.newArrayList();
        //主对象反写字段
        if (CollectionUtils.isNotEmpty(entity.getIntegrationStreamNodes().getReverseWriteNode().getFieldMappings())) {
            //主对象，已设置字段同步映射的源对象字段
            List<String> masterObjFieldApiNames = entity.getFieldMappings().stream().map(FieldMappingData::getSourceApiName).collect(Collectors.toList());
            for (FieldMappingData fieldMappingData : entity.getIntegrationStreamNodes().getReverseWriteNode().getFieldMappings()) {
                if (masterObjFieldApiNames.contains(fieldMappingData.getDestApiName())) {
                    IntegrationFieldVerifyResult integrationFieldVerifyResult = new IntegrationFieldVerifyResult(ResultCodeEnum.FIELD_CAN_NOT_REVERSE_WRITE,
                            entity.getSourceObjectApiName(), fieldMappingData.getDestApiName());
                    fieldResult.add(integrationFieldVerifyResult);
                }
            }
        }
        //明细对象反写字段
        if (CollectionUtils.isNotEmpty(entity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings())) {
            //明细对象，已设置字段同步映射的源对象字段
            Map<String, List<String>> detailObjFieldApiNames = Maps.newHashMap();
            for (DetailObjectMappingsData.DetailObjectMappingData detail : entity.getDetailObjectMappings()) {
                if (CollectionUtils.isNotEmpty(detail.getFieldMappings())) {
                    List<String> fieldApiNameList = detail.getFieldMappings().stream().map(FieldMappingData::getSourceApiName).collect(Collectors.toList());
                    detailObjFieldApiNames.put(detail.getSourceObjectApiName(), fieldApiNameList);
                }
            }
            for (DetailObjectMappingsData.DetailObjectMappingData detail : entity.getIntegrationStreamNodes().getReverseWriteNode().getDetailObjectMappings()) {
                if (detailObjFieldApiNames.containsKey(detail.getDestObjectApiName())) {
                    for (FieldMappingData fieldMappingData : detail.getFieldMappings()) {
                        if (detailObjFieldApiNames.get(detail.getDestObjectApiName()).contains(fieldMappingData.getDestApiName())) {
                            IntegrationFieldVerifyResult integrationFieldVerifyResult = new IntegrationFieldVerifyResult(ResultCodeEnum.FIELD_CAN_NOT_REVERSE_WRITE,
                                    detail.getDestObjectApiName(), fieldMappingData.getDestApiName());
                            fieldResult.add(integrationFieldVerifyResult);
                        }
                    }
                }
            }
        }

        return fieldResult;
    }
}