package com.fxiaoke.open.erpsyncdata.common.constant;

import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TenantTypeEnum {
    CRM(1, "CRM", I18NStringEnum.s696.getI18nKey()),
    ERP(2, "ERP", I18NStringEnum.s697.getI18nKey()),
    ;
    private int type;
    private String name;
    private String i18nKey;

    public static String getNameByType(int type) {
        for (TenantTypeEnum tenantType : TenantTypeEnum.values()) {
            if (tenantType.getType() == type) {
                return tenantType.getName();
            }
        }
        return null;
    }

    /**
     * @see
     * @param syncDirection
     * @return
     */
    public static TenantTypeEnum convertTenantEnum(Integer syncDirection){
        if(CommonCustant.CRM2ERP_DIRECTION.equals(syncDirection)){
            return TenantTypeEnum.CRM;
        }else{
            return TenantTypeEnum.ERP;
        }
    }


    public static TenantTypeEnum getByType(int type) {
        for (TenantTypeEnum tenantType : TenantTypeEnum.values()) {
            if (tenantType.getType() == type) {
                return tenantType;
            }
        }
        return null;
    }
}
