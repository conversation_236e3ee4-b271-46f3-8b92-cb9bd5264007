package com.fxiaoke.open.erpsyncdata.preprocess.arg;

import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogStatusEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.SyncLogTypeEnum;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/6/13
 */
@Data
@Builder
public class SyncLogPageArg {
    private String tenantId;
    @NotNull
    private String realObjApiName;
    @NotNull
    private SyncLogTypeEnum type;
    private QueryTempDataFilterArg queryTempDataFilterArg;
    @Nullable
    private String streamId;
    private Date beginUpdateTime;
    private Date endUpdateTime;
    /**
     * @see SyncLogStatusEnum
     */
    private Integer status;
    private List<String> logId;
    @Builder.Default
    private int offset = 0;
    @Builder.Default
    private int limit = 20;
    /**
     * 不需要查询的字段。减少数据返回
     */
    private List<String> excludeFiled;
    @Data
    public static class QueryTempDataFilterArg{
        private String taskNum;
        private String dataId;
        private String dataNum;
    }

    //        回写节点特殊逻辑,全链路日志加上回写节点后可删除
    private List<SyncLogTypeEnum> types;
    public List<SyncLogTypeEnum> getTypes() {
        if (CollectionUtils.isNotEmpty(types)) {
            return types;
        }

        types = Lists.newArrayList(type);
        return types;
    }
}
