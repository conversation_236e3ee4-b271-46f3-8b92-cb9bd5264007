package com.fxiaoke.open.erpsyncdata.probe.service.impl;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.preprocess.service.ScanSyncWarnningService;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class ScanSyncWarnningServiceTest extends BaseTest {

    @Autowired
    private ScanSyncWarnningService scanSyncWarnningService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private I18NStringManager i18NStringManager;

    @Test
    public void testTenantScan(){
        //scanSyncWarnningService.scanSyncDataMappingErrorNumber("81243");
    }

    @Test
    public void testNotificationService(){
        notificationService.sendFsUserIds("81243","628312575457230848","集成平台数据同步存在错误数据，请及时修复错误数据\n"
          + "企业：(81243)zsl测试企业022\n" + "同步对象：源对象ApiName(SalesOrderObj)->目标对象ApiName(SAL_SaleOrder.BillHead)\n"
          + "同步方向：CRM->ERP\n" + "主对象同步失败数量：89\n" + "\n" + "企业：(81243)zsl测试企业022\n"
          + "同步对象：源对象ApiName(SAL_SaleOrder.BillHead)->目标对象ApiName(SalesOrderObj)\n" + "同步方向：ERP->CRM\n"
          + "主对象同步失败数量：136\n" + "\n" + "企业：(81243)zsl测试企业022\n"
          + "同步对象：源对象ApiName(BD_MATERIALUNITCONVERT.BillHead)->目标对象ApiName(MultiUnitRelatedObj)\n" + "同步方向：ERP->CRM\n"
          + "主对象同步失败数量：163\n" + "\n" + "企业：(81243)zsl测试企业022\n"
          + "同步对象：源对象ApiName(AccountObj)->目标对象ApiName(BD_Customer.BillHead)\n" + "同步方向：CRM->ERP\n" + "主对象同步失败数量：50\n"
          + "\n" + "企业：(81243)zsl测试企业022\n" + "同步对象：源对象ApiName(BD_MATERIAL.BillHead)->目标对象ApiName(ProductObj)\n"
          + "同步方向：ERP->CRM\n" + "主对象同步失败数量：204\n" + "\n" + "企业：(81243)zsl测试企业022\n"
          + "同步对象：源对象ApiName(BD_STOCK.BillHead)->目标对象ApiName(WarehouseObj)\n" + "同步方向：ERP->CRM\n" + "主对象同步失败数量：21",
                AlarmRuleType.OTHER,
                AlarmRuleType.OTHER.getName(i18NStringManager,null,"81243"),
                AlarmType.OTHER,
                AlarmLevel.GENERAL);
    }


}
