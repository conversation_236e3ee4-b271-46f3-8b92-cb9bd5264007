package com.fxiaoke.open.oasyncdata.model;

import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import okhttp3.Response;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date: 10:02 2020/11/6
 * @Desc:
 */
public class HttpResponse {

    private int code;

    private String message;

    private String body;

    public HttpResponse(Response response) throws IOException {
        this.code = response.code();
        this.message = response.message();
        this.body = response.body().string();
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HttpResponse that = (HttpResponse)o;
        return code == that.code && Objects.equal(message, that.message) && Objects.equal(body, that.body);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(code, message, body);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("code", code).add("message", message).add("body", body).toString();
    }
}
