package com.fxiaoke.open.erpsyncdata.dbproxy.entity;

import com.fxiaoke.open.erpsyncdata.dbproxy.annotation.TenantID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 14:06 2021/12/7
 * @Desc:erp用户关键操作日志
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "erp_user_operation_log")
public class ErpUserOperationLogEntity implements Serializable {

    @Id
    @Column(name = "id")
    private String id;

    /**
     * 企业id
     */
    @TenantID
    @Column(name = "tenant_id")
    private String tenantId;

    /**
     * 操作类型
     */
    @Column(name = "operation_type")
    private String operationType;

    /**
     * 用户id
     */
    @Column(name = "operation_user")
    private String operationUser;


    /**
     * 用户电话
     */
    @Column(name = "user_phone")
    private String userPhone;

    /**
     * 对象
     */
    @Column(name = "obj_api_name")
    private String objApiName;

    /**
     * 操作参数
     */
    @Column(name = "arg")
    private String arg;

    /**
     * 操作结果
     */
    @Column(name = "result")
    private String result;


    /**
     * 操作时间
     */
    @Column(name = "operation_time")
    private Long operationTime;
}