package com.fxiaoke.open.erpsyncdata.web.transfer.handler;

import com.facishare.transfer.handler.page.RateLimitPageTransferHandler;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.util.concurrent.RateLimiter;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:37:10
 */
public abstract class ErpRateLimitPageTransferHandler<K, RV, CV> extends RateLimitPageTransferHandler<K, RV, CV> {

    @Override
    public int getThreadNum() {
        return 3;
    }

    @Override
    public int tryNum() {
        return 1;
    }

    @Override
    public void afterPropertiesSet() {
        ConfigFactory.getConfig("erp-sync-data-all", "shard-mongo-transfer", this::initRateLimiter, true);
    }

    @Override
    protected void initRateLimiter(final IConfig iConfig) {
        rateLimiter = RateLimiter.create(iConfig.getInt("ratelimit", 10000));
    }
}
