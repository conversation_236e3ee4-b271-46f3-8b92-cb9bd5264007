package com.fxiaoke.open.oasyncdata.result;


import com.fxiaoke.open.oasyncdata.model.ObjectData;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/22
 */
@Data
public class ErpObjDataResult implements Serializable {
    private static final long serialVersionUID = 3853646186417602342L;
    /**
     * 源数据时间类型，主从对象数据共用
     */
    private Integer sourceEventType;
    /**
     * 源数据
     */
    private ObjectData sourceData;
    /**
     * 明细数据
     * key: 从对象entry(erp_object中可以查到绑定的erp拆分对象名)， value：明细数据
     */
    private Map<String, List<ObjectData>> detailData = new HashMap<>();
}
