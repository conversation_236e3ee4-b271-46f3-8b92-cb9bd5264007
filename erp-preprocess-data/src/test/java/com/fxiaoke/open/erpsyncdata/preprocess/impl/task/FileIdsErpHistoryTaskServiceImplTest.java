package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.ExcelListener.HistoryTaskTypeIdsListener;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.FileManager;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.IdsErpHistoryTaskManager;
import com.fxiaoke.open.erpsyncdata.preprocess.model.DataIdExcelVo;
import com.fxiaoke.open.erpsyncdata.preprocess.model.FileData;
import com.fxiaoke.open.erpsyncdata.preprocess.model.ReadExcel;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * FileIdsErpHistoryTaskServiceImpl 单元测试类
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
@ExtendWith(MockitoExtension.class)
class FileIdsErpHistoryTaskServiceImplTest {

    @Mock
    private IdsErpHistoryTaskManager idsErpHistoryTaskManager;

    @Mock
    private FileManager fileManager;

    @InjectMocks
    private FileIdsErpHistoryTaskServiceImpl fileIdsErpHistoryTaskService;

    private ErpHistoryDataTaskResult taskResult;
    private ErpHistoryDataTaskEntity taskEntity;
    private FileData fileData;

    @BeforeEach
    void setUp() {
        // Given: 初始化测试数据
        fileData = new FileData();
        fileData.setPath("/test/file/path.xlsx");
        fileData.setFilename("test.xlsx");
        fileData.setExt("xlsx");

        taskResult = new ErpHistoryDataTaskResult();
        taskResult.setFileData(fileData);
        taskResult.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_FILE_IDS.getStatus());

        taskEntity = new ErpHistoryDataTaskEntity();
        taskEntity.setId("test-task-id");
        taskEntity.setDataIds(JSONObject.toJSONString(fileData));
        taskEntity.setOffset(0L);
    }

    @Test
    @DisplayName("测试taskType方法返回正确的任务类型")
    void testTaskType() {
        // When: 调用taskType方法
        ErpHistoryDataTaskTypeEnum result = fileIdsErpHistoryTaskService.taskType();

        // Then: 验证返回正确的任务类型
        assertEquals(ErpHistoryDataTaskTypeEnum.TYPE_FILE_IDS, result);
    }

    @Test
    @DisplayName("测试checkParamsError - 文件数据为null时返回true")
    void testCheckParamsError_WhenFileDataIsNull() {
        // Given: 文件数据为null
        taskResult.setFileData(null);

        // When: 调用checkParamsError方法
        boolean result = fileIdsErpHistoryTaskService.checkParamsError(taskResult);

        // Then: 验证返回true
        assertTrue(result);
    }

    @Test
    @DisplayName("测试checkParamsError - 文件路径为空时返回true")
    void testCheckParamsError_WhenFilePathIsBlank() {
        // Given: 文件路径为空
        fileData.setPath("");
        taskResult.setFileData(fileData);

        // When: 调用checkParamsError方法
        boolean result = fileIdsErpHistoryTaskService.checkParamsError(taskResult);

        // Then: 验证返回true
        assertTrue(result);
    }

    @Test
    @DisplayName("测试checkParamsError - 文件路径为null时返回true")
    void testCheckParamsError_WhenFilePathIsNull() {
        // Given: 文件路径为null
        fileData.setPath(null);
        taskResult.setFileData(fileData);

        // When: 调用checkParamsError方法
        boolean result = fileIdsErpHistoryTaskService.checkParamsError(taskResult);

        // Then: 验证返回true
        assertTrue(result);
    }

    @Test
    @DisplayName("测试checkParamsError - 文件路径有效时返回false")
    void testCheckParamsError_WhenFilePathIsValid() {
        // Given: 文件路径有效
        fileData.setPath("/valid/path/file.xlsx");
        taskResult.setFileData(fileData);

        // When: 调用checkParamsError方法
        boolean result = fileIdsErpHistoryTaskService.checkParamsError(taskResult);

        // Then: 验证返回false
        assertFalse(result);
    }

    @Test
    @DisplayName("测试checkTaskValid - 数据ID为空时返回true")
    void testCheckTaskValid_WhenDataIdsIsBlank() {
        // Given: 数据ID为空
        taskEntity.setDataIds("");

        // When: 调用checkTaskValid方法
        boolean result = fileIdsErpHistoryTaskService.checkTaskValid(taskEntity);

        // Then: 验证返回true
        assertTrue(result);
    }

    @Test
    @DisplayName("测试checkTaskValid - 数据ID为null时返回true")
    void testCheckTaskValid_WhenDataIdsIsNull() {
        // Given: 数据ID为null
        taskEntity.setDataIds(null);

        // When: 调用checkTaskValid方法
        boolean result = fileIdsErpHistoryTaskService.checkTaskValid(taskEntity);

        // Then: 验证返回true
        assertTrue(result);
    }

    @Test
    @DisplayName("测试checkTaskValid - 数据ID有效时返回false")
    void testCheckTaskValid_WhenDataIdsIsValid() {
        // Given: 数据ID有效
        taskEntity.setDataIds(JSONObject.toJSONString(fileData));

        // When: 调用checkTaskValid方法
        boolean result = fileIdsErpHistoryTaskService.checkTaskValid(taskEntity);

        // Then: 验证返回false
        assertFalse(result);
    }

    @Test
    @DisplayName("测试saveTaskSuccess - 成功保存任务")
    void testSaveTaskSuccess() {
        // Given: 准备测试数据
        String tenantId = "test-tenant";
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();

        // 模拟父类方法
        FileIdsErpHistoryTaskServiceImpl spyService = spy(fileIdsErpHistoryTaskService);
        doReturn(true).when(spyService).createHistoryTask(eq(tenantId), anyList());

        // When: 调用saveTaskSuccess方法
        boolean result = spyService.saveTaskSuccess(tenantId, taskResult, entity);

        // Then: 验证结果和设置
        assertTrue(result);
        assertEquals(taskResult.getTaskType(), entity.getTaskType());
        assertEquals(JSONObject.toJSONString(taskResult.getFileData()), entity.getDataIds());
        verify(spyService).createHistoryTask(eq(tenantId), anyList());
    }

    @Test
    @DisplayName("测试editTaskSuccess - 成功编辑任务")
    void testEditTaskSuccess() {
        // Given: 准备测试数据
        String tenantId = "test-tenant";
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();

        // 模拟父类方法
        FileIdsErpHistoryTaskServiceImpl spyService = spy(fileIdsErpHistoryTaskService);
        doReturn(true).when(spyService).editHistorySnapshotTask(tenantId, entity);

        // When: 调用editTaskSuccess方法
        boolean result = spyService.editTaskSuccess(tenantId, taskResult, entity);

        // Then: 验证结果和设置
        assertTrue(result);
        assertEquals(taskResult.getTaskType(), entity.getTaskType());
        assertEquals(JSONObject.toJSONString(taskResult.getFileData()), entity.getDataIds());
        verify(spyService).editHistorySnapshotTask(tenantId, entity);
    }

    @Test
    @DisplayName("测试doTask - 调用verifyDataIdByFile方法")
    void testDoTask() {
        // Given: 准备测试数据
        String tenantId = "test-tenant";
        Result<Void> expectedResult = Result.newSuccess();

        FileIdsErpHistoryTaskServiceImpl spyService = spy(fileIdsErpHistoryTaskService);
        doReturn(expectedResult).when(spyService).verifyDataIdByFile(tenantId, taskEntity);

        // When: 调用doTask方法
        Result<Void> result = spyService.doTask(tenantId, taskEntity);

        // Then: 验证调用了verifyDataIdByFile方法
        assertEquals(expectedResult, result);
        verify(spyService).verifyDataIdByFile(tenantId, taskEntity);
    }

    @Test
    @DisplayName("测试afterConvert2Vo - 正确转换实体到VO")
    void testAfterConvert2Vo() {
        // Given: 准备测试数据
        ErpHistoryDataTaskResult copy = new ErpHistoryDataTaskResult();
        taskEntity.setDataIds(JSONObject.toJSONString(fileData));

        // When: 调用afterConvert2Vo方法
        fileIdsErpHistoryTaskService.afterConvert2Vo(copy, taskEntity);

        // Then: 验证转换结果
        assertEquals(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus(), copy.getTaskType());
        assertEquals(ErpHistoryDataTaskResult.IdsInputType.fileImport, copy.getIdsInputType());
        assertNotNull(copy.getFileData());
        assertEquals(fileData.getPath(), copy.getFileData().getPath());
        assertEquals(fileData.getFilename(), copy.getFileData().getFilename());
        assertEquals(fileData.getExt(), copy.getFileData().getExt());
        assertNull(taskEntity.getDataIds());
    }

    @Test
    @DisplayName("测试verifyDataIdByFile - 成功处理文件")
    void testVerifyDataIdByFile_Success() {
        // Given: 准备测试数据
        String tenantId = "test-tenant";
        byte[] fileBytes = "test file content".getBytes();
        Result<Void> expectedResult = Result.newSuccess();

        // 模拟文件下载
        when(fileManager.downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath())))
                .thenReturn(fileBytes);

        // 模拟Excel读取
        doNothing().when(fileManager).readExcel(any(ReadExcel.Arg.class));

        // 使用MockedStatic来模拟HistoryTaskTypeIdsListener的构造
        try (MockedStatic<HistoryTaskTypeIdsListener> mockedListener = mockStatic(HistoryTaskTypeIdsListener.class)) {
            HistoryTaskTypeIdsListener mockListener = mock(HistoryTaskTypeIdsListener.class);
            when(mockListener.getResult()).thenReturn(expectedResult);

            mockedListener.when(() -> new HistoryTaskTypeIdsListener(
                    eq(tenantId), eq(taskEntity), eq(1L), eq(idsErpHistoryTaskManager)))
                    .thenReturn(mockListener);

            // When: 调用verifyDataIdByFile方法
            Result<Void> result = fileIdsErpHistoryTaskService.verifyDataIdByFile(tenantId, taskEntity);

            // Then: 验证结果
            assertEquals(expectedResult, result);
            verify(fileManager).downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath()));
            verify(fileManager).readExcel(any(ReadExcel.Arg.class));
        }
    }

    @Test
    @DisplayName("测试verifyDataIdByFile - 带offset的情况")
    void testVerifyDataIdByFile_WithOffset() {
        // Given: 准备测试数据，设置offset
        String tenantId = "test-tenant";
        taskEntity.setOffset(5L);
        byte[] fileBytes = "test file content".getBytes();
        Result<Void> expectedResult = Result.newSuccess();

        // 模拟文件下载
        when(fileManager.downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath())))
                .thenReturn(fileBytes);

        // 模拟Excel读取
        doNothing().when(fileManager).readExcel(any(ReadExcel.Arg.class));

        // 使用MockedStatic来模拟HistoryTaskTypeIdsListener的构造
        try (MockedStatic<HistoryTaskTypeIdsListener> mockedListener = mockStatic(HistoryTaskTypeIdsListener.class)) {
            HistoryTaskTypeIdsListener mockListener = mock(HistoryTaskTypeIdsListener.class);
            when(mockListener.getResult()).thenReturn(expectedResult);

            mockedListener.when(() -> new HistoryTaskTypeIdsListener(
                    eq(tenantId), eq(taskEntity), eq(6L), eq(idsErpHistoryTaskManager)))
                    .thenReturn(mockListener);

            // When: 调用verifyDataIdByFile方法
            Result<Void> result = fileIdsErpHistoryTaskService.verifyDataIdByFile(tenantId, taskEntity);

            // Then: 验证结果和ignoreSize计算正确
            assertEquals(expectedResult, result);
            verify(fileManager).downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath()));
        }
    }

    @Test
    @DisplayName("测试verifyDataIdByFile - 验证ReadExcel.Arg参数设置")
    void testVerifyDataIdByFile_VerifyReadExcelArg() {
        // Given: 准备测试数据
        String tenantId = "test-tenant";
        byte[] fileBytes = "test file content".getBytes();

        // 模拟文件下载
        when(fileManager.downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath())))
                .thenReturn(fileBytes);

        // 捕获ReadExcel.Arg参数
        ArgumentCaptor<ReadExcel.Arg<DataIdExcelVo>> argCaptor = ArgumentCaptor.forClass(ReadExcel.Arg.class);
        doNothing().when(fileManager).readExcel(argCaptor.capture());

        // 使用MockedStatic来模拟HistoryTaskTypeIdsListener的构造
        try (MockedStatic<HistoryTaskTypeIdsListener> mockedListener = mockStatic(HistoryTaskTypeIdsListener.class)) {
            HistoryTaskTypeIdsListener mockListener = mock(HistoryTaskTypeIdsListener.class);
            when(mockListener.getResult()).thenReturn(Result.newSuccess());

            mockedListener.when(() -> new HistoryTaskTypeIdsListener(
                    eq(tenantId), eq(taskEntity), eq(1L), eq(idsErpHistoryTaskManager)))
                    .thenReturn(mockListener);

            // When: 调用verifyDataIdByFile方法
            fileIdsErpHistoryTaskService.verifyDataIdByFile(tenantId, taskEntity);

            // Then: 验证ReadExcel.Arg参数设置正确
            ReadExcel.Arg<DataIdExcelVo> capturedArg = argCaptor.getValue();
            assertNotNull(capturedArg);
            assertEquals(DataIdExcelVo.class, capturedArg.getType());
            assertNotNull(capturedArg.getInputStream());
            assertTrue(capturedArg.getInputStream() instanceof ByteArrayInputStream);
            assertEquals(mockListener, capturedArg.getExcelListener());
        }
    }

    @Test
    @DisplayName("测试verifyDataIdByFile - 文件下载失败的情况")
    void testVerifyDataIdByFile_FileDownloadFails() {
        // Given: 准备测试数据，模拟文件下载返回null
        String tenantId = "test-tenant";

        when(fileManager.downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath())))
                .thenReturn(null);

        // When & Then: 调用方法应该抛出异常
        assertThrows(NullPointerException.class, () -> {
            fileIdsErpHistoryTaskService.verifyDataIdByFile(tenantId, taskEntity);
        });

        verify(fileManager).downTnFile(eq(tenantId), isNull(), isNull(), eq(fileData.getPath()));
        verify(fileManager, never()).readExcel(any(ReadExcel.Arg.class));
    }

    @Test
    @DisplayName("测试verifyDataIdByFile - JSON解析异常")
    void testVerifyDataIdByFile_JsonParseException() {
        // Given: 准备无效的JSON数据
        String tenantId = "test-tenant";
        taskEntity.setDataIds("invalid json");

        // When & Then: 调用方法应该抛出JSON解析异常
        assertThrows(Exception.class, () -> {
            fileIdsErpHistoryTaskService.verifyDataIdByFile(tenantId, taskEntity);
        });
    }
}
