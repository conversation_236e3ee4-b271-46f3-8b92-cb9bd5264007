package com.fxiaoke.open.erpsyncdata.apiproxy.utils;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/12/6
 */

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
public class TimeUtils {


    /**
     * yyyy-MM-dd HH:mm:ss"
     * 2011-12-03 10:15:30
     * @param milliSecond
     * @return
     */
    public static String getDateTime(Long milliSecond){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        ZonedDateTime zonedDateTime = Instant.ofEpochMilli(milliSecond).atZone(ZoneId.systemDefault());
        String format = dtf.format(zonedDateTime);
        return format;
    }
}
