package com.fxiaoke.open.erpsyncdata.preprocess.manager;

import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.apiproxy.constant.K3CloudForm;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.GetByIdBreakManager;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Ignore
public class GetByIdBreakManagerTest extends BaseTest {
    @Autowired
    private GetByIdBreakManager getByIdBreakManager;

    @Test
    public void test() {
//        getByIdBreakManager.removeBreak("82777",
//                "696453487420604416",
//                K3CloudForm.SAL_SaleOrder);
//        GetByIdBreakManager.FailedData failedData = getByIdBreakManager.getFailedData("82777",
//                "696453487420604416",
//                K3CloudForm.BD_MATERIAL);
        getByIdBreakManager.increaseFailedCount("81772",
                "642530472589131776",
                ErpChannelEnum.ERP_K3CLOUD,
                K3CloudForm.BD_MATERIAL,
                "getbyid熔断消息");
    }
}
