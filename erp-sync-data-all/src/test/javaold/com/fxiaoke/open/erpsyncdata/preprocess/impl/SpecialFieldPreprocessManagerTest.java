package com.fxiaoke.open.erpsyncdata.preprocess.impl;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.open.erpsyncdata.BaseTest;
import com.fxiaoke.open.erpsyncdata.converter.manager.NodeDoProcessManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.JacksonUtil;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.manager.SpecialFieldPreprocessManager;
import com.fxiaoke.open.erpsyncdata.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 16:37 2020/8/26
 * @Desc:
 */
@Ignore
@Slf4j
public class SpecialFieldPreprocessManagerTest extends BaseTest {

    @Autowired
    private SpecialFieldPreprocessManager specialFieldPreprocessManager;
    @Autowired
    private com.fxiaoke.open.erpsyncdata.preprocess.manager.SpecialFieldPreprocessManager specialFieldPreprocessService;
    @Autowired
    private NodeDoProcessManager doProcessManager;

    @Test
    public void convertErpFieldValue2Crm() {
        SyncDataContextEvent erpObjDataVO = getData();
        List<SyncDataContextEvent> erpObjDataVOS = specialFieldPreprocessManager.convertErpFieldValue2CrmList("79675","","", Lists.newArrayList(erpObjDataVO));
        log.info("result={}", erpObjDataVOS);
    }

    @Test
    public void convertCrmFieldValue2Erp() {
        String json = "{\"syncDataData\":{\"destDataId\":\"64883d3372cfde0001b2f50e\",\"destEventType\":1,\"destObjectApiName\":\"DepartmentObj\",\"destTenantId\":\"88521\",\"destTenantType\":1,\"id\":\"64883d33d23f1808dbe640f9\",\"sourceData\":{\"tenant_id\":\"88521\",\"code\":\"2023061302\",\"object_describe_api_name\":\"department_1fnthceo5\",\"name\":\"部门061302\",\"_id\":\"2023061302\"},\"sourceDataId\":\"2023061302\",\"sourceDetailSyncDataIds\":{},\"sourceEventType\":2,\"sourceObjectApiName\":\"department_1fnthceo5\",\"sourceTenantId\":\"88521\",\"sourceTenantType\":2,\"status\":2,\"syncPloyDetailSnapshotId\":\"39eead37e0084a71949f4802f9709985\",\"tenantId\":\"88521\"}}";
        SyncDataContextEvent doProcessMqData = JSONObject.parseObject(json, SyncDataContextEvent.class);
        SyncDataContextEvent data = doProcessManager.processMessage(doProcessMqData);
        System.out.println(data);
    }

    @Test
    public void convertCrmFieldValue2Erp2() {
        String json = "{\"destData\":{\"object_describe_api_name\":\"BD_Empinfo.BillHead\",\"tenant_id\":\"82777\",\"FCreateOrgId.FNumber\":\"000\",\"FName\":\"吴贝贝2\",\"FStaffNumber\":\"5637\",\"FViceDepartment\":[\"dep_1004\"],\"FMobile\":\"18926584794\",\"_id\":\"783967\"},\"destDataId\":\"783967\",\"destDetailSyncDataIdAndDestDataMap\":{},\"destEventType\":2,\"destObjectApiName\":\"BD_Empinfo.BillHead\",\"destTenantId\":\"82777\",\"destTenantType\":2,\"sourceTenantId\":\"82777\",\"syncDataId\":\"fcfe0630a1154fb6a780d3b4f9f90478\",\"syncPloyDetailSnapshotId\":\"0a7a526bebb54981909138ead9024ed6\",\"tenantId\":\"82777\"}";
        SyncDataContextEvent doWriteMqData = JSONObject.parseObject(json, SyncDataContextEvent.class);
        SyncDataContextEvent data = specialFieldPreprocessManager.convertCrmFieldValue2Erp("82777","696453487420604416",doWriteMqData);
        System.out.println(data);
    }

    @Test
    public void convertErp2Crm() {
        SyncDataContextEvent doWriteMqData = JacksonUtil.fromJson(objDataStr, SyncDataContextEvent.class);
        specialFieldPreprocessManager.convertErpFieldValue2Crm("82777","696453487420604416","", doWriteMqData);
    }

    String objDataStr = "{\"detailData\":{\"BD_Customer.FT_BD_CUSTCONTACT\":[],\"BD_Customer.FT_BD_CUSTBANK\":[{\"OPENBANKNAME\":[],\"DetailId\":0,\"FISDEFAULT1\":false,\"object_describe_api_name\":\"BD_Customer.FT_BD_CUSTBANK\",\"_id\":\"null\",\"tenant_id\":\"82777\",\"last_modified_by\":[\"-10000\"],\"name\":\"null\",\"fake_master_detail\":\"CUST00693\"}]},\"sourceData\":{\"erp_id\":\"782558\",\"erp_num\":\"CUST00693\",\"id\":782558,\"FCreateOrgId.FNumber\":\"000\",\"FCreatorId.FUserId\":741776,\"FDescription\":\" \",\"UseOrgId.Name\":\"纷享销客\",\"COUNTRY.Id\":\"46a524cf-5797-4e46-bd0a-7203fc426d9c\",\"CreateOrgId.Id\":1,\"CreateOrgId.Name\":\"纷享销客\",\"CustTypeId.Id\":\"673cb7c55ea24626ae639ff2ec5adf0e\",\"FADDRESS\":\" \",\"FAPPROVEDATE\":\"2022-01-21T14:49:28.81\",\"FCPAdminCode\":\" \",\"FDocumentStatus\":\"C\",\"FFAX\":\" \",\"FForbidStatus\":\"A\",\"FINVOICEADDRESS\":\" \",\"FINVOICEBANKACCOUNT\":\" \",\"FINVOICEBANKNAME\":\" \",\"FINVOICETEL\":\" \",\"FInvoiceType\":\"1\",\"FISCREDITCHECK\":true,\"FIsDefPayer\":false,\"FIsGroup\":false,\"FIsTrade\":true,\"FPriority\":1,\"FShortName\":\" \",\"FTaxRate.FNumber\":\"SL02_SYS\",\"FT_BD_CUSTOMEREXT[0].FDebitCard\":\" \",\"FT_BD_CUSTOMEREXT.FALLOWJOINZHJ\":false,\"FT_BD_CUSTOMEREXT.FMarginLevel\":0.0,\"FT_BD_CUSTORDERORG[0].FIsDefaultOrderOrg\":true,\"FTRADINGCURRID.FNumber\":\"PRE007\",\"FTRANSLEADTIME\":0,\"FUseOrgId.FNumber\":\"000\",\"FWEBSITE\":\" \",\"FZIP\":\" \",\"INVOICETITLE\":\"贝贝测试客户0119\",\"name\":\"贝贝测试客户0119\",\"Number\":\"CUST00693\",\"TaxRate.Id\":234,\"TaxRate.Name\":\"13%增值税\",\"TaxType.Id\":\"9e855eb97bec43e7b50c3e0e0bf51210\",\"TRADINGCURRID.Id\":7,\"TRADINGCURRID.Name\":\"美元\",\"UseOrgId.Id\":1,\"FModifyDate\":\"2022-01-21T14:49:22.753\",\"FModifierId.FUserId\":741776,\"FAPPROVERID.FUserId\":741776,\"FTAXREGISTERCODE\":\" \",\"FTEL\":\" \",\"FCreateDate\":\"2022-01-19T19:49:21.243\",\"F_PAEZ_MulCombo\":\"1,2\",\"F_PAEZ_MulSaller\":[{\"PkId\":100014,\"F_PAEZ_MulSaller\":{\"msterID\":782536,\"MultiLanguageText\":[{\"PkId\":102408,\"LocaleId\":2052,\"Name\":\"吴贝贝\"}],\"Number\":\"00408_GW000156_1\",\"BD_SALESMANENTRY\":[],\"DeptId_Id\":103684,\"Id\":782536,\"DeptId\":{\"msterID\":103684,\"MultiLanguageText\":[{\"PkId\":100212,\"LocaleId\":2052,\"Name\":\"研发中心-深研-互联业务线\"}],\"Number\":\"04.02.01\",\"Id\":103684,\"Name\":[{\"Value\":\"研发中心-深研-互联业务线\",\"Key\":2052}]},\"Name\":[{\"Value\":\"吴贝贝\",\"Key\":2052}]},\"F_PAEZ_MulSaller_Id\":782536},{\"PkId\":100015,\"F_PAEZ_MulSaller\":{\"msterID\":708085,\"MultiLanguageText\":[{\"PkId\":102665,\"LocaleId\":2052,\"Name\":\"郑素兰\"}],\"Number\":\"00665_GW000226_1\",\"BD_SALESMANENTRY\":[],\"DeptId_Id\":103684,\"Id\":708085,\"DeptId\":{\"msterID\":103684,\"MultiLanguageText\":[{\"PkId\":100212,\"LocaleId\":2052,\"Name\":\"研发中心-深研-互联业务线\"}],\"Number\":\"04.02.01\",\"Id\":103684,\"Name\":[{\"Value\":\"研发中心-深研-互联业务线\",\"Key\":2052}]},\"Name\":[{\"Value\":\"郑素兰\",\"Key\":2052}]},\"F_PAEZ_MulSaller_Id\":708085}],\"F_PAEZ_MulCustomer\":[{\"PkId\":100005,\"F_PAEZ_MulCustomer_Id\":781743,\"F_PAEZ_MulCustomer\":{\"msterID\":781743,\"MultiLanguageText\":[{\"PkId\":128850,\"LocaleId\":2052,\"Name\":\"82777_20\"}],\"Number\":\"20220104-000021\",\"Id\":781743,\"Name\":[{\"Value\":\"82777_20\",\"Key\":2052}]}},{\"PkId\":100006,\"F_PAEZ_MulCustomer_Id\":782114,\"F_PAEZ_MulCustomer\":{\"msterID\":782114,\"MultiLanguageText\":[{\"PkId\":128890,\"LocaleId\":2052,\"Name\":\"82777_22\"}],\"Number\":\"20220111-000023\",\"Id\":782114,\"Name\":[{\"Value\":\"82777_22\",\"Key\":2052}]}}],\"mongo_id\":\"61e7fb63cb69647054265f5f\",\"object_describe_api_name\":\"BD_Customer.BillHead\",\"tenant_id\":\"82777\",\"_id\":\"CUST00693\",\"last_modified_by\":[\"-10000\"]},\"sourceEventType\":2}";

    public SyncDataContextEvent getData() {
        SyncDataContextEvent erpObjDataVO = new SyncDataContextEvent();
        erpObjDataVO.setSourceEventType(1);
        erpObjDataVO.setDetailData(Maps.newHashMap());
        for (int i = 1; i < 4; i++) {
            ObjectData objectData = new ObjectData();
            for (int j = 1; j < 5; j++) {
                objectData.put("field" + i + j, "value" + i + j);
            }
            if (i == 1) {
                String obj = "objApiName" + i;
                objectData.putApiName(obj);
                erpObjDataVO.setSourceData(objectData);
            } else {
                String detailObj = "detailObjApiName" + i;
                objectData.putApiName(detailObj);
                erpObjDataVO.getDetailData().put(detailObj, Lists.newArrayList(objectData));
            }
        }
        return erpObjDataVO;
    }

    @Test
    public void CacheTest() {
        StopWatch stopWatch = new StopWatch("test Cache");
        for (int i = 0; i < 10; i++) {
            stopWatch.start(String.format("%s time", i));
            Map<String, ErpObjectFieldEntity> erpObjFieldMap = specialFieldPreprocessService.getErpObjFieldMap("81138","", "BD_Customer.BillHead");
            stopWatch.stop();
        }
        System.out.println(stopWatch.prettyPrint());

    }
}