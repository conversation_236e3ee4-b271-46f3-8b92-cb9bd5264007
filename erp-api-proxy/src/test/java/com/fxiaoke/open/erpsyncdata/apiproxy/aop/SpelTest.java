package com.fxiaoke.open.erpsyncdata.apiproxy.aop;

import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2022/12/7 14:54:34
 */

@Ignore
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "test/test-aop.xml")
public class SpelTest {
    @Autowired
    TestBaseErpDataManager testBaseErpDataManager;

    @Test
    public void testAop() {
        final ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity();
        final ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setSourceEventType(EventTypeEnum.ADD.getType());
        testBaseErpDataManager.getErpObjData(erpIdArg, connectInfo);
    }

    /**
     * 测试内部调用能否抓到
     */
    @Test
    public void testInnerAop() {
        final ErpConnectInfoEntity connectInfo = new ErpConnectInfoEntity();
        final ErpIdArg erpIdArg = new ErpIdArg();
        erpIdArg.setSourceEventType(EventTypeEnum.ADD.getType());
        testBaseErpDataManager.getErpObjData2(erpIdArg, connectInfo);
    }
}