package com.fxiaoke.open.erpsyncdata.common.exception;

import com.fxiaoke.open.erpsyncdata.common.constant.ResultCodeEnum;
import com.github.trace.aop.IgnorableException;
import lombok.Getter;

public class SyncDataException extends RuntimeException implements IgnorableException {
    /**
     * 错误码
     */
    @Getter
    private int errCode;
    /**
     * 错误信息
     */
    @Getter
    private String errMsg;

    public SyncDataException(Integer errCode, String errMsg) {
        super("errCode=" + errCode + ",errMsg=" + errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public SyncDataException(ResultCodeEnum resultCode) {
        super("errCode=" + resultCode.getErrCode() + ",errMsg=" + resultCode.getErrMsg());
        this.errCode = resultCode.getErrCode();
        this.errMsg = resultCode.getErrMsg();
    }

    public SyncDataException(Throwable e) {
        super(e);
    }

    public static SyncDataException wrap(Throwable e) {
        if (e instanceof SyncDataException) {
            return (SyncDataException) e;
        }
        return new SyncDataException(e);
    }

    @Override
    public String getErrorCode() {
        //使这个异常不会上报失败，s306244开头不会上报
        return "s306244000";
    }
}
