package com.fxiaoke.open.erpsyncdata.web

import com.fxiaoke.open.erpsyncdata.dbproxy.util.GsonUtil
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult
import com.fxiaoke.open.erpsyncdata.web.interceptor.CepGsonConverter
import com.google.gson.*
import spock.lang.Specification
import spock.lang.Unroll

import java.lang.reflect.Type

/**
 * <AUTHOR> 
 * @date 2024/6/13 14:23:47
 */
class GsonUtilTest extends Specification {
    @Unroll
    def "fromJson-#name"() {
        when:
        String json = '{"fieldApiName": "create_time", "operate": "GTE", "fieldValue": [' + num + ']}'
        def object = gson.fromJson(json, Map)
        def value = object['fieldValue'][0]

        then:
        value == result

        where:
        name          | gson               | num             || result
        "long"        | GsonUtil.getGson() | '1704042000000' || 1704042000000
        "long"        | cepGson()          | '1704042000000' || 1704042000000
        "long"        | CepGsonConverter() | '1704042000000' || 1704042000000
        "科学计数法"  | GsonUtil.getGson() | '1.7E+12'       || 1.7E12
        "科学计数法2" | cepGson()          | '1.7E+12'       || 1.7E12
        "科学计数法3" | numGson()          | '1.7E+12'       || 1.7E12
        "科学计数法4" | CepGsonConverter() | '1.7E+12'       || 1.7E12
    }

    @Unroll
    def "toJson-#name"() {
        when:
        def object = ["fieldValue": [num]]
        def str = gson.toJson(object)
        println str
        def object2 = gson.fromJson(str, Map)
        println object2
        def value = object2['fieldValue'][0]
        println value

        then:
        String.valueOf(value) == result
        json == str

        where:
        name          | gson               | num           || result          | json
        "long"        | GsonUtil.getGson() | 1704042000000 || '1.704042E12'   | '{"fieldValue":[1704042000000]}'
        "long"        | cepGson()          | 1704042000000 || '1.704042E12'   | '{"fieldValue":[1704042000000]}'
        "long"        | numGson()          | 1704042000000 || '1704042000000' | '{"fieldValue":[1704042000000]}'
        "long"        | CepGsonConverter() | 1704042000000 || '1.704042E12'   | '{"fieldValue":[1704042000000]}'
        "科学计数法"  | GsonUtil.getGson() | 1.7E+12       || '1.7E12'        | '{"fieldValue":[1.7E+12]}'
        "科学计数法2" | cepGson()          | 1.7E+12       || '1.7E12'        | '{"fieldValue":[1.7E+12]}'
        "科学计数法3" | numGson()          | 1.7E+12       || '1.7E12'        | '{"fieldValue":[1.7E+12]}'
        "科学计数法4" | CepGsonConverter() | 1.7E+12       || '1.7E12'        | '{"fieldValue":[1.7E+12]}'
        "科学计数法5" | GsonUtil.getGson() | 1.7E12        || '1.7E12'        | '{"fieldValue":[1.7E+12]}'
    }

    @Unroll
    def "toJson2-#name"() {
        expect:
        def filters = GsonUtil.fromArrayJson("[{\"objectApiName\":\"SalesOrderObj\",\"filters\":[[{\"fieldApiName\":\"create_time\",\"fieldType\":\"date_time\",\"label\":\"创建时间\",\"operate\":\"GTE\",\"fieldValue\":[1704038400000],\"isVariableBetween\":false}]]}]", ErpHistoryDataTaskResult.CrmFilters.class)
        println filters[0]['filters'][0][0]['fieldValue']

        def filter = GsonUtil.fromArrayJson("[{\"objectApiName\":\"SalesOrderObj\",\"filters\":[[{\"fieldApiName\":\"create_time\",\"fieldType\":\"date_time\",\"label\":\"创建时间\",\"operate\":\"GTE\",\"fieldValue\":[1.7040384E12],\"isVariableBetween\":false}]]}]", ErpHistoryDataTaskResult.CrmFilters.class)
        println filter['filters'][0][0]['fieldValue']
    }

    @Unroll
    def "double->long"() {
        when:
        def longValue = ((Double) num).longValue()
        def equals = ((Double) num) == longValue
        println((Double) num)
        println longValue

        then:
        longValue == l
        equals == result

        where:
        num    || result | l
        1.7E12 || true   | 1700000000000
        1.7    || false  | 1
    }


    Gson cepGson() {
        return new GsonBuilder().registerTypeAdapter(Double.class, new JsonSerializer<Double>() {
            JsonElement serialize(Double src, Type typeOfSrc, JsonSerializationContext context) {
                return src == (double) src.longValue() ? new JsonPrimitive(src.longValue()) : new JsonPrimitive(src);
            }
        }).create()
    }

    Gson numGson() {
        return new GsonBuilder().setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE).create()
    }

    Gson CepGsonConverter() {
        return new CepGsonConverter().getGson()
    }
}
