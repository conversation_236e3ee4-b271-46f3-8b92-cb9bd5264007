package com.fxiaoke.open.erpsyncdata.preprocess.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 10:38 2020/9/15
 * @Desc:
 */
@ApiModel
@Data
public class ErpWarehouseResult implements Serializable {
    @ApiModelProperty("erp仓库id")
    public String erpWarehouseId; //
    @ApiModelProperty("erp仓库名称")
    public String erpWarehouseName; //
    @ApiModelProperty("erp仓库mq数据")
    public ErpObjDataResult erpWarehouseMQData; //
}
