package com.fxiaoke.open.erpsyncdata.preprocess.constant

import spock.lang.Specification

import static com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpChannelEnum.*

/**
 * <AUTHOR> 
 * @date 2023/8/28 14:12:38
 */
class ErpChannelEnumTest extends Specification {

    def "getHeaderFunctionName"() {
        expect:
        channel.getHeaderFunctionName(connectParam) == functionName

        where:
        channel          | functionName        | connectParam
        STANDARD_CHANNEL | "TestInvalid__c"    | "{\"systemName\":\"test system\",\"baseUrl\":\"12231\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\"},\"headerScript\":\"\",\"headerFunctionName\":\"TestInvalid__c\",\"resultFormat\":{\"codeName\":\"1231\",\"msgName\":\"123\",\"dataName\":\"1\",\"successCode\":\"122\"},\"pushDataApiNames\":[]}"

        ERP_DB_PROXY     | null                | "{\"baseUrl\":\"http://**********:8080/dev/dbadapt/proxy/api\",\"userName\":\"admin\",\"password\":\"123456\",\"pushDataApiNames\":[],\"version\":\"1.1.beta.20230801\",\"dbName\":\"db2\",\"dbType\":\"postgresql\"}"

        ERP_K3CLOUD      | null                | "{\"baseUrl\":\"https://api.kingdee.com/galaxyapi/\",\"dbId\":\"1584143499752675328\",\"dbName\":\"测试_北京仁宇通达供应链管理有限公司\",\"authType\":2,\"userName\":\"Administrator\",\"password\":\"337d709a41b24bc7b0d4d9570a971ed3\",\"appId\":\"241627_w35OQaEv6vAexeWE265DS9UI2g7b7rkv\",\"lcid\":2052,\"pushDataApiNames\":[],\"useFsHttpClient\":true}"

        ERP_SAP          | "delete_mapping__c" | "{\"baseUrl\":\"www.baidu.com\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\",\"invalid\":\"invalid\",\"invalidDetail\":\"invalidDetail\"},\"headerScript\":\"\",\"headerFunctionName\":\"delete_mapping__c\",\"resultFormat\":{\"codeName\":\"500\",\"msgName\":\"1111\",\"dataName\":\"111\",\"successCode\":\"200\"},\"pushDataApiNames\":[]}"

        ERP_U8_EAI       | null                | "{\"baseUrl\":\"123\",\"sender\":\"123\",\"reciver\":\"123\",\"authType\":1,\"userName\":\"123\",\"password\":\"123\"}"

        ERP_U8           | null                | "{\"baseUrl\":\"http://api.yonyouup.com\",\"fromAccount\":\"1113\",\"toAccount\":\"222\",\"appKey\":\"111\",\"appSecret\":\"111\",\"ds_sequence\":222,\"pushDataApiNames\":[\"saleorder\"]}"
    }

    def "getPushDataApiNames"() {
        expect:
        channel.getPushDataApiNames(connectParam) == pushNames

        where:
        channel          | pushNames             | connectParam
        STANDARD_CHANNEL | ["kjshfg", "ksduhfj"] | "{\"systemName\":\"test system\",\"baseUrl\":\"12231\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\"},\"headerScript\":\"\",\"headerFunctionName\":\"TestInvalid__c\",\"resultFormat\":{\"codeName\":\"1231\",\"msgName\":\"123\",\"dataName\":\"1\",\"successCode\":\"122\"},\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"]}"

        ERP_DB_PROXY     | ["kjshfg", "ksduhfj"] | "{\"baseUrl\":\"http://**********:8080/dev/dbadapt/proxy/api\",\"userName\":\"admin\",\"password\":\"123456\",\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"] ,\"version\":\"1.1.beta.20230801\",\"dbName\":\"db2\",\"dbType\":\"postgresql\"}"

        ERP_K3CLOUD      | ["kjshfg", "ksduhfj"] | "{\"baseUrl\":\"https://api.kingdee.com/galaxyapi/\",\"dbId\":\"1584143499752675328\",\"dbName\":\"测试_北京仁宇通达供应链管理有限公司\",\"authType\":2,\"userName\":\"Administrator\",\"password\":\"337d709a41b24bc7b0d4d9570a971ed3\",\"appId\":\"241627_w35OQaEv6vAexeWE265DS9UI2g7b7rkv\",\"lcid\":2052,\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"] ,\"useFsHttpClient\":true}"

        ERP_SAP          | ["kjshfg", "ksduhfj"] | "{\"baseUrl\":\"www.baidu.com\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\",\"invalid\":\"invalid\",\"invalidDetail\":\"invalidDetail\"},\"headerScript\":\"\",\"headerFunctionName\":\"delete_mapping__c\",\"resultFormat\":{\"codeName\":\"500\",\"msgName\":\"1111\",\"dataName\":\"111\",\"successCode\":\"200\"},\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"] }"

        ERP_U8_EAI       | null                  | "{\"baseUrl\":\"123\",\"sender\":\"123\",\"reciver\":\"123\",\"authType\":1,\"userName\":\"123\",\"password\":\"123\"}"

        ERP_U8           | ["saleorder"]         | "{\"baseUrl\":\"http://api.yonyouup.com\",\"fromAccount\":\"1113\",\"toAccount\":\"222\",\"appKey\":\"111\",\"appSecret\":\"111\",\"ds_sequence\":222,\"pushDataApiNames\":[\"saleorder\"]}"
    }

    def "getNewConnectParam"() {
        when:
        def object = channel.getNewConnectParam(connectParam)[fieldName]

        then:
        object != null
        object.getClass() == channel.getClassName()

        where:
        channel          | fieldName  | connectParam
        STANDARD_CHANNEL | "standard" | "{\"systemName\":\"test system\",\"baseUrl\":\"12231\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\"},\"headerScript\":\"\",\"headerFunctionName\":\"TestInvalid__c\",\"resultFormat\":{\"codeName\":\"1231\",\"msgName\":\"123\",\"dataName\":\"1\",\"successCode\":\"122\"},\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"]}"

        ERP_DB_PROXY     | "dbProxy"  | "{\"baseUrl\":\"http://**********:8080/dev/dbadapt/proxy/api\",\"userName\":\"admin\",\"password\":\"123456\",\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"] ,\"version\":\"1.1.beta.20230801\",\"dbName\":\"db2\",\"dbType\":\"postgresql\"}"

        ERP_K3CLOUD      | "k3Cloud"  | "{\"baseUrl\":\"https://api.kingdee.com/galaxyapi/\",\"dbId\":\"1584143499752675328\",\"dbName\":\"测试_北京仁宇通达供应链管理有限公司\",\"authType\":2,\"userName\":\"Administrator\",\"password\":\"337d709a41b24bc7b0d4d9570a971ed3\",\"appId\":\"241627_w35OQaEv6vAexeWE265DS9UI2g7b7rkv\",\"lcid\":2052,\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"] ,\"useFsHttpClient\":true}"

        ERP_SAP          | "sap"      | "{\"baseUrl\":\"www.baidu.com\",\"servicePath\":{\"create\":\"create\",\"createDetail\":\"createDetail\",\"update\":\"update\",\"updateDetail\":\"updateDetail\",\"view\":\"queryMasterById\",\"queryByTime\":\"queryMasterBatch\",\"queryInvalidByTime\":\"queryInvalid\",\"invalid\":\"invalid\",\"invalidDetail\":\"invalidDetail\"},\"headerScript\":\"\",\"headerFunctionName\":\"delete_mapping__c\",\"resultFormat\":{\"codeName\":\"500\",\"msgName\":\"1111\",\"dataName\":\"111\",\"successCode\":\"200\"},\"pushDataApiNames\":[\"kjshfg\",\"ksduhfj\"] }"

        ERP_U8_EAI       | "u8Eai"    | "{\"baseUrl\":\"123\",\"sender\":\"123\",\"reciver\":\"123\",\"authType\":1,\"userName\":\"123\",\"password\":\"123\"}"

        ERP_U8           | "u8"       | "{\"baseUrl\":\"http://api.yonyouup.com\",\"fromAccount\":\"1113\",\"toAccount\":\"222\",\"appKey\":\"111\",\"appSecret\":\"111\",\"ds_sequence\":222,\"pushDataApiNames\":[\"saleorder\"]}"
    }
}
