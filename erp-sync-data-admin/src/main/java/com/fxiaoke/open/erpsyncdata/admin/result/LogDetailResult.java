package com.fxiaoke.open.erpsyncdata.admin.result;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/21 15:06
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class LogDetailResult<T> implements Serializable {
    private String syncLogType;
    private List<T> data;
}
