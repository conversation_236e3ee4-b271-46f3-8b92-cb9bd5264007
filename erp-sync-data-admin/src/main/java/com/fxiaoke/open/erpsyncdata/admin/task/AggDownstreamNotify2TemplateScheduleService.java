package com.fxiaoke.open.erpsyncdata.admin.task;

import com.facishare.converter.EIEAConverter;
import com.fxiaoke.open.erpsyncdata.admin.remote.UserRoleManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.RelationManageGroupDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.RelationManageGroupEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.UpstreamAlertAggregationDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.UpstreamAlertAggregationEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.util.ConfigCenter;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.SendTextNoticeArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmLevel;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmRuleType;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.AlarmType;
import com.fxiaoke.open.erpsyncdata.preprocess.service.NotificationService;
import com.fxiaoke.open.erpsyncdata.dbproxy.remote.service.UserCenterService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/3/26 11:22:21
 */
@Component
@Slf4j
public class AggDownstreamNotify2TemplateScheduleService implements InitializingBean {

    @Autowired
    private UpstreamAlertAggregationDao upstreamAlertAggregationDao;

    @Autowired
    private RelationManageGroupDao relationManageGroupDao;

    @Autowired
    private I18NStringManager i18NStringManager;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private EIEAConverter eieaConverter;

    private final ScheduledExecutorService scheduledThreadPool =
            new ScheduledThreadPoolExecutor(1, new ThreadFactoryBuilder()
                    .setNameFormat("agg_downstream_notify_to_template-%d").build());

    @Override
    public void afterPropertiesSet() throws Exception {
        scheduledThreadPool.scheduleAtFixedRate(this::scheduleTask, 30, 10, TimeUnit.SECONDS);
    }

    private void scheduleTask() {
        while (true) {
            final UpstreamAlertAggregationEntity entity = upstreamAlertAggregationDao.getFirstAndDelete(ConfigCenter.UPSTREAM_ALERT_AGG_DELAY_TIME_SECONDS);
            if (Objects.isNull(entity)) {
                break;
            }
            try {
                notifyUpstream(entity);
            } catch (Exception e) {
                log.error("AggDownstreamNotify2TemplateScheduleService error entity:{}", entity, e);
            }
        }
    }

    private void notifyUpstream(UpstreamAlertAggregationEntity entity) {
        final String tenantId = entity.getTenantId();
        final RelationManageGroupEntity relationManageGroupEntity = relationManageGroupDao.queryByTenantIdAndDcId(tenantId, entity.getDcId());
        if (Objects.isNull(relationManageGroupEntity)) {
            log.info("notifyUpstream 分组已删除,不通知");
            return;
        }

        final String start = DateFormatUtils.format(entity.getCreateTime(), "yyyy-MM-dd hh:mm:ss");
        final String end = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd hh:mm:ss");

        String content = i18NStringManager.get2(I18NStringEnum.s3613, null, tenantId, relationManageGroupEntity.getName()) + "\n" +
                i18NStringManager.get2(I18NStringEnum.s3614, null, tenantId, start, end) + "\n" +
                getNotifyNum(entity) + "\n" +
                i18NStringManager.get(I18NStringEnum.s3616, null, tenantId) + "\n" +
                getNotifyEnterpriseName(entity);

        final String msgTitle = i18NStringManager.get(I18NStringEnum.s3617, null, tenantId);

        sendMsg2ErpDssRole(tenantId, msgTitle, content, AlarmLevel.IMPORTANT);
    }

    public void sendMsg2ErpDssRole(String tenantId, String msgTitle, String content, AlarmLevel alarmLevel) {
        final List<Integer> userIds = userRoleManager.getUserIdsBySystemRoleCode(tenantId, UserRoleManager.ERPDSS_MANAGER_ROLE_CODE);
        if (CollectionUtils.isEmpty(userIds)) {
            log.info("notifyUpstream 没有管理员,不通知");
            return;
        }
        final SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setEnterpriseAccount(eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId)));
        arg.setTenantId(tenantId);
        arg.setTenantIdOld(tenantId);
        arg.setReceivers(userIds);
        arg.setMsgTitle(msgTitle);
        arg.setMsg(content);

        notificationService.sendErpSyncDataAppNotice(arg, AlarmRuleType.OTHER, msgTitle, AlarmType.OTHER,alarmLevel);
    }

    private String getNotifyNum(UpstreamAlertAggregationEntity entity) {
        return Stream.of(
                getNotifyNum(entity.getTenantId(), entity.getUrgentTenantIds(), entity.getUrgentPloyIds(), I18NStringEnum.s2269),
                getNotifyNum(entity.getTenantId(), entity.getImportantTenantIds(), entity.getImportantPloyIds(), I18NStringEnum.s2270),
                getNotifyNum(entity.getTenantId(), entity.getGeneralTenantIds(), entity.getGeneralPloyIds(), I18NStringEnum.s2271)
        ).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n"));
    }

    private String getNotifyNum(String tenantId, List<String> eis, List<String> ployDetailIds, I18NStringEnum i18NStringEnum) {
        if (CollectionUtils.isEmpty(eis)) {
            return null;
        }

        return i18NStringManager.get(i18NStringEnum, null, tenantId) +
                i18NStringManager.get2(I18NStringEnum.s3615, null, tenantId,
                        String.valueOf(eis.size()),
                        String.valueOf(Optional.ofNullable(ployDetailIds).map(Collection::size).orElse(0)));
    }

    private String getNotifyEnterpriseName(UpstreamAlertAggregationEntity entity) {
        return Stream.of(
                getNotifyEnterpriseName(entity.getTenantId(), entity.getUrgentTenantIds(), I18NStringEnum.s2269),
                getNotifyEnterpriseName(entity.getTenantId(), entity.getImportantTenantIds(), I18NStringEnum.s2270),
                getNotifyEnterpriseName(entity.getTenantId(), entity.getGeneralTenantIds(), I18NStringEnum.s2271)
        ).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n"));
    }

    private String getNotifyEnterpriseName(String tenantId, List<String> eis, I18NStringEnum i18NStringEnum) {
        if (CollectionUtils.isEmpty(eis)) {
            return null;
        }

        final Map<String, String> nameMap = userCenterService.batchGetEnterpriseName(eis);
        String enterpriseName = String.join(",", nameMap.values());
        return i18NStringManager.get(i18NStringEnum, null, tenantId) + ": " + enterpriseName;
    }
}
